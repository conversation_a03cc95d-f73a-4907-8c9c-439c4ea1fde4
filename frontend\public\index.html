<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#0ea5e9" />
    <meta name="description" content="نظام إدارة الملفات التعليمية - منصة شاملة لتنظيم ومشاركة الملفات التعليمية" />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&family=Roboto:wght@300;400;500;700&family=Fira+Code:wght@300;400;500;600&display=swap" rel="stylesheet">
    
    <!-- Meta Tags for SEO -->
    <meta property="og:title" content="مكتبة الملفات التعليمية" />
    <meta property="og:description" content="نظام إدارة الملفات التعليمية - منصة شاملة لتنظيم ومشاركة الملفات التعليمية" />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="%PUBLIC_URL%/logo512.png" />
    
    <title>مكتبة الملفات التعليمية</title>
    
    <style>
      /* Loading Screen Styles */
      #loading-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 50%, #fdf4ff 100%);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        transition: opacity 0.5s ease-out;
      }
      
      #loading-screen.fade-out {
        opacity: 0;
        pointer-events: none;
      }
      
      .loading-logo {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #0ea5e9, #0284c7);
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 32px;
        margin-bottom: 24px;
        animation: pulse 2s infinite;
        box-shadow: 0 10px 25px rgba(14, 165, 233, 0.3);
      }
      
      .loading-text {
        font-family: 'Cairo', sans-serif;
        font-size: 24px;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 16px;
        text-align: center;
      }
      
      .loading-subtitle {
        font-family: 'Cairo', sans-serif;
        font-size: 16px;
        color: #6b7280;
        text-align: center;
        margin-bottom: 32px;
      }
      
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid #e5e7eb;
        border-top: 3px solid #0ea5e9;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes pulse {
        0%, 100% {
          transform: scale(1);
        }
        50% {
          transform: scale(1.05);
        }
      }
      
      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }
      
      /* Hide scrollbar during loading */
      body.loading {
        overflow: hidden;
      }
      
      /* Custom scrollbar */
      ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }
      
      ::-webkit-scrollbar-track {
        background: #f3f4f6;
        border-radius: 4px;
      }
      
      ::-webkit-scrollbar-thumb {
        background: #d1d5db;
        border-radius: 4px;
      }
      
      ::-webkit-scrollbar-thumb:hover {
        background: #9ca3af;
      }
    </style>
  </head>
  <body class="loading">
    <noscript>يجب تفعيل JavaScript لتشغيل هذا التطبيق.</noscript>
    
    <!-- Loading Screen -->
    <div id="loading-screen">
      <div class="loading-logo">
        📚
      </div>
      <div class="loading-text">مكتبة الملفات التعليمية</div>
      <div class="loading-subtitle">جاري تحميل النظام...</div>
      <div class="loading-spinner"></div>
    </div>
    
    <div id="root"></div>
    
    <script>
      // Hide loading screen when app is ready
      window.addEventListener('load', function() {
        setTimeout(function() {
          const loadingScreen = document.getElementById('loading-screen');
          const body = document.body;
          
          if (loadingScreen) {
            loadingScreen.classList.add('fade-out');
            body.classList.remove('loading');
            
            setTimeout(function() {
              loadingScreen.style.display = 'none';
            }, 500);
          }
        }, 1000); // Show loading for at least 1 second
      });
      
      // Fallback: hide loading screen after 5 seconds
      setTimeout(function() {
        const loadingScreen = document.getElementById('loading-screen');
        const body = document.body;
        
        if (loadingScreen && !loadingScreen.classList.contains('fade-out')) {
          loadingScreen.classList.add('fade-out');
          body.classList.remove('loading');
          
          setTimeout(function() {
            loadingScreen.style.display = 'none';
          }, 500);
        }
      }, 5000);
    </script>
  </body>
</html>
