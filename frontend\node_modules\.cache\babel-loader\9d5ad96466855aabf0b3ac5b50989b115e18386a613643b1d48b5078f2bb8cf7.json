{"ast": null, "code": "export function buildMatchPatternFn(args) {\n  return (string, options = {}) => {\n    const matchResult = string.match(args.matchPattern);\n    if (!matchResult) return null;\n    const matchedString = matchResult[0];\n    const parseResult = string.match(args.parsePattern);\n    if (!parseResult) return null;\n    let value = args.valueCallback ? args.valueCallback(parseResult[0]) : parseResult[0];\n\n    // [TODO] I challenge you to fix the type\n    value = options.valueCallback ? options.valueCallback(value) : value;\n    const rest = string.slice(matchedString.length);\n    return {\n      value,\n      rest\n    };\n  };\n}", "map": {"version": 3, "names": ["buildMatchPatternFn", "args", "string", "options", "matchResult", "match", "matchPattern", "matchedString", "parseResult", "parsePattern", "value", "valueCallback", "rest", "slice", "length"], "sources": ["D:/menasa/frontend/node_modules/date-fns/locale/_lib/buildMatchPatternFn.js"], "sourcesContent": ["export function buildMatchPatternFn(args) {\n  return (string, options = {}) => {\n    const matchResult = string.match(args.matchPattern);\n    if (!matchResult) return null;\n    const matchedString = matchResult[0];\n\n    const parseResult = string.match(args.parsePattern);\n    if (!parseResult) return null;\n    let value = args.valueCallback\n      ? args.valueCallback(parseResult[0])\n      : parseResult[0];\n\n    // [TODO] I challenge you to fix the type\n    value = options.valueCallback ? options.valueCallback(value) : value;\n\n    const rest = string.slice(matchedString.length);\n\n    return { value, rest };\n  };\n}\n"], "mappings": "AAAA,OAAO,SAASA,mBAAmBA,CAACC,IAAI,EAAE;EACxC,OAAO,CAACC,MAAM,EAAEC,OAAO,GAAG,CAAC,CAAC,KAAK;IAC/B,MAAMC,WAAW,GAAGF,MAAM,CAACG,KAAK,CAACJ,IAAI,CAACK,YAAY,CAAC;IACnD,IAAI,CAACF,WAAW,EAAE,OAAO,IAAI;IAC7B,MAAMG,aAAa,GAAGH,WAAW,CAAC,CAAC,CAAC;IAEpC,MAAMI,WAAW,GAAGN,MAAM,CAACG,KAAK,CAACJ,IAAI,CAACQ,YAAY,CAAC;IACnD,IAAI,CAACD,WAAW,EAAE,OAAO,IAAI;IAC7B,IAAIE,KAAK,GAAGT,IAAI,CAACU,aAAa,GAC1BV,IAAI,CAACU,aAAa,CAACH,WAAW,CAAC,CAAC,CAAC,CAAC,GAClCA,WAAW,CAAC,CAAC,CAAC;;IAElB;IACAE,KAAK,GAAGP,OAAO,CAACQ,aAAa,GAAGR,OAAO,CAACQ,aAAa,CAACD,KAAK,CAAC,GAAGA,KAAK;IAEpE,MAAME,IAAI,GAAGV,MAAM,CAACW,KAAK,CAACN,aAAa,CAACO,MAAM,CAAC;IAE/C,OAAO;MAAEJ,KAAK;MAAEE;IAAK,CAAC;EACxB,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}