{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nconst getTag = require('../_internal/getTag.js');\nfunction isArguments(value) {\n  return value !== null && typeof value === 'object' && getTag.getTag(value) === '[object Arguments]';\n}\nexports.isArguments = isArguments;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "getTag", "require", "isArguments"], "sources": ["D:/menasa/frontend/node_modules/es-toolkit/dist/compat/predicate/isArguments.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nconst getTag = require('../_internal/getTag.js');\n\nfunction isArguments(value) {\n    return value !== null && typeof value === 'object' && getTag.getTag(value) === '[object Arguments]';\n}\n\nexports.isArguments = isArguments;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,MAAMC,MAAM,GAAGC,OAAO,CAAC,wBAAwB,CAAC;AAEhD,SAASC,WAAWA,CAACH,KAAK,EAAE;EACxB,OAAOA,KAAK,KAAK,IAAI,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIC,MAAM,CAACA,MAAM,CAACD,KAAK,CAAC,KAAK,oBAAoB;AACvG;AAEAH,OAAO,CAACM,WAAW,GAAGA,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}