{"version": 3, "file": "utils.esm.js", "sources": ["../../src/utils.ts"], "sourcesContent": ["import * as React from 'react'\nimport SuperJSON from 'superjson'\n\nimport { useTheme } from './theme'\nimport useMediaQuery from './useMediaQuery'\nimport type { Theme } from './theme'\nimport type { Query } from '@tanstack/react-query'\n\ntype StyledComponent<T> = T extends 'button'\n  ? React.DetailedHTMLProps<\n      React.ButtonHTMLAttributes<HTMLButtonElement>,\n      HTMLButtonElement\n    >\n  : T extends 'input'\n  ? React.DetailedHTMLProps<\n      React.InputHTMLAttributes<HTMLInputElement>,\n      HTMLInputElement\n    >\n  : T extends 'select'\n  ? React.DetailedHTMLProps<\n      React.SelectHTMLAttributes<HTMLSelectElement>,\n      HTMLSelectElement\n    >\n  : T extends keyof HTMLElementTagNameMap\n  ? React.HTMLAttributes<HTMLElementTagNameMap[T]>\n  : never\n\nexport function getQueryStatusColor({\n  queryState,\n  observerCount,\n  isStale,\n  theme,\n}: {\n  queryState: Query['state']\n  observerCount: number\n  isStale: boolean\n  theme: Theme\n}) {\n  return queryState.fetchStatus === 'fetching'\n    ? theme.active\n    : !observerCount\n    ? theme.gray\n    : queryState.fetchStatus === 'paused'\n    ? theme.paused\n    : isStale\n    ? theme.warning\n    : theme.success\n}\n\nexport function getQueryStatusLabel(query: Query) {\n  return query.state.fetchStatus === 'fetching'\n    ? 'fetching'\n    : !query.getObserversCount()\n    ? 'inactive'\n    : query.state.fetchStatus === 'paused'\n    ? 'paused'\n    : query.isStale()\n    ? 'stale'\n    : 'fresh'\n}\n\ntype Styles =\n  | React.CSSProperties\n  | ((props: Record<string, any>, theme: Theme) => React.CSSProperties)\n\nexport function styled<T extends keyof HTMLElementTagNameMap>(\n  type: T,\n  newStyles: Styles,\n  queries: Record<string, Styles> = {},\n) {\n  return React.forwardRef<HTMLElementTagNameMap[T], StyledComponent<T>>(\n    ({ style, ...rest }, ref) => {\n      const theme = useTheme()\n\n      const mediaStyles = Object.entries(queries).reduce(\n        (current, [key, value]) => {\n          // eslint-disable-next-line react-hooks/rules-of-hooks\n          return useMediaQuery(key)\n            ? {\n                ...current,\n                ...(typeof value === 'function' ? value(rest, theme) : value),\n              }\n            : current\n        },\n        {},\n      )\n\n      return React.createElement(type, {\n        ...rest,\n        style: {\n          ...(typeof newStyles === 'function'\n            ? newStyles(rest, theme)\n            : newStyles),\n          ...style,\n          ...mediaStyles,\n        },\n        ref,\n      })\n    },\n  )\n}\n\nexport function useIsMounted() {\n  const mountedRef = React.useRef(false)\n  const isMounted = React.useCallback(() => mountedRef.current, [])\n\n  React.useEffect(() => {\n    mountedRef.current = true\n    return () => {\n      mountedRef.current = false\n    }\n  }, [])\n\n  return isMounted\n}\n\n/**\n * Displays a string regardless the type of the data\n * @param {unknown} value Value to be stringified\n * @param {boolean} beautify Formats json to multiline\n */\nexport const displayValue = (value: unknown, beautify: boolean = false) => {\n  const { json } = SuperJSON.serialize(value)\n\n  return JSON.stringify(json, null, beautify ? 2 : undefined)\n}\n\n// Sorting functions\ntype SortFn = (a: Query, b: Query) => number\n\nconst getStatusRank = (q: Query) =>\n  q.state.fetchStatus !== 'idle'\n    ? 0\n    : !q.getObserversCount()\n    ? 3\n    : q.isStale()\n    ? 2\n    : 1\n\nconst queryHashSort: SortFn = (a, b) => a.queryHash.localeCompare(b.queryHash)\n\nconst dateSort: SortFn = (a, b) =>\n  a.state.dataUpdatedAt < b.state.dataUpdatedAt ? 1 : -1\n\nconst statusAndDateSort: SortFn = (a, b) => {\n  if (getStatusRank(a) === getStatusRank(b)) {\n    return dateSort(a, b)\n  }\n\n  return getStatusRank(a) > getStatusRank(b) ? 1 : -1\n}\n\nexport const sortFns: Record<string, SortFn> = {\n  'Status > Last Updated': statusAndDateSort,\n  'Query Hash': queryHashSort,\n  'Last Updated': dateSort,\n}\n\nexport const minPanelSize = 70\nexport const defaultPanelSize = 500\nexport const sides: Record<Side, Side> = {\n  top: 'bottom',\n  bottom: 'top',\n  left: 'right',\n  right: 'left',\n}\n\nexport type Corner = 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'\nexport type Side = 'left' | 'right' | 'top' | 'bottom'\n/**\n * Check if the given side is vertical (left/right)\n */\nexport function isVerticalSide(side: Side) {\n  return ['left', 'right'].includes(side)\n}\n/**\n * Get the opposite side, eg 'left' => 'right'. 'top' => 'bottom', etc\n */\nexport function getOppositeSide(side: Side): Side {\n  return sides[side]\n}\n/**\n * Given as css prop it will return a sided css prop based on a given side\n * Example given `border` and `right` it return `borderRight`\n */\nexport function getSidedProp<T extends string>(prop: T, side: Side) {\n  return `${prop}${\n    side.charAt(0).toUpperCase() + side.slice(1)\n  }` as `${T}${Capitalize<Side>}`\n}\n\nexport interface SidePanelStyleOptions {\n  /**\n   * Position of the panel\n   * Defaults to 'bottom'\n   */\n  position?: Side\n  /**\n   * Staring height for the panel, it is set if the position is horizontal eg 'top' or 'bottom'\n   * Defaults to 500\n   */\n  height?: number\n  /**\n   * Staring width for the panel, it is set if the position is vertical eg 'left' or 'right'\n   * Defaults to 500\n   */\n  width?: number\n  /**\n   * RQ devtools theme\n   */\n  devtoolsTheme: Theme\n  /**\n   * Sets the correct transition and visibility styles\n   */\n  isOpen?: boolean\n  /**\n   * If the panel is resizing set to true to apply the correct transition styles\n   */\n  isResizing?: boolean\n  /**\n   * Extra panel style passed by the user\n   */\n  panelStyle?: React.CSSProperties\n}\n\nexport function getSidePanelStyle({\n  position = 'bottom',\n  height,\n  width,\n  devtoolsTheme,\n  isOpen,\n  isResizing,\n  panelStyle,\n}: SidePanelStyleOptions): React.CSSProperties {\n  const oppositeSide = getOppositeSide(position)\n  const borderSide = getSidedProp('border', oppositeSide)\n  const isVertical = isVerticalSide(position)\n\n  return {\n    ...panelStyle,\n    direction: 'ltr',\n    position: 'fixed',\n    [position]: 0,\n    [borderSide]: `1px solid ${devtoolsTheme.gray}`,\n    transformOrigin: oppositeSide,\n    boxShadow: '0 0 20px rgba(0,0,0,.3)',\n    zIndex: 99999,\n    // visibility will be toggled after transitions, but set initial state here\n    visibility: isOpen ? 'visible' : 'hidden',\n    ...(isResizing\n      ? {\n          transition: `none`,\n        }\n      : { transition: `all .2s ease` }),\n    ...(isOpen\n      ? {\n          opacity: 1,\n          pointerEvents: 'all',\n          transform: isVertical\n            ? `translateX(0) scale(1)`\n            : `translateY(0) scale(1)`,\n        }\n      : {\n          opacity: 0,\n          pointerEvents: 'none',\n          transform: isVertical\n            ? `translateX(15px) scale(1.02)`\n            : `translateY(15px) scale(1.02)`,\n        }),\n    ...(isVertical\n      ? {\n          top: 0,\n          height: '100vh',\n          maxWidth: '90%',\n          width:\n            typeof width === 'number' && width >= minPanelSize\n              ? width\n              : defaultPanelSize,\n        }\n      : {\n          left: 0,\n          width: '100%',\n          maxHeight: '90%',\n          height:\n            typeof height === 'number' && height >= minPanelSize\n              ? height\n              : defaultPanelSize,\n        }),\n  }\n}\n\n/**\n * Get resize handle style based on a given side\n */\nexport function getResizeHandleStyle(\n  position: Side = 'bottom',\n): React.CSSProperties {\n  const isVertical = isVerticalSide(position)\n  const oppositeSide = getOppositeSide(position)\n  const marginSide = getSidedProp('margin', oppositeSide)\n\n  return {\n    position: 'absolute',\n    cursor: isVertical ? 'col-resize' : 'row-resize',\n    zIndex: 100000,\n    [oppositeSide]: 0,\n    [marginSide]: `-4px`,\n    ...(isVertical\n      ? {\n          top: 0,\n          height: '100%',\n          width: '4px',\n        }\n      : {\n          width: '100%',\n          height: '4px',\n        }),\n  }\n}\n"], "names": ["getQueryStatusColor", "queryState", "observerCount", "isStale", "theme", "fetchStatus", "active", "gray", "paused", "warning", "success", "getQueryStatusLabel", "query", "state", "getObserversCount", "styled", "type", "newStyles", "queries", "React", "forwardRef", "style", "rest", "ref", "useTheme", "mediaStyles", "Object", "entries", "reduce", "current", "key", "value", "useMediaQuery", "createElement", "useIsMounted", "mountedRef", "useRef", "isMounted", "useCallback", "useEffect", "displayValue", "beautify", "json", "SuperJSON", "serialize", "JSON", "stringify", "undefined", "getStatusRank", "q", "queryHashSort", "a", "b", "queryHash", "localeCompare", "dateSort", "dataUpdatedAt", "statusAndDateSort", "sortFns", "minPanelSize", "defaultPanelSize", "sides", "top", "bottom", "left", "right", "isVerticalSide", "side", "includes", "getOppositeSide", "getSidedProp", "prop", "char<PERSON>t", "toUpperCase", "slice", "getSidePanelStyle", "position", "height", "width", "devtoolsTheme", "isOpen", "isResizing", "panelStyle", "oppositeSide", "borderSide", "isVertical", "direction", "transform<PERSON><PERSON>in", "boxShadow", "zIndex", "visibility", "transition", "opacity", "pointerEvents", "transform", "max<PERSON><PERSON><PERSON>", "maxHeight", "getResizeHandleStyle", "marginSide", "cursor"], "mappings": ";;;;;AA2BO,SAASA,mBAAT,CAA6B;EAClCC,UADkC;EAElCC,aAFkC;EAGlCC,OAHkC;AAIlCC,EAAAA,KAAAA;AAJkC,CAA7B,EAUJ;AACD,EAAA,OAAOH,UAAU,CAACI,WAAX,KAA2B,UAA3B,GACHD,KAAK,CAACE,MADH,GAEH,CAACJ,aAAD,GACAE,KAAK,CAACG,IADN,GAEAN,UAAU,CAACI,WAAX,KAA2B,QAA3B,GACAD,KAAK,CAACI,MADN,GAEAL,OAAO,GACPC,KAAK,CAACK,OADC,GAEPL,KAAK,CAACM,OARV,CAAA;AASD,CAAA;AAEM,SAASC,mBAAT,CAA6BC,KAA7B,EAA2C;AAChD,EAAA,OAAOA,KAAK,CAACC,KAAN,CAAYR,WAAZ,KAA4B,UAA5B,GACH,UADG,GAEH,CAACO,KAAK,CAACE,iBAAN,EAAD,GACA,UADA,GAEAF,KAAK,CAACC,KAAN,CAAYR,WAAZ,KAA4B,QAA5B,GACA,QADA,GAEAO,KAAK,CAACT,OAAN,EACA,GAAA,OADA,GAEA,OARJ,CAAA;AASD,CAAA;AAMM,SAASY,MAAT,CACLC,IADK,EAELC,SAFK,EAGLC,OAA+B,GAAG,EAH7B,EAIL;AACA,EAAA,oBAAOC,KAAK,CAACC,UAAN,CACL,CAAC;IAAEC,KAAF;IAAS,GAAGC,IAAAA;GAAb,EAAqBC,GAArB,KAA6B;IAC3B,MAAMnB,KAAK,GAAGoB,QAAQ,EAAtB,CAAA;AAEA,IAAA,MAAMC,WAAW,GAAGC,MAAM,CAACC,OAAP,CAAeT,OAAf,CAAwBU,CAAAA,MAAxB,CAClB,CAACC,OAAD,EAAU,CAACC,GAAD,EAAMC,KAAN,CAAV,KAA2B;AACzB;AACA,MAAA,OAAOC,aAAa,CAACF,GAAD,CAAb,GACH,EACE,GAAGD,OADL;AAEE,QAAA,IAAI,OAAOE,KAAP,KAAiB,UAAjB,GAA8BA,KAAK,CAACT,IAAD,EAAOlB,KAAP,CAAnC,GAAmD2B,KAAvD,CAAA;AAFF,OADG,GAKHF,OALJ,CAAA;KAHgB,EAUlB,EAVkB,CAApB,CAAA;IAaA,oBAAOV,KAAK,CAACc,aAAN,CAAoBjB,IAApB,EAA0B,EAC/B,GAAGM,IAD4B;AAE/BD,MAAAA,KAAK,EAAE,EACL,IAAI,OAAOJ,SAAP,KAAqB,UAArB,GACAA,SAAS,CAACK,IAAD,EAAOlB,KAAP,CADT,GAEAa,SAFJ,CADK;AAIL,QAAA,GAAGI,KAJE;QAKL,GAAGI,WAAAA;OAP0B;AAS/BF,MAAAA,GAAAA;AAT+B,KAA1B,CAAP,CAAA;AAWD,GA5BI,CAAP,CAAA;AA8BD,CAAA;AAEM,SAASW,YAAT,GAAwB;AAC7B,EAAA,MAAMC,UAAU,GAAGhB,KAAK,CAACiB,MAAN,CAAa,KAAb,CAAnB,CAAA;AACA,EAAA,MAAMC,SAAS,GAAGlB,KAAK,CAACmB,WAAN,CAAkB,MAAMH,UAAU,CAACN,OAAnC,EAA4C,EAA5C,CAAlB,CAAA;EAEAV,KAAK,CAACoB,SAAN,CAAgB,MAAM;IACpBJ,UAAU,CAACN,OAAX,GAAqB,IAArB,CAAA;AACA,IAAA,OAAO,MAAM;MACXM,UAAU,CAACN,OAAX,GAAqB,KAArB,CAAA;KADF,CAAA;AAGD,GALD,EAKG,EALH,CAAA,CAAA;AAOA,EAAA,OAAOQ,SAAP,CAAA;AACD,CAAA;AAED;AACA;AACA;AACA;AACA;;AACO,MAAMG,YAAY,GAAG,CAACT,KAAD,EAAiBU,QAAiB,GAAG,KAArC,KAA+C;EACzE,MAAM;AAAEC,IAAAA,IAAAA;AAAF,GAAA,GAAWC,SAAS,CAACC,SAAV,CAAoBb,KAApB,CAAjB,CAAA;AAEA,EAAA,OAAOc,IAAI,CAACC,SAAL,CAAeJ,IAAf,EAAqB,IAArB,EAA2BD,QAAQ,GAAG,CAAH,GAAOM,SAA1C,CAAP,CAAA;AACD;;AAKD,MAAMC,aAAa,GAAIC,CAAD,IACpBA,CAAC,CAACpC,KAAF,CAAQR,WAAR,KAAwB,MAAxB,GACI,CADJ,GAEI,CAAC4C,CAAC,CAACnC,iBAAF,EAAD,GACA,CADA,GAEAmC,CAAC,CAAC9C,OAAF,EAAA,GACA,CADA,GAEA,CAPN,CAAA;;AASA,MAAM+C,aAAqB,GAAG,CAACC,CAAD,EAAIC,CAAJ,KAAUD,CAAC,CAACE,SAAF,CAAYC,aAAZ,CAA0BF,CAAC,CAACC,SAA5B,CAAxC,CAAA;;AAEA,MAAME,QAAgB,GAAG,CAACJ,CAAD,EAAIC,CAAJ,KACvBD,CAAC,CAACtC,KAAF,CAAQ2C,aAAR,GAAwBJ,CAAC,CAACvC,KAAF,CAAQ2C,aAAhC,GAAgD,CAAhD,GAAoD,CAAC,CADvD,CAAA;;AAGA,MAAMC,iBAAyB,GAAG,CAACN,CAAD,EAAIC,CAAJ,KAAU;EAC1C,IAAIJ,aAAa,CAACG,CAAD,CAAb,KAAqBH,aAAa,CAACI,CAAD,CAAtC,EAA2C;AACzC,IAAA,OAAOG,QAAQ,CAACJ,CAAD,EAAIC,CAAJ,CAAf,CAAA;AACD,GAAA;;AAED,EAAA,OAAOJ,aAAa,CAACG,CAAD,CAAb,GAAmBH,aAAa,CAACI,CAAD,CAAhC,GAAsC,CAAtC,GAA0C,CAAC,CAAlD,CAAA;AACD,CAND,CAAA;;AAQO,MAAMM,OAA+B,GAAG;AAC7C,EAAA,uBAAA,EAAyBD,iBADoB;AAE7C,EAAA,YAAA,EAAcP,aAF+B;EAG7C,cAAgBK,EAAAA,QAAAA;AAH6B,EAAxC;AAMA,MAAMI,YAAY,GAAG,GAArB;AACA,MAAMC,gBAAgB,GAAG,IAAzB;AACA,MAAMC,KAAyB,GAAG;AACvCC,EAAAA,GAAG,EAAE,QADkC;AAEvCC,EAAAA,MAAM,EAAE,KAF+B;AAGvCC,EAAAA,IAAI,EAAE,OAHiC;AAIvCC,EAAAA,KAAK,EAAE,MAAA;AAJgC,EAAlC;;AASP;AACA;AACA;AACO,SAASC,cAAT,CAAwBC,IAAxB,EAAoC;EACzC,OAAO,CAAC,MAAD,EAAS,OAAT,EAAkBC,QAAlB,CAA2BD,IAA3B,CAAP,CAAA;AACD,CAAA;AACD;AACA;AACA;;AACO,SAASE,eAAT,CAAyBF,IAAzB,EAA2C;EAChD,OAAON,KAAK,CAACM,IAAD,CAAZ,CAAA;AACD,CAAA;AACD;AACA;AACA;AACA;;AACO,SAASG,YAAT,CAAwCC,IAAxC,EAAiDJ,IAAjD,EAA6D;AAClE,EAAA,OAAA,EAAA,GAAUI,IAAV,IACEJ,IAAI,CAACK,MAAL,CAAY,CAAZ,CAAeC,CAAAA,WAAf,KAA+BN,IAAI,CAACO,KAAL,CAAW,CAAX,CADjC,CAAA,CAAA;AAGD,CAAA;AAoCM,SAASC,iBAAT,CAA2B;AAChCC,EAAAA,QAAQ,GAAG,QADqB;EAEhCC,MAFgC;EAGhCC,KAHgC;EAIhCC,aAJgC;EAKhCC,MALgC;EAMhCC,UANgC;AAOhCC,EAAAA,UAAAA;AAPgC,CAA3B,EAQwC;AAC7C,EAAA,MAAMC,YAAY,GAAGd,eAAe,CAACO,QAAD,CAApC,CAAA;AACA,EAAA,MAAMQ,UAAU,GAAGd,YAAY,CAAC,QAAD,EAAWa,YAAX,CAA/B,CAAA;AACA,EAAA,MAAME,UAAU,GAAGnB,cAAc,CAACU,QAAD,CAAjC,CAAA;EAEA,OAAO,EACL,GAAGM,UADE;AAELI,IAAAA,SAAS,EAAE,KAFN;AAGLV,IAAAA,QAAQ,EAAE,OAHL;IAIL,CAACA,QAAD,GAAY,CAJP;AAKL,IAAA,CAACQ,UAAD,GAAA,YAAA,GAA2BL,aAAa,CAACxE,IALpC;AAMLgF,IAAAA,eAAe,EAAEJ,YANZ;AAOLK,IAAAA,SAAS,EAAE,yBAPN;AAQLC,IAAAA,MAAM,EAAE,KARH;AASL;AACAC,IAAAA,UAAU,EAAEV,MAAM,GAAG,SAAH,GAAe,QAV5B;AAWL,IAAA,IAAIC,UAAU,GACV;MACEU,UAAU,EAAA,MAAA;AADZ,KADU,GAIV;MAAEA,UAAU,EAAA,cAAA;AAAZ,KAJJ,CAXK;AAgBL,IAAA,IAAIX,MAAM,GACN;AACEY,MAAAA,OAAO,EAAE,CADX;AAEEC,MAAAA,aAAa,EAAE,KAFjB;AAGEC,MAAAA,SAAS,EAAET,UAAU,GAAA,wBAAA,GAAA,wBAAA;AAHvB,KADM,GAQN;AACEO,MAAAA,OAAO,EAAE,CADX;AAEEC,MAAAA,aAAa,EAAE,MAFjB;AAGEC,MAAAA,SAAS,EAAET,UAAU,GAAA,8BAAA,GAAA,8BAAA;AAHvB,KARJ,CAhBK;AA+BL,IAAA,IAAIA,UAAU,GACV;AACEvB,MAAAA,GAAG,EAAE,CADP;AAEEe,MAAAA,MAAM,EAAE,OAFV;AAGEkB,MAAAA,QAAQ,EAAE,KAHZ;MAIEjB,KAAK,EACH,OAAOA,KAAP,KAAiB,QAAjB,IAA6BA,KAAK,IAAInB,YAAtC,GACImB,KADJ,GAEIlB,gBAAAA;AAPR,KADU,GAUV;AACEI,MAAAA,IAAI,EAAE,CADR;AAEEc,MAAAA,KAAK,EAAE,MAFT;AAGEkB,MAAAA,SAAS,EAAE,KAHb;MAIEnB,MAAM,EACJ,OAAOA,MAAP,KAAkB,QAAlB,IAA8BA,MAAM,IAAIlB,YAAxC,GACIkB,MADJ,GAEIjB,gBAAAA;KAjBZ,CAAA;GA/BF,CAAA;AAmDD,CAAA;AAED;AACA;AACA;;AACO,SAASqC,oBAAT,CACLrB,QAAc,GAAG,QADZ,EAEgB;AACrB,EAAA,MAAMS,UAAU,GAAGnB,cAAc,CAACU,QAAD,CAAjC,CAAA;AACA,EAAA,MAAMO,YAAY,GAAGd,eAAe,CAACO,QAAD,CAApC,CAAA;AACA,EAAA,MAAMsB,UAAU,GAAG5B,YAAY,CAAC,QAAD,EAAWa,YAAX,CAA/B,CAAA;EAEA,OAAO;AACLP,IAAAA,QAAQ,EAAE,UADL;AAELuB,IAAAA,MAAM,EAAEd,UAAU,GAAG,YAAH,GAAkB,YAF/B;AAGLI,IAAAA,MAAM,EAAE,MAHH;IAIL,CAACN,YAAD,GAAgB,CAJX;AAKL,IAAA,CAACe,UAAD,GALK,MAAA;AAML,IAAA,IAAIb,UAAU,GACV;AACEvB,MAAAA,GAAG,EAAE,CADP;AAEEe,MAAAA,MAAM,EAAE,MAFV;AAGEC,MAAAA,KAAK,EAAE,KAAA;AAHT,KADU,GAMV;AACEA,MAAAA,KAAK,EAAE,MADT;AAEED,MAAAA,MAAM,EAAE,KAAA;KARd,CAAA;GANF,CAAA;AAiBD;;;;"}