{"ast": null, "code": "import { useEffect } from 'react';\nimport { useAppDispatch } from './hooks';\nimport { addBar, removeBar } from './graphicalItemsSlice';\nexport var ReportBar = () => {\n  var dispatch = useAppDispatch();\n  useEffect(() => {\n    dispatch(addBar());\n    return () => {\n      dispatch(removeBar());\n    };\n  });\n  return null;\n};", "map": {"version": 3, "names": ["useEffect", "useAppDispatch", "addBar", "removeBar", "ReportBar", "dispatch"], "sources": ["D:/menasa/frontend/node_modules/recharts/es6/state/ReportBar.js"], "sourcesContent": ["import { useEffect } from 'react';\nimport { useAppDispatch } from './hooks';\nimport { addBar, removeBar } from './graphicalItemsSlice';\nexport var ReportBar = () => {\n  var dispatch = useAppDispatch();\n  useEffect(() => {\n    dispatch(addBar());\n    return () => {\n      dispatch(removeBar());\n    };\n  });\n  return null;\n};"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,cAAc,QAAQ,SAAS;AACxC,SAASC,MAAM,EAAEC,SAAS,QAAQ,uBAAuB;AACzD,OAAO,IAAIC,SAAS,GAAGA,CAAA,KAAM;EAC3B,IAAIC,QAAQ,GAAGJ,cAAc,CAAC,CAAC;EAC/BD,SAAS,CAAC,MAAM;IACdK,QAAQ,CAACH,MAAM,CAAC,CAAC,CAAC;IAClB,OAAO,MAAM;MACXG,QAAQ,CAACF,SAAS,CAAC,CAAC,CAAC;IACvB,CAAC;EACH,CAAC,CAAC;EACF,OAAO,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}