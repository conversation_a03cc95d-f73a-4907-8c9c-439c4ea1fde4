{"ast": null, "code": "// مكتبة الأمان المتقدمة\n\nimport CryptoJS from 'crypto-js';\n\n// مفتاح التشفير (يجب أن يكون في متغيرات البيئة في الإنتاج)\nconst ENCRYPTION_KEY = process.env.REACT_APP_ENCRYPTION_KEY || 'default-key-change-in-production';\n\n// فئة إدارة الأمان\nexport class SecurityManager {\n  constructor() {\n    this.sessionTimeout = 30 * 60 * 1000; // 30 دقيقة\n    this.maxLoginAttempts = 5;\n    this.lockoutDuration = 15 * 60 * 1000; // 15 دقيقة\n    this.initializeSecurityMonitoring();\n  }\n\n  // تشفير البيانات الحساسة\n  encrypt(data) {\n    try {\n      const encrypted = CryptoJS.AES.encrypt(JSON.stringify(data), ENCRYPTION_KEY).toString();\n      return encrypted;\n    } catch (error) {\n      console.error('Encryption failed:', error);\n      throw new Error('فشل في تشفير البيانات');\n    }\n  }\n\n  // فك تشفير البيانات\n  decrypt(encryptedData) {\n    try {\n      const bytes = CryptoJS.AES.decrypt(encryptedData, ENCRYPTION_KEY);\n      const decrypted = bytes.toString(CryptoJS.enc.Utf8);\n      return JSON.parse(decrypted);\n    } catch (error) {\n      console.error('Decryption failed:', error);\n      throw new Error('فشل في فك تشفير البيانات');\n    }\n  }\n\n  // تشفير كلمات المرور\n  hashPassword(password, salt = null) {\n    const saltToUse = salt || CryptoJS.lib.WordArray.random(128 / 8).toString();\n    const hash = CryptoJS.PBKDF2(password, saltToUse, {\n      keySize: 256 / 32,\n      iterations: 10000\n    }).toString();\n    return {\n      hash: hash,\n      salt: saltToUse\n    };\n  }\n\n  // التحقق من كلمة المرور\n  verifyPassword(password, hash, salt) {\n    const verifyHash = CryptoJS.PBKDF2(password, salt, {\n      keySize: 256 / 32,\n      iterations: 10000\n    }).toString();\n    return verifyHash === hash;\n  }\n\n  // إنشاء رمز مميز آمن\n  generateSecureToken(length = 32) {\n    const array = new Uint8Array(length);\n    window.crypto.getRandomValues(array);\n    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');\n  }\n\n  // التحقق من قوة كلمة المرور\n  checkPasswordStrength(password) {\n    const checks = {\n      length: password.length >= 8,\n      lowercase: /[a-z]/.test(password),\n      uppercase: /[A-Z]/.test(password),\n      numbers: /\\d/.test(password),\n      symbols: /[^A-Za-z0-9]/.test(password),\n      noCommonPatterns: !this.hasCommonPatterns(password)\n    };\n    const score = Object.values(checks).filter(Boolean).length;\n    return {\n      score,\n      checks,\n      strength: this.getPasswordStrengthLevel(score),\n      suggestions: this.getPasswordSuggestions(checks)\n    };\n  }\n\n  // التحقق من الأنماط الشائعة في كلمات المرور\n  hasCommonPatterns(password) {\n    const commonPatterns = [/123456/, /password/i, /qwerty/i, /admin/i, /letmein/i, /welcome/i, /monkey/i, /dragon/i];\n    return commonPatterns.some(pattern => pattern.test(password));\n  }\n\n  // تحديد مستوى قوة كلمة المرور\n  getPasswordStrengthLevel(score) {\n    if (score <= 2) return {\n      level: 'ضعيف جداً',\n      color: '#ef4444'\n    };\n    if (score <= 3) return {\n      level: 'ضعيف',\n      color: '#f97316'\n    };\n    if (score <= 4) return {\n      level: 'متوسط',\n      color: '#eab308'\n    };\n    if (score <= 5) return {\n      level: 'جيد',\n      color: '#22c55e'\n    };\n    return {\n      level: 'قوي جداً',\n      color: '#16a34a'\n    };\n  }\n\n  // اقتراحات لتحسين كلمة المرور\n  getPasswordSuggestions(checks) {\n    const suggestions = [];\n    if (!checks.length) suggestions.push('استخدم 8 أحرف على الأقل');\n    if (!checks.lowercase) suggestions.push('أضف أحرف صغيرة');\n    if (!checks.uppercase) suggestions.push('أضف أحرف كبيرة');\n    if (!checks.numbers) suggestions.push('أضف أرقام');\n    if (!checks.symbols) suggestions.push('أضف رموز خاصة');\n    if (!checks.noCommonPatterns) suggestions.push('تجنب الكلمات الشائعة');\n    return suggestions;\n  }\n\n  // إدارة محاولات تسجيل الدخول\n  trackLoginAttempt(email, success = false) {\n    const key = `login_attempts_${email}`;\n    const attempts = JSON.parse(localStorage.getItem(key) || '[]');\n    if (success) {\n      // مسح المحاولات عند النجاح\n      localStorage.removeItem(key);\n      return {\n        allowed: true,\n        remainingAttempts: this.maxLoginAttempts\n      };\n    }\n\n    // إضافة محاولة فاشلة\n    attempts.push(Date.now());\n\n    // إزالة المحاولات القديمة (أكثر من ساعة)\n    const oneHourAgo = Date.now() - 60 * 60 * 1000;\n    const recentAttempts = attempts.filter(time => time > oneHourAgo);\n    localStorage.setItem(key, JSON.stringify(recentAttempts));\n    const remainingAttempts = this.maxLoginAttempts - recentAttempts.length;\n    const isLocked = recentAttempts.length >= this.maxLoginAttempts;\n    return {\n      allowed: !isLocked,\n      remainingAttempts: Math.max(0, remainingAttempts),\n      lockoutTime: isLocked ? this.lockoutDuration : 0\n    };\n  }\n\n  // التحقق من انتهاء الجلسة\n  checkSessionTimeout() {\n    const lastActivity = localStorage.getItem('lastActivity');\n    if (!lastActivity) return false;\n    const timeSinceLastActivity = Date.now() - parseInt(lastActivity);\n    return timeSinceLastActivity > this.sessionTimeout;\n  }\n\n  // تحديث وقت النشاط الأخير\n  updateLastActivity() {\n    localStorage.setItem('lastActivity', Date.now().toString());\n  }\n\n  // تنظيف البيانات الحساسة عند تسجيل الخروج\n  clearSensitiveData() {\n    const keysToRemove = ['lastActivity', 'userPreferences', 'tempData'];\n    keysToRemove.forEach(key => {\n      localStorage.removeItem(key);\n      sessionStorage.removeItem(key);\n    });\n  }\n\n  // التحقق من سلامة البيانات\n  validateDataIntegrity(data, expectedHash) {\n    const calculatedHash = CryptoJS.SHA256(JSON.stringify(data)).toString();\n    return calculatedHash === expectedHash;\n  }\n\n  // إنشاء بصمة للجهاز\n  generateDeviceFingerprint() {\n    const canvas = document.createElement('canvas');\n    const ctx = canvas.getContext('2d');\n    ctx.textBaseline = 'top';\n    ctx.font = '14px Arial';\n    ctx.fillText('Device fingerprint', 2, 2);\n    const fingerprint = {\n      userAgent: navigator.userAgent,\n      language: navigator.language,\n      platform: navigator.platform,\n      // eslint-disable-next-line no-restricted-globals\n      screen: `${screen.width}x${screen.height}`,\n      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,\n      canvas: canvas.toDataURL(),\n      webgl: this.getWebGLFingerprint()\n    };\n    return CryptoJS.SHA256(JSON.stringify(fingerprint)).toString();\n  }\n\n  // الحصول على بصمة WebGL\n  getWebGLFingerprint() {\n    try {\n      const canvas = document.createElement('canvas');\n      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');\n      if (!gl) return 'no-webgl';\n      const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');\n      const vendor = gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL);\n      const renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL);\n      return `${vendor}~${renderer}`;\n    } catch (error) {\n      return 'webgl-error';\n    }\n  }\n\n  // مراقبة الأنشطة المشبوهة\n  initializeSecurityMonitoring() {\n    // مراقبة محاولات الوصول المتكررة\n    this.monitorRapidRequests();\n\n    // مراقبة تغييرات الجلسة\n    this.monitorSessionChanges();\n\n    // مراقبة أدوات المطور\n    this.monitorDevTools();\n  }\n\n  // مراقبة الطلبات السريعة المتتالية\n  monitorRapidRequests() {\n    let requestCount = 0;\n    const resetInterval = 60000; // دقيقة واحدة\n\n    setInterval(() => {\n      requestCount = 0;\n    }, resetInterval);\n\n    // يمكن استخدامها في interceptors\n    window.securityManager = {\n      ...window.securityManager,\n      checkRateLimit: () => {\n        requestCount++;\n        if (requestCount > 100) {\n          // 100 طلب في الدقيقة\n          console.warn('Rate limit exceeded');\n          return false;\n        }\n        return true;\n      }\n    };\n  }\n\n  // مراقبة تغييرات الجلسة\n  monitorSessionChanges() {\n    let lastFingerprint = this.generateDeviceFingerprint();\n    setInterval(() => {\n      const currentFingerprint = this.generateDeviceFingerprint();\n      if (currentFingerprint !== lastFingerprint) {\n        console.warn('Device fingerprint changed - possible session hijacking');\n        // يمكن إضافة إجراءات أمنية هنا\n      }\n      lastFingerprint = currentFingerprint;\n    }, 30000); // كل 30 ثانية\n  }\n\n  // مراقبة أدوات المطور\n  monitorDevTools() {\n    let devtools = {\n      open: false,\n      orientation: null\n    };\n    setInterval(() => {\n      if (window.outerHeight - window.innerHeight > 200 || window.outerWidth - window.innerWidth > 200) {\n        if (!devtools.open) {\n          devtools.open = true;\n          console.warn('Developer tools detected');\n          // يمكن إضافة إجراءات أمنية هنا\n        }\n      } else {\n        devtools.open = false;\n      }\n    }, 1000);\n  }\n\n  // تنظيف الذاكرة من البيانات الحساسة\n  secureCleanup() {\n    // مسح المتغيرات الحساسة\n    if (window.sensitiveData) {\n      for (let key in window.sensitiveData) {\n        delete window.sensitiveData[key];\n      }\n    }\n\n    // تشغيل garbage collection إذا كان متاحاً\n    if (window.gc) {\n      window.gc();\n    }\n  }\n}\n\n// إنشاء مثيل واحد من مدير الأمان\nexport const securityManager = new SecurityManager();\n\n// دوال مساعدة للاستخدام السريع\nexport const encrypt = data => securityManager.encrypt(data);\nexport const decrypt = data => securityManager.decrypt(data);\nexport const generateToken = length => securityManager.generateSecureToken(length);\nexport const checkPasswordStrength = password => securityManager.checkPasswordStrength(password);\nexport const trackLoginAttempt = (email, success) => securityManager.trackLoginAttempt(email, success);\n\n// تصدير افتراضي\nexport default securityManager;", "map": {"version": 3, "names": ["CryptoJS", "ENCRYPTION_KEY", "process", "env", "REACT_APP_ENCRYPTION_KEY", "SecurityManager", "constructor", "sessionTimeout", "maxLogin<PERSON><PERSON><PERSON>s", "lockoutDuration", "initializeSecurityMonitoring", "encrypt", "data", "encrypted", "AES", "JSON", "stringify", "toString", "error", "console", "Error", "decrypt", "encryptedData", "bytes", "decrypted", "enc", "Utf8", "parse", "hashPassword", "password", "salt", "saltToUse", "lib", "WordArray", "random", "hash", "PBKDF2", "keySize", "iterations", "verifyPassword", "verifyHash", "generateSecureToken", "length", "array", "Uint8Array", "window", "crypto", "getRandomValues", "Array", "from", "byte", "padStart", "join", "checkPasswordStrength", "checks", "lowercase", "test", "uppercase", "numbers", "symbols", "noCommonPatterns", "hasCommonPatterns", "score", "Object", "values", "filter", "Boolean", "strength", "getPasswordStrengthLevel", "suggestions", "getPasswordSuggestions", "commonPatterns", "some", "pattern", "level", "color", "push", "trackLoginAttempt", "email", "success", "key", "attempts", "localStorage", "getItem", "removeItem", "allowed", "remainingAttempts", "Date", "now", "oneHourAgo", "recentAttempts", "time", "setItem", "isLocked", "Math", "max", "lockoutTime", "checkSessionTimeout", "lastActivity", "timeSinceLastActivity", "parseInt", "updateLastActivity", "clearSensitiveData", "keysToRemove", "for<PERSON>ach", "sessionStorage", "validateDataIntegrity", "expectedHash", "calculatedHash", "SHA256", "generateDeviceFingerprint", "canvas", "document", "createElement", "ctx", "getContext", "textBaseline", "font", "fillText", "fingerprint", "userAgent", "navigator", "language", "platform", "screen", "width", "height", "timezone", "Intl", "DateTimeFormat", "resolvedOptions", "timeZone", "toDataURL", "webgl", "getWebGLFingerprint", "gl", "debugInfo", "getExtension", "vendor", "getParameter", "UNMASKED_VENDOR_WEBGL", "renderer", "UNMASKED_RENDERER_WEBGL", "monitorRapidRequests", "monitorSessionChanges", "monitorDevTools", "requestCount", "resetInterval", "setInterval", "securityManager", "checkRateLimit", "warn", "lastFingerprint", "currentFingerprint", "devtools", "open", "orientation", "outerHeight", "innerHeight", "outerWidth", "innerWidth", "secureCleanup", "sensitiveData", "gc", "generateToken"], "sources": ["D:/menasa/frontend/src/utils/security.js"], "sourcesContent": ["// مكتبة الأمان المتقدمة\n\nimport CryptoJS from 'crypto-js';\n\n// مفتاح التشفير (يجب أن يكون في متغيرات البيئة في الإنتاج)\nconst ENCRYPTION_KEY = process.env.REACT_APP_ENCRYPTION_KEY || 'default-key-change-in-production';\n\n// فئة إدارة الأمان\nexport class SecurityManager {\n  constructor() {\n    this.sessionTimeout = 30 * 60 * 1000; // 30 دقيقة\n    this.maxLoginAttempts = 5;\n    this.lockoutDuration = 15 * 60 * 1000; // 15 دقيقة\n    this.initializeSecurityMonitoring();\n  }\n\n  // تشفير البيانات الحساسة\n  encrypt(data) {\n    try {\n      const encrypted = CryptoJS.AES.encrypt(JSON.stringify(data), ENCRYPTION_KEY).toString();\n      return encrypted;\n    } catch (error) {\n      console.error('Encryption failed:', error);\n      throw new Error('فشل في تشفير البيانات');\n    }\n  }\n\n  // فك تشفير البيانات\n  decrypt(encryptedData) {\n    try {\n      const bytes = CryptoJS.AES.decrypt(encryptedData, ENCRYPTION_KEY);\n      const decrypted = bytes.toString(CryptoJS.enc.Utf8);\n      return JSON.parse(decrypted);\n    } catch (error) {\n      console.error('Decryption failed:', error);\n      throw new Error('فشل في فك تشفير البيانات');\n    }\n  }\n\n  // تشفير كلمات المرور\n  hashPassword(password, salt = null) {\n    const saltToUse = salt || CryptoJS.lib.WordArray.random(128/8).toString();\n    const hash = CryptoJS.PBKDF2(password, saltToUse, {\n      keySize: 256/32,\n      iterations: 10000\n    }).toString();\n    \n    return {\n      hash: hash,\n      salt: saltToUse\n    };\n  }\n\n  // التحقق من كلمة المرور\n  verifyPassword(password, hash, salt) {\n    const verifyHash = CryptoJS.PBKDF2(password, salt, {\n      keySize: 256/32,\n      iterations: 10000\n    }).toString();\n    \n    return verifyHash === hash;\n  }\n\n  // إنشاء رمز مميز آمن\n  generateSecureToken(length = 32) {\n    const array = new Uint8Array(length);\n    window.crypto.getRandomValues(array);\n    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');\n  }\n\n  // التحقق من قوة كلمة المرور\n  checkPasswordStrength(password) {\n    const checks = {\n      length: password.length >= 8,\n      lowercase: /[a-z]/.test(password),\n      uppercase: /[A-Z]/.test(password),\n      numbers: /\\d/.test(password),\n      symbols: /[^A-Za-z0-9]/.test(password),\n      noCommonPatterns: !this.hasCommonPatterns(password)\n    };\n\n    const score = Object.values(checks).filter(Boolean).length;\n    \n    return {\n      score,\n      checks,\n      strength: this.getPasswordStrengthLevel(score),\n      suggestions: this.getPasswordSuggestions(checks)\n    };\n  }\n\n  // التحقق من الأنماط الشائعة في كلمات المرور\n  hasCommonPatterns(password) {\n    const commonPatterns = [\n      /123456/,\n      /password/i,\n      /qwerty/i,\n      /admin/i,\n      /letmein/i,\n      /welcome/i,\n      /monkey/i,\n      /dragon/i\n    ];\n\n    return commonPatterns.some(pattern => pattern.test(password));\n  }\n\n  // تحديد مستوى قوة كلمة المرور\n  getPasswordStrengthLevel(score) {\n    if (score <= 2) return { level: 'ضعيف جداً', color: '#ef4444' };\n    if (score <= 3) return { level: 'ضعيف', color: '#f97316' };\n    if (score <= 4) return { level: 'متوسط', color: '#eab308' };\n    if (score <= 5) return { level: 'جيد', color: '#22c55e' };\n    return { level: 'قوي جداً', color: '#16a34a' };\n  }\n\n  // اقتراحات لتحسين كلمة المرور\n  getPasswordSuggestions(checks) {\n    const suggestions = [];\n    \n    if (!checks.length) suggestions.push('استخدم 8 أحرف على الأقل');\n    if (!checks.lowercase) suggestions.push('أضف أحرف صغيرة');\n    if (!checks.uppercase) suggestions.push('أضف أحرف كبيرة');\n    if (!checks.numbers) suggestions.push('أضف أرقام');\n    if (!checks.symbols) suggestions.push('أضف رموز خاصة');\n    if (!checks.noCommonPatterns) suggestions.push('تجنب الكلمات الشائعة');\n    \n    return suggestions;\n  }\n\n  // إدارة محاولات تسجيل الدخول\n  trackLoginAttempt(email, success = false) {\n    const key = `login_attempts_${email}`;\n    const attempts = JSON.parse(localStorage.getItem(key) || '[]');\n    \n    if (success) {\n      // مسح المحاولات عند النجاح\n      localStorage.removeItem(key);\n      return { allowed: true, remainingAttempts: this.maxLoginAttempts };\n    }\n\n    // إضافة محاولة فاشلة\n    attempts.push(Date.now());\n    \n    // إزالة المحاولات القديمة (أكثر من ساعة)\n    const oneHourAgo = Date.now() - (60 * 60 * 1000);\n    const recentAttempts = attempts.filter(time => time > oneHourAgo);\n    \n    localStorage.setItem(key, JSON.stringify(recentAttempts));\n    \n    const remainingAttempts = this.maxLoginAttempts - recentAttempts.length;\n    const isLocked = recentAttempts.length >= this.maxLoginAttempts;\n    \n    return {\n      allowed: !isLocked,\n      remainingAttempts: Math.max(0, remainingAttempts),\n      lockoutTime: isLocked ? this.lockoutDuration : 0\n    };\n  }\n\n  // التحقق من انتهاء الجلسة\n  checkSessionTimeout() {\n    const lastActivity = localStorage.getItem('lastActivity');\n    if (!lastActivity) return false;\n    \n    const timeSinceLastActivity = Date.now() - parseInt(lastActivity);\n    return timeSinceLastActivity > this.sessionTimeout;\n  }\n\n  // تحديث وقت النشاط الأخير\n  updateLastActivity() {\n    localStorage.setItem('lastActivity', Date.now().toString());\n  }\n\n  // تنظيف البيانات الحساسة عند تسجيل الخروج\n  clearSensitiveData() {\n    const keysToRemove = [\n      'lastActivity',\n      'userPreferences',\n      'tempData'\n    ];\n    \n    keysToRemove.forEach(key => {\n      localStorage.removeItem(key);\n      sessionStorage.removeItem(key);\n    });\n  }\n\n  // التحقق من سلامة البيانات\n  validateDataIntegrity(data, expectedHash) {\n    const calculatedHash = CryptoJS.SHA256(JSON.stringify(data)).toString();\n    return calculatedHash === expectedHash;\n  }\n\n  // إنشاء بصمة للجهاز\n  generateDeviceFingerprint() {\n    const canvas = document.createElement('canvas');\n    const ctx = canvas.getContext('2d');\n    ctx.textBaseline = 'top';\n    ctx.font = '14px Arial';\n    ctx.fillText('Device fingerprint', 2, 2);\n    \n    const fingerprint = {\n      userAgent: navigator.userAgent,\n      language: navigator.language,\n      platform: navigator.platform,\n      // eslint-disable-next-line no-restricted-globals\n      screen: `${screen.width}x${screen.height}`,\n      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,\n      canvas: canvas.toDataURL(),\n      webgl: this.getWebGLFingerprint()\n    };\n    \n    return CryptoJS.SHA256(JSON.stringify(fingerprint)).toString();\n  }\n\n  // الحصول على بصمة WebGL\n  getWebGLFingerprint() {\n    try {\n      const canvas = document.createElement('canvas');\n      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');\n      \n      if (!gl) return 'no-webgl';\n      \n      const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');\n      const vendor = gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL);\n      const renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL);\n      \n      return `${vendor}~${renderer}`;\n    } catch (error) {\n      return 'webgl-error';\n    }\n  }\n\n  // مراقبة الأنشطة المشبوهة\n  initializeSecurityMonitoring() {\n    // مراقبة محاولات الوصول المتكررة\n    this.monitorRapidRequests();\n    \n    // مراقبة تغييرات الجلسة\n    this.monitorSessionChanges();\n    \n    // مراقبة أدوات المطور\n    this.monitorDevTools();\n  }\n\n  // مراقبة الطلبات السريعة المتتالية\n  monitorRapidRequests() {\n    let requestCount = 0;\n    const resetInterval = 60000; // دقيقة واحدة\n    \n    setInterval(() => {\n      requestCount = 0;\n    }, resetInterval);\n    \n    // يمكن استخدامها في interceptors\n    window.securityManager = {\n      ...window.securityManager,\n      checkRateLimit: () => {\n        requestCount++;\n        if (requestCount > 100) { // 100 طلب في الدقيقة\n          console.warn('Rate limit exceeded');\n          return false;\n        }\n        return true;\n      }\n    };\n  }\n\n  // مراقبة تغييرات الجلسة\n  monitorSessionChanges() {\n    let lastFingerprint = this.generateDeviceFingerprint();\n    \n    setInterval(() => {\n      const currentFingerprint = this.generateDeviceFingerprint();\n      if (currentFingerprint !== lastFingerprint) {\n        console.warn('Device fingerprint changed - possible session hijacking');\n        // يمكن إضافة إجراءات أمنية هنا\n      }\n      lastFingerprint = currentFingerprint;\n    }, 30000); // كل 30 ثانية\n  }\n\n  // مراقبة أدوات المطور\n  monitorDevTools() {\n    let devtools = { open: false, orientation: null };\n    \n    setInterval(() => {\n      if (window.outerHeight - window.innerHeight > 200 || \n          window.outerWidth - window.innerWidth > 200) {\n        if (!devtools.open) {\n          devtools.open = true;\n          console.warn('Developer tools detected');\n          // يمكن إضافة إجراءات أمنية هنا\n        }\n      } else {\n        devtools.open = false;\n      }\n    }, 1000);\n  }\n\n  // تنظيف الذاكرة من البيانات الحساسة\n  secureCleanup() {\n    // مسح المتغيرات الحساسة\n    if (window.sensitiveData) {\n      for (let key in window.sensitiveData) {\n        delete window.sensitiveData[key];\n      }\n    }\n    \n    // تشغيل garbage collection إذا كان متاحاً\n    if (window.gc) {\n      window.gc();\n    }\n  }\n}\n\n// إنشاء مثيل واحد من مدير الأمان\nexport const securityManager = new SecurityManager();\n\n// دوال مساعدة للاستخدام السريع\nexport const encrypt = (data) => securityManager.encrypt(data);\nexport const decrypt = (data) => securityManager.decrypt(data);\nexport const generateToken = (length) => securityManager.generateSecureToken(length);\nexport const checkPasswordStrength = (password) => securityManager.checkPasswordStrength(password);\nexport const trackLoginAttempt = (email, success) => securityManager.trackLoginAttempt(email, success);\n\n// تصدير افتراضي\nexport default securityManager;\n"], "mappings": "AAAA;;AAEA,OAAOA,QAAQ,MAAM,WAAW;;AAEhC;AACA,MAAMC,cAAc,GAAGC,OAAO,CAACC,GAAG,CAACC,wBAAwB,IAAI,kCAAkC;;AAEjG;AACA,OAAO,MAAMC,eAAe,CAAC;EAC3BC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,cAAc,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;IACtC,IAAI,CAACC,gBAAgB,GAAG,CAAC;IACzB,IAAI,CAACC,eAAe,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;IACvC,IAAI,CAACC,4BAA4B,CAAC,CAAC;EACrC;;EAEA;EACAC,OAAOA,CAACC,IAAI,EAAE;IACZ,IAAI;MACF,MAAMC,SAAS,GAAGb,QAAQ,CAACc,GAAG,CAACH,OAAO,CAACI,IAAI,CAACC,SAAS,CAACJ,IAAI,CAAC,EAAEX,cAAc,CAAC,CAACgB,QAAQ,CAAC,CAAC;MACvF,OAAOJ,SAAS;IAClB,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,MAAM,IAAIE,KAAK,CAAC,uBAAuB,CAAC;IAC1C;EACF;;EAEA;EACAC,OAAOA,CAACC,aAAa,EAAE;IACrB,IAAI;MACF,MAAMC,KAAK,GAAGvB,QAAQ,CAACc,GAAG,CAACO,OAAO,CAACC,aAAa,EAAErB,cAAc,CAAC;MACjE,MAAMuB,SAAS,GAAGD,KAAK,CAACN,QAAQ,CAACjB,QAAQ,CAACyB,GAAG,CAACC,IAAI,CAAC;MACnD,OAAOX,IAAI,CAACY,KAAK,CAACH,SAAS,CAAC;IAC9B,CAAC,CAAC,OAAON,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,MAAM,IAAIE,KAAK,CAAC,0BAA0B,CAAC;IAC7C;EACF;;EAEA;EACAQ,YAAYA,CAACC,QAAQ,EAAEC,IAAI,GAAG,IAAI,EAAE;IAClC,MAAMC,SAAS,GAAGD,IAAI,IAAI9B,QAAQ,CAACgC,GAAG,CAACC,SAAS,CAACC,MAAM,CAAC,GAAG,GAAC,CAAC,CAAC,CAACjB,QAAQ,CAAC,CAAC;IACzE,MAAMkB,IAAI,GAAGnC,QAAQ,CAACoC,MAAM,CAACP,QAAQ,EAAEE,SAAS,EAAE;MAChDM,OAAO,EAAE,GAAG,GAAC,EAAE;MACfC,UAAU,EAAE;IACd,CAAC,CAAC,CAACrB,QAAQ,CAAC,CAAC;IAEb,OAAO;MACLkB,IAAI,EAAEA,IAAI;MACVL,IAAI,EAAEC;IACR,CAAC;EACH;;EAEA;EACAQ,cAAcA,CAACV,QAAQ,EAAEM,IAAI,EAAEL,IAAI,EAAE;IACnC,MAAMU,UAAU,GAAGxC,QAAQ,CAACoC,MAAM,CAACP,QAAQ,EAAEC,IAAI,EAAE;MACjDO,OAAO,EAAE,GAAG,GAAC,EAAE;MACfC,UAAU,EAAE;IACd,CAAC,CAAC,CAACrB,QAAQ,CAAC,CAAC;IAEb,OAAOuB,UAAU,KAAKL,IAAI;EAC5B;;EAEA;EACAM,mBAAmBA,CAACC,MAAM,GAAG,EAAE,EAAE;IAC/B,MAAMC,KAAK,GAAG,IAAIC,UAAU,CAACF,MAAM,CAAC;IACpCG,MAAM,CAACC,MAAM,CAACC,eAAe,CAACJ,KAAK,CAAC;IACpC,OAAOK,KAAK,CAACC,IAAI,CAACN,KAAK,EAAEO,IAAI,IAAIA,IAAI,CAACjC,QAAQ,CAAC,EAAE,CAAC,CAACkC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;EAC/E;;EAEA;EACAC,qBAAqBA,CAACxB,QAAQ,EAAE;IAC9B,MAAMyB,MAAM,GAAG;MACbZ,MAAM,EAAEb,QAAQ,CAACa,MAAM,IAAI,CAAC;MAC5Ba,SAAS,EAAE,OAAO,CAACC,IAAI,CAAC3B,QAAQ,CAAC;MACjC4B,SAAS,EAAE,OAAO,CAACD,IAAI,CAAC3B,QAAQ,CAAC;MACjC6B,OAAO,EAAE,IAAI,CAACF,IAAI,CAAC3B,QAAQ,CAAC;MAC5B8B,OAAO,EAAE,cAAc,CAACH,IAAI,CAAC3B,QAAQ,CAAC;MACtC+B,gBAAgB,EAAE,CAAC,IAAI,CAACC,iBAAiB,CAAChC,QAAQ;IACpD,CAAC;IAED,MAAMiC,KAAK,GAAGC,MAAM,CAACC,MAAM,CAACV,MAAM,CAAC,CAACW,MAAM,CAACC,OAAO,CAAC,CAACxB,MAAM;IAE1D,OAAO;MACLoB,KAAK;MACLR,MAAM;MACNa,QAAQ,EAAE,IAAI,CAACC,wBAAwB,CAACN,KAAK,CAAC;MAC9CO,WAAW,EAAE,IAAI,CAACC,sBAAsB,CAAChB,MAAM;IACjD,CAAC;EACH;;EAEA;EACAO,iBAAiBA,CAAChC,QAAQ,EAAE;IAC1B,MAAM0C,cAAc,GAAG,CACrB,QAAQ,EACR,WAAW,EACX,SAAS,EACT,QAAQ,EACR,UAAU,EACV,UAAU,EACV,SAAS,EACT,SAAS,CACV;IAED,OAAOA,cAAc,CAACC,IAAI,CAACC,OAAO,IAAIA,OAAO,CAACjB,IAAI,CAAC3B,QAAQ,CAAC,CAAC;EAC/D;;EAEA;EACAuC,wBAAwBA,CAACN,KAAK,EAAE;IAC9B,IAAIA,KAAK,IAAI,CAAC,EAAE,OAAO;MAAEY,KAAK,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAU,CAAC;IAC/D,IAAIb,KAAK,IAAI,CAAC,EAAE,OAAO;MAAEY,KAAK,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAU,CAAC;IAC1D,IAAIb,KAAK,IAAI,CAAC,EAAE,OAAO;MAAEY,KAAK,EAAE,OAAO;MAAEC,KAAK,EAAE;IAAU,CAAC;IAC3D,IAAIb,KAAK,IAAI,CAAC,EAAE,OAAO;MAAEY,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAU,CAAC;IACzD,OAAO;MAAED,KAAK,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAU,CAAC;EAChD;;EAEA;EACAL,sBAAsBA,CAAChB,MAAM,EAAE;IAC7B,MAAMe,WAAW,GAAG,EAAE;IAEtB,IAAI,CAACf,MAAM,CAACZ,MAAM,EAAE2B,WAAW,CAACO,IAAI,CAAC,yBAAyB,CAAC;IAC/D,IAAI,CAACtB,MAAM,CAACC,SAAS,EAAEc,WAAW,CAACO,IAAI,CAAC,gBAAgB,CAAC;IACzD,IAAI,CAACtB,MAAM,CAACG,SAAS,EAAEY,WAAW,CAACO,IAAI,CAAC,gBAAgB,CAAC;IACzD,IAAI,CAACtB,MAAM,CAACI,OAAO,EAAEW,WAAW,CAACO,IAAI,CAAC,WAAW,CAAC;IAClD,IAAI,CAACtB,MAAM,CAACK,OAAO,EAAEU,WAAW,CAACO,IAAI,CAAC,eAAe,CAAC;IACtD,IAAI,CAACtB,MAAM,CAACM,gBAAgB,EAAES,WAAW,CAACO,IAAI,CAAC,sBAAsB,CAAC;IAEtE,OAAOP,WAAW;EACpB;;EAEA;EACAQ,iBAAiBA,CAACC,KAAK,EAAEC,OAAO,GAAG,KAAK,EAAE;IACxC,MAAMC,GAAG,GAAG,kBAAkBF,KAAK,EAAE;IACrC,MAAMG,QAAQ,GAAGlE,IAAI,CAACY,KAAK,CAACuD,YAAY,CAACC,OAAO,CAACH,GAAG,CAAC,IAAI,IAAI,CAAC;IAE9D,IAAID,OAAO,EAAE;MACX;MACAG,YAAY,CAACE,UAAU,CAACJ,GAAG,CAAC;MAC5B,OAAO;QAAEK,OAAO,EAAE,IAAI;QAAEC,iBAAiB,EAAE,IAAI,CAAC9E;MAAiB,CAAC;IACpE;;IAEA;IACAyE,QAAQ,CAACL,IAAI,CAACW,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;;IAEzB;IACA,MAAMC,UAAU,GAAGF,IAAI,CAACC,GAAG,CAAC,CAAC,GAAI,EAAE,GAAG,EAAE,GAAG,IAAK;IAChD,MAAME,cAAc,GAAGT,QAAQ,CAAChB,MAAM,CAAC0B,IAAI,IAAIA,IAAI,GAAGF,UAAU,CAAC;IAEjEP,YAAY,CAACU,OAAO,CAACZ,GAAG,EAAEjE,IAAI,CAACC,SAAS,CAAC0E,cAAc,CAAC,CAAC;IAEzD,MAAMJ,iBAAiB,GAAG,IAAI,CAAC9E,gBAAgB,GAAGkF,cAAc,CAAChD,MAAM;IACvE,MAAMmD,QAAQ,GAAGH,cAAc,CAAChD,MAAM,IAAI,IAAI,CAAClC,gBAAgB;IAE/D,OAAO;MACL6E,OAAO,EAAE,CAACQ,QAAQ;MAClBP,iBAAiB,EAAEQ,IAAI,CAACC,GAAG,CAAC,CAAC,EAAET,iBAAiB,CAAC;MACjDU,WAAW,EAAEH,QAAQ,GAAG,IAAI,CAACpF,eAAe,GAAG;IACjD,CAAC;EACH;;EAEA;EACAwF,mBAAmBA,CAAA,EAAG;IACpB,MAAMC,YAAY,GAAGhB,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;IACzD,IAAI,CAACe,YAAY,EAAE,OAAO,KAAK;IAE/B,MAAMC,qBAAqB,GAAGZ,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGY,QAAQ,CAACF,YAAY,CAAC;IACjE,OAAOC,qBAAqB,GAAG,IAAI,CAAC5F,cAAc;EACpD;;EAEA;EACA8F,kBAAkBA,CAAA,EAAG;IACnBnB,YAAY,CAACU,OAAO,CAAC,cAAc,EAAEL,IAAI,CAACC,GAAG,CAAC,CAAC,CAACvE,QAAQ,CAAC,CAAC,CAAC;EAC7D;;EAEA;EACAqF,kBAAkBA,CAAA,EAAG;IACnB,MAAMC,YAAY,GAAG,CACnB,cAAc,EACd,iBAAiB,EACjB,UAAU,CACX;IAEDA,YAAY,CAACC,OAAO,CAACxB,GAAG,IAAI;MAC1BE,YAAY,CAACE,UAAU,CAACJ,GAAG,CAAC;MAC5ByB,cAAc,CAACrB,UAAU,CAACJ,GAAG,CAAC;IAChC,CAAC,CAAC;EACJ;;EAEA;EACA0B,qBAAqBA,CAAC9F,IAAI,EAAE+F,YAAY,EAAE;IACxC,MAAMC,cAAc,GAAG5G,QAAQ,CAAC6G,MAAM,CAAC9F,IAAI,CAACC,SAAS,CAACJ,IAAI,CAAC,CAAC,CAACK,QAAQ,CAAC,CAAC;IACvE,OAAO2F,cAAc,KAAKD,YAAY;EACxC;;EAEA;EACAG,yBAAyBA,CAAA,EAAG;IAC1B,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAC/C,MAAMC,GAAG,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;IACnCD,GAAG,CAACE,YAAY,GAAG,KAAK;IACxBF,GAAG,CAACG,IAAI,GAAG,YAAY;IACvBH,GAAG,CAACI,QAAQ,CAAC,oBAAoB,EAAE,CAAC,EAAE,CAAC,CAAC;IAExC,MAAMC,WAAW,GAAG;MAClBC,SAAS,EAAEC,SAAS,CAACD,SAAS;MAC9BE,QAAQ,EAAED,SAAS,CAACC,QAAQ;MAC5BC,QAAQ,EAAEF,SAAS,CAACE,QAAQ;MAC5B;MACAC,MAAM,EAAE,GAAGA,MAAM,CAACC,KAAK,IAAID,MAAM,CAACE,MAAM,EAAE;MAC1CC,QAAQ,EAAEC,IAAI,CAACC,cAAc,CAAC,CAAC,CAACC,eAAe,CAAC,CAAC,CAACC,QAAQ;MAC1DpB,MAAM,EAAEA,MAAM,CAACqB,SAAS,CAAC,CAAC;MAC1BC,KAAK,EAAE,IAAI,CAACC,mBAAmB,CAAC;IAClC,CAAC;IAED,OAAOtI,QAAQ,CAAC6G,MAAM,CAAC9F,IAAI,CAACC,SAAS,CAACuG,WAAW,CAAC,CAAC,CAACtG,QAAQ,CAAC,CAAC;EAChE;;EAEA;EACAqH,mBAAmBA,CAAA,EAAG;IACpB,IAAI;MACF,MAAMvB,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;MAC/C,MAAMsB,EAAE,GAAGxB,MAAM,CAACI,UAAU,CAAC,OAAO,CAAC,IAAIJ,MAAM,CAACI,UAAU,CAAC,oBAAoB,CAAC;MAEhF,IAAI,CAACoB,EAAE,EAAE,OAAO,UAAU;MAE1B,MAAMC,SAAS,GAAGD,EAAE,CAACE,YAAY,CAAC,2BAA2B,CAAC;MAC9D,MAAMC,MAAM,GAAGH,EAAE,CAACI,YAAY,CAACH,SAAS,CAACI,qBAAqB,CAAC;MAC/D,MAAMC,QAAQ,GAAGN,EAAE,CAACI,YAAY,CAACH,SAAS,CAACM,uBAAuB,CAAC;MAEnE,OAAO,GAAGJ,MAAM,IAAIG,QAAQ,EAAE;IAChC,CAAC,CAAC,OAAO3H,KAAK,EAAE;MACd,OAAO,aAAa;IACtB;EACF;;EAEA;EACAR,4BAA4BA,CAAA,EAAG;IAC7B;IACA,IAAI,CAACqI,oBAAoB,CAAC,CAAC;;IAE3B;IACA,IAAI,CAACC,qBAAqB,CAAC,CAAC;;IAE5B;IACA,IAAI,CAACC,eAAe,CAAC,CAAC;EACxB;;EAEA;EACAF,oBAAoBA,CAAA,EAAG;IACrB,IAAIG,YAAY,GAAG,CAAC;IACpB,MAAMC,aAAa,GAAG,KAAK,CAAC,CAAC;;IAE7BC,WAAW,CAAC,MAAM;MAChBF,YAAY,GAAG,CAAC;IAClB,CAAC,EAAEC,aAAa,CAAC;;IAEjB;IACAtG,MAAM,CAACwG,eAAe,GAAG;MACvB,GAAGxG,MAAM,CAACwG,eAAe;MACzBC,cAAc,EAAEA,CAAA,KAAM;QACpBJ,YAAY,EAAE;QACd,IAAIA,YAAY,GAAG,GAAG,EAAE;UAAE;UACxB/H,OAAO,CAACoI,IAAI,CAAC,qBAAqB,CAAC;UACnC,OAAO,KAAK;QACd;QACA,OAAO,IAAI;MACb;IACF,CAAC;EACH;;EAEA;EACAP,qBAAqBA,CAAA,EAAG;IACtB,IAAIQ,eAAe,GAAG,IAAI,CAAC1C,yBAAyB,CAAC,CAAC;IAEtDsC,WAAW,CAAC,MAAM;MAChB,MAAMK,kBAAkB,GAAG,IAAI,CAAC3C,yBAAyB,CAAC,CAAC;MAC3D,IAAI2C,kBAAkB,KAAKD,eAAe,EAAE;QAC1CrI,OAAO,CAACoI,IAAI,CAAC,yDAAyD,CAAC;QACvE;MACF;MACAC,eAAe,GAAGC,kBAAkB;IACtC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;EACb;;EAEA;EACAR,eAAeA,CAAA,EAAG;IAChB,IAAIS,QAAQ,GAAG;MAAEC,IAAI,EAAE,KAAK;MAAEC,WAAW,EAAE;IAAK,CAAC;IAEjDR,WAAW,CAAC,MAAM;MAChB,IAAIvG,MAAM,CAACgH,WAAW,GAAGhH,MAAM,CAACiH,WAAW,GAAG,GAAG,IAC7CjH,MAAM,CAACkH,UAAU,GAAGlH,MAAM,CAACmH,UAAU,GAAG,GAAG,EAAE;QAC/C,IAAI,CAACN,QAAQ,CAACC,IAAI,EAAE;UAClBD,QAAQ,CAACC,IAAI,GAAG,IAAI;UACpBxI,OAAO,CAACoI,IAAI,CAAC,0BAA0B,CAAC;UACxC;QACF;MACF,CAAC,MAAM;QACLG,QAAQ,CAACC,IAAI,GAAG,KAAK;MACvB;IACF,CAAC,EAAE,IAAI,CAAC;EACV;;EAEA;EACAM,aAAaA,CAAA,EAAG;IACd;IACA,IAAIpH,MAAM,CAACqH,aAAa,EAAE;MACxB,KAAK,IAAIlF,GAAG,IAAInC,MAAM,CAACqH,aAAa,EAAE;QACpC,OAAOrH,MAAM,CAACqH,aAAa,CAAClF,GAAG,CAAC;MAClC;IACF;;IAEA;IACA,IAAInC,MAAM,CAACsH,EAAE,EAAE;MACbtH,MAAM,CAACsH,EAAE,CAAC,CAAC;IACb;EACF;AACF;;AAEA;AACA,OAAO,MAAMd,eAAe,GAAG,IAAIhJ,eAAe,CAAC,CAAC;;AAEpD;AACA,OAAO,MAAMM,OAAO,GAAIC,IAAI,IAAKyI,eAAe,CAAC1I,OAAO,CAACC,IAAI,CAAC;AAC9D,OAAO,MAAMS,OAAO,GAAIT,IAAI,IAAKyI,eAAe,CAAChI,OAAO,CAACT,IAAI,CAAC;AAC9D,OAAO,MAAMwJ,aAAa,GAAI1H,MAAM,IAAK2G,eAAe,CAAC5G,mBAAmB,CAACC,MAAM,CAAC;AACpF,OAAO,MAAMW,qBAAqB,GAAIxB,QAAQ,IAAKwH,eAAe,CAAChG,qBAAqB,CAACxB,QAAQ,CAAC;AAClG,OAAO,MAAMgD,iBAAiB,GAAGA,CAACC,KAAK,EAAEC,OAAO,KAAKsE,eAAe,CAACxE,iBAAiB,CAACC,KAAK,EAAEC,OAAO,CAAC;;AAEtG;AACA,eAAesE,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}