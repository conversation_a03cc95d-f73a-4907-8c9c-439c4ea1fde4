{"ast": null, "code": "import * as React from 'react';\nconst getItem = key => {\n  try {\n    const itemValue = localStorage.getItem(key);\n    if (typeof itemValue === 'string') {\n      return JSON.parse(itemValue);\n    }\n    return undefined;\n  } catch {\n    return undefined;\n  }\n};\nfunction useLocalStorage(key, defaultValue) {\n  const [value, setValue] = React.useState();\n  React.useEffect(() => {\n    const initialValue = getItem(key);\n    if (typeof initialValue === 'undefined' || initialValue === null) {\n      setValue(typeof defaultValue === 'function' ? defaultValue() : defaultValue);\n    } else {\n      setValue(initialValue);\n    }\n  }, [defaultValue, key]);\n  const setter = React.useCallback(updater => {\n    setValue(old => {\n      let newVal = updater;\n      if (typeof updater == 'function') {\n        newVal = updater(old);\n      }\n      try {\n        localStorage.setItem(key, JSON.stringify(newVal));\n      } catch {}\n      return newVal;\n    });\n  }, [key]);\n  return [value, setter];\n}\nexport { useLocalStorage as default };", "map": {"version": 3, "names": ["getItem", "key", "itemValue", "localStorage", "JSON", "parse", "undefined", "useLocalStorage", "defaultValue", "value", "setValue", "React", "useState", "useEffect", "initialValue", "setter", "useCallback", "updater", "old", "newVal", "setItem", "stringify"], "sources": ["D:\\menasa\\frontend\\node_modules\\@tanstack\\react-query-devtools\\src\\useLocalStorage.ts"], "sourcesContent": ["import * as React from 'react'\n\nconst getItem = (key: string): unknown => {\n  try {\n    const itemValue = localStorage.getItem(key)\n    if (typeof itemValue === 'string') {\n      return JSON.parse(itemValue)\n    }\n    return undefined\n  } catch {\n    return undefined\n  }\n}\n\nexport default function useLocalStorage<T>(\n  key: string,\n  defaultValue: T | undefined,\n): [T | undefined, (newVal: T | ((prevVal: T) => T)) => void] {\n  const [value, setValue] = React.useState<T>()\n\n  React.useEffect(() => {\n    const initialValue = getItem(key) as T | undefined\n\n    if (typeof initialValue === 'undefined' || initialValue === null) {\n      setValue(\n        typeof defaultValue === 'function' ? defaultValue() : defaultValue,\n      )\n    } else {\n      setValue(initialValue)\n    }\n  }, [defaultValue, key])\n\n  const setter = React.useCallback(\n    (updater: any) => {\n      setValue((old) => {\n        let newVal = updater\n\n        if (typeof updater == 'function') {\n          newVal = updater(old)\n        }\n        try {\n          localStorage.setItem(key, JSON.stringify(newVal))\n        } catch {}\n\n        return newVal\n      })\n    },\n    [key],\n  )\n\n  return [value, setter]\n}\n"], "mappings": ";AAEA,MAAMA,OAAO,GAAIC,GAAD,IAA0B;EACxC,IAAI;IACF,MAAMC,SAAS,GAAGC,YAAY,CAACH,OAAb,CAAqBC,GAArB,CAAlB;IACA,IAAI,OAAOC,SAAP,KAAqB,QAAzB,EAAmC;MACjC,OAAOE,IAAI,CAACC,KAAL,CAAWH,SAAX,CAAP;IACD;IACD,OAAOI,SAAP;EACD,CAND,CAME,MAAM;IACN,OAAOA,SAAP;EACD;AACF,CAVD;AAYe,SAASC,eAATA,CACbN,GADa,EAEbO,YAFa,EAG+C;EAC5D,MAAM,CAACC,KAAD,EAAQC,QAAR,IAAoBC,KAAK,CAACC,QAAN,EAA1B;EAEAD,KAAK,CAACE,SAAN,CAAgB,MAAM;IACpB,MAAMC,YAAY,GAAGd,OAAO,CAACC,GAAD,CAA5B;IAEA,IAAI,OAAOa,YAAP,KAAwB,WAAxB,IAAuCA,YAAY,KAAK,IAA5D,EAAkE;MAChEJ,QAAQ,CACN,OAAOF,YAAP,KAAwB,UAAxB,GAAqCA,YAAY,EAAjD,GAAsDA,YADhD,CAAR;IAGD,CAJD,MAIO;MACLE,QAAQ,CAACI,YAAD,CAAR;IACD;EACF,CAVD,EAUG,CAACN,YAAD,EAAeP,GAAf,CAVH;EAYA,MAAMc,MAAM,GAAGJ,KAAK,CAACK,WAAN,CACZC,OAAD,IAAkB;IAChBP,QAAQ,CAAEQ,GAAD,IAAS;MAChB,IAAIC,MAAM,GAAGF,OAAb;MAEA,IAAI,OAAOA,OAAP,IAAkB,UAAtB,EAAkC;QAChCE,MAAM,GAAGF,OAAO,CAACC,GAAD,CAAhB;MACD;MACD,IAAI;QACFf,YAAY,CAACiB,OAAb,CAAqBnB,GAArB,EAA0BG,IAAI,CAACiB,SAAL,CAAeF,MAAf,CAA1B;OADF,CAEE,MAAM;MAER,OAAOA,MAAP;IACD,CAXO,CAAR;EAYD,CAdY,EAeb,CAAClB,GAAD,CAfa,CAAf;EAkBA,OAAO,CAACQ,KAAD,EAAQM,MAAR,CAAP;AACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}