{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nconst debounce = require('./debounce.js');\nfunction throttle(func, throttleMs = 0, options = {}) {\n  const {\n    leading = true,\n    trailing = true\n  } = options;\n  return debounce.debounce(func, throttleMs, {\n    leading,\n    maxWait: throttleMs,\n    trailing\n  });\n}\nexports.throttle = throttle;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "debounce", "require", "throttle", "func", "throttleMs", "options", "leading", "trailing", "max<PERSON><PERSON>"], "sources": ["D:/menasa/frontend/node_modules/es-toolkit/dist/compat/function/throttle.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nconst debounce = require('./debounce.js');\n\nfunction throttle(func, throttleMs = 0, options = {}) {\n    const { leading = true, trailing = true } = options;\n    return debounce.debounce(func, throttleMs, {\n        leading,\n        maxWait: throttleMs,\n        trailing,\n    });\n}\n\nexports.throttle = throttle;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,MAAMC,QAAQ,GAAGC,OAAO,CAAC,eAAe,CAAC;AAEzC,SAASC,QAAQA,CAACC,IAAI,EAAEC,UAAU,GAAG,CAAC,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;EAClD,MAAM;IAAEC,OAAO,GAAG,IAAI;IAAEC,QAAQ,GAAG;EAAK,CAAC,GAAGF,OAAO;EACnD,OAAOL,QAAQ,CAACA,QAAQ,CAACG,IAAI,EAAEC,UAAU,EAAE;IACvCE,OAAO;IACPE,OAAO,EAAEJ,UAAU;IACnBG;EACJ,CAAC,CAAC;AACN;AAEAX,OAAO,CAACM,QAAQ,GAAGA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}