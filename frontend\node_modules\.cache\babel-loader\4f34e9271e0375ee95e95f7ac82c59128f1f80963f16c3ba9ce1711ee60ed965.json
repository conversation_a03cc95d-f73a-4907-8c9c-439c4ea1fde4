{"ast": null, "code": "import { color } from '../../../value/types/color/index.mjs';\nimport { filter } from '../../../value/types/complex/filter.mjs';\nimport { numberValueTypes } from './number.mjs';\n\n/**\n * A map of default value types for common values\n */\nconst defaultValueTypes = {\n  ...numberValueTypes,\n  // Color props\n  color,\n  backgroundColor: color,\n  outlineColor: color,\n  fill: color,\n  stroke: color,\n  // Border props\n  borderColor: color,\n  borderTopColor: color,\n  borderRightColor: color,\n  borderBottomColor: color,\n  borderLeftColor: color,\n  filter,\n  WebkitFilter: filter\n};\n/**\n * Gets the default ValueType for the provided value key\n */\nconst getDefaultValueType = key => defaultValueTypes[key];\nexport { defaultValueTypes, getDefaultValueType };", "map": {"version": 3, "names": ["color", "filter", "numberValueTypes", "defaultValueTypes", "backgroundColor", "outlineColor", "fill", "stroke", "borderColor", "borderTopColor", "borderRightColor", "borderBottomColor", "borderLeftColor", "WebkitFilter", "getDefaultValueType", "key"], "sources": ["D:/menasa/frontend/node_modules/framer-motion/dist/es/render/dom/value-types/defaults.mjs"], "sourcesContent": ["import { color } from '../../../value/types/color/index.mjs';\nimport { filter } from '../../../value/types/complex/filter.mjs';\nimport { numberValueTypes } from './number.mjs';\n\n/**\n * A map of default value types for common values\n */\nconst defaultValueTypes = {\n    ...numberValueTypes,\n    // Color props\n    color,\n    backgroundColor: color,\n    outlineColor: color,\n    fill: color,\n    stroke: color,\n    // Border props\n    borderColor: color,\n    borderTopColor: color,\n    borderRightColor: color,\n    borderBottomColor: color,\n    borderLeftColor: color,\n    filter,\n    WebkitFilter: filter,\n};\n/**\n * Gets the default ValueType for the provided value key\n */\nconst getDefaultValueType = (key) => defaultValueTypes[key];\n\nexport { defaultValueTypes, getDefaultValueType };\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,sCAAsC;AAC5D,SAASC,MAAM,QAAQ,yCAAyC;AAChE,SAASC,gBAAgB,QAAQ,cAAc;;AAE/C;AACA;AACA;AACA,MAAMC,iBAAiB,GAAG;EACtB,GAAGD,gBAAgB;EACnB;EACAF,KAAK;EACLI,eAAe,EAAEJ,KAAK;EACtBK,YAAY,EAAEL,KAAK;EACnBM,IAAI,EAAEN,KAAK;EACXO,MAAM,EAAEP,KAAK;EACb;EACAQ,WAAW,EAAER,KAAK;EAClBS,cAAc,EAAET,KAAK;EACrBU,gBAAgB,EAAEV,KAAK;EACvBW,iBAAiB,EAAEX,KAAK;EACxBY,eAAe,EAAEZ,KAAK;EACtBC,MAAM;EACNY,YAAY,EAAEZ;AAClB,CAAC;AACD;AACA;AACA;AACA,MAAMa,mBAAmB,GAAIC,GAAG,IAAKZ,iBAAiB,CAACY,GAAG,CAAC;AAE3D,SAASZ,iBAAiB,EAAEW,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}