{"ast": null, "code": "var __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nimport { resolveFetch } from './helper';\nimport { FunctionsFetchError, FunctionsHttpError, FunctionsRelayError, FunctionRegion } from './types';\nexport class FunctionsClient {\n  constructor(url, {\n    headers = {},\n    customFetch,\n    region = FunctionRegion.Any\n  } = {}) {\n    this.url = url;\n    this.headers = headers;\n    this.region = region;\n    this.fetch = resolveFetch(customFetch);\n  }\n  /**\n   * Updates the authorization header\n   * @param token - the new jwt token sent in the authorisation header\n   */\n  setAuth(token) {\n    this.headers.Authorization = `Bearer ${token}`;\n  }\n  /**\n   * Invokes a function\n   * @param functionName - The name of the Function to invoke.\n   * @param options - Options for invoking the Function.\n   */\n  invoke(functionName, options = {}) {\n    var _a;\n    return __awaiter(this, void 0, void 0, function* () {\n      try {\n        const {\n          headers,\n          method,\n          body: functionArgs\n        } = options;\n        let _headers = {};\n        let {\n          region\n        } = options;\n        if (!region) {\n          region = this.region;\n        }\n        // Add region as query parameter using URL API\n        const url = new URL(`${this.url}/${functionName}`);\n        if (region && region !== 'any') {\n          _headers['x-region'] = region;\n          url.searchParams.set('forceFunctionRegion', region);\n        }\n        let body;\n        if (functionArgs && (headers && !Object.prototype.hasOwnProperty.call(headers, 'Content-Type') || !headers)) {\n          if (typeof Blob !== 'undefined' && functionArgs instanceof Blob || functionArgs instanceof ArrayBuffer) {\n            // will work for File as File inherits Blob\n            // also works for ArrayBuffer as it is the same underlying structure as a Blob\n            _headers['Content-Type'] = 'application/octet-stream';\n            body = functionArgs;\n          } else if (typeof functionArgs === 'string') {\n            // plain string\n            _headers['Content-Type'] = 'text/plain';\n            body = functionArgs;\n          } else if (typeof FormData !== 'undefined' && functionArgs instanceof FormData) {\n            // don't set content-type headers\n            // Request will automatically add the right boundary value\n            body = functionArgs;\n          } else {\n            // default, assume this is JSON\n            _headers['Content-Type'] = 'application/json';\n            body = JSON.stringify(functionArgs);\n          }\n        }\n        const response = yield this.fetch(url.toString(), {\n          method: method || 'POST',\n          // headers priority is (high to low):\n          // 1. invoke-level headers\n          // 2. client-level headers\n          // 3. default Content-Type header\n          headers: Object.assign(Object.assign(Object.assign({}, _headers), this.headers), headers),\n          body\n        }).catch(fetchError => {\n          throw new FunctionsFetchError(fetchError);\n        });\n        const isRelayError = response.headers.get('x-relay-error');\n        if (isRelayError && isRelayError === 'true') {\n          throw new FunctionsRelayError(response);\n        }\n        if (!response.ok) {\n          throw new FunctionsHttpError(response);\n        }\n        let responseType = ((_a = response.headers.get('Content-Type')) !== null && _a !== void 0 ? _a : 'text/plain').split(';')[0].trim();\n        let data;\n        if (responseType === 'application/json') {\n          data = yield response.json();\n        } else if (responseType === 'application/octet-stream') {\n          data = yield response.blob();\n        } else if (responseType === 'text/event-stream') {\n          data = response;\n        } else if (responseType === 'multipart/form-data') {\n          data = yield response.formData();\n        } else {\n          // default to text\n          data = yield response.text();\n        }\n        return {\n          data,\n          error: null,\n          response\n        };\n      } catch (error) {\n        return {\n          data: null,\n          error,\n          response: error instanceof FunctionsHttpError || error instanceof FunctionsRelayError ? error.context : undefined\n        };\n      }\n    });\n  }\n}", "map": {"version": 3, "names": ["resolveFetch", "FunctionsFetchError", "FunctionsHttpError", "FunctionsRelayError", "FunctionRegion", "FunctionsClient", "constructor", "url", "headers", "customFetch", "region", "Any", "fetch", "setAuth", "token", "Authorization", "invoke", "functionName", "options", "method", "body", "functionArgs", "_headers", "URL", "searchParams", "set", "Object", "prototype", "hasOwnProperty", "call", "Blob", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FormData", "JSON", "stringify", "response", "toString", "assign", "catch", "fetchError", "isRelayError", "get", "ok", "responseType", "_a", "split", "trim", "data", "json", "blob", "formData", "text", "error", "context", "undefined"], "sources": ["D:\\menasa\\frontend\\node_modules\\@supabase\\functions-js\\src\\FunctionsClient.ts"], "sourcesContent": ["import { resolveFetch } from './helper'\nimport {\n  Fetch,\n  FunctionsFetchError,\n  FunctionsHttpError,\n  FunctionsRelayError,\n  FunctionsResponse,\n  FunctionInvokeOptions,\n  FunctionRegion,\n} from './types'\n\nexport class FunctionsClient {\n  protected url: string\n  protected headers: Record<string, string>\n  protected region: FunctionRegion\n  protected fetch: Fetch\n\n  constructor(\n    url: string,\n    {\n      headers = {},\n      customFetch,\n      region = FunctionRegion.Any,\n    }: {\n      headers?: Record<string, string>\n      customFetch?: Fetch\n      region?: FunctionRegion\n    } = {}\n  ) {\n    this.url = url\n    this.headers = headers\n    this.region = region\n    this.fetch = resolveFetch(customFetch)\n  }\n\n  /**\n   * Updates the authorization header\n   * @param token - the new jwt token sent in the authorisation header\n   */\n  setAuth(token: string) {\n    this.headers.Authorization = `Bearer ${token}`\n  }\n\n  /**\n   * Invokes a function\n   * @param functionName - The name of the Function to invoke.\n   * @param options - Options for invoking the Function.\n   */\n  async invoke<T = any>(\n    functionName: string,\n    options: FunctionInvokeOptions = {}\n  ): Promise<FunctionsResponse<T>> {\n    try {\n      const { headers, method, body: functionArgs } = options\n      let _headers: Record<string, string> = {}\n      let { region } = options\n      if (!region) {\n        region = this.region\n      }\n      // Add region as query parameter using URL API\n      const url = new URL(`${this.url}/${functionName}`)\n      if (region && region !== 'any') {\n        _headers['x-region'] = region\n        url.searchParams.set('forceFunctionRegion', region)\n      }\n      let body: any\n      if (\n        functionArgs &&\n        ((headers && !Object.prototype.hasOwnProperty.call(headers, 'Content-Type')) || !headers)\n      ) {\n        if (\n          (typeof Blob !== 'undefined' && functionArgs instanceof Blob) ||\n          functionArgs instanceof ArrayBuffer\n        ) {\n          // will work for File as File inherits Blob\n          // also works for ArrayBuffer as it is the same underlying structure as a Blob\n          _headers['Content-Type'] = 'application/octet-stream'\n          body = functionArgs\n        } else if (typeof functionArgs === 'string') {\n          // plain string\n          _headers['Content-Type'] = 'text/plain'\n          body = functionArgs\n        } else if (typeof FormData !== 'undefined' && functionArgs instanceof FormData) {\n          // don't set content-type headers\n          // Request will automatically add the right boundary value\n          body = functionArgs\n        } else {\n          // default, assume this is JSON\n          _headers['Content-Type'] = 'application/json'\n          body = JSON.stringify(functionArgs)\n        }\n      }\n\n      const response = await this.fetch(url.toString(), {\n        method: method || 'POST',\n        // headers priority is (high to low):\n        // 1. invoke-level headers\n        // 2. client-level headers\n        // 3. default Content-Type header\n        headers: { ..._headers, ...this.headers, ...headers },\n        body,\n      }).catch((fetchError) => {\n        throw new FunctionsFetchError(fetchError)\n      })\n\n      const isRelayError = response.headers.get('x-relay-error')\n      if (isRelayError && isRelayError === 'true') {\n        throw new FunctionsRelayError(response)\n      }\n\n      if (!response.ok) {\n        throw new FunctionsHttpError(response)\n      }\n\n      let responseType = (response.headers.get('Content-Type') ?? 'text/plain').split(';')[0].trim()\n      let data: any\n      if (responseType === 'application/json') {\n        data = await response.json()\n      } else if (responseType === 'application/octet-stream') {\n        data = await response.blob()\n      } else if (responseType === 'text/event-stream') {\n        data = response\n      } else if (responseType === 'multipart/form-data') {\n        data = await response.formData()\n      } else {\n        // default to text\n        data = await response.text()\n      }\n\n      return { data, error: null, response }\n    } catch (error) {\n      return {\n        data: null,\n        error,\n        response:\n          error instanceof FunctionsHttpError || error instanceof FunctionsRelayError\n            ? error.context\n            : undefined,\n      }\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAASA,YAAY,QAAQ,UAAU;AACvC,SAEEC,mBAAmB,EACnBC,kBAAkB,EAClBC,mBAAmB,EAGnBC,cAAc,QACT,SAAS;AAEhB,OAAM,MAAOC,eAAe;EAM1BC,YACEC,GAAW,EACX;IACEC,OAAO,GAAG,EAAE;IACZC,WAAW;IACXC,MAAM,GAAGN,cAAc,CAACO;EAAG,IAKzB,EAAE;IAEN,IAAI,CAACJ,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACE,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACE,KAAK,GAAGZ,YAAY,CAACS,WAAW,CAAC;EACxC;EAEA;;;;EAIAI,OAAOA,CAACC,KAAa;IACnB,IAAI,CAACN,OAAO,CAACO,aAAa,GAAG,UAAUD,KAAK,EAAE;EAChD;EAEA;;;;;EAKME,MAAMA,CACVC,YAAoB,EACpBC,OAAA,GAAiC,EAAE;;;MAEnC,IAAI;QACF,MAAM;UAAEV,OAAO;UAAEW,MAAM;UAAEC,IAAI,EAAEC;QAAY,CAAE,GAAGH,OAAO;QACvD,IAAII,QAAQ,GAA2B,EAAE;QACzC,IAAI;UAAEZ;QAAM,CAAE,GAAGQ,OAAO;QACxB,IAAI,CAACR,MAAM,EAAE;UACXA,MAAM,GAAG,IAAI,CAACA,MAAM;;QAEtB;QACA,MAAMH,GAAG,GAAG,IAAIgB,GAAG,CAAC,GAAG,IAAI,CAAChB,GAAG,IAAIU,YAAY,EAAE,CAAC;QAClD,IAAIP,MAAM,IAAIA,MAAM,KAAK,KAAK,EAAE;UAC9BY,QAAQ,CAAC,UAAU,CAAC,GAAGZ,MAAM;UAC7BH,GAAG,CAACiB,YAAY,CAACC,GAAG,CAAC,qBAAqB,EAAEf,MAAM,CAAC;;QAErD,IAAIU,IAAS;QACb,IACEC,YAAY,KACVb,OAAO,IAAI,CAACkB,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACrB,OAAO,EAAE,cAAc,CAAC,IAAK,CAACA,OAAO,CAAC,EACzF;UACA,IACG,OAAOsB,IAAI,KAAK,WAAW,IAAIT,YAAY,YAAYS,IAAI,IAC5DT,YAAY,YAAYU,WAAW,EACnC;YACA;YACA;YACAT,QAAQ,CAAC,cAAc,CAAC,GAAG,0BAA0B;YACrDF,IAAI,GAAGC,YAAY;WACpB,MAAM,IAAI,OAAOA,YAAY,KAAK,QAAQ,EAAE;YAC3C;YACAC,QAAQ,CAAC,cAAc,CAAC,GAAG,YAAY;YACvCF,IAAI,GAAGC,YAAY;WACpB,MAAM,IAAI,OAAOW,QAAQ,KAAK,WAAW,IAAIX,YAAY,YAAYW,QAAQ,EAAE;YAC9E;YACA;YACAZ,IAAI,GAAGC,YAAY;WACpB,MAAM;YACL;YACAC,QAAQ,CAAC,cAAc,CAAC,GAAG,kBAAkB;YAC7CF,IAAI,GAAGa,IAAI,CAACC,SAAS,CAACb,YAAY,CAAC;;;QAIvC,MAAMc,QAAQ,GAAG,MAAM,IAAI,CAACvB,KAAK,CAACL,GAAG,CAAC6B,QAAQ,EAAE,EAAE;UAChDjB,MAAM,EAAEA,MAAM,IAAI,MAAM;UACxB;UACA;UACA;UACA;UACAX,OAAO,EAAAkB,MAAA,CAAAW,MAAA,CAAAX,MAAA,CAAAW,MAAA,CAAAX,MAAA,CAAAW,MAAA,KAAOf,QAAQ,GAAK,IAAI,CAACd,OAAO,GAAKA,OAAO,CAAE;UACrDY;SACD,CAAC,CAACkB,KAAK,CAAEC,UAAU,IAAI;UACtB,MAAM,IAAItC,mBAAmB,CAACsC,UAAU,CAAC;QAC3C,CAAC,CAAC;QAEF,MAAMC,YAAY,GAAGL,QAAQ,CAAC3B,OAAO,CAACiC,GAAG,CAAC,eAAe,CAAC;QAC1D,IAAID,YAAY,IAAIA,YAAY,KAAK,MAAM,EAAE;UAC3C,MAAM,IAAIrC,mBAAmB,CAACgC,QAAQ,CAAC;;QAGzC,IAAI,CAACA,QAAQ,CAACO,EAAE,EAAE;UAChB,MAAM,IAAIxC,kBAAkB,CAACiC,QAAQ,CAAC;;QAGxC,IAAIQ,YAAY,GAAG,CAAC,CAAAC,EAAA,GAAAT,QAAQ,CAAC3B,OAAO,CAACiC,GAAG,CAAC,cAAc,CAAC,cAAAG,EAAA,cAAAA,EAAA,GAAI,YAAY,EAAEC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,EAAE;QAC9F,IAAIC,IAAS;QACb,IAAIJ,YAAY,KAAK,kBAAkB,EAAE;UACvCI,IAAI,GAAG,MAAMZ,QAAQ,CAACa,IAAI,EAAE;SAC7B,MAAM,IAAIL,YAAY,KAAK,0BAA0B,EAAE;UACtDI,IAAI,GAAG,MAAMZ,QAAQ,CAACc,IAAI,EAAE;SAC7B,MAAM,IAAIN,YAAY,KAAK,mBAAmB,EAAE;UAC/CI,IAAI,GAAGZ,QAAQ;SAChB,MAAM,IAAIQ,YAAY,KAAK,qBAAqB,EAAE;UACjDI,IAAI,GAAG,MAAMZ,QAAQ,CAACe,QAAQ,EAAE;SACjC,MAAM;UACL;UACAH,IAAI,GAAG,MAAMZ,QAAQ,CAACgB,IAAI,EAAE;;QAG9B,OAAO;UAAEJ,IAAI;UAAEK,KAAK,EAAE,IAAI;UAAEjB;QAAQ,CAAE;OACvC,CAAC,OAAOiB,KAAK,EAAE;QACd,OAAO;UACLL,IAAI,EAAE,IAAI;UACVK,KAAK;UACLjB,QAAQ,EACNiB,KAAK,YAAYlD,kBAAkB,IAAIkD,KAAK,YAAYjD,mBAAmB,GACvEiD,KAAK,CAACC,OAAO,GACbC;SACP", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}