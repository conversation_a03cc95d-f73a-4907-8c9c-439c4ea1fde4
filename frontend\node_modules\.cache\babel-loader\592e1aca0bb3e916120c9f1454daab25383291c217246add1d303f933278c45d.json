{"ast": null, "code": "/*\n  This file draws heavily from https://github.com/phoenixframework/phoenix/blob/d344ec0a732ab4ee204215b31de69cf4be72e3bf/assets/js/phoenix/presence.js\n  License: https://github.com/phoenixframework/phoenix/blob/d344ec0a732ab4ee204215b31de69cf4be72e3bf/LICENSE.md\n*/\nexport var REALTIME_PRESENCE_LISTEN_EVENTS;\n(function (REALTIME_PRESENCE_LISTEN_EVENTS) {\n  REALTIME_PRESENCE_LISTEN_EVENTS[\"SYNC\"] = \"sync\";\n  REALTIME_PRESENCE_LISTEN_EVENTS[\"JOIN\"] = \"join\";\n  REALTIME_PRESENCE_LISTEN_EVENTS[\"LEAVE\"] = \"leave\";\n})(REALTIME_PRESENCE_LISTEN_EVENTS || (REALTIME_PRESENCE_LISTEN_EVENTS = {}));\nexport default class RealtimePresence {\n  /**\n   * Initializes the Presence.\n   *\n   * @param channel - The RealtimeChannel\n   * @param opts - The options,\n   *        for example `{events: {state: 'state', diff: 'diff'}}`\n   */\n  constructor(channel, opts) {\n    this.channel = channel;\n    this.state = {};\n    this.pendingDiffs = [];\n    this.joinRef = null;\n    this.caller = {\n      onJoin: () => {},\n      onLeave: () => {},\n      onSync: () => {}\n    };\n    const events = (opts === null || opts === void 0 ? void 0 : opts.events) || {\n      state: 'presence_state',\n      diff: 'presence_diff'\n    };\n    this.channel._on(events.state, {}, newState => {\n      const {\n        onJoin,\n        onLeave,\n        onSync\n      } = this.caller;\n      this.joinRef = this.channel._joinRef();\n      this.state = RealtimePresence.syncState(this.state, newState, onJoin, onLeave);\n      this.pendingDiffs.forEach(diff => {\n        this.state = RealtimePresence.syncDiff(this.state, diff, onJoin, onLeave);\n      });\n      this.pendingDiffs = [];\n      onSync();\n    });\n    this.channel._on(events.diff, {}, diff => {\n      const {\n        onJoin,\n        onLeave,\n        onSync\n      } = this.caller;\n      if (this.inPendingSyncState()) {\n        this.pendingDiffs.push(diff);\n      } else {\n        this.state = RealtimePresence.syncDiff(this.state, diff, onJoin, onLeave);\n        onSync();\n      }\n    });\n    this.onJoin((key, currentPresences, newPresences) => {\n      this.channel._trigger('presence', {\n        event: 'join',\n        key,\n        currentPresences,\n        newPresences\n      });\n    });\n    this.onLeave((key, currentPresences, leftPresences) => {\n      this.channel._trigger('presence', {\n        event: 'leave',\n        key,\n        currentPresences,\n        leftPresences\n      });\n    });\n    this.onSync(() => {\n      this.channel._trigger('presence', {\n        event: 'sync'\n      });\n    });\n  }\n  /**\n   * Used to sync the list of presences on the server with the\n   * client's state.\n   *\n   * An optional `onJoin` and `onLeave` callback can be provided to\n   * react to changes in the client's local presences across\n   * disconnects and reconnects with the server.\n   *\n   * @internal\n   */\n  static syncState(currentState, newState, onJoin, onLeave) {\n    const state = this.cloneDeep(currentState);\n    const transformedState = this.transformState(newState);\n    const joins = {};\n    const leaves = {};\n    this.map(state, (key, presences) => {\n      if (!transformedState[key]) {\n        leaves[key] = presences;\n      }\n    });\n    this.map(transformedState, (key, newPresences) => {\n      const currentPresences = state[key];\n      if (currentPresences) {\n        const newPresenceRefs = newPresences.map(m => m.presence_ref);\n        const curPresenceRefs = currentPresences.map(m => m.presence_ref);\n        const joinedPresences = newPresences.filter(m => curPresenceRefs.indexOf(m.presence_ref) < 0);\n        const leftPresences = currentPresences.filter(m => newPresenceRefs.indexOf(m.presence_ref) < 0);\n        if (joinedPresences.length > 0) {\n          joins[key] = joinedPresences;\n        }\n        if (leftPresences.length > 0) {\n          leaves[key] = leftPresences;\n        }\n      } else {\n        joins[key] = newPresences;\n      }\n    });\n    return this.syncDiff(state, {\n      joins,\n      leaves\n    }, onJoin, onLeave);\n  }\n  /**\n   * Used to sync a diff of presence join and leave events from the\n   * server, as they happen.\n   *\n   * Like `syncState`, `syncDiff` accepts optional `onJoin` and\n   * `onLeave` callbacks to react to a user joining or leaving from a\n   * device.\n   *\n   * @internal\n   */\n  static syncDiff(state, diff, onJoin, onLeave) {\n    const {\n      joins,\n      leaves\n    } = {\n      joins: this.transformState(diff.joins),\n      leaves: this.transformState(diff.leaves)\n    };\n    if (!onJoin) {\n      onJoin = () => {};\n    }\n    if (!onLeave) {\n      onLeave = () => {};\n    }\n    this.map(joins, (key, newPresences) => {\n      var _a;\n      const currentPresences = (_a = state[key]) !== null && _a !== void 0 ? _a : [];\n      state[key] = this.cloneDeep(newPresences);\n      if (currentPresences.length > 0) {\n        const joinedPresenceRefs = state[key].map(m => m.presence_ref);\n        const curPresences = currentPresences.filter(m => joinedPresenceRefs.indexOf(m.presence_ref) < 0);\n        state[key].unshift(...curPresences);\n      }\n      onJoin(key, currentPresences, newPresences);\n    });\n    this.map(leaves, (key, leftPresences) => {\n      let currentPresences = state[key];\n      if (!currentPresences) return;\n      const presenceRefsToRemove = leftPresences.map(m => m.presence_ref);\n      currentPresences = currentPresences.filter(m => presenceRefsToRemove.indexOf(m.presence_ref) < 0);\n      state[key] = currentPresences;\n      onLeave(key, currentPresences, leftPresences);\n      if (currentPresences.length === 0) delete state[key];\n    });\n    return state;\n  }\n  /** @internal */\n  static map(obj, func) {\n    return Object.getOwnPropertyNames(obj).map(key => func(key, obj[key]));\n  }\n  /**\n   * Remove 'metas' key\n   * Change 'phx_ref' to 'presence_ref'\n   * Remove 'phx_ref' and 'phx_ref_prev'\n   *\n   * @example\n   * // returns {\n   *  abc123: [\n   *    { presence_ref: '2', user_id: 1 },\n   *    { presence_ref: '3', user_id: 2 }\n   *  ]\n   * }\n   * RealtimePresence.transformState({\n   *  abc123: {\n   *    metas: [\n   *      { phx_ref: '2', phx_ref_prev: '1' user_id: 1 },\n   *      { phx_ref: '3', user_id: 2 }\n   *    ]\n   *  }\n   * })\n   *\n   * @internal\n   */\n  static transformState(state) {\n    state = this.cloneDeep(state);\n    return Object.getOwnPropertyNames(state).reduce((newState, key) => {\n      const presences = state[key];\n      if ('metas' in presences) {\n        newState[key] = presences.metas.map(presence => {\n          presence['presence_ref'] = presence['phx_ref'];\n          delete presence['phx_ref'];\n          delete presence['phx_ref_prev'];\n          return presence;\n        });\n      } else {\n        newState[key] = presences;\n      }\n      return newState;\n    }, {});\n  }\n  /** @internal */\n  static cloneDeep(obj) {\n    return JSON.parse(JSON.stringify(obj));\n  }\n  /** @internal */\n  onJoin(callback) {\n    this.caller.onJoin = callback;\n  }\n  /** @internal */\n  onLeave(callback) {\n    this.caller.onLeave = callback;\n  }\n  /** @internal */\n  onSync(callback) {\n    this.caller.onSync = callback;\n  }\n  /** @internal */\n  inPendingSyncState() {\n    return !this.joinRef || this.joinRef !== this.channel._joinRef();\n  }\n}", "map": {"version": 3, "names": ["REALTIME_PRESENCE_LISTEN_EVENTS", "RealtimePresence", "constructor", "channel", "opts", "state", "pendingDiffs", "joinRef", "caller", "onJoin", "onLeave", "onSync", "events", "diff", "_on", "newState", "_joinRef", "syncState", "for<PERSON>ach", "syncDiff", "inPendingSyncState", "push", "key", "currentPresences", "newPresences", "_trigger", "event", "leftPresences", "currentState", "cloneDeep", "transformedState", "transformState", "joins", "leaves", "map", "presences", "newPresenceRefs", "m", "presence_ref", "curPresenceRefs", "joinedPresences", "filter", "indexOf", "length", "_a", "joinedPresenceRefs", "curPresences", "unshift", "presenceRefsToRemove", "obj", "func", "Object", "getOwnPropertyNames", "reduce", "metas", "presence", "JSON", "parse", "stringify", "callback"], "sources": ["D:\\menasa\\frontend\\node_modules\\@supabase\\realtime-js\\src\\RealtimePresence.ts"], "sourcesContent": ["/*\n  This file draws heavily from https://github.com/phoenixframework/phoenix/blob/d344ec0a732ab4ee204215b31de69cf4be72e3bf/assets/js/phoenix/presence.js\n  License: https://github.com/phoenixframework/phoenix/blob/d344ec0a732ab4ee204215b31de69cf4be72e3bf/LICENSE.md\n*/\n\nimport type {\n  PresenceOpts,\n  PresenceOnJoinCallback,\n  PresenceOnLeaveCallback,\n} from 'phoenix'\nimport type RealtimeChannel from './RealtimeChannel'\n\ntype Presence<T extends { [key: string]: any } = {}> = {\n  presence_ref: string\n} & T\n\nexport type RealtimePresenceState<T extends { [key: string]: any } = {}> = {\n  [key: string]: Presence<T>[]\n}\n\nexport type RealtimePresenceJoinPayload<T extends { [key: string]: any }> = {\n  event: `${REALTIME_PRESENCE_LISTEN_EVENTS.JOIN}`\n  key: string\n  currentPresences: Presence<T>[]\n  newPresences: Presence<T>[]\n}\n\nexport type RealtimePresenceLeavePayload<T extends { [key: string]: any }> = {\n  event: `${REALTIME_PRESENCE_LISTEN_EVENTS.LEAVE}`\n  key: string\n  currentPresences: Presence<T>[]\n  leftPresences: Presence<T>[]\n}\n\nexport enum REALTIME_PRESENCE_LISTEN_EVENTS {\n  SYNC = 'sync',\n  JOIN = 'join',\n  LEAVE = 'leave',\n}\n\ntype PresenceDiff = {\n  joins: RealtimePresenceState\n  leaves: RealtimePresenceState\n}\n\ntype RawPresenceState = {\n  [key: string]: {\n    metas: {\n      phx_ref?: string\n      phx_ref_prev?: string\n      [key: string]: any\n    }[]\n  }\n}\n\ntype RawPresenceDiff = {\n  joins: RawPresenceState\n  leaves: RawPresenceState\n}\n\ntype PresenceChooser<T> = (key: string, presences: Presence[]) => T\n\nexport default class RealtimePresence {\n  state: RealtimePresenceState = {}\n  pendingDiffs: RawPresenceDiff[] = []\n  joinRef: string | null = null\n  caller: {\n    onJoin: PresenceOnJoinCallback\n    onLeave: PresenceOnLeaveCallback\n    onSync: () => void\n  } = {\n    onJoin: () => {},\n    onLeave: () => {},\n    onSync: () => {},\n  }\n\n  /**\n   * Initializes the Presence.\n   *\n   * @param channel - The RealtimeChannel\n   * @param opts - The options,\n   *        for example `{events: {state: 'state', diff: 'diff'}}`\n   */\n  constructor(public channel: RealtimeChannel, opts?: PresenceOpts) {\n    const events = opts?.events || {\n      state: 'presence_state',\n      diff: 'presence_diff',\n    }\n\n    this.channel._on(events.state, {}, (newState: RawPresenceState) => {\n      const { onJoin, onLeave, onSync } = this.caller\n\n      this.joinRef = this.channel._joinRef()\n\n      this.state = RealtimePresence.syncState(\n        this.state,\n        newState,\n        onJoin,\n        onLeave\n      )\n\n      this.pendingDiffs.forEach((diff) => {\n        this.state = RealtimePresence.syncDiff(\n          this.state,\n          diff,\n          onJoin,\n          onLeave\n        )\n      })\n\n      this.pendingDiffs = []\n\n      onSync()\n    })\n\n    this.channel._on(events.diff, {}, (diff: RawPresenceDiff) => {\n      const { onJoin, onLeave, onSync } = this.caller\n\n      if (this.inPendingSyncState()) {\n        this.pendingDiffs.push(diff)\n      } else {\n        this.state = RealtimePresence.syncDiff(\n          this.state,\n          diff,\n          onJoin,\n          onLeave\n        )\n\n        onSync()\n      }\n    })\n\n    this.onJoin((key, currentPresences, newPresences) => {\n      this.channel._trigger('presence', {\n        event: 'join',\n        key,\n        currentPresences,\n        newPresences,\n      })\n    })\n\n    this.onLeave((key, currentPresences, leftPresences) => {\n      this.channel._trigger('presence', {\n        event: 'leave',\n        key,\n        currentPresences,\n        leftPresences,\n      })\n    })\n\n    this.onSync(() => {\n      this.channel._trigger('presence', { event: 'sync' })\n    })\n  }\n\n  /**\n   * Used to sync the list of presences on the server with the\n   * client's state.\n   *\n   * An optional `onJoin` and `onLeave` callback can be provided to\n   * react to changes in the client's local presences across\n   * disconnects and reconnects with the server.\n   *\n   * @internal\n   */\n  private static syncState(\n    currentState: RealtimePresenceState,\n    newState: RawPresenceState | RealtimePresenceState,\n    onJoin: PresenceOnJoinCallback,\n    onLeave: PresenceOnLeaveCallback\n  ): RealtimePresenceState {\n    const state = this.cloneDeep(currentState)\n    const transformedState = this.transformState(newState)\n    const joins: RealtimePresenceState = {}\n    const leaves: RealtimePresenceState = {}\n\n    this.map(state, (key: string, presences: Presence[]) => {\n      if (!transformedState[key]) {\n        leaves[key] = presences\n      }\n    })\n\n    this.map(transformedState, (key, newPresences: Presence[]) => {\n      const currentPresences: Presence[] = state[key]\n\n      if (currentPresences) {\n        const newPresenceRefs = newPresences.map(\n          (m: Presence) => m.presence_ref\n        )\n        const curPresenceRefs = currentPresences.map(\n          (m: Presence) => m.presence_ref\n        )\n        const joinedPresences: Presence[] = newPresences.filter(\n          (m: Presence) => curPresenceRefs.indexOf(m.presence_ref) < 0\n        )\n        const leftPresences: Presence[] = currentPresences.filter(\n          (m: Presence) => newPresenceRefs.indexOf(m.presence_ref) < 0\n        )\n\n        if (joinedPresences.length > 0) {\n          joins[key] = joinedPresences\n        }\n\n        if (leftPresences.length > 0) {\n          leaves[key] = leftPresences\n        }\n      } else {\n        joins[key] = newPresences\n      }\n    })\n\n    return this.syncDiff(state, { joins, leaves }, onJoin, onLeave)\n  }\n\n  /**\n   * Used to sync a diff of presence join and leave events from the\n   * server, as they happen.\n   *\n   * Like `syncState`, `syncDiff` accepts optional `onJoin` and\n   * `onLeave` callbacks to react to a user joining or leaving from a\n   * device.\n   *\n   * @internal\n   */\n  private static syncDiff(\n    state: RealtimePresenceState,\n    diff: RawPresenceDiff | PresenceDiff,\n    onJoin: PresenceOnJoinCallback,\n    onLeave: PresenceOnLeaveCallback\n  ): RealtimePresenceState {\n    const { joins, leaves } = {\n      joins: this.transformState(diff.joins),\n      leaves: this.transformState(diff.leaves),\n    }\n\n    if (!onJoin) {\n      onJoin = () => {}\n    }\n\n    if (!onLeave) {\n      onLeave = () => {}\n    }\n\n    this.map(joins, (key, newPresences: Presence[]) => {\n      const currentPresences: Presence[] = state[key] ?? []\n      state[key] = this.cloneDeep(newPresences)\n\n      if (currentPresences.length > 0) {\n        const joinedPresenceRefs = state[key].map(\n          (m: Presence) => m.presence_ref\n        )\n        const curPresences: Presence[] = currentPresences.filter(\n          (m: Presence) => joinedPresenceRefs.indexOf(m.presence_ref) < 0\n        )\n\n        state[key].unshift(...curPresences)\n      }\n\n      onJoin(key, currentPresences, newPresences)\n    })\n\n    this.map(leaves, (key, leftPresences: Presence[]) => {\n      let currentPresences: Presence[] = state[key]\n\n      if (!currentPresences) return\n\n      const presenceRefsToRemove = leftPresences.map(\n        (m: Presence) => m.presence_ref\n      )\n      currentPresences = currentPresences.filter(\n        (m: Presence) => presenceRefsToRemove.indexOf(m.presence_ref) < 0\n      )\n\n      state[key] = currentPresences\n\n      onLeave(key, currentPresences, leftPresences)\n\n      if (currentPresences.length === 0) delete state[key]\n    })\n\n    return state\n  }\n\n  /** @internal */\n  private static map<T = any>(\n    obj: RealtimePresenceState,\n    func: PresenceChooser<T>\n  ): T[] {\n    return Object.getOwnPropertyNames(obj).map((key) => func(key, obj[key]))\n  }\n\n  /**\n   * Remove 'metas' key\n   * Change 'phx_ref' to 'presence_ref'\n   * Remove 'phx_ref' and 'phx_ref_prev'\n   *\n   * @example\n   * // returns {\n   *  abc123: [\n   *    { presence_ref: '2', user_id: 1 },\n   *    { presence_ref: '3', user_id: 2 }\n   *  ]\n   * }\n   * RealtimePresence.transformState({\n   *  abc123: {\n   *    metas: [\n   *      { phx_ref: '2', phx_ref_prev: '1' user_id: 1 },\n   *      { phx_ref: '3', user_id: 2 }\n   *    ]\n   *  }\n   * })\n   *\n   * @internal\n   */\n  private static transformState(\n    state: RawPresenceState | RealtimePresenceState\n  ): RealtimePresenceState {\n    state = this.cloneDeep(state)\n\n    return Object.getOwnPropertyNames(state).reduce((newState, key) => {\n      const presences = state[key]\n\n      if ('metas' in presences) {\n        newState[key] = presences.metas.map((presence) => {\n          presence['presence_ref'] = presence['phx_ref']\n\n          delete presence['phx_ref']\n          delete presence['phx_ref_prev']\n\n          return presence\n        }) as Presence[]\n      } else {\n        newState[key] = presences\n      }\n\n      return newState\n    }, {} as RealtimePresenceState)\n  }\n\n  /** @internal */\n  private static cloneDeep(obj: { [key: string]: any }) {\n    return JSON.parse(JSON.stringify(obj))\n  }\n\n  /** @internal */\n  private onJoin(callback: PresenceOnJoinCallback): void {\n    this.caller.onJoin = callback\n  }\n\n  /** @internal */\n  private onLeave(callback: PresenceOnLeaveCallback): void {\n    this.caller.onLeave = callback\n  }\n\n  /** @internal */\n  private onSync(callback: () => void): void {\n    this.caller.onSync = callback\n  }\n\n  /** @internal */\n  private inPendingSyncState(): boolean {\n    return !this.joinRef || this.joinRef !== this.channel._joinRef()\n  }\n}\n"], "mappings": "AAAA;;;;AAkCA,WAAYA,+BAIX;AAJD,WAAYA,+BAA+B;EACzCA,+BAAA,iBAAa;EACbA,+BAAA,iBAAa;EACbA,+BAAA,mBAAe;AACjB,CAAC,EAJWA,+BAA+B,KAA/BA,+BAA+B;AA4B3C,eAAc,MAAOC,gBAAgB;EAcnC;;;;;;;EAOAC,YAAmBC,OAAwB,EAAEC,IAAmB;IAA7C,KAAAD,OAAO,GAAPA,OAAO;IApB1B,KAAAE,KAAK,GAA0B,EAAE;IACjC,KAAAC,YAAY,GAAsB,EAAE;IACpC,KAAAC,OAAO,GAAkB,IAAI;IAC7B,KAAAC,MAAM,GAIF;MACFC,MAAM,EAAEA,CAAA,KAAK,CAAE,CAAC;MAChBC,OAAO,EAAEA,CAAA,KAAK,CAAE,CAAC;MACjBC,MAAM,EAAEA,CAAA,KAAK,CAAE;KAChB;IAUC,MAAMC,MAAM,GAAG,CAAAR,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEQ,MAAM,KAAI;MAC7BP,KAAK,EAAE,gBAAgB;MACvBQ,IAAI,EAAE;KACP;IAED,IAAI,CAACV,OAAO,CAACW,GAAG,CAACF,MAAM,CAACP,KAAK,EAAE,EAAE,EAAGU,QAA0B,IAAI;MAChE,MAAM;QAAEN,MAAM;QAAEC,OAAO;QAAEC;MAAM,CAAE,GAAG,IAAI,CAACH,MAAM;MAE/C,IAAI,CAACD,OAAO,GAAG,IAAI,CAACJ,OAAO,CAACa,QAAQ,EAAE;MAEtC,IAAI,CAACX,KAAK,GAAGJ,gBAAgB,CAACgB,SAAS,CACrC,IAAI,CAACZ,KAAK,EACVU,QAAQ,EACRN,MAAM,EACNC,OAAO,CACR;MAED,IAAI,CAACJ,YAAY,CAACY,OAAO,CAAEL,IAAI,IAAI;QACjC,IAAI,CAACR,KAAK,GAAGJ,gBAAgB,CAACkB,QAAQ,CACpC,IAAI,CAACd,KAAK,EACVQ,IAAI,EACJJ,MAAM,EACNC,OAAO,CACR;MACH,CAAC,CAAC;MAEF,IAAI,CAACJ,YAAY,GAAG,EAAE;MAEtBK,MAAM,EAAE;IACV,CAAC,CAAC;IAEF,IAAI,CAACR,OAAO,CAACW,GAAG,CAACF,MAAM,CAACC,IAAI,EAAE,EAAE,EAAGA,IAAqB,IAAI;MAC1D,MAAM;QAAEJ,MAAM;QAAEC,OAAO;QAAEC;MAAM,CAAE,GAAG,IAAI,CAACH,MAAM;MAE/C,IAAI,IAAI,CAACY,kBAAkB,EAAE,EAAE;QAC7B,IAAI,CAACd,YAAY,CAACe,IAAI,CAACR,IAAI,CAAC;MAC9B,CAAC,MAAM;QACL,IAAI,CAACR,KAAK,GAAGJ,gBAAgB,CAACkB,QAAQ,CACpC,IAAI,CAACd,KAAK,EACVQ,IAAI,EACJJ,MAAM,EACNC,OAAO,CACR;QAEDC,MAAM,EAAE;MACV;IACF,CAAC,CAAC;IAEF,IAAI,CAACF,MAAM,CAAC,CAACa,GAAG,EAAEC,gBAAgB,EAAEC,YAAY,KAAI;MAClD,IAAI,CAACrB,OAAO,CAACsB,QAAQ,CAAC,UAAU,EAAE;QAChCC,KAAK,EAAE,MAAM;QACbJ,GAAG;QACHC,gBAAgB;QAChBC;OACD,CAAC;IACJ,CAAC,CAAC;IAEF,IAAI,CAACd,OAAO,CAAC,CAACY,GAAG,EAAEC,gBAAgB,EAAEI,aAAa,KAAI;MACpD,IAAI,CAACxB,OAAO,CAACsB,QAAQ,CAAC,UAAU,EAAE;QAChCC,KAAK,EAAE,OAAO;QACdJ,GAAG;QACHC,gBAAgB;QAChBI;OACD,CAAC;IACJ,CAAC,CAAC;IAEF,IAAI,CAAChB,MAAM,CAAC,MAAK;MACf,IAAI,CAACR,OAAO,CAACsB,QAAQ,CAAC,UAAU,EAAE;QAAEC,KAAK,EAAE;MAAM,CAAE,CAAC;IACtD,CAAC,CAAC;EACJ;EAEA;;;;;;;;;;EAUQ,OAAOT,SAASA,CACtBW,YAAmC,EACnCb,QAAkD,EAClDN,MAA8B,EAC9BC,OAAgC;IAEhC,MAAML,KAAK,GAAG,IAAI,CAACwB,SAAS,CAACD,YAAY,CAAC;IAC1C,MAAME,gBAAgB,GAAG,IAAI,CAACC,cAAc,CAAChB,QAAQ,CAAC;IACtD,MAAMiB,KAAK,GAA0B,EAAE;IACvC,MAAMC,MAAM,GAA0B,EAAE;IAExC,IAAI,CAACC,GAAG,CAAC7B,KAAK,EAAE,CAACiB,GAAW,EAAEa,SAAqB,KAAI;MACrD,IAAI,CAACL,gBAAgB,CAACR,GAAG,CAAC,EAAE;QAC1BW,MAAM,CAACX,GAAG,CAAC,GAAGa,SAAS;MACzB;IACF,CAAC,CAAC;IAEF,IAAI,CAACD,GAAG,CAACJ,gBAAgB,EAAE,CAACR,GAAG,EAAEE,YAAwB,KAAI;MAC3D,MAAMD,gBAAgB,GAAelB,KAAK,CAACiB,GAAG,CAAC;MAE/C,IAAIC,gBAAgB,EAAE;QACpB,MAAMa,eAAe,GAAGZ,YAAY,CAACU,GAAG,CACrCG,CAAW,IAAKA,CAAC,CAACC,YAAY,CAChC;QACD,MAAMC,eAAe,GAAGhB,gBAAgB,CAACW,GAAG,CACzCG,CAAW,IAAKA,CAAC,CAACC,YAAY,CAChC;QACD,MAAME,eAAe,GAAehB,YAAY,CAACiB,MAAM,CACpDJ,CAAW,IAAKE,eAAe,CAACG,OAAO,CAACL,CAAC,CAACC,YAAY,CAAC,GAAG,CAAC,CAC7D;QACD,MAAMX,aAAa,GAAeJ,gBAAgB,CAACkB,MAAM,CACtDJ,CAAW,IAAKD,eAAe,CAACM,OAAO,CAACL,CAAC,CAACC,YAAY,CAAC,GAAG,CAAC,CAC7D;QAED,IAAIE,eAAe,CAACG,MAAM,GAAG,CAAC,EAAE;UAC9BX,KAAK,CAACV,GAAG,CAAC,GAAGkB,eAAe;QAC9B;QAEA,IAAIb,aAAa,CAACgB,MAAM,GAAG,CAAC,EAAE;UAC5BV,MAAM,CAACX,GAAG,CAAC,GAAGK,aAAa;QAC7B;MACF,CAAC,MAAM;QACLK,KAAK,CAACV,GAAG,CAAC,GAAGE,YAAY;MAC3B;IACF,CAAC,CAAC;IAEF,OAAO,IAAI,CAACL,QAAQ,CAACd,KAAK,EAAE;MAAE2B,KAAK;MAAEC;IAAM,CAAE,EAAExB,MAAM,EAAEC,OAAO,CAAC;EACjE;EAEA;;;;;;;;;;EAUQ,OAAOS,QAAQA,CACrBd,KAA4B,EAC5BQ,IAAoC,EACpCJ,MAA8B,EAC9BC,OAAgC;IAEhC,MAAM;MAAEsB,KAAK;MAAEC;IAAM,CAAE,GAAG;MACxBD,KAAK,EAAE,IAAI,CAACD,cAAc,CAAClB,IAAI,CAACmB,KAAK,CAAC;MACtCC,MAAM,EAAE,IAAI,CAACF,cAAc,CAAClB,IAAI,CAACoB,MAAM;KACxC;IAED,IAAI,CAACxB,MAAM,EAAE;MACXA,MAAM,GAAGA,CAAA,KAAK,CAAE,CAAC;IACnB;IAEA,IAAI,CAACC,OAAO,EAAE;MACZA,OAAO,GAAGA,CAAA,KAAK,CAAE,CAAC;IACpB;IAEA,IAAI,CAACwB,GAAG,CAACF,KAAK,EAAE,CAACV,GAAG,EAAEE,YAAwB,KAAI;;MAChD,MAAMD,gBAAgB,GAAe,CAAAqB,EAAA,GAAAvC,KAAK,CAACiB,GAAG,CAAC,cAAAsB,EAAA,cAAAA,EAAA,GAAI,EAAE;MACrDvC,KAAK,CAACiB,GAAG,CAAC,GAAG,IAAI,CAACO,SAAS,CAACL,YAAY,CAAC;MAEzC,IAAID,gBAAgB,CAACoB,MAAM,GAAG,CAAC,EAAE;QAC/B,MAAME,kBAAkB,GAAGxC,KAAK,CAACiB,GAAG,CAAC,CAACY,GAAG,CACtCG,CAAW,IAAKA,CAAC,CAACC,YAAY,CAChC;QACD,MAAMQ,YAAY,GAAevB,gBAAgB,CAACkB,MAAM,CACrDJ,CAAW,IAAKQ,kBAAkB,CAACH,OAAO,CAACL,CAAC,CAACC,YAAY,CAAC,GAAG,CAAC,CAChE;QAEDjC,KAAK,CAACiB,GAAG,CAAC,CAACyB,OAAO,CAAC,GAAGD,YAAY,CAAC;MACrC;MAEArC,MAAM,CAACa,GAAG,EAAEC,gBAAgB,EAAEC,YAAY,CAAC;IAC7C,CAAC,CAAC;IAEF,IAAI,CAACU,GAAG,CAACD,MAAM,EAAE,CAACX,GAAG,EAAEK,aAAyB,KAAI;MAClD,IAAIJ,gBAAgB,GAAelB,KAAK,CAACiB,GAAG,CAAC;MAE7C,IAAI,CAACC,gBAAgB,EAAE;MAEvB,MAAMyB,oBAAoB,GAAGrB,aAAa,CAACO,GAAG,CAC3CG,CAAW,IAAKA,CAAC,CAACC,YAAY,CAChC;MACDf,gBAAgB,GAAGA,gBAAgB,CAACkB,MAAM,CACvCJ,CAAW,IAAKW,oBAAoB,CAACN,OAAO,CAACL,CAAC,CAACC,YAAY,CAAC,GAAG,CAAC,CAClE;MAEDjC,KAAK,CAACiB,GAAG,CAAC,GAAGC,gBAAgB;MAE7Bb,OAAO,CAACY,GAAG,EAAEC,gBAAgB,EAAEI,aAAa,CAAC;MAE7C,IAAIJ,gBAAgB,CAACoB,MAAM,KAAK,CAAC,EAAE,OAAOtC,KAAK,CAACiB,GAAG,CAAC;IACtD,CAAC,CAAC;IAEF,OAAOjB,KAAK;EACd;EAEA;EACQ,OAAO6B,GAAGA,CAChBe,GAA0B,EAC1BC,IAAwB;IAExB,OAAOC,MAAM,CAACC,mBAAmB,CAACH,GAAG,CAAC,CAACf,GAAG,CAAEZ,GAAG,IAAK4B,IAAI,CAAC5B,GAAG,EAAE2B,GAAG,CAAC3B,GAAG,CAAC,CAAC,CAAC;EAC1E;EAEA;;;;;;;;;;;;;;;;;;;;;;;EAuBQ,OAAOS,cAAcA,CAC3B1B,KAA+C;IAE/CA,KAAK,GAAG,IAAI,CAACwB,SAAS,CAACxB,KAAK,CAAC;IAE7B,OAAO8C,MAAM,CAACC,mBAAmB,CAAC/C,KAAK,CAAC,CAACgD,MAAM,CAAC,CAACtC,QAAQ,EAAEO,GAAG,KAAI;MAChE,MAAMa,SAAS,GAAG9B,KAAK,CAACiB,GAAG,CAAC;MAE5B,IAAI,OAAO,IAAIa,SAAS,EAAE;QACxBpB,QAAQ,CAACO,GAAG,CAAC,GAAGa,SAAS,CAACmB,KAAK,CAACpB,GAAG,CAAEqB,QAAQ,IAAI;UAC/CA,QAAQ,CAAC,cAAc,CAAC,GAAGA,QAAQ,CAAC,SAAS,CAAC;UAE9C,OAAOA,QAAQ,CAAC,SAAS,CAAC;UAC1B,OAAOA,QAAQ,CAAC,cAAc,CAAC;UAE/B,OAAOA,QAAQ;QACjB,CAAC,CAAe;MAClB,CAAC,MAAM;QACLxC,QAAQ,CAACO,GAAG,CAAC,GAAGa,SAAS;MAC3B;MAEA,OAAOpB,QAAQ;IACjB,CAAC,EAAE,EAA2B,CAAC;EACjC;EAEA;EACQ,OAAOc,SAASA,CAACoB,GAA2B;IAClD,OAAOO,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACT,GAAG,CAAC,CAAC;EACxC;EAEA;EACQxC,MAAMA,CAACkD,QAAgC;IAC7C,IAAI,CAACnD,MAAM,CAACC,MAAM,GAAGkD,QAAQ;EAC/B;EAEA;EACQjD,OAAOA,CAACiD,QAAiC;IAC/C,IAAI,CAACnD,MAAM,CAACE,OAAO,GAAGiD,QAAQ;EAChC;EAEA;EACQhD,MAAMA,CAACgD,QAAoB;IACjC,IAAI,CAACnD,MAAM,CAACG,MAAM,GAAGgD,QAAQ;EAC/B;EAEA;EACQvC,kBAAkBA,CAAA;IACxB,OAAO,CAAC,IAAI,CAACb,OAAO,IAAI,IAAI,CAACA,OAAO,KAAK,IAAI,CAACJ,OAAO,CAACa,QAAQ,EAAE;EAClE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}