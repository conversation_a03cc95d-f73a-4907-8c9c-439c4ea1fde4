-- إعد<PERSON> قاعدة البيانات لنظام إدارة الملفات التعليمية
-- يجب تشغيل هذا الملف في Supabase SQL Editor

-- إن<PERSON>اء جدول profiles لتوسيع بيانات المستخدمين
CREATE TABLE IF NOT EXISTS public.profiles (
  id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  full_name TEXT,
  avatar_url TEXT,
  role TEXT DEFAULT 'student' CHECK (role IN ('student', 'admin')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إن<PERSON>اء فهارس على جدول profiles
CREATE INDEX IF NOT EXISTS profiles_email_idx ON public.profiles(email);
CREATE INDEX IF NOT EXISTS profiles_role_idx ON public.profiles(role);

-- <PERSON><PERSON><PERSON><PERSON><PERSON> جدول الملفات
CREATE TABLE IF NOT EXISTS public.files (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT,
  file_name TEXT NOT NULL,
  file_path TEXT NOT NULL,
  file_type TEXT NOT NULL,
  file_size BIGINT NOT NULL,
  subject TEXT,
  semester TEXT,
  uploaded_by UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
  download_count INTEGER DEFAULT 0,
  average_rating DECIMAL(3,2) DEFAULT 0.0,
  rating_count INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS files_title_idx ON public.files USING gin(to_tsvector('arabic', title));
CREATE INDEX IF NOT EXISTS files_subject_idx ON public.files(subject);
CREATE INDEX IF NOT EXISTS files_semester_idx ON public.files(semester);
CREATE INDEX IF NOT EXISTS files_uploaded_by_idx ON public.files(uploaded_by);
CREATE INDEX IF NOT EXISTS files_created_at_idx ON public.files(created_at DESC);
CREATE INDEX IF NOT EXISTS files_download_count_idx ON public.files(download_count DESC);
CREATE INDEX IF NOT EXISTS files_average_rating_idx ON public.files(average_rating DESC);

-- إنشاء جدول التقييمات
CREATE TABLE IF NOT EXISTS public.ratings (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  file_id UUID REFERENCES public.files(id) ON DELETE CASCADE,
  user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
  rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(file_id, user_id)
);

-- إنشاء فهارس لجدول التقييمات
CREATE INDEX IF NOT EXISTS ratings_file_id_idx ON public.ratings(file_id);
CREATE INDEX IF NOT EXISTS ratings_user_id_idx ON public.ratings(user_id);

-- إنشاء جدول التحميلات
CREATE TABLE IF NOT EXISTS public.downloads (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  file_id UUID REFERENCES public.files(id) ON DELETE CASCADE,
  user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
  downloaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء فهارس لجدول التحميلات
CREATE INDEX IF NOT EXISTS downloads_file_id_idx ON public.downloads(file_id);
CREATE INDEX IF NOT EXISTS downloads_user_id_idx ON public.downloads(user_id);
CREATE INDEX IF NOT EXISTS downloads_downloaded_at_idx ON public.downloads(downloaded_at DESC);

-- إنشاء جدول المفضلة
CREATE TABLE IF NOT EXISTS public.favorites (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  file_id UUID REFERENCES public.files(id) ON DELETE CASCADE,
  user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(file_id, user_id)
);

-- إنشاء فهارس لجدول المفضلة
CREATE INDEX IF NOT EXISTS favorites_file_id_idx ON public.favorites(file_id);
CREATE INDEX IF NOT EXISTS favorites_user_id_idx ON public.favorites(user_id);
CREATE INDEX IF NOT EXISTS favorites_created_at_idx ON public.favorites(created_at DESC);

-- تفعيل Row Level Security على جميع الجداول
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.files ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.ratings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.downloads ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.favorites ENABLE ROW LEVEL SECURITY;

-- سياسات RLS لجدول profiles
CREATE POLICY "Users can view own profile" ON public.profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.profiles
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON public.profiles
  FOR INSERT WITH CHECK (auth.uid() = id);

-- سياسات RLS لجدول files
CREATE POLICY "Anyone can view active files" ON public.files
  FOR SELECT USING (is_active = true);

CREATE POLICY "Only admins can insert files" ON public.files
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

CREATE POLICY "Admins and owners can update files" ON public.files
  FOR UPDATE USING (
    uploaded_by = auth.uid() OR
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

CREATE POLICY "Admins and owners can delete files" ON public.files
  FOR DELETE USING (
    uploaded_by = auth.uid() OR
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- سياسات RLS لجدول ratings
CREATE POLICY "Anyone can view ratings" ON public.ratings
  FOR SELECT USING (true);

CREATE POLICY "Users can insert own ratings" ON public.ratings
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own ratings" ON public.ratings
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own ratings" ON public.ratings
  FOR DELETE USING (auth.uid() = user_id);

-- سياسات RLS لجدول downloads
CREATE POLICY "Users can view own downloads" ON public.downloads
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own downloads" ON public.downloads
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Admins can view all downloads" ON public.downloads
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- سياسات RLS لجدول favorites
CREATE POLICY "Users can view own favorites" ON public.favorites
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own favorites" ON public.favorites
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete own favorites" ON public.favorites
  FOR DELETE USING (auth.uid() = user_id);

-- دالة لإنشاء ملف شخصي تلقائياً عند التسجيل
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, email, full_name)
  VALUES (NEW.id, NEW.email, NEW.raw_user_meta_data->>'full_name');
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- إنشاء trigger لتشغيل الدالة عند إنشاء مستخدم جديد
CREATE OR REPLACE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- دالة لتحديث متوسط التقييم في جدول الملفات
CREATE OR REPLACE FUNCTION update_file_rating()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE public.files
  SET 
    average_rating = (
      SELECT COALESCE(AVG(rating), 0)
      FROM public.ratings
      WHERE file_id = COALESCE(NEW.file_id, OLD.file_id)
    ),
    rating_count = (
      SELECT COUNT(*)
      FROM public.ratings
      WHERE file_id = COALESCE(NEW.file_id, OLD.file_id)
    )
  WHERE id = COALESCE(NEW.file_id, OLD.file_id);
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- إنشاء triggers لتحديث متوسط التقييم
CREATE TRIGGER rating_insert_trigger
  AFTER INSERT ON public.ratings
  FOR EACH ROW EXECUTE FUNCTION update_file_rating();

CREATE TRIGGER rating_update_trigger
  AFTER UPDATE ON public.ratings
  FOR EACH ROW EXECUTE FUNCTION update_file_rating();

CREATE TRIGGER rating_delete_trigger
  AFTER DELETE ON public.ratings
  FOR EACH ROW EXECUTE FUNCTION update_file_rating();

-- دالة لتحديث عداد التحميلات
CREATE OR REPLACE FUNCTION update_download_count()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE public.files
  SET download_count = (
    SELECT COUNT(*)
    FROM public.downloads
    WHERE file_id = NEW.file_id
  )
  WHERE id = NEW.file_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إنشاء trigger لتحديث عداد التحميلات
CREATE TRIGGER download_count_trigger
  AFTER INSERT ON public.downloads
  FOR EACH ROW EXECUTE FUNCTION update_download_count();
