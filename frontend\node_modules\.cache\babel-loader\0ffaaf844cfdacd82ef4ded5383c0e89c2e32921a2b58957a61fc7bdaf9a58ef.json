{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nconst isLength = require('../../predicate/isLength.js');\nfunction isArrayLike(value) {\n  return value != null && typeof value !== 'function' && isLength.isLength(value.length);\n}\nexports.isArrayLike = isArrayLike;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "<PERSON><PERSON><PERSON><PERSON>", "require", "isArrayLike", "length"], "sources": ["D:/menasa/frontend/node_modules/es-toolkit/dist/compat/predicate/isArrayLike.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nconst isLength = require('../../predicate/isLength.js');\n\nfunction isArrayLike(value) {\n    return value != null && typeof value !== 'function' && isLength.isLength(value.length);\n}\n\nexports.isArrayLike = isArrayLike;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,MAAMC,QAAQ,GAAGC,OAAO,CAAC,6BAA6B,CAAC;AAEvD,SAASC,WAAWA,CAACH,KAAK,EAAE;EACxB,OAAOA,KAAK,IAAI,IAAI,IAAI,OAAOA,KAAK,KAAK,UAAU,IAAIC,QAAQ,CAACA,QAAQ,CAACD,KAAK,CAACI,MAAM,CAAC;AAC1F;AAEAP,OAAO,CAACM,WAAW,GAAGA,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}