import React, { useState, forwardRef } from 'react';
import styled, { css } from 'styled-components';
import { motion } from 'framer-motion';

const InputWrapper = styled.div`
  position: relative;
  width: 100%;
`;

const InputLabel = styled.label`
  display: block;
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  color: ${({ theme }) => theme.colors.gray[700]};
  margin-bottom: ${({ theme }) => theme.spacing[2]};
`;

const InputBase = styled(motion.input)`
  width: 100%;
  font-family: ${({ theme }) => theme.fonts.primary};
  font-size: ${({ theme }) => theme.fontSizes.base};
  color: ${({ theme }) => theme.colors.gray[900]};
  background: ${({ theme }) => theme.colors.white};
  border: 2px solid ${({ theme }) => theme.colors.gray[200]};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  padding: ${({ theme }) => theme.spacing[3]} ${({ theme }) => theme.spacing[4]};
  transition: all ${({ theme }) => theme.transitions.fast};
  direction: ${({ dir }) => dir || 'rtl'};
  text-align: ${({ dir }) => dir === 'ltr' ? 'left' : 'right'};

  &::placeholder {
    color: ${({ theme }) => theme.colors.gray[400]};
  }

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary[500]};
    box-shadow: 0 0 0 3px ${({ theme }) => theme.colors.primary[100]};
  }

  &:disabled {
    background: ${({ theme }) => theme.colors.gray[50]};
    color: ${({ theme }) => theme.colors.gray[500]};
    cursor: not-allowed;
  }

  /* Size Variants */
  ${({ size, theme }) => {
    switch (size) {
      case 'sm':
        return css`
          padding: ${theme.spacing[2]} ${theme.spacing[3]};
          font-size: ${theme.fontSizes.sm};
        `;
      case 'lg':
        return css`
          padding: ${theme.spacing[4]} ${theme.spacing[5]};
          font-size: ${theme.fontSizes.lg};
        `;
      default:
        return css`
          padding: ${theme.spacing[3]} ${theme.spacing[4]};
          font-size: ${theme.fontSizes.base};
        `;
    }
  }}

  /* Error State */
  ${({ error, theme }) =>
    error &&
    css`
      border-color: ${theme.colors.error[500]};
      
      &:focus {
        border-color: ${theme.colors.error[500]};
        box-shadow: 0 0 0 3px ${theme.colors.error[100]};
      }
    `}

  /* Success State */
  ${({ success, theme }) =>
    success &&
    css`
      border-color: ${theme.colors.success[500]};
      
      &:focus {
        border-color: ${theme.colors.success[500]};
        box-shadow: 0 0 0 3px ${theme.colors.success[100]};
      }
    `}

  /* With Icon */
  ${({ hasLeftIcon, hasRightIcon, theme }) => {
    if (hasLeftIcon && hasRightIcon) {
      return css`
        padding-left: ${theme.spacing[12]};
        padding-right: ${theme.spacing[12]};
      `;
    } else if (hasLeftIcon) {
      return css`
        padding-left: ${theme.spacing[12]};
      `;
    } else if (hasRightIcon) {
      return css`
        padding-right: ${theme.spacing[12]};
      `;
    }
  }}
`;

const IconWrapper = styled.div`
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${({ theme }) => theme.colors.gray[400]};
  pointer-events: none;
  z-index: 1;

  ${({ position, theme }) =>
    position === 'left'
      ? css`
          left: ${theme.spacing[3]};
        `
      : css`
          right: ${theme.spacing[3]};
        `}
`;

const ErrorMessage = styled(motion.div)`
  margin-top: ${({ theme }) => theme.spacing[1]};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.error[600]};
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[1]};
`;

const HelperText = styled.div`
  margin-top: ${({ theme }) => theme.spacing[1]};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.gray[500]};
`;

const Input = forwardRef(({
  label,
  error,
  helperText,
  leftIcon,
  rightIcon,
  size = 'md',
  dir,
  className,
  ...props
}, ref) => {
  const [isFocused, setIsFocused] = useState(false);

  return (
    <InputWrapper className={className}>
      {label && <InputLabel>{label}</InputLabel>}
      
      <div style={{ position: 'relative' }}>
        {leftIcon && (
          <IconWrapper position="left">
            {leftIcon}
          </IconWrapper>
        )}
        
        <InputBase
          ref={ref}
          size={size}
          error={error}
          hasLeftIcon={!!leftIcon}
          hasRightIcon={!!rightIcon}
          dir={dir}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          initial={{ scale: 1 }}
          whileFocus={{ scale: 1.01 }}
          {...props}
        />
        
        {rightIcon && (
          <IconWrapper position="right">
            {rightIcon}
          </IconWrapper>
        )}
      </div>
      
      {error && (
        <ErrorMessage
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
        >
          {error}
        </ErrorMessage>
      )}
      
      {helperText && !error && (
        <HelperText>{helperText}</HelperText>
      )}
    </InputWrapper>
  );
});

Input.displayName = 'Input';

export default Input;
