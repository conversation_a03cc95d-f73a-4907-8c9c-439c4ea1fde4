-- Views مفيدة لنظام إدارة الملفات التعليمية
-- يجب تشغيل هذا الملف بعد setup.sql

-- إنشاء view للملفات مع بيانات المرفوع
CREATE OR REPLACE VIEW files_with_uploader AS
SELECT 
  f.*,
  p.full_name as uploader_name,
  p.email as uploader_email
FROM public.files f
LEFT JOIN public.profiles p ON f.uploaded_by = p.id;

-- إنشاء view للإحصائيات العامة
CREATE OR REPLACE VIEW admin_stats AS
SELECT 
  (
    SELECT COUNT(*) 
    FROM public.profiles
  ) as total_users,
  (
    SELECT COUNT(*) 
    FROM public.profiles 
    WHERE role = 'student'
  ) as total_students,
  (
    SELECT COUNT(*) 
    FROM public.profiles 
    WHERE role = 'admin'
  ) as total_admins,
  (
    SELECT COUNT(*) 
    FROM public.files 
    WHERE is_active = true
  ) as total_files,
  (
    SELECT COUNT(*) 
    FROM public.downloads
  ) as total_downloads,
  (
    SELECT COALESCE(AVG(average_rating), 0) 
    FROM public.files 
    WHERE is_active = true AND rating_count > 0
  ) as overall_average_rating,
  (
    SELECT COUNT(*) 
    FROM public.ratings
  ) as total_ratings,
  (
    SELECT COUNT(*) 
    FROM public.favorites
  ) as total_favorites;

-- إنشاء view للملفات الأكثر تحميلاً
CREATE OR REPLACE VIEW popular_files AS
SELECT 
  f.*,
  p.full_name as uploader_name
FROM public.files f
LEFT JOIN public.profiles p ON f.uploaded_by = p.id
WHERE f.is_active = true
ORDER BY f.download_count DESC, f.average_rating DESC
LIMIT 10;

-- إنشاء view للملفات الحديثة
CREATE OR REPLACE VIEW recent_files AS
SELECT 
  f.*,
  p.full_name as uploader_name
FROM public.files f
LEFT JOIN public.profiles p ON f.uploaded_by = p.id
WHERE f.is_active = true
ORDER BY f.created_at DESC
LIMIT 10;

-- إنشاء view للأنشطة الحديثة
CREATE OR REPLACE VIEW recent_activities AS
(
  SELECT 
    'download' as activity_type,
    d.downloaded_at as activity_time,
    f.title as file_title,
    p.full_name as user_name,
    f.id as file_id
  FROM public.downloads d
  JOIN public.files f ON d.file_id = f.id
  JOIN public.profiles p ON d.user_id = p.id
  ORDER BY d.downloaded_at DESC
  LIMIT 5
)
UNION ALL
(
  SELECT 
    'rating' as activity_type,
    r.created_at as activity_time,
    f.title as file_title,
    p.full_name as user_name,
    f.id as file_id
  FROM public.ratings r
  JOIN public.files f ON r.file_id = f.id
  JOIN public.profiles p ON r.user_id = p.id
  ORDER BY r.created_at DESC
  LIMIT 5
)
UNION ALL
(
  SELECT 
    'upload' as activity_type,
    f.created_at as activity_time,
    f.title as file_title,
    p.full_name as user_name,
    f.id as file_id
  FROM public.files f
  JOIN public.profiles p ON f.uploaded_by = p.id
  WHERE f.is_active = true
  ORDER BY f.created_at DESC
  LIMIT 5
)
ORDER BY activity_time DESC
LIMIT 15;

-- إنشاء view لإحصائيات المواد
CREATE OR REPLACE VIEW subject_stats AS
SELECT 
  subject,
  COUNT(*) as file_count,
  SUM(download_count) as total_downloads,
  AVG(average_rating) as avg_rating
FROM public.files
WHERE is_active = true AND subject IS NOT NULL
GROUP BY subject
ORDER BY file_count DESC;

-- إنشاء view للملفات مع تفاصيل كاملة
CREATE OR REPLACE VIEW files_detailed AS
SELECT 
  f.*,
  p.full_name as uploader_name,
  p.email as uploader_email,
  (
    SELECT COUNT(*) 
    FROM public.favorites fav 
    WHERE fav.file_id = f.id
  ) as favorite_count,
  (
    SELECT AVG(rating)::DECIMAL(3,2)
    FROM public.ratings r 
    WHERE r.file_id = f.id
  ) as calculated_rating
FROM public.files f
LEFT JOIN public.profiles p ON f.uploaded_by = p.id;

-- إنشاء view لإحصائيات المستخدمين
CREATE OR REPLACE VIEW user_stats AS
SELECT 
  p.id,
  p.full_name,
  p.email,
  p.role,
  (
    SELECT COUNT(*) 
    FROM public.files f 
    WHERE f.uploaded_by = p.id AND f.is_active = true
  ) as uploaded_files,
  (
    SELECT COUNT(*) 
    FROM public.downloads d 
    WHERE d.user_id = p.id
  ) as total_downloads,
  (
    SELECT COUNT(*) 
    FROM public.ratings r 
    WHERE r.user_id = p.id
  ) as total_ratings,
  (
    SELECT COUNT(*) 
    FROM public.favorites fav 
    WHERE fav.user_id = p.id
  ) as total_favorites,
  p.created_at as join_date
FROM public.profiles p;

-- إنشاء view للملفات الأعلى تقييماً
CREATE OR REPLACE VIEW top_rated_files AS
SELECT 
  f.*,
  p.full_name as uploader_name
FROM public.files f
LEFT JOIN public.profiles p ON f.uploaded_by = p.id
WHERE f.is_active = true 
  AND f.rating_count >= 3  -- على الأقل 3 تقييمات
ORDER BY f.average_rating DESC, f.rating_count DESC
LIMIT 10;

-- إنشاء view للملفات حسب الفصل الدراسي
CREATE OR REPLACE VIEW files_by_semester AS
SELECT 
  semester,
  COUNT(*) as file_count,
  SUM(download_count) as total_downloads,
  AVG(average_rating) as avg_rating
FROM public.files
WHERE is_active = true AND semester IS NOT NULL
GROUP BY semester
ORDER BY 
  CASE semester
    WHEN 'first' THEN 1
    WHEN 'second' THEN 2
    WHEN 'summer' THEN 3
    ELSE 4
  END;

-- إنشاء view للتحميلات اليومية (آخر 30 يوم)
CREATE OR REPLACE VIEW daily_downloads AS
SELECT 
  DATE(downloaded_at) as download_date,
  COUNT(*) as download_count
FROM public.downloads
WHERE downloaded_at >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY DATE(downloaded_at)
ORDER BY download_date DESC;

-- إنشاء view للملفات المفضلة الأكثر شعبية
CREATE OR REPLACE VIEW most_favorited_files AS
SELECT
  f.*,
  p.full_name as uploader_name,
  COUNT(fav.id) as favorite_count
FROM public.files f
LEFT JOIN public.profiles p ON f.uploaded_by = p.id
LEFT JOIN public.favorites fav ON f.id = fav.file_id
WHERE f.is_active = true
GROUP BY f.id, p.full_name
HAVING COUNT(fav.id) > 0
ORDER BY favorite_count DESC, f.average_rating DESC
LIMIT 10;

-- إنشاء view للتعليقات مع بيانات المستخدم
CREATE OR REPLACE VIEW comments_with_user AS
SELECT
  c.*,
  p.full_name as user_name,
  p.email as user_email,
  (
    SELECT COUNT(*)
    FROM public.comment_likes cl
    WHERE cl.comment_id = c.id
  ) as likes_count,
  (
    SELECT EXISTS (
      SELECT 1 FROM public.comment_likes cl
      WHERE cl.comment_id = c.id AND cl.user_id = auth.uid()
    )
  ) as user_liked
FROM public.comments c
LEFT JOIN public.profiles p ON c.user_id = p.id
ORDER BY c.created_at DESC;

-- إنشاء view للمشاركة مع بيانات الملف
CREATE OR REPLACE VIEW file_shares_with_file AS
SELECT
  fs.*,
  f.title as file_title,
  f.file_name,
  f.file_type,
  f.file_size,
  f.description as file_description,
  p.full_name as creator_name
FROM public.file_shares fs
LEFT JOIN public.files f ON fs.file_id = f.id
LEFT JOIN public.profiles p ON fs.created_by = p.id
WHERE fs.is_active = true
ORDER BY fs.created_at DESC;
