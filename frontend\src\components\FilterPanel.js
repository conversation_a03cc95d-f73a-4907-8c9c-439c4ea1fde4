import React, { useState } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  FiFilter, 
  FiX, 
  FiChevronDown, 
  FiCalendar,
  FiFile,
  FiStar,
  FiUser
} from 'react-icons/fi';
import { <PERSON><PERSON>, Card } from './ui';

const FilterContainer = styled(Card)`
  margin-bottom: ${({ theme }) => theme.spacing[6]};
`;

const FilterHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: ${({ theme }) => theme.spacing[4]};
`;

const FilterTitle = styled.h3`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  color: ${({ theme }) => theme.colors.gray[900]};
  margin: 0;
`;

const FilterGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: ${({ theme }) => theme.spacing[4]};
  margin-bottom: ${({ theme }) => theme.spacing[4]};
`;

const FilterGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing[2]};
`;

const FilterLabel = styled.label`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  color: ${({ theme }) => theme.colors.gray[700]};
`;

const FilterSelect = styled.select`
  width: 100%;
  padding: ${({ theme }) => theme.spacing[3]} ${({ theme }) => theme.spacing[4]};
  border: 2px solid ${({ theme }) => theme.colors.gray[200]};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  background: ${({ theme }) => theme.colors.white};
  font-family: ${({ theme }) => theme.fonts.primary};
  font-size: ${({ theme }) => theme.fontSizes.base};
  color: ${({ theme }) => theme.colors.gray[900]};
  transition: all ${({ theme }) => theme.transitions.fast};
  
  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary[500]};
    box-shadow: 0 0 0 3px ${({ theme }) => theme.colors.primary[100]};
  }
`;

const FilterCheckboxGroup = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: ${({ theme }) => theme.spacing[2]};
`;

const FilterCheckbox = styled.label`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
  padding: ${({ theme }) => theme.spacing[2]} ${({ theme }) => theme.spacing[3]};
  border: 2px solid ${({ theme }) => theme.colors.gray[200]};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  cursor: pointer;
  transition: all ${({ theme }) => theme.transitions.fast};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  
  &:hover {
    border-color: ${({ theme }) => theme.colors.primary[300]};
    background: ${({ theme }) => theme.colors.primary[25]};
  }
  
  ${({ checked, theme }) =>
    checked &&
    `
    border-color: ${theme.colors.primary[500]};
    background: ${theme.colors.primary[50]};
    color: ${theme.colors.primary[700]};
  `}
  
  input {
    display: none;
  }
`;

const FilterRange = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
`;

const RangeInput = styled.input`
  flex: 1;
  padding: ${({ theme }) => theme.spacing[2]} ${({ theme }) => theme.spacing[3]};
  border: 2px solid ${({ theme }) => theme.colors.gray[200]};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  font-family: ${({ theme }) => theme.fonts.primary};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  
  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary[500]};
  }
`;

const FilterActions = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing[3]};
  padding-top: ${({ theme }) => theme.spacing[4]};
  border-top: 1px solid ${({ theme }) => theme.colors.gray[200]};
`;

const ActiveFilters = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: ${({ theme }) => theme.spacing[2]};
  margin-bottom: ${({ theme }) => theme.spacing[4]};
`;

const FilterTag = styled(motion.div)`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[1]};
  padding: ${({ theme }) => theme.spacing[1]} ${({ theme }) => theme.spacing[2]};
  background: ${({ theme }) => theme.colors.primary[100]};
  color: ${({ theme }) => theme.colors.primary[700]};
  border-radius: ${({ theme }) => theme.borderRadius.base};
  font-size: ${({ theme }) => theme.fontSizes.sm};
`;

const RemoveFilterButton = styled.button`
  background: none;
  border: none;
  color: ${({ theme }) => theme.colors.primary[600]};
  cursor: pointer;
  padding: 2px;
  border-radius: 50%;
  
  &:hover {
    background: ${({ theme }) => theme.colors.primary[200]};
  }
`;

const FilterPanel = ({ filters, onChange, onClear }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const fileTypes = [
    { value: 'pdf', label: 'PDF' },
    { value: 'doc', label: 'Word' },
    { value: 'ppt', label: 'PowerPoint' },
    { value: 'mp4', label: 'فيديو' },
    { value: 'mp3', label: 'صوت' },
    { value: 'jpg', label: 'صورة' },
  ];

  const subjects = [
    { value: 'math', label: 'الرياضيات' },
    { value: 'science', label: 'العلوم' },
    { value: 'arabic', label: 'اللغة العربية' },
    { value: 'english', label: 'اللغة الإنجليزية' },
    { value: 'history', label: 'التاريخ' },
    { value: 'geography', label: 'الجغرافيا' },
  ];

  const semesters = [
    { value: 'first', label: 'الفصل الأول' },
    { value: 'second', label: 'الفصل الثاني' },
    { value: 'summer', label: 'الفصل الصيفي' },
  ];

  const handleFilterChange = (key, value) => {
    onChange?.({
      ...filters,
      [key]: value
    });
  };

  const handleCheckboxChange = (key, value, checked) => {
    const currentValues = filters[key] || [];
    const newValues = checked
      ? [...currentValues, value]
      : currentValues.filter(v => v !== value);
    
    handleFilterChange(key, newValues);
  };

  const removeFilter = (key, value = null) => {
    if (value === null) {
      const newFilters = { ...filters };
      delete newFilters[key];
      onChange?.(newFilters);
    } else {
      const currentValues = filters[key] || [];
      const newValues = currentValues.filter(v => v !== value);
      handleFilterChange(key, newValues);
    }
  };

  const clearAllFilters = () => {
    onChange?.({});
    onClear?.();
  };

  const getActiveFilters = () => {
    const active = [];
    
    Object.entries(filters).forEach(([key, value]) => {
      if (Array.isArray(value) && value.length > 0) {
        value.forEach(v => {
          let label = v;
          if (key === 'fileTypes') {
            label = fileTypes.find(ft => ft.value === v)?.label || v;
          } else if (key === 'subjects') {
            label = subjects.find(s => s.value === v)?.label || v;
          } else if (key === 'semesters') {
            label = semesters.find(s => s.value === v)?.label || v;
          }
          active.push({ key, value: v, label });
        });
      } else if (value && !Array.isArray(value)) {
        active.push({ key, value, label: value });
      }
    });
    
    return active;
  };

  const activeFilters = getActiveFilters();

  return (
    <FilterContainer padding="lg">
      <FilterHeader>
        <FilterTitle>
          <FiFilter size={20} />
          تصفية النتائج
        </FilterTitle>
        <Button
          variant="ghost"
          size="sm"
          leftIcon={<FiChevronDown size={16} style={{ 
            transform: isExpanded ? 'rotate(180deg)' : 'rotate(0deg)',
            transition: 'transform 0.2s ease'
          }} />}
          onClick={() => setIsExpanded(!isExpanded)}
        >
          {isExpanded ? 'إخفاء' : 'إظهار'}
        </Button>
      </FilterHeader>

      {activeFilters.length > 0 && (
        <ActiveFilters>
          {activeFilters.map((filter, index) => (
            <FilterTag
              key={`${filter.key}-${filter.value}`}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
            >
              {filter.label}
              <RemoveFilterButton
                onClick={() => removeFilter(filter.key, filter.value)}
              >
                <FiX size={12} />
              </RemoveFilterButton>
            </FilterTag>
          ))}
        </ActiveFilters>
      )}

      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            <FilterGrid>
              <FilterGroup>
                <FilterLabel>
                  <FiFile size={16} />
                  نوع الملف
                </FilterLabel>
                <FilterCheckboxGroup>
                  {fileTypes.map(type => (
                    <FilterCheckbox
                      key={type.value}
                      checked={filters.fileTypes?.includes(type.value)}
                    >
                      <input
                        type="checkbox"
                        checked={filters.fileTypes?.includes(type.value) || false}
                        onChange={(e) => handleCheckboxChange('fileTypes', type.value, e.target.checked)}
                      />
                      {type.label}
                    </FilterCheckbox>
                  ))}
                </FilterCheckboxGroup>
              </FilterGroup>

              <FilterGroup>
                <FilterLabel>
                  <FiUser size={16} />
                  المادة
                </FilterLabel>
                <FilterSelect
                  value={filters.subject || ''}
                  onChange={(e) => handleFilterChange('subject', e.target.value)}
                >
                  <option value="">جميع المواد</option>
                  {subjects.map(subject => (
                    <option key={subject.value} value={subject.value}>
                      {subject.label}
                    </option>
                  ))}
                </FilterSelect>
              </FilterGroup>

              <FilterGroup>
                <FilterLabel>
                  <FiCalendar size={16} />
                  الفصل الدراسي
                </FilterLabel>
                <FilterSelect
                  value={filters.semester || ''}
                  onChange={(e) => handleFilterChange('semester', e.target.value)}
                >
                  <option value="">جميع الفصول</option>
                  {semesters.map(semester => (
                    <option key={semester.value} value={semester.value}>
                      {semester.label}
                    </option>
                  ))}
                </FilterSelect>
              </FilterGroup>

              <FilterGroup>
                <FilterLabel>
                  <FiStar size={16} />
                  التقييم
                </FilterLabel>
                <FilterRange>
                  <RangeInput
                    type="number"
                    min="0"
                    max="5"
                    step="0.1"
                    placeholder="من"
                    value={filters.minRating || ''}
                    onChange={(e) => handleFilterChange('minRating', e.target.value)}
                  />
                  <span>إلى</span>
                  <RangeInput
                    type="number"
                    min="0"
                    max="5"
                    step="0.1"
                    placeholder="إلى"
                    value={filters.maxRating || ''}
                    onChange={(e) => handleFilterChange('maxRating', e.target.value)}
                  />
                </FilterRange>
              </FilterGroup>
            </FilterGrid>

            <FilterActions>
              <Button
                variant="primary"
                size="sm"
                onClick={() => setIsExpanded(false)}
              >
                تطبيق الفلاتر
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={clearAllFilters}
                leftIcon={<FiX size={16} />}
              >
                مسح الكل
              </Button>
            </FilterActions>
          </motion.div>
        )}
      </AnimatePresence>
    </FilterContainer>
  );
};

export default FilterPanel;
