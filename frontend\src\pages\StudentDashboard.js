import React, { useState } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { useQuery } from 'react-query';
import {
  FiDownload,
  FiTrendingUp,
  FiClock,
  FiStar
} from 'react-icons/fi';
import FileCard from '../components/FileCard';
import SearchBar from '../components/SearchBar';
import FilterPanel from '../components/FilterPanel';
import { Card } from '../components/ui';
import { files } from '../services/api';
import { toast } from 'react-toastify';

const Dashboard = styled.div`
  min-height: 100vh;
  background: ${({ theme }) => theme.colors.gray[50]};
  padding: ${({ theme }) => theme.spacing[6]} 0;
`;

const Container = styled.div`
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 ${({ theme }) => theme.spacing[4]};
`;

const Header = styled(motion.div)`
  margin-bottom: ${({ theme }) => theme.spacing[8]};
`;

const WelcomeSection = styled.div`
  text-align: center;
  margin-bottom: ${({ theme }) => theme.spacing[8]};
`;

const WelcomeTitle = styled.h1`
  font-size: ${({ theme }) => theme.fontSizes['4xl']};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  color: ${({ theme }) => theme.colors.gray[900]};
  margin-bottom: ${({ theme }) => theme.spacing[4]};
  background: linear-gradient(135deg, ${({ theme }) => theme.colors.primary[600]}, ${({ theme }) => theme.colors.secondary[600]});
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
`;

const WelcomeSubtitle = styled.p`
  font-size: ${({ theme }) => theme.fontSizes.xl};
  color: ${({ theme }) => theme.colors.gray[600]};
  max-width: 600px;
  margin: 0 auto;
  line-height: ${({ theme }) => theme.lineHeights.relaxed};
`;

const SearchSection = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[4]};
  margin-bottom: ${({ theme }) => theme.spacing[8]};
`;

const StatsSection = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: ${({ theme }) => theme.spacing[6]};
  margin-bottom: ${({ theme }) => theme.spacing[8]};
`;

const StatCard = styled(Card)`
  text-align: center;
  transition: all ${({ theme }) => theme.transitions.normal};

  &:hover {
    transform: translateY(-4px);
    box-shadow: ${({ theme }) => theme.shadows.xl};
  }
`;

const StatIcon = styled.div`
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto ${({ theme }) => theme.spacing[4]};
  font-size: ${({ theme }) => theme.fontSizes['2xl']};
  color: white;

  ${({ variant, theme }) => {
    switch (variant) {
      case 'primary':
        return `background: linear-gradient(135deg, ${theme.colors.primary[500]}, ${theme.colors.primary[600]});`;
      case 'success':
        return `background: linear-gradient(135deg, ${theme.colors.success[500]}, ${theme.colors.success[600]});`;
      case 'warning':
        return `background: linear-gradient(135deg, ${theme.colors.warning[500]}, ${theme.colors.warning[600]});`;
      case 'secondary':
        return `background: linear-gradient(135deg, ${theme.colors.secondary[500]}, ${theme.colors.secondary[600]});`;
      default:
        return `background: linear-gradient(135deg, ${theme.colors.gray[500]}, ${theme.colors.gray[600]});`;
    }
  }}
`;

const StatNumber = styled.div`
  font-size: ${({ theme }) => theme.fontSizes['3xl']};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  color: ${({ theme }) => theme.colors.gray[900]};
  margin-bottom: ${({ theme }) => theme.spacing[2]};
`;

const StatLabel = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.base};
  color: ${({ theme }) => theme.colors.gray[600]};
`;

const SectionTitle = styled.h2`
  font-size: ${({ theme }) => theme.fontSizes['2xl']};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  color: ${({ theme }) => theme.colors.gray[900]};
  margin-bottom: ${({ theme }) => theme.spacing[6]};
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[3]};
`;

const FilesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: ${({ theme }) => theme.spacing[6]};
  margin-top: ${({ theme }) => theme.spacing[6]};
`;

const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
`;

const LoadingSpinner = styled(motion.div)`
  width: 40px;
  height: 40px;
  border: 3px solid ${({ theme }) => theme.colors.gray[200]};
  border-top: 3px solid ${({ theme }) => theme.colors.primary[500]};
  border-radius: 50%;
  animation: spin 1s linear infinite;

  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }
`;

const EmptyState = styled.div`
  text-align: center;
  padding: ${({ theme }) => theme.spacing[12]} ${({ theme }) => theme.spacing[4]};
  color: ${({ theme }) => theme.colors.gray[500]};
`;

function StudentDashboard() {
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({});
  const [showFilters, setShowFilters] = useState(false);

  const { data: filesData, isLoading, error } = useQuery(
    ['files', searchTerm, filters],
    () => files.getAll({ search: searchTerm, ...filters }),
    {
      select: (data) => data.data,
      onError: (error) => {
        toast.error('فشل في تحميل الملفات');
      }
    }
  );

  const handleFileView = (file) => {
    // Navigate to file viewer
    window.open(`/file/${file._id}`, '_blank');
  };

  const handleFileDownload = (file) => {
    toast.success(`تم تحميل ${file.title}`);
  };

  const handleFileFavorite = (file, isFavorite) => {
    toast.success(isFavorite ? 'تم إضافة الملف للمفضلة' : 'تم إزالة الملف من المفضلة');
  };

  const stats = [
    {
      icon: <FiDownload />,
      number: '1,234',
      label: 'إجمالي التحميلات',
      variant: 'primary'
    },
    {
      icon: <FiStar />,
      number: '567',
      label: 'الملفات المفضلة',
      variant: 'warning'
    },
    {
      icon: <FiClock />,
      number: '89',
      label: 'الملفات الحديثة',
      variant: 'success'
    },
    {
      icon: <FiTrendingUp />,
      number: '45',
      label: 'الأكثر شعبية',
      variant: 'secondary'
    }
  ];

  return (
    <Dashboard>
      <Container>
        <Header
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <WelcomeSection>
            <WelcomeTitle>مكتبة الملفات التعليمية</WelcomeTitle>
            <WelcomeSubtitle>
              اكتشف مجموعة واسعة من الملفات التعليمية المتنوعة والمفيدة لدعم رحلتك التعليمية
            </WelcomeSubtitle>
          </WelcomeSection>

          <SearchSection>
            <SearchBar
              value={searchTerm}
              onChange={setSearchTerm}
              placeholder="ابحث عن الملفات، المواد، أو المواضيع..."
              showFilter={true}
              onFilterClick={() => setShowFilters(!showFilters)}
            />
          </SearchSection>
        </Header>

        <StatsSection>
          {stats.map((stat, index) => (
            <StatCard
              key={index}
              as={motion.div}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              padding="lg"
            >
              <StatIcon variant={stat.variant}>
                {stat.icon}
              </StatIcon>
              <StatNumber>{stat.number}</StatNumber>
              <StatLabel>{stat.label}</StatLabel>
            </StatCard>
          ))}
        </StatsSection>

        {showFilters && (
          <FilterPanel
            filters={filters}
            onChange={setFilters}
            onClear={() => setFilters({})}
          />
        )}

        <div>
          <SectionTitle>
            <FiTrendingUp />
            الملفات المتاحة
          </SectionTitle>

          {isLoading ? (
            <LoadingContainer>
              <LoadingSpinner
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
              />
            </LoadingContainer>
          ) : error ? (
            <EmptyState>
              <p>حدث خطأ في تحميل الملفات</p>
            </EmptyState>
          ) : filesData?.length === 0 ? (
            <EmptyState>
              <p>لا توجد ملفات متاحة</p>
            </EmptyState>
          ) : (
            <FilesGrid>
              {filesData?.map((file, index) => (
                <motion.div
                  key={file._id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <FileCard
                    file={file}
                    onView={handleFileView}
                    onDownload={handleFileDownload}
                    onFavorite={handleFileFavorite}
                  />
                </motion.div>
              ))}
            </FilesGrid>
          )}
        </div>
      </Container>
    </Dashboard>
  );
}

export default StudentDashboard;