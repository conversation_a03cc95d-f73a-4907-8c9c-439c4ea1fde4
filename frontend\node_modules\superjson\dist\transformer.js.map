{"version": 3, "file": "transformer.js", "sourceRoot": "", "sources": ["../src/transformer.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2BAec;AACd,+BAAiC;AA2BjC,SAAS,oBAAoB,CAC3B,YAAsD,EACtD,UAAa,EACb,SAA4C,EAC5C,WAA8C;IAE9C,OAAO;QACL,YAAY,cAAA;QACZ,UAAU,YAAA;QACV,SAAS,WAAA;QACT,WAAW,aAAA;KACZ,CAAC;AACJ,CAAC;AAED,IAAM,WAAW,GAAG;IAClB,oBAAoB,CAClB,gBAAW,EACX,WAAW,EACX,cAAM,OAAA,IAAI,EAAJ,CAAI,EACV,cAAM,OAAA,SAAS,EAAT,CAAS,CAChB;IACD,oBAAoB,CAClB,aAAQ,EACR,QAAQ,EACR,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,QAAQ,EAAE,EAAZ,CAAY,EACjB,UAAA,CAAC;QACC,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;YACjC,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC;SAClB;QAED,OAAO,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC;QAE/C,OAAO,CAAQ,CAAC;IAClB,CAAC,CACF;IACD,oBAAoB,CAClB,WAAM,EACN,MAAM,EACN,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,WAAW,EAAE,EAAf,CAAe,EACpB,UAAA,CAAC,IAAI,OAAA,IAAI,IAAI,CAAC,CAAC,CAAC,EAAX,CAAW,CACjB;IAED,oBAAoB,CAClB,YAAO,EACP,OAAO,EACP,UAAC,CAAC,EAAE,SAAS;QACX,IAAM,SAAS,GAAQ;YACrB,IAAI,EAAE,CAAC,CAAC,IAAI;YACZ,OAAO,EAAE,CAAC,CAAC,OAAO;SACnB,CAAC;QAEF,SAAS,CAAC,iBAAiB,CAAC,OAAO,CAAC,UAAA,IAAI;YACtC,SAAS,CAAC,IAAI,CAAC,GAAI,CAAS,CAAC,IAAI,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,OAAO,SAAS,CAAC;IACnB,CAAC,EACD,UAAC,CAAC,EAAE,SAAS;QACX,IAAM,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QAC/B,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;QAChB,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;QAElB,SAAS,CAAC,iBAAiB,CAAC,OAAO,CAAC,UAAA,IAAI;YACrC,CAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,OAAO,CAAC,CAAC;IACX,CAAC,CACF;IAED,oBAAoB,CAClB,aAAQ,EACR,QAAQ,EACR,UAAA,CAAC,IAAI,OAAA,EAAE,GAAG,CAAC,EAAN,CAAM,EACX,UAAA,KAAK;QACH,IAAM,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC;QACpD,IAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QACtD,OAAO,IAAI,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IACjC,CAAC,CACF;IAED,oBAAoB,CAClB,UAAK,EACL,KAAK;IACL,4BAA4B;IAC5B,8CAA8C;IAC9C,UAAA,CAAC,IAAI,gCAAI,CAAC,CAAC,MAAM,EAAE,IAAd,CAAe,EACpB,UAAA,CAAC,IAAI,OAAA,IAAI,GAAG,CAAC,CAAC,CAAC,EAAV,CAAU,CAChB;IACD,oBAAoB,CAClB,UAAK,EACL,KAAK,EACL,UAAA,CAAC,IAAI,gCAAI,CAAC,CAAC,OAAO,EAAE,IAAf,CAAgB,EACrB,UAAA,CAAC,IAAI,OAAA,IAAI,GAAG,CAAC,CAAC,CAAC,EAAV,CAAU,CAChB;IAED,oBAAoB,CAClB,UAAC,CAAC,IAAkB,OAAA,eAAU,CAAC,CAAC,CAAC,IAAI,eAAU,CAAC,CAAC,CAAC,EAA9B,CAA8B,EAClD,QAAQ,EACR,UAAA,CAAC;QACC,IAAI,eAAU,CAAC,CAAC,CAAC,EAAE;YACjB,OAAO,KAAK,CAAC;SACd;QAED,IAAI,CAAC,GAAG,CAAC,EAAE;YACT,OAAO,UAAU,CAAC;SACnB;aAAM;YACL,OAAO,WAAW,CAAC;SACpB;IACH,CAAC,EACD,MAAM,CACP;IAED,oBAAoB,CAClB,UAAC,CAAC,IAAkB,OAAA,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,EAA9B,CAA8B,EAClD,QAAQ,EACR;QACE,OAAO,IAAI,CAAC;IACd,CAAC,EACD,MAAM,CACP;IAED,oBAAoB,CAClB,UAAK,EACL,KAAK,EACL,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,QAAQ,EAAE,EAAZ,CAAY,EACjB,UAAA,CAAC,IAAI,OAAA,IAAI,GAAG,CAAC,CAAC,CAAC,EAAV,CAAU,CAChB;CACF,CAAC;AAEF,SAAS,uBAAuB,CAC9B,YAAsD,EACtD,UAA6C,EAC7C,SAA4C,EAC5C,WAAoD;IAEpD,OAAO;QACL,YAAY,cAAA;QACZ,UAAU,YAAA;QACV,SAAS,WAAA;QACT,WAAW,aAAA;KACZ,CAAC;AACJ,CAAC;AAED,IAAM,UAAU,GAAG,uBAAuB,CACxC,UAAC,CAAC,EAAE,SAAS;IACX,IAAI,aAAQ,CAAC,CAAC,CAAC,EAAE;QACf,IAAM,YAAY,GAAG,CAAC,CAAC,SAAS,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;QACjE,OAAO,YAAY,CAAC;KACrB;IACD,OAAO,KAAK,CAAC;AACf,CAAC,EACD,UAAC,CAAC,EAAE,SAAS;IACX,IAAM,UAAU,GAAG,SAAS,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;IAC7D,OAAO,CAAC,QAAQ,EAAE,UAAW,CAAC,CAAC;AACjC,CAAC,EACD,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,WAAW,EAAb,CAAa,EAClB,UAAC,CAAC,EAAE,CAAC,EAAE,SAAS;IACd,IAAM,KAAK,GAAG,SAAS,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtD,IAAI,CAAC,KAAK,EAAE;QACV,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;KACzD;IACD,OAAO,KAAK,CAAC;AACf,CAAC,CACF,CAAC;AAEF,IAAM,iBAAiB,GAAG;IACxB,SAAS;IACT,UAAU;IACV,UAAU;IACV,WAAW;IACX,UAAU;IACV,WAAW;IACX,YAAY;IACZ,YAAY;IACZ,iBAAiB;CAClB,CAAC,MAAM,CAAwC,UAAC,GAAG,EAAE,IAAI;IACxD,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;IACtB,OAAO,GAAG,CAAC;AACb,CAAC,EAAE,EAAE,CAAC,CAAC;AAEP,IAAM,cAAc,GAAG,uBAAuB,CAC5C,iBAAY,EACZ,UAAA,CAAC,IAAI,OAAA,CAAC,aAAa,EAAE,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,EAAnC,CAAmC,EACxC,UAAA,CAAC,IAAI,gCAAI,CAAC,IAAL,CAAM,EACX,UAAC,CAAC,EAAE,CAAC;IACH,IAAM,IAAI,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAErC,IAAI,CAAC,IAAI,EAAE;QACT,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;KAC9D;IAED,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;AACrB,CAAC,CACF,CAAC;AAEF,SAAgB,2BAA2B,CACzC,cAAmB,EACnB,SAAoB;IAEpB,IAAI,cAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,WAAW,EAAE;QAC/B,IAAM,YAAY,GAAG,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,aAAa,CAC1D,cAAc,CAAC,WAAW,CAC3B,CAAC;QACF,OAAO,YAAY,CAAC;KACrB;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAXD,kEAWC;AAED,IAAM,SAAS,GAAG,uBAAuB,CACvC,2BAA2B,EAC3B,UAAC,KAAK,EAAE,SAAS;IACf,IAAM,UAAU,GAAG,SAAS,CAAC,aAAa,CAAC,aAAa,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;IAC5E,OAAO,CAAC,OAAO,EAAE,UAAW,CAAC,CAAC;AAChC,CAAC,EACD,UAAC,KAAK,EAAE,SAAS;IACf,IAAM,YAAY,GAAG,SAAS,CAAC,aAAa,CAAC,eAAe,CAC1D,KAAK,CAAC,WAAW,CAClB,CAAC;IACF,IAAI,CAAC,YAAY,EAAE;QACjB,oBAAY,KAAK,EAAG;KACrB;IAED,IAAM,MAAM,GAAQ,EAAE,CAAC;IACvB,YAAY,CAAC,OAAO,CAAC,UAAA,IAAI;QACvB,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC;IAC7B,CAAC,CAAC,CAAC;IACH,OAAO,MAAM,CAAC;AAChB,CAAC,EACD,UAAC,CAAC,EAAE,CAAC,EAAE,SAAS;IACd,IAAM,KAAK,GAAG,SAAS,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAErD,IAAI,CAAC,KAAK,EAAE;QACV,MAAM,IAAI,KAAK,CACb,qHAAqH,CACtH,CAAC;KACH;IAED,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;AAC1D,CAAC,CACF,CAAC;AAEF,IAAM,UAAU,GAAG,uBAAuB,CACxC,UAAC,KAAK,EAAE,SAAS;IACf,OAAO,CAAC,CAAC,SAAS,CAAC,yBAAyB,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;AACrE,CAAC,EACD,UAAC,KAAK,EAAE,SAAS;IACf,IAAM,WAAW,GAAG,SAAS,CAAC,yBAAyB,CAAC,cAAc,CACpE,KAAK,CACL,CAAC;IACH,OAAO,CAAC,QAAQ,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC;AACtC,CAAC,EACD,UAAC,KAAK,EAAE,SAAS;IACf,IAAM,WAAW,GAAG,SAAS,CAAC,yBAAyB,CAAC,cAAc,CACpE,KAAK,CACL,CAAC;IACH,OAAO,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;AACtC,CAAC,EACD,UAAC,CAAC,EAAE,CAAC,EAAE,SAAS;IACd,IAAM,WAAW,GAAG,SAAS,CAAC,yBAAyB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzE,IAAI,CAAC,WAAW,EAAE;QAChB,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;KAC/D;IACD,OAAO,WAAW,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;AACpC,CAAC,CACF,CAAC;AAEF,IAAM,cAAc,GAAG,CAAC,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,cAAc,CAAC,CAAC;AAEpE,IAAM,cAAc,GAAG,UAC5B,KAAU,EACV,SAAoB;IAEpB,IAAM,uBAAuB,GAAG,cAAO,CAAC,cAAc,EAAE,UAAA,IAAI;QAC1D,OAAA,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,SAAS,CAAC;IAAnC,CAAmC,CACpC,CAAC;IACF,IAAI,uBAAuB,EAAE;QAC3B,OAAO;YACL,KAAK,EAAE,uBAAuB,CAAC,SAAS,CAAC,KAAc,EAAE,SAAS,CAAC;YACnE,IAAI,EAAE,uBAAuB,CAAC,UAAU,CAAC,KAAK,EAAE,SAAS,CAAC;SAC3D,CAAC;KACH;IAED,IAAM,oBAAoB,GAAG,cAAO,CAAC,WAAW,EAAE,UAAA,IAAI;QACpD,OAAA,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,SAAS,CAAC;IAAnC,CAAmC,CACpC,CAAC;IAEF,IAAI,oBAAoB,EAAE;QACxB,OAAO;YACL,KAAK,EAAE,oBAAoB,CAAC,SAAS,CAAC,KAAc,EAAE,SAAS,CAAC;YAChE,IAAI,EAAE,oBAAoB,CAAC,UAAU;SACtC,CAAC;KACH;IAED,OAAO,SAAS,CAAC;AACnB,CAAC,CAAC;AA1BW,QAAA,cAAc,kBA0BzB;AAEF,IAAM,uBAAuB,GAA0C,EAAE,CAAC;AAC1E,WAAW,CAAC,OAAO,CAAC,UAAA,IAAI;IACtB,uBAAuB,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC;AAClD,CAAC,CAAC,CAAC;AAEI,IAAM,gBAAgB,GAAG,UAC9B,IAAS,EACT,IAAoB,EACpB,SAAoB;IAEpB,IAAI,YAAO,CAAC,IAAI,CAAC,EAAE;QACjB,QAAQ,IAAI,CAAC,CAAC,CAAC,EAAE;YACf,KAAK,QAAQ;gBACX,OAAO,UAAU,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;YACvD,KAAK,OAAO;gBACV,OAAO,SAAS,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;YACtD,KAAK,QAAQ;gBACX,OAAO,UAAU,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;YACvD,KAAK,aAAa;gBAChB,OAAO,cAAc,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;YAC3D;gBACE,MAAM,IAAI,KAAK,CAAC,0BAA0B,GAAG,IAAI,CAAC,CAAC;SACtD;KACF;SAAM;QACL,IAAM,cAAc,GAAG,uBAAuB,CAAC,IAAI,CAAC,CAAC;QACrD,IAAI,CAAC,cAAc,EAAE;YACnB,MAAM,IAAI,KAAK,CAAC,0BAA0B,GAAG,IAAI,CAAC,CAAC;SACpD;QAED,OAAO,cAAc,CAAC,WAAW,CAAC,IAAa,EAAE,SAAS,CAAC,CAAC;KAC7D;AACH,CAAC,CAAC;AA1BW,QAAA,gBAAgB,oBA0B3B"}