{"ast": null, "code": "export default function map(values, mapper) {\n  if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n  if (typeof mapper !== \"function\") throw new TypeError(\"mapper is not a function\");\n  return Array.from(values, (value, index) => mapper(value, index, values));\n}", "map": {"version": 3, "names": ["map", "values", "mapper", "Symbol", "iterator", "TypeError", "Array", "from", "value", "index"], "sources": ["D:/menasa/frontend/node_modules/d3-array/src/map.js"], "sourcesContent": ["export default function map(values, mapper) {\n  if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n  if (typeof mapper !== \"function\") throw new TypeError(\"mapper is not a function\");\n  return Array.from(values, (value, index) => mapper(value, index, values));\n}\n"], "mappings": "AAAA,eAAe,SAASA,GAAGA,CAACC,MAAM,EAAEC,MAAM,EAAE;EAC1C,IAAI,OAAOD,MAAM,CAACE,MAAM,CAACC,QAAQ,CAAC,KAAK,UAAU,EAAE,MAAM,IAAIC,SAAS,CAAC,wBAAwB,CAAC;EAChG,IAAI,OAAOH,MAAM,KAAK,UAAU,EAAE,MAAM,IAAIG,SAAS,CAAC,0BAA0B,CAAC;EACjF,OAAOC,KAAK,CAACC,IAAI,CAACN,MAAM,EAAE,CAACO,KAAK,EAAEC,KAAK,KAAKP,MAAM,CAACM,KAAK,EAAEC,KAAK,EAAER,MAAM,CAAC,CAAC;AAC3E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}