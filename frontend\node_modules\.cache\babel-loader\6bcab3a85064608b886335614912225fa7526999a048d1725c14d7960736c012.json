{"ast": null, "code": "var _jsxFileName = \"D:\\\\menasa\\\\frontend\\\\src\\\\pages\\\\StudentDashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport styled from 'styled-components';\nimport { motion } from 'framer-motion';\nimport { useQuery, useQueryClient } from '@tanstack/react-query';\nimport { FiDownload, FiTrendingUp, FiClock, FiStar } from 'react-icons/fi';\nimport FileCard from '../components/FileCard';\nimport AdvancedSearch from '../components/search/AdvancedSearch';\nimport FilterPanel from '../components/FilterPanel';\nimport { Card } from '../components/ui';\nimport { files, admin } from '../services/api';\nimport { supabase, subscribeToFiles } from '../lib/supabase';\nimport { useAuth } from '../contexts/AuthContext';\nimport { toast } from 'react-toastify';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = styled.div`\n  min-height: 100vh;\n  background: ${({\n  theme\n}) => theme.colors.gray[50]};\n  padding: ${({\n  theme\n}) => theme.spacing[6]} 0;\n`;\n_c = Dashboard;\nconst Container = styled.div`\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 0 ${({\n  theme\n}) => theme.spacing[4]};\n`;\n_c2 = Container;\nconst Header = styled(motion.div)`\n  margin-bottom: ${({\n  theme\n}) => theme.spacing[8]};\n`;\n_c3 = Header;\nconst WelcomeSection = styled.div`\n  text-align: center;\n  margin-bottom: ${({\n  theme\n}) => theme.spacing[8]};\n`;\n_c4 = WelcomeSection;\nconst WelcomeTitle = styled.h1`\n  font-size: ${({\n  theme\n}) => theme.fontSizes['4xl']};\n  font-weight: ${({\n  theme\n}) => theme.fontWeights.bold};\n  color: ${({\n  theme\n}) => theme.colors.gray[900]};\n  margin-bottom: ${({\n  theme\n}) => theme.spacing[4]};\n  background: linear-gradient(135deg, ${({\n  theme\n}) => theme.colors.primary[600]}, ${({\n  theme\n}) => theme.colors.secondary[600]});\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n`;\n_c5 = WelcomeTitle;\nconst WelcomeSubtitle = styled.p`\n  font-size: ${({\n  theme\n}) => theme.fontSizes.xl};\n  color: ${({\n  theme\n}) => theme.colors.gray[600]};\n  max-width: 600px;\n  margin: 0 auto;\n  line-height: ${({\n  theme\n}) => theme.lineHeights.relaxed};\n`;\n_c6 = WelcomeSubtitle;\nconst SearchSection = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: ${({\n  theme\n}) => theme.spacing[4]};\n  margin-bottom: ${({\n  theme\n}) => theme.spacing[8]};\n`;\n_c7 = SearchSection;\nconst StatsSection = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: ${({\n  theme\n}) => theme.spacing[6]};\n  margin-bottom: ${({\n  theme\n}) => theme.spacing[8]};\n`;\n_c8 = StatsSection;\nconst StatCard = styled(Card)`\n  text-align: center;\n  transition: all ${({\n  theme\n}) => theme.transitions.normal};\n\n  &:hover {\n    transform: translateY(-4px);\n    box-shadow: ${({\n  theme\n}) => theme.shadows.xl};\n  }\n`;\n_c9 = StatCard;\nconst StatIcon = styled.div`\n  width: 60px;\n  height: 60px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin: 0 auto ${({\n  theme\n}) => theme.spacing[4]};\n  font-size: ${({\n  theme\n}) => theme.fontSizes['2xl']};\n  color: white;\n\n  ${({\n  variant,\n  theme\n}) => {\n  switch (variant) {\n    case 'primary':\n      return `background: linear-gradient(135deg, ${theme.colors.primary[500]}, ${theme.colors.primary[600]});`;\n    case 'success':\n      return `background: linear-gradient(135deg, ${theme.colors.success[500]}, ${theme.colors.success[600]});`;\n    case 'warning':\n      return `background: linear-gradient(135deg, ${theme.colors.warning[500]}, ${theme.colors.warning[600]});`;\n    case 'secondary':\n      return `background: linear-gradient(135deg, ${theme.colors.secondary[500]}, ${theme.colors.secondary[600]});`;\n    default:\n      return `background: linear-gradient(135deg, ${theme.colors.gray[500]}, ${theme.colors.gray[600]});`;\n  }\n}}\n`;\n_c0 = StatIcon;\nconst StatNumber = styled.div`\n  font-size: ${({\n  theme\n}) => theme.fontSizes['3xl']};\n  font-weight: ${({\n  theme\n}) => theme.fontWeights.bold};\n  color: ${({\n  theme\n}) => theme.colors.gray[900]};\n  margin-bottom: ${({\n  theme\n}) => theme.spacing[2]};\n`;\n_c1 = StatNumber;\nconst StatLabel = styled.div`\n  font-size: ${({\n  theme\n}) => theme.fontSizes.base};\n  color: ${({\n  theme\n}) => theme.colors.gray[600]};\n`;\n_c10 = StatLabel;\nconst SectionTitle = styled.h2`\n  font-size: ${({\n  theme\n}) => theme.fontSizes['2xl']};\n  font-weight: ${({\n  theme\n}) => theme.fontWeights.semibold};\n  color: ${({\n  theme\n}) => theme.colors.gray[900]};\n  margin-bottom: ${({\n  theme\n}) => theme.spacing[6]};\n  display: flex;\n  align-items: center;\n  gap: ${({\n  theme\n}) => theme.spacing[3]};\n`;\n_c11 = SectionTitle;\nconst FilesGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));\n  gap: ${({\n  theme\n}) => theme.spacing[6]};\n  margin-top: ${({\n  theme\n}) => theme.spacing[6]};\n`;\n_c12 = FilesGrid;\nconst LoadingContainer = styled.div`\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 200px;\n`;\n_c13 = LoadingContainer;\nconst LoadingSpinner = styled(motion.div)`\n  width: 40px;\n  height: 40px;\n  border: 3px solid ${({\n  theme\n}) => theme.colors.gray[200]};\n  border-top: 3px solid ${({\n  theme\n}) => theme.colors.primary[500]};\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n\n  @keyframes spin {\n    to {\n      transform: rotate(360deg);\n    }\n  }\n`;\n_c14 = LoadingSpinner;\nconst EmptyState = styled.div`\n  text-align: center;\n  padding: ${({\n  theme\n}) => theme.spacing[12]} ${({\n  theme\n}) => theme.spacing[4]};\n  color: ${({\n  theme\n}) => theme.colors.gray[500]};\n`;\n_c15 = EmptyState;\nfunction StudentDashboard() {\n  _s();\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filters, setFilters] = useState({});\n  const [showFilters, setShowFilters] = useState(false);\n  const {\n    user\n  } = useAuth();\n  const queryClient = useQueryClient();\n\n  // جلب الملفات مع البحث والفلترة\n  const {\n    data: filesData,\n    isLoading,\n    error\n  } = useQuery({\n    queryKey: ['files', searchTerm, filters],\n    queryFn: () => files.getAll({\n      search: searchTerm,\n      ...filters,\n      limit: 50\n    }),\n    select: data => data.data,\n    onError: error => {\n      toast.error('فشل في تحميل الملفات');\n      console.error('Error fetching files:', error);\n    },\n    refetchOnWindowFocus: false,\n    staleTime: 5 * 60 * 1000 // 5 minutes\n  });\n\n  // جلب إحصائيات المستخدم\n  const {\n    data: userStats\n  } = useQuery({\n    queryKey: ['user-stats', user === null || user === void 0 ? void 0 : user.id],\n    queryFn: async () => {\n      if (!user) return null;\n\n      // جلب عدد التحميلات للمستخدم\n      const {\n        data: downloads,\n        error: downloadsError\n      } = await supabase.from('downloads').select('id').eq('user_id', user.id);\n      if (downloadsError) throw downloadsError;\n\n      // جلب عدد المفضلة للمستخدم\n      const {\n        data: favorites,\n        error: favoritesError\n      } = await supabase.from('favorites').select('id').eq('user_id', user.id);\n      if (favoritesError) throw favoritesError;\n\n      // جلب عدد التقييمات للمستخدم\n      const {\n        data: ratings,\n        error: ratingsError\n      } = await supabase.from('ratings').select('id').eq('user_id', user.id);\n      if (ratingsError) throw ratingsError;\n      return {\n        downloads: (downloads === null || downloads === void 0 ? void 0 : downloads.length) || 0,\n        favorites: (favorites === null || favorites === void 0 ? void 0 : favorites.length) || 0,\n        ratings: (ratings === null || ratings === void 0 ? void 0 : ratings.length) || 0,\n        totalFiles: (filesData === null || filesData === void 0 ? void 0 : filesData.length) || 0\n      };\n    },\n    enabled: !!user,\n    refetchOnWindowFocus: false\n  });\n\n  // الاشتراك في التحديثات المباشرة للملفات\n  useEffect(() => {\n    const subscription = subscribeToFiles(payload => {\n      console.log('Files updated:', payload);\n      // إعادة جلب البيانات عند حدوث تغيير\n      queryClient.invalidateQueries(['files']);\n    });\n    return () => {\n      subscription.unsubscribe();\n    };\n  }, [queryClient]);\n  const handleFileView = file => {\n    window.open(`/file/${file.id}`, '_blank');\n  };\n  const handleFileDownload = async file => {\n    try {\n      const response = await files.download(file.id);\n\n      // إنشاء رابط تحميل\n      const url = window.URL.createObjectURL(response.data);\n      const link = document.createElement('a');\n      link.href = url;\n      link.setAttribute('download', response.fileName);\n      document.body.appendChild(link);\n      link.click();\n      link.remove();\n      window.URL.revokeObjectURL(url);\n      toast.success(`تم تحميل ${file.title}`);\n\n      // تحديث الإحصائيات\n      queryClient.invalidateQueries(['user-stats']);\n    } catch (error) {\n      toast.error('فشل في تحميل الملف');\n      console.error('Download error:', error);\n    }\n  };\n  const handleFileFavorite = async (file, isFavorite) => {\n    try {\n      await files.toggleFavorite(file.id);\n      toast.success(isFavorite ? 'تم إضافة الملف للمفضلة' : 'تم إزالة الملف من المفضلة');\n\n      // تحديث الإحصائيات\n      queryClient.invalidateQueries(['user-stats']);\n    } catch (error) {\n      toast.error('فشل في تحديث المفضلة');\n      console.error('Favorite error:', error);\n    }\n  };\n  const stats = [{\n    icon: /*#__PURE__*/_jsxDEV(FiDownload, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 293,\n      columnNumber: 13\n    }, this),\n    number: (userStats === null || userStats === void 0 ? void 0 : userStats.downloads) || 0,\n    label: 'تحميلاتي',\n    variant: 'primary'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(FiStar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 299,\n      columnNumber: 13\n    }, this),\n    number: (userStats === null || userStats === void 0 ? void 0 : userStats.favorites) || 0,\n    label: 'مفضلتي',\n    variant: 'warning'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(FiClock, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 305,\n      columnNumber: 13\n    }, this),\n    number: (userStats === null || userStats === void 0 ? void 0 : userStats.ratings) || 0,\n    label: 'تقييماتي',\n    variant: 'success'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(FiTrendingUp, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 311,\n      columnNumber: 13\n    }, this),\n    number: (userStats === null || userStats === void 0 ? void 0 : userStats.totalFiles) || 0,\n    label: 'إجمالي الملفات',\n    variant: 'secondary'\n  }];\n  return /*#__PURE__*/_jsxDEV(Dashboard, {\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      children: [/*#__PURE__*/_jsxDEV(Header, {\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.5\n        },\n        children: [/*#__PURE__*/_jsxDEV(WelcomeSection, {\n          children: [/*#__PURE__*/_jsxDEV(WelcomeTitle, {\n            children: \"\\u0645\\u0643\\u062A\\u0628\\u0629 \\u0627\\u0644\\u0645\\u0644\\u0641\\u0627\\u062A \\u0627\\u0644\\u062A\\u0639\\u0644\\u064A\\u0645\\u064A\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(WelcomeSubtitle, {\n            children: \"\\u0627\\u0643\\u062A\\u0634\\u0641 \\u0645\\u062C\\u0645\\u0648\\u0639\\u0629 \\u0648\\u0627\\u0633\\u0639\\u0629 \\u0645\\u0646 \\u0627\\u0644\\u0645\\u0644\\u0641\\u0627\\u062A \\u0627\\u0644\\u062A\\u0639\\u0644\\u064A\\u0645\\u064A\\u0629 \\u0627\\u0644\\u0645\\u062A\\u0646\\u0648\\u0639\\u0629 \\u0648\\u0627\\u0644\\u0645\\u0641\\u064A\\u062F\\u0629 \\u0644\\u062F\\u0639\\u0645 \\u0631\\u062D\\u0644\\u062A\\u0643 \\u0627\\u0644\\u062A\\u0639\\u0644\\u064A\\u0645\\u064A\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SearchSection, {\n          children: /*#__PURE__*/_jsxDEV(AdvancedSearch, {\n            onSearch: (searchValue, searchFilters) => {\n              setSearchTerm(searchValue);\n              setFilters(searchFilters);\n            },\n            onFiltersChange: setFilters,\n            placeholder: \"\\u0627\\u0628\\u062D\\u062B \\u0639\\u0646 \\u0627\\u0644\\u0645\\u0644\\u0641\\u0627\\u062A\\u060C \\u0627\\u0644\\u0645\\u0648\\u0627\\u062F\\u060C \\u0623\\u0648 \\u0627\\u0644\\u0645\\u0648\\u0627\\u0636\\u064A\\u0639...\",\n            initialFilters: filters\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 321,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatsSection, {\n        children: stats.map((stat, index) => /*#__PURE__*/_jsxDEV(StatCard, {\n          as: motion.div,\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            delay: index * 0.1\n          },\n          padding: \"lg\",\n          children: [/*#__PURE__*/_jsxDEV(StatIcon, {\n            variant: stat.variant,\n            children: stat.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(StatNumber, {\n            children: stat.number\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n            children: stat.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 346,\n        columnNumber: 9\n      }, this), showFilters && /*#__PURE__*/_jsxDEV(FilterPanel, {\n        filters: filters,\n        onChange: setFilters,\n        onClear: () => setFilters({})\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 366,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n          children: [/*#__PURE__*/_jsxDEV(FiTrendingUp, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 13\n          }, this), \"\\u0627\\u0644\\u0645\\u0644\\u0641\\u0627\\u062A \\u0627\\u0644\\u0645\\u062A\\u0627\\u062D\\u0629\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 374,\n          columnNumber: 11\n        }, this), isLoading ? /*#__PURE__*/_jsxDEV(LoadingContainer, {\n          children: /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n            animate: {\n              rotate: 360\n            },\n            transition: {\n              duration: 1,\n              repeat: Infinity,\n              ease: 'linear'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 13\n        }, this) : error ? /*#__PURE__*/_jsxDEV(EmptyState, {\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"\\u062D\\u062F\\u062B \\u062E\\u0637\\u0623 \\u0641\\u064A \\u062A\\u062D\\u0645\\u064A\\u0644 \\u0627\\u0644\\u0645\\u0644\\u0641\\u0627\\u062A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 387,\n          columnNumber: 13\n        }, this) : (filesData === null || filesData === void 0 ? void 0 : filesData.length) === 0 ? /*#__PURE__*/_jsxDEV(EmptyState, {\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"\\u0644\\u0627 \\u062A\\u0648\\u062C\\u062F \\u0645\\u0644\\u0641\\u0627\\u062A \\u0645\\u062A\\u0627\\u062D\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 391,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(FilesGrid, {\n          children: filesData === null || filesData === void 0 ? void 0 : filesData.map((file, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: index * 0.1\n            },\n            children: /*#__PURE__*/_jsxDEV(FileCard, {\n              file: {\n                ...file,\n                uploadedBy: {\n                  name: file.uploader_name || 'غير محدد'\n                }\n              },\n              onView: handleFileView,\n              onDownload: handleFileDownload,\n              onFavorite: handleFileFavorite\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 19\n            }, this)\n          }, file.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 395,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 373,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 320,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 319,\n    columnNumber: 5\n  }, this);\n}\n_s(StudentDashboard, \"CRC4UJ7RPavVW3u7+b1PgNj8DgI=\", false, function () {\n  return [useAuth, useQueryClient, useQuery, useQuery];\n});\n_c16 = StudentDashboard;\nexport default StudentDashboard;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16;\n$RefreshReg$(_c, \"Dashboard\");\n$RefreshReg$(_c2, \"Container\");\n$RefreshReg$(_c3, \"Header\");\n$RefreshReg$(_c4, \"WelcomeSection\");\n$RefreshReg$(_c5, \"WelcomeTitle\");\n$RefreshReg$(_c6, \"WelcomeSubtitle\");\n$RefreshReg$(_c7, \"SearchSection\");\n$RefreshReg$(_c8, \"StatsSection\");\n$RefreshReg$(_c9, \"StatCard\");\n$RefreshReg$(_c0, \"StatIcon\");\n$RefreshReg$(_c1, \"StatNumber\");\n$RefreshReg$(_c10, \"StatLabel\");\n$RefreshReg$(_c11, \"SectionTitle\");\n$RefreshReg$(_c12, \"FilesGrid\");\n$RefreshReg$(_c13, \"LoadingContainer\");\n$RefreshReg$(_c14, \"LoadingSpinner\");\n$RefreshReg$(_c15, \"EmptyState\");\n$RefreshReg$(_c16, \"StudentDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "styled", "motion", "useQuery", "useQueryClient", "FiDownload", "FiTrendingUp", "<PERSON><PERSON><PERSON>", "FiStar", "FileCard", "AdvancedSearch", "FilterPanel", "Card", "files", "admin", "supabase", "subscribeToFiles", "useAuth", "toast", "jsxDEV", "_jsxDEV", "Dashboard", "div", "theme", "colors", "gray", "spacing", "_c", "Container", "_c2", "Header", "_c3", "WelcomeSection", "_c4", "WelcomeTitle", "h1", "fontSizes", "fontWeights", "bold", "primary", "secondary", "_c5", "WelcomeSubtitle", "p", "xl", "lineHeights", "relaxed", "_c6", "SearchSection", "_c7", "StatsSection", "_c8", "StatCard", "transitions", "normal", "shadows", "_c9", "StatIcon", "variant", "success", "warning", "_c0", "StatNumber", "_c1", "StatLabel", "base", "_c10", "SectionTitle", "h2", "semibold", "_c11", "FilesGrid", "_c12", "LoadingContainer", "_c13", "LoadingSpinner", "_c14", "EmptyState", "_c15", "StudentDashboard", "_s", "searchTerm", "setSearchTerm", "filters", "setFilters", "showFilters", "setShowFilters", "user", "queryClient", "data", "filesData", "isLoading", "error", "query<PERSON><PERSON>", "queryFn", "getAll", "search", "limit", "select", "onError", "console", "refetchOnWindowFocus", "staleTime", "userStats", "id", "downloads", "downloadsError", "from", "eq", "favorites", "favoritesError", "ratings", "ratingsError", "length", "totalFiles", "enabled", "subscription", "payload", "log", "invalidateQueries", "unsubscribe", "handleFileView", "file", "window", "open", "handleFileDownload", "response", "download", "url", "URL", "createObjectURL", "link", "document", "createElement", "href", "setAttribute", "fileName", "body", "append<PERSON><PERSON><PERSON>", "click", "remove", "revokeObjectURL", "title", "handleFileFavorite", "isFavorite", "toggleFavorite", "stats", "icon", "_jsxFileName", "lineNumber", "columnNumber", "number", "label", "children", "initial", "opacity", "y", "animate", "transition", "duration", "onSearch", "searchValue", "searchFilters", "onFiltersChange", "placeholder", "initialFilters", "map", "stat", "index", "as", "delay", "padding", "onChange", "onClear", "rotate", "repeat", "Infinity", "ease", "uploadedBy", "name", "uploader_name", "onView", "onDownload", "onFavorite", "_c16", "$RefreshReg$"], "sources": ["D:/menasa/frontend/src/pages/StudentDashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport styled from 'styled-components';\nimport { motion } from 'framer-motion';\nimport { useQuery, useQueryClient } from '@tanstack/react-query';\nimport {\n  FiDownload,\n  FiTrendingUp,\n  FiClock,\n  FiStar\n} from 'react-icons/fi';\nimport FileCard from '../components/FileCard';\nimport AdvancedSearch from '../components/search/AdvancedSearch';\nimport FilterPanel from '../components/FilterPanel';\nimport { Card } from '../components/ui';\nimport { files, admin } from '../services/api';\nimport { supabase, subscribeToFiles } from '../lib/supabase';\nimport { useAuth } from '../contexts/AuthContext';\nimport { toast } from 'react-toastify';\n\nconst Dashboard = styled.div`\n  min-height: 100vh;\n  background: ${({ theme }) => theme.colors.gray[50]};\n  padding: ${({ theme }) => theme.spacing[6]} 0;\n`;\n\nconst Container = styled.div`\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 0 ${({ theme }) => theme.spacing[4]};\n`;\n\nconst Header = styled(motion.div)`\n  margin-bottom: ${({ theme }) => theme.spacing[8]};\n`;\n\nconst WelcomeSection = styled.div`\n  text-align: center;\n  margin-bottom: ${({ theme }) => theme.spacing[8]};\n`;\n\nconst WelcomeTitle = styled.h1`\n  font-size: ${({ theme }) => theme.fontSizes['4xl']};\n  font-weight: ${({ theme }) => theme.fontWeights.bold};\n  color: ${({ theme }) => theme.colors.gray[900]};\n  margin-bottom: ${({ theme }) => theme.spacing[4]};\n  background: linear-gradient(135deg, ${({ theme }) => theme.colors.primary[600]}, ${({ theme }) => theme.colors.secondary[600]});\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n`;\n\nconst WelcomeSubtitle = styled.p`\n  font-size: ${({ theme }) => theme.fontSizes.xl};\n  color: ${({ theme }) => theme.colors.gray[600]};\n  max-width: 600px;\n  margin: 0 auto;\n  line-height: ${({ theme }) => theme.lineHeights.relaxed};\n`;\n\nconst SearchSection = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: ${({ theme }) => theme.spacing[4]};\n  margin-bottom: ${({ theme }) => theme.spacing[8]};\n`;\n\nconst StatsSection = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: ${({ theme }) => theme.spacing[6]};\n  margin-bottom: ${({ theme }) => theme.spacing[8]};\n`;\n\nconst StatCard = styled(Card)`\n  text-align: center;\n  transition: all ${({ theme }) => theme.transitions.normal};\n\n  &:hover {\n    transform: translateY(-4px);\n    box-shadow: ${({ theme }) => theme.shadows.xl};\n  }\n`;\n\nconst StatIcon = styled.div`\n  width: 60px;\n  height: 60px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin: 0 auto ${({ theme }) => theme.spacing[4]};\n  font-size: ${({ theme }) => theme.fontSizes['2xl']};\n  color: white;\n\n  ${({ variant, theme }) => {\n    switch (variant) {\n      case 'primary':\n        return `background: linear-gradient(135deg, ${theme.colors.primary[500]}, ${theme.colors.primary[600]});`;\n      case 'success':\n        return `background: linear-gradient(135deg, ${theme.colors.success[500]}, ${theme.colors.success[600]});`;\n      case 'warning':\n        return `background: linear-gradient(135deg, ${theme.colors.warning[500]}, ${theme.colors.warning[600]});`;\n      case 'secondary':\n        return `background: linear-gradient(135deg, ${theme.colors.secondary[500]}, ${theme.colors.secondary[600]});`;\n      default:\n        return `background: linear-gradient(135deg, ${theme.colors.gray[500]}, ${theme.colors.gray[600]});`;\n    }\n  }}\n`;\n\nconst StatNumber = styled.div`\n  font-size: ${({ theme }) => theme.fontSizes['3xl']};\n  font-weight: ${({ theme }) => theme.fontWeights.bold};\n  color: ${({ theme }) => theme.colors.gray[900]};\n  margin-bottom: ${({ theme }) => theme.spacing[2]};\n`;\n\nconst StatLabel = styled.div`\n  font-size: ${({ theme }) => theme.fontSizes.base};\n  color: ${({ theme }) => theme.colors.gray[600]};\n`;\n\nconst SectionTitle = styled.h2`\n  font-size: ${({ theme }) => theme.fontSizes['2xl']};\n  font-weight: ${({ theme }) => theme.fontWeights.semibold};\n  color: ${({ theme }) => theme.colors.gray[900]};\n  margin-bottom: ${({ theme }) => theme.spacing[6]};\n  display: flex;\n  align-items: center;\n  gap: ${({ theme }) => theme.spacing[3]};\n`;\n\nconst FilesGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));\n  gap: ${({ theme }) => theme.spacing[6]};\n  margin-top: ${({ theme }) => theme.spacing[6]};\n`;\n\nconst LoadingContainer = styled.div`\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 200px;\n`;\n\nconst LoadingSpinner = styled(motion.div)`\n  width: 40px;\n  height: 40px;\n  border: 3px solid ${({ theme }) => theme.colors.gray[200]};\n  border-top: 3px solid ${({ theme }) => theme.colors.primary[500]};\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n\n  @keyframes spin {\n    to {\n      transform: rotate(360deg);\n    }\n  }\n`;\n\nconst EmptyState = styled.div`\n  text-align: center;\n  padding: ${({ theme }) => theme.spacing[12]} ${({ theme }) => theme.spacing[4]};\n  color: ${({ theme }) => theme.colors.gray[500]};\n`;\n\nfunction StudentDashboard() {\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filters, setFilters] = useState({});\n  const [showFilters, setShowFilters] = useState(false);\n\n  const { user } = useAuth();\n  const queryClient = useQueryClient();\n\n  // جلب الملفات مع البحث والفلترة\n  const { data: filesData, isLoading, error } = useQuery({\n    queryKey: ['files', searchTerm, filters],\n    queryFn: () => files.getAll({\n      search: searchTerm,\n      ...filters,\n      limit: 50\n    }),\n    select: (data) => data.data,\n    onError: (error) => {\n      toast.error('فشل في تحميل الملفات');\n      console.error('Error fetching files:', error);\n    },\n    refetchOnWindowFocus: false,\n    staleTime: 5 * 60 * 1000, // 5 minutes\n  });\n\n  // جلب إحصائيات المستخدم\n  const { data: userStats } = useQuery({\n    queryKey: ['user-stats', user?.id],\n    queryFn: async () => {\n      if (!user) return null;\n\n      // جلب عدد التحميلات للمستخدم\n      const { data: downloads, error: downloadsError } = await supabase\n        .from('downloads')\n        .select('id')\n        .eq('user_id', user.id);\n\n      if (downloadsError) throw downloadsError;\n\n      // جلب عدد المفضلة للمستخدم\n      const { data: favorites, error: favoritesError } = await supabase\n        .from('favorites')\n        .select('id')\n        .eq('user_id', user.id);\n\n      if (favoritesError) throw favoritesError;\n\n      // جلب عدد التقييمات للمستخدم\n      const { data: ratings, error: ratingsError } = await supabase\n        .from('ratings')\n        .select('id')\n        .eq('user_id', user.id);\n\n      if (ratingsError) throw ratingsError;\n\n      return {\n        downloads: downloads?.length || 0,\n        favorites: favorites?.length || 0,\n        ratings: ratings?.length || 0,\n        totalFiles: filesData?.length || 0\n      };\n    },\n    enabled: !!user,\n    refetchOnWindowFocus: false,\n  });\n\n  // الاشتراك في التحديثات المباشرة للملفات\n  useEffect(() => {\n    const subscription = subscribeToFiles((payload) => {\n      console.log('Files updated:', payload);\n      // إعادة جلب البيانات عند حدوث تغيير\n      queryClient.invalidateQueries(['files']);\n    });\n\n    return () => {\n      subscription.unsubscribe();\n    };\n  }, [queryClient]);\n\n  const handleFileView = (file) => {\n    window.open(`/file/${file.id}`, '_blank');\n  };\n\n  const handleFileDownload = async (file) => {\n    try {\n      const response = await files.download(file.id);\n\n      // إنشاء رابط تحميل\n      const url = window.URL.createObjectURL(response.data);\n      const link = document.createElement('a');\n      link.href = url;\n      link.setAttribute('download', response.fileName);\n      document.body.appendChild(link);\n      link.click();\n      link.remove();\n      window.URL.revokeObjectURL(url);\n\n      toast.success(`تم تحميل ${file.title}`);\n\n      // تحديث الإحصائيات\n      queryClient.invalidateQueries(['user-stats']);\n    } catch (error) {\n      toast.error('فشل في تحميل الملف');\n      console.error('Download error:', error);\n    }\n  };\n\n  const handleFileFavorite = async (file, isFavorite) => {\n    try {\n      await files.toggleFavorite(file.id);\n      toast.success(isFavorite ? 'تم إضافة الملف للمفضلة' : 'تم إزالة الملف من المفضلة');\n\n      // تحديث الإحصائيات\n      queryClient.invalidateQueries(['user-stats']);\n    } catch (error) {\n      toast.error('فشل في تحديث المفضلة');\n      console.error('Favorite error:', error);\n    }\n  };\n\n\n\n  const stats = [\n    {\n      icon: <FiDownload />,\n      number: userStats?.downloads || 0,\n      label: 'تحميلاتي',\n      variant: 'primary'\n    },\n    {\n      icon: <FiStar />,\n      number: userStats?.favorites || 0,\n      label: 'مفضلتي',\n      variant: 'warning'\n    },\n    {\n      icon: <FiClock />,\n      number: userStats?.ratings || 0,\n      label: 'تقييماتي',\n      variant: 'success'\n    },\n    {\n      icon: <FiTrendingUp />,\n      number: userStats?.totalFiles || 0,\n      label: 'إجمالي الملفات',\n      variant: 'secondary'\n    }\n  ];\n\n  return (\n    <Dashboard>\n      <Container>\n        <Header\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5 }}\n        >\n          <WelcomeSection>\n            <WelcomeTitle>مكتبة الملفات التعليمية</WelcomeTitle>\n            <WelcomeSubtitle>\n              اكتشف مجموعة واسعة من الملفات التعليمية المتنوعة والمفيدة لدعم رحلتك التعليمية\n            </WelcomeSubtitle>\n          </WelcomeSection>\n\n          <SearchSection>\n            <AdvancedSearch\n              onSearch={(searchValue, searchFilters) => {\n                setSearchTerm(searchValue);\n                setFilters(searchFilters);\n              }}\n              onFiltersChange={setFilters}\n              placeholder=\"ابحث عن الملفات، المواد، أو المواضيع...\"\n              initialFilters={filters}\n            />\n          </SearchSection>\n        </Header>\n\n        <StatsSection>\n          {stats.map((stat, index) => (\n            <StatCard\n              key={index}\n              as={motion.div}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: index * 0.1 }}\n              padding=\"lg\"\n            >\n              <StatIcon variant={stat.variant}>\n                {stat.icon}\n              </StatIcon>\n              <StatNumber>{stat.number}</StatNumber>\n              <StatLabel>{stat.label}</StatLabel>\n            </StatCard>\n          ))}\n        </StatsSection>\n\n        {showFilters && (\n          <FilterPanel\n            filters={filters}\n            onChange={setFilters}\n            onClear={() => setFilters({})}\n          />\n        )}\n\n        <div>\n          <SectionTitle>\n            <FiTrendingUp />\n            الملفات المتاحة\n          </SectionTitle>\n\n          {isLoading ? (\n            <LoadingContainer>\n              <LoadingSpinner\n                animate={{ rotate: 360 }}\n                transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}\n              />\n            </LoadingContainer>\n          ) : error ? (\n            <EmptyState>\n              <p>حدث خطأ في تحميل الملفات</p>\n            </EmptyState>\n          ) : filesData?.length === 0 ? (\n            <EmptyState>\n              <p>لا توجد ملفات متاحة</p>\n            </EmptyState>\n          ) : (\n            <FilesGrid>\n              {filesData?.map((file, index) => (\n                <motion.div\n                  key={file.id}\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: index * 0.1 }}\n                >\n                  <FileCard\n                    file={{\n                      ...file,\n                      uploadedBy: {\n                        name: file.uploader_name || 'غير محدد'\n                      }\n                    }}\n                    onView={handleFileView}\n                    onDownload={handleFileDownload}\n                    onFavorite={handleFileFavorite}\n                  />\n                </motion.div>\n              ))}\n            </FilesGrid>\n          )}\n        </div>\n      </Container>\n    </Dashboard>\n  );\n}\n\nexport default StudentDashboard;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,QAAQ,EAAEC,cAAc,QAAQ,uBAAuB;AAChE,SACEC,UAAU,EACVC,YAAY,EACZC,OAAO,EACPC,MAAM,QACD,gBAAgB;AACvB,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,OAAOC,cAAc,MAAM,qCAAqC;AAChE,OAAOC,WAAW,MAAM,2BAA2B;AACnD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,KAAK,EAAEC,KAAK,QAAQ,iBAAiB;AAC9C,SAASC,QAAQ,EAAEC,gBAAgB,QAAQ,iBAAiB;AAC5D,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,KAAK,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,SAAS,GAAGpB,MAAM,CAACqB,GAAG;AAC5B;AACA,gBAAgB,CAAC;EAAEC;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACC,IAAI,CAAC,EAAE,CAAC;AACpD,aAAa,CAAC;EAAEF;AAAM,CAAC,KAAKA,KAAK,CAACG,OAAO,CAAC,CAAC,CAAC;AAC5C,CAAC;AAACC,EAAA,GAJIN,SAAS;AAMf,MAAMO,SAAS,GAAG3B,MAAM,CAACqB,GAAG;AAC5B;AACA;AACA,eAAe,CAAC;EAAEC;AAAM,CAAC,KAAKA,KAAK,CAACG,OAAO,CAAC,CAAC,CAAC;AAC9C,CAAC;AAACG,GAAA,GAJID,SAAS;AAMf,MAAME,MAAM,GAAG7B,MAAM,CAACC,MAAM,CAACoB,GAAG,CAAC;AACjC,mBAAmB,CAAC;EAAEC;AAAM,CAAC,KAAKA,KAAK,CAACG,OAAO,CAAC,CAAC,CAAC;AAClD,CAAC;AAACK,GAAA,GAFID,MAAM;AAIZ,MAAME,cAAc,GAAG/B,MAAM,CAACqB,GAAG;AACjC;AACA,mBAAmB,CAAC;EAAEC;AAAM,CAAC,KAAKA,KAAK,CAACG,OAAO,CAAC,CAAC,CAAC;AAClD,CAAC;AAACO,GAAA,GAHID,cAAc;AAKpB,MAAME,YAAY,GAAGjC,MAAM,CAACkC,EAAE;AAC9B,eAAe,CAAC;EAAEZ;AAAM,CAAC,KAAKA,KAAK,CAACa,SAAS,CAAC,KAAK,CAAC;AACpD,iBAAiB,CAAC;EAAEb;AAAM,CAAC,KAAKA,KAAK,CAACc,WAAW,CAACC,IAAI;AACtD,WAAW,CAAC;EAAEf;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACC,IAAI,CAAC,GAAG,CAAC;AAChD,mBAAmB,CAAC;EAAEF;AAAM,CAAC,KAAKA,KAAK,CAACG,OAAO,CAAC,CAAC,CAAC;AAClD,wCAAwC,CAAC;EAAEH;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACe,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC;EAAEhB;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACgB,SAAS,CAAC,GAAG,CAAC;AAC/H;AACA;AACA;AACA,CAAC;AAACC,GAAA,GATIP,YAAY;AAWlB,MAAMQ,eAAe,GAAGzC,MAAM,CAAC0C,CAAC;AAChC,eAAe,CAAC;EAAEpB;AAAM,CAAC,KAAKA,KAAK,CAACa,SAAS,CAACQ,EAAE;AAChD,WAAW,CAAC;EAAErB;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACC,IAAI,CAAC,GAAG,CAAC;AAChD;AACA;AACA,iBAAiB,CAAC;EAAEF;AAAM,CAAC,KAAKA,KAAK,CAACsB,WAAW,CAACC,OAAO;AACzD,CAAC;AAACC,GAAA,GANIL,eAAe;AAQrB,MAAMM,aAAa,GAAG/C,MAAM,CAACqB,GAAG;AAChC;AACA;AACA;AACA,SAAS,CAAC;EAAEC;AAAM,CAAC,KAAKA,KAAK,CAACG,OAAO,CAAC,CAAC,CAAC;AACxC,mBAAmB,CAAC;EAAEH;AAAM,CAAC,KAAKA,KAAK,CAACG,OAAO,CAAC,CAAC,CAAC;AAClD,CAAC;AAACuB,GAAA,GANID,aAAa;AAQnB,MAAME,YAAY,GAAGjD,MAAM,CAACqB,GAAG;AAC/B;AACA;AACA,SAAS,CAAC;EAAEC;AAAM,CAAC,KAAKA,KAAK,CAACG,OAAO,CAAC,CAAC,CAAC;AACxC,mBAAmB,CAAC;EAAEH;AAAM,CAAC,KAAKA,KAAK,CAACG,OAAO,CAAC,CAAC,CAAC;AAClD,CAAC;AAACyB,GAAA,GALID,YAAY;AAOlB,MAAME,QAAQ,GAAGnD,MAAM,CAACW,IAAI,CAAC;AAC7B;AACA,oBAAoB,CAAC;EAAEW;AAAM,CAAC,KAAKA,KAAK,CAAC8B,WAAW,CAACC,MAAM;AAC3D;AACA;AACA;AACA,kBAAkB,CAAC;EAAE/B;AAAM,CAAC,KAAKA,KAAK,CAACgC,OAAO,CAACX,EAAE;AACjD;AACA,CAAC;AAACY,GAAA,GARIJ,QAAQ;AAUd,MAAMK,QAAQ,GAAGxD,MAAM,CAACqB,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,CAAC;EAAEC;AAAM,CAAC,KAAKA,KAAK,CAACG,OAAO,CAAC,CAAC,CAAC;AAClD,eAAe,CAAC;EAAEH;AAAM,CAAC,KAAKA,KAAK,CAACa,SAAS,CAAC,KAAK,CAAC;AACpD;AACA;AACA,IAAI,CAAC;EAAEsB,OAAO;EAAEnC;AAAM,CAAC,KAAK;EACxB,QAAQmC,OAAO;IACb,KAAK,SAAS;MACZ,OAAO,uCAAuCnC,KAAK,CAACC,MAAM,CAACe,OAAO,CAAC,GAAG,CAAC,KAAKhB,KAAK,CAACC,MAAM,CAACe,OAAO,CAAC,GAAG,CAAC,IAAI;IAC3G,KAAK,SAAS;MACZ,OAAO,uCAAuChB,KAAK,CAACC,MAAM,CAACmC,OAAO,CAAC,GAAG,CAAC,KAAKpC,KAAK,CAACC,MAAM,CAACmC,OAAO,CAAC,GAAG,CAAC,IAAI;IAC3G,KAAK,SAAS;MACZ,OAAO,uCAAuCpC,KAAK,CAACC,MAAM,CAACoC,OAAO,CAAC,GAAG,CAAC,KAAKrC,KAAK,CAACC,MAAM,CAACoC,OAAO,CAAC,GAAG,CAAC,IAAI;IAC3G,KAAK,WAAW;MACd,OAAO,uCAAuCrC,KAAK,CAACC,MAAM,CAACgB,SAAS,CAAC,GAAG,CAAC,KAAKjB,KAAK,CAACC,MAAM,CAACgB,SAAS,CAAC,GAAG,CAAC,IAAI;IAC/G;MACE,OAAO,uCAAuCjB,KAAK,CAACC,MAAM,CAACC,IAAI,CAAC,GAAG,CAAC,KAAKF,KAAK,CAACC,MAAM,CAACC,IAAI,CAAC,GAAG,CAAC,IAAI;EACvG;AACF,CAAC;AACH,CAAC;AAACoC,GAAA,GAzBIJ,QAAQ;AA2Bd,MAAMK,UAAU,GAAG7D,MAAM,CAACqB,GAAG;AAC7B,eAAe,CAAC;EAAEC;AAAM,CAAC,KAAKA,KAAK,CAACa,SAAS,CAAC,KAAK,CAAC;AACpD,iBAAiB,CAAC;EAAEb;AAAM,CAAC,KAAKA,KAAK,CAACc,WAAW,CAACC,IAAI;AACtD,WAAW,CAAC;EAAEf;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACC,IAAI,CAAC,GAAG,CAAC;AAChD,mBAAmB,CAAC;EAAEF;AAAM,CAAC,KAAKA,KAAK,CAACG,OAAO,CAAC,CAAC,CAAC;AAClD,CAAC;AAACqC,GAAA,GALID,UAAU;AAOhB,MAAME,SAAS,GAAG/D,MAAM,CAACqB,GAAG;AAC5B,eAAe,CAAC;EAAEC;AAAM,CAAC,KAAKA,KAAK,CAACa,SAAS,CAAC6B,IAAI;AAClD,WAAW,CAAC;EAAE1C;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACC,IAAI,CAAC,GAAG,CAAC;AAChD,CAAC;AAACyC,IAAA,GAHIF,SAAS;AAKf,MAAMG,YAAY,GAAGlE,MAAM,CAACmE,EAAE;AAC9B,eAAe,CAAC;EAAE7C;AAAM,CAAC,KAAKA,KAAK,CAACa,SAAS,CAAC,KAAK,CAAC;AACpD,iBAAiB,CAAC;EAAEb;AAAM,CAAC,KAAKA,KAAK,CAACc,WAAW,CAACgC,QAAQ;AAC1D,WAAW,CAAC;EAAE9C;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACC,IAAI,CAAC,GAAG,CAAC;AAChD,mBAAmB,CAAC;EAAEF;AAAM,CAAC,KAAKA,KAAK,CAACG,OAAO,CAAC,CAAC,CAAC;AAClD;AACA;AACA,SAAS,CAAC;EAAEH;AAAM,CAAC,KAAKA,KAAK,CAACG,OAAO,CAAC,CAAC,CAAC;AACxC,CAAC;AAAC4C,IAAA,GARIH,YAAY;AAUlB,MAAMI,SAAS,GAAGtE,MAAM,CAACqB,GAAG;AAC5B;AACA;AACA,SAAS,CAAC;EAAEC;AAAM,CAAC,KAAKA,KAAK,CAACG,OAAO,CAAC,CAAC,CAAC;AACxC,gBAAgB,CAAC;EAAEH;AAAM,CAAC,KAAKA,KAAK,CAACG,OAAO,CAAC,CAAC,CAAC;AAC/C,CAAC;AAAC8C,IAAA,GALID,SAAS;AAOf,MAAME,gBAAgB,GAAGxE,MAAM,CAACqB,GAAG;AACnC;AACA;AACA;AACA;AACA,CAAC;AAACoD,IAAA,GALID,gBAAgB;AAOtB,MAAME,cAAc,GAAG1E,MAAM,CAACC,MAAM,CAACoB,GAAG,CAAC;AACzC;AACA;AACA,sBAAsB,CAAC;EAAEC;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACC,IAAI,CAAC,GAAG,CAAC;AAC3D,0BAA0B,CAAC;EAAEF;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACe,OAAO,CAAC,GAAG,CAAC;AAClE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACqC,IAAA,GAbID,cAAc;AAepB,MAAME,UAAU,GAAG5E,MAAM,CAACqB,GAAG;AAC7B;AACA,aAAa,CAAC;EAAEC;AAAM,CAAC,KAAKA,KAAK,CAACG,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC;EAAEH;AAAM,CAAC,KAAKA,KAAK,CAACG,OAAO,CAAC,CAAC,CAAC;AAChF,WAAW,CAAC;EAAEH;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACC,IAAI,CAAC,GAAG,CAAC;AAChD,CAAC;AAACqD,IAAA,GAJID,UAAU;AAMhB,SAASE,gBAAgBA,CAAA,EAAG;EAAAC,EAAA;EAC1B,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGnF,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACoF,OAAO,EAAEC,UAAU,CAAC,GAAGrF,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1C,MAAM,CAACsF,WAAW,EAAEC,cAAc,CAAC,GAAGvF,QAAQ,CAAC,KAAK,CAAC;EAErD,MAAM;IAAEwF;EAAK,CAAC,GAAGtE,OAAO,CAAC,CAAC;EAC1B,MAAMuE,WAAW,GAAGpF,cAAc,CAAC,CAAC;;EAEpC;EACA,MAAM;IAAEqF,IAAI,EAAEC,SAAS;IAAEC,SAAS;IAAEC;EAAM,CAAC,GAAGzF,QAAQ,CAAC;IACrD0F,QAAQ,EAAE,CAAC,OAAO,EAAEZ,UAAU,EAAEE,OAAO,CAAC;IACxCW,OAAO,EAAEA,CAAA,KAAMjF,KAAK,CAACkF,MAAM,CAAC;MAC1BC,MAAM,EAAEf,UAAU;MAClB,GAAGE,OAAO;MACVc,KAAK,EAAE;IACT,CAAC,CAAC;IACFC,MAAM,EAAGT,IAAI,IAAKA,IAAI,CAACA,IAAI;IAC3BU,OAAO,EAAGP,KAAK,IAAK;MAClB1E,KAAK,CAAC0E,KAAK,CAAC,sBAAsB,CAAC;MACnCQ,OAAO,CAACR,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C,CAAC;IACDS,oBAAoB,EAAE,KAAK;IAC3BC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAE;EAC5B,CAAC,CAAC;;EAEF;EACA,MAAM;IAAEb,IAAI,EAAEc;EAAU,CAAC,GAAGpG,QAAQ,CAAC;IACnC0F,QAAQ,EAAE,CAAC,YAAY,EAAEN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiB,EAAE,CAAC;IAClCV,OAAO,EAAE,MAAAA,CAAA,KAAY;MACnB,IAAI,CAACP,IAAI,EAAE,OAAO,IAAI;;MAEtB;MACA,MAAM;QAAEE,IAAI,EAAEgB,SAAS;QAAEb,KAAK,EAAEc;MAAe,CAAC,GAAG,MAAM3F,QAAQ,CAC9D4F,IAAI,CAAC,WAAW,CAAC,CACjBT,MAAM,CAAC,IAAI,CAAC,CACZU,EAAE,CAAC,SAAS,EAAErB,IAAI,CAACiB,EAAE,CAAC;MAEzB,IAAIE,cAAc,EAAE,MAAMA,cAAc;;MAExC;MACA,MAAM;QAAEjB,IAAI,EAAEoB,SAAS;QAAEjB,KAAK,EAAEkB;MAAe,CAAC,GAAG,MAAM/F,QAAQ,CAC9D4F,IAAI,CAAC,WAAW,CAAC,CACjBT,MAAM,CAAC,IAAI,CAAC,CACZU,EAAE,CAAC,SAAS,EAAErB,IAAI,CAACiB,EAAE,CAAC;MAEzB,IAAIM,cAAc,EAAE,MAAMA,cAAc;;MAExC;MACA,MAAM;QAAErB,IAAI,EAAEsB,OAAO;QAAEnB,KAAK,EAAEoB;MAAa,CAAC,GAAG,MAAMjG,QAAQ,CAC1D4F,IAAI,CAAC,SAAS,CAAC,CACfT,MAAM,CAAC,IAAI,CAAC,CACZU,EAAE,CAAC,SAAS,EAAErB,IAAI,CAACiB,EAAE,CAAC;MAEzB,IAAIQ,YAAY,EAAE,MAAMA,YAAY;MAEpC,OAAO;QACLP,SAAS,EAAE,CAAAA,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEQ,MAAM,KAAI,CAAC;QACjCJ,SAAS,EAAE,CAAAA,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEI,MAAM,KAAI,CAAC;QACjCF,OAAO,EAAE,CAAAA,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEE,MAAM,KAAI,CAAC;QAC7BC,UAAU,EAAE,CAAAxB,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEuB,MAAM,KAAI;MACnC,CAAC;IACH,CAAC;IACDE,OAAO,EAAE,CAAC,CAAC5B,IAAI;IACfc,oBAAoB,EAAE;EACxB,CAAC,CAAC;;EAEF;EACArG,SAAS,CAAC,MAAM;IACd,MAAMoH,YAAY,GAAGpG,gBAAgB,CAAEqG,OAAO,IAAK;MACjDjB,OAAO,CAACkB,GAAG,CAAC,gBAAgB,EAAED,OAAO,CAAC;MACtC;MACA7B,WAAW,CAAC+B,iBAAiB,CAAC,CAAC,OAAO,CAAC,CAAC;IAC1C,CAAC,CAAC;IAEF,OAAO,MAAM;MACXH,YAAY,CAACI,WAAW,CAAC,CAAC;IAC5B,CAAC;EACH,CAAC,EAAE,CAAChC,WAAW,CAAC,CAAC;EAEjB,MAAMiC,cAAc,GAAIC,IAAI,IAAK;IAC/BC,MAAM,CAACC,IAAI,CAAC,SAASF,IAAI,CAAClB,EAAE,EAAE,EAAE,QAAQ,CAAC;EAC3C,CAAC;EAED,MAAMqB,kBAAkB,GAAG,MAAOH,IAAI,IAAK;IACzC,IAAI;MACF,MAAMI,QAAQ,GAAG,MAAMjH,KAAK,CAACkH,QAAQ,CAACL,IAAI,CAAClB,EAAE,CAAC;;MAE9C;MACA,MAAMwB,GAAG,GAAGL,MAAM,CAACM,GAAG,CAACC,eAAe,CAACJ,QAAQ,CAACrC,IAAI,CAAC;MACrD,MAAM0C,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGN,GAAG;MACfG,IAAI,CAACI,YAAY,CAAC,UAAU,EAAET,QAAQ,CAACU,QAAQ,CAAC;MAChDJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;MAC/BA,IAAI,CAACQ,KAAK,CAAC,CAAC;MACZR,IAAI,CAACS,MAAM,CAAC,CAAC;MACbjB,MAAM,CAACM,GAAG,CAACY,eAAe,CAACb,GAAG,CAAC;MAE/B9G,KAAK,CAACyC,OAAO,CAAC,YAAY+D,IAAI,CAACoB,KAAK,EAAE,CAAC;;MAEvC;MACAtD,WAAW,CAAC+B,iBAAiB,CAAC,CAAC,YAAY,CAAC,CAAC;IAC/C,CAAC,CAAC,OAAO3B,KAAK,EAAE;MACd1E,KAAK,CAAC0E,KAAK,CAAC,oBAAoB,CAAC;MACjCQ,OAAO,CAACR,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;IACzC;EACF,CAAC;EAED,MAAMmD,kBAAkB,GAAG,MAAAA,CAAOrB,IAAI,EAAEsB,UAAU,KAAK;IACrD,IAAI;MACF,MAAMnI,KAAK,CAACoI,cAAc,CAACvB,IAAI,CAAClB,EAAE,CAAC;MACnCtF,KAAK,CAACyC,OAAO,CAACqF,UAAU,GAAG,wBAAwB,GAAG,2BAA2B,CAAC;;MAElF;MACAxD,WAAW,CAAC+B,iBAAiB,CAAC,CAAC,YAAY,CAAC,CAAC;IAC/C,CAAC,CAAC,OAAO3B,KAAK,EAAE;MACd1E,KAAK,CAAC0E,KAAK,CAAC,sBAAsB,CAAC;MACnCQ,OAAO,CAACR,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;IACzC;EACF,CAAC;EAID,MAAMsD,KAAK,GAAG,CACZ;IACEC,IAAI,eAAE/H,OAAA,CAACf,UAAU;MAAAmI,QAAA,EAAAY,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACpBC,MAAM,EAAE,CAAAhD,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEE,SAAS,KAAI,CAAC;IACjC+C,KAAK,EAAE,UAAU;IACjB9F,OAAO,EAAE;EACX,CAAC,EACD;IACEyF,IAAI,eAAE/H,OAAA,CAACZ,MAAM;MAAAgI,QAAA,EAAAY,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAChBC,MAAM,EAAE,CAAAhD,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEM,SAAS,KAAI,CAAC;IACjC2C,KAAK,EAAE,QAAQ;IACf9F,OAAO,EAAE;EACX,CAAC,EACD;IACEyF,IAAI,eAAE/H,OAAA,CAACb,OAAO;MAAAiI,QAAA,EAAAY,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACjBC,MAAM,EAAE,CAAAhD,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEQ,OAAO,KAAI,CAAC;IAC/ByC,KAAK,EAAE,UAAU;IACjB9F,OAAO,EAAE;EACX,CAAC,EACD;IACEyF,IAAI,eAAE/H,OAAA,CAACd,YAAY;MAAAkI,QAAA,EAAAY,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,MAAM,EAAE,CAAAhD,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEW,UAAU,KAAI,CAAC;IAClCsC,KAAK,EAAE,gBAAgB;IACvB9F,OAAO,EAAE;EACX,CAAC,CACF;EAED,oBACEtC,OAAA,CAACC,SAAS;IAAAoI,QAAA,eACRrI,OAAA,CAACQ,SAAS;MAAA6H,QAAA,gBACRrI,OAAA,CAACU,MAAM;QACL4H,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAAAN,QAAA,gBAE9BrI,OAAA,CAACY,cAAc;UAAAyH,QAAA,gBACbrI,OAAA,CAACc,YAAY;YAAAuH,QAAA,EAAC;UAAuB;YAAAjB,QAAA,EAAAY,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC,eACpDlI,OAAA,CAACsB,eAAe;YAAA+G,QAAA,EAAC;UAEjB;YAAAjB,QAAA,EAAAY,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAiB,CAAC;QAAA;UAAAd,QAAA,EAAAY,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAEjBlI,OAAA,CAAC4B,aAAa;UAAAyG,QAAA,eACZrI,OAAA,CAACV,cAAc;YACbsJ,QAAQ,EAAEA,CAACC,WAAW,EAAEC,aAAa,KAAK;cACxChF,aAAa,CAAC+E,WAAW,CAAC;cAC1B7E,UAAU,CAAC8E,aAAa,CAAC;YAC3B,CAAE;YACFC,eAAe,EAAE/E,UAAW;YAC5BgF,WAAW,EAAC,oMAAyC;YACrDC,cAAc,EAAElF;UAAQ;YAAAqD,QAAA,EAAAY,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB;QAAC;UAAAd,QAAA,EAAAY,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACW,CAAC;MAAA;QAAAd,QAAA,EAAAY,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAETlI,OAAA,CAAC8B,YAAY;QAAAuG,QAAA,EACVP,KAAK,CAACoB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACrBpJ,OAAA,CAACgC,QAAQ;UAEPqH,EAAE,EAAEvK,MAAM,CAACoB,GAAI;UACfoI,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BE,UAAU,EAAE;YAAEY,KAAK,EAAEF,KAAK,GAAG;UAAI,CAAE;UACnCG,OAAO,EAAC,IAAI;UAAAlB,QAAA,gBAEZrI,OAAA,CAACqC,QAAQ;YAACC,OAAO,EAAE6G,IAAI,CAAC7G,OAAQ;YAAA+F,QAAA,EAC7Bc,IAAI,CAACpB;UAAI;YAAAX,QAAA,EAAAY,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACXlI,OAAA,CAAC0C,UAAU;YAAA2F,QAAA,EAAEc,IAAI,CAAChB;UAAM;YAAAf,QAAA,EAAAY,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACtClI,OAAA,CAAC4C,SAAS;YAAAyF,QAAA,EAAEc,IAAI,CAACf;UAAK;YAAAhB,QAAA,EAAAY,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA,GAX9BkB,KAAK;UAAAhC,QAAA,EAAAY,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAYF,CACX;MAAC;QAAAd,QAAA,EAAAY,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU,CAAC,EAEdjE,WAAW,iBACVjE,OAAA,CAACT,WAAW;QACVwE,OAAO,EAAEA,OAAQ;QACjByF,QAAQ,EAAExF,UAAW;QACrByF,OAAO,EAAEA,CAAA,KAAMzF,UAAU,CAAC,CAAC,CAAC;MAAE;QAAAoD,QAAA,EAAAY,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CACF,eAEDlI,OAAA;QAAAqI,QAAA,gBACErI,OAAA,CAAC+C,YAAY;UAAAsF,QAAA,gBACXrI,OAAA,CAACd,YAAY;YAAAkI,QAAA,EAAAY,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,yFAElB;QAAA;UAAAd,QAAA,EAAAY,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC,EAEd3D,SAAS,gBACRvE,OAAA,CAACqD,gBAAgB;UAAAgF,QAAA,eACfrI,OAAA,CAACuD,cAAc;YACbkF,OAAO,EAAE;cAAEiB,MAAM,EAAE;YAAI,CAAE;YACzBhB,UAAU,EAAE;cAAEC,QAAQ,EAAE,CAAC;cAAEgB,MAAM,EAAEC,QAAQ;cAAEC,IAAI,EAAE;YAAS;UAAE;YAAAzC,QAAA,EAAAY,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D;QAAC;UAAAd,QAAA,EAAAY,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACc,CAAC,GACjB1D,KAAK,gBACPxE,OAAA,CAACyD,UAAU;UAAA4E,QAAA,eACTrI,OAAA;YAAAqI,QAAA,EAAG;UAAwB;YAAAjB,QAAA,EAAAY,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAd,QAAA,EAAAY,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,GACX,CAAA5D,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEuB,MAAM,MAAK,CAAC,gBACzB7F,OAAA,CAACyD,UAAU;UAAA4E,QAAA,eACTrI,OAAA;YAAAqI,QAAA,EAAG;UAAmB;YAAAjB,QAAA,EAAAY,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAd,QAAA,EAAAY,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,gBAEblI,OAAA,CAACmD,SAAS;UAAAkF,QAAA,EACP/D,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE4E,GAAG,CAAC,CAAC5C,IAAI,EAAE8C,KAAK,kBAC1BpJ,OAAA,CAAClB,MAAM,CAACoB,GAAG;YAEToI,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEY,KAAK,EAAEF,KAAK,GAAG;YAAI,CAAE;YAAAf,QAAA,eAEnCrI,OAAA,CAACX,QAAQ;cACPiH,IAAI,EAAE;gBACJ,GAAGA,IAAI;gBACPwD,UAAU,EAAE;kBACVC,IAAI,EAAEzD,IAAI,CAAC0D,aAAa,IAAI;gBAC9B;cACF,CAAE;cACFC,MAAM,EAAE5D,cAAe;cACvB6D,UAAU,EAAEzD,kBAAmB;cAC/B0D,UAAU,EAAExC;YAAmB;cAAAP,QAAA,EAAAY,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC;UAAC,GAfG5B,IAAI,CAAClB,EAAE;YAAAgC,QAAA,EAAAY,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgBF,CACb;QAAC;UAAAd,QAAA,EAAAY,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CACZ;MAAA;QAAAd,QAAA,EAAAY,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAd,QAAA,EAAAY,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAd,QAAA,EAAAY,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEhB;AAACtE,EAAA,CA7PQD,gBAAgB;EAAA,QAKN9D,OAAO,EACJb,cAAc,EAGYD,QAAQ,EAiB1BA,QAAQ;AAAA;AAAAqL,IAAA,GA1B7BzG,gBAAgB;AA+PzB,eAAeA,gBAAgB;AAAC,IAAApD,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAQ,GAAA,EAAAM,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAK,GAAA,EAAAK,GAAA,EAAAE,GAAA,EAAAG,IAAA,EAAAI,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAA0G,IAAA;AAAAC,YAAA,CAAA9J,EAAA;AAAA8J,YAAA,CAAA5J,GAAA;AAAA4J,YAAA,CAAA1J,GAAA;AAAA0J,YAAA,CAAAxJ,GAAA;AAAAwJ,YAAA,CAAAhJ,GAAA;AAAAgJ,YAAA,CAAA1I,GAAA;AAAA0I,YAAA,CAAAxI,GAAA;AAAAwI,YAAA,CAAAtI,GAAA;AAAAsI,YAAA,CAAAjI,GAAA;AAAAiI,YAAA,CAAA5H,GAAA;AAAA4H,YAAA,CAAA1H,GAAA;AAAA0H,YAAA,CAAAvH,IAAA;AAAAuH,YAAA,CAAAnH,IAAA;AAAAmH,YAAA,CAAAjH,IAAA;AAAAiH,YAAA,CAAA/G,IAAA;AAAA+G,YAAA,CAAA7G,IAAA;AAAA6G,YAAA,CAAA3G,IAAA;AAAA2G,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}