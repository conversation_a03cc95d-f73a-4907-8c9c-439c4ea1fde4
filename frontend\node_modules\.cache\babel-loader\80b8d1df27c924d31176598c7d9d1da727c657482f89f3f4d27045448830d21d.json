{"ast": null, "code": "import { createAction, createListenerMiddleware } from '@reduxjs/toolkit';\nimport { setActiveMouseOverItemIndex, setMouseOverAxisIndex } from './tooltipSlice';\nimport { selectActivePropsFromChartPointer } from './selectors/selectActivePropsFromChartPointer';\nimport { getChartPointer } from '../util/getChartPointer';\nimport { selectTooltipEventType } from './selectors/selectTooltipEventType';\nimport { DATA_ITEM_DATAKEY_ATTRIBUTE_NAME, DATA_ITEM_INDEX_ATTRIBUTE_NAME } from '../util/Constants';\nimport { selectTooltipCoordinate } from './selectors/touchSelectors';\nexport var touchEventAction = createAction('touchMove');\nexport var touchEventMiddleware = createListenerMiddleware();\ntouchEventMiddleware.startListening({\n  actionCreator: touchEventAction,\n  effect: (action, listenerApi) => {\n    var touchEvent = action.payload;\n    var state = listenerApi.getState();\n    var tooltipEventType = selectTooltipEventType(state, state.tooltip.settings.shared);\n    if (tooltipEventType === 'axis') {\n      var activeProps = selectActivePropsFromChartPointer(state, getChartPointer({\n        clientX: touchEvent.touches[0].clientX,\n        clientY: touchEvent.touches[0].clientY,\n        currentTarget: touchEvent.currentTarget\n      }));\n      if ((activeProps === null || activeProps === void 0 ? void 0 : activeProps.activeIndex) != null) {\n        listenerApi.dispatch(setMouseOverAxisIndex({\n          activeIndex: activeProps.activeIndex,\n          activeDataKey: undefined,\n          activeCoordinate: activeProps.activeCoordinate\n        }));\n      }\n    } else if (tooltipEventType === 'item') {\n      var _target$getAttribute;\n      var touch = touchEvent.touches[0];\n      var target = document.elementFromPoint(touch.clientX, touch.clientY);\n      if (!target || !target.getAttribute) {\n        return;\n      }\n      var itemIndex = target.getAttribute(DATA_ITEM_INDEX_ATTRIBUTE_NAME);\n      var dataKey = (_target$getAttribute = target.getAttribute(DATA_ITEM_DATAKEY_ATTRIBUTE_NAME)) !== null && _target$getAttribute !== void 0 ? _target$getAttribute : undefined;\n      var coordinate = selectTooltipCoordinate(listenerApi.getState(), itemIndex, dataKey);\n      listenerApi.dispatch(setActiveMouseOverItemIndex({\n        activeDataKey: dataKey,\n        activeIndex: itemIndex,\n        activeCoordinate: coordinate\n      }));\n    }\n  }\n});", "map": {"version": 3, "names": ["createAction", "createListenerMiddleware", "setActiveMouseOverItemIndex", "setMouseOverAxisIndex", "selectActivePropsFromChartPointer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectTooltipEventType", "DATA_ITEM_DATAKEY_ATTRIBUTE_NAME", "DATA_ITEM_INDEX_ATTRIBUTE_NAME", "selectTooltipCoordinate", "touchEventAction", "touchEventMiddleware", "startListening", "actionCreator", "effect", "action", "listenerApi", "touchEvent", "payload", "state", "getState", "tooltipEventType", "tooltip", "settings", "shared", "activeProps", "clientX", "touches", "clientY", "currentTarget", "activeIndex", "dispatch", "activeDataKey", "undefined", "activeCoordinate", "_target$getAttribute", "touch", "target", "document", "elementFromPoint", "getAttribute", "itemIndex", "dataKey", "coordinate"], "sources": ["D:/menasa/frontend/node_modules/recharts/es6/state/touchEventsMiddleware.js"], "sourcesContent": ["import { createAction, createListenerMiddleware } from '@reduxjs/toolkit';\nimport { setActiveMouseOverItemIndex, setMouseOverAxisIndex } from './tooltipSlice';\nimport { selectActivePropsFromChartPointer } from './selectors/selectActivePropsFromChartPointer';\nimport { getChartPointer } from '../util/getChartPointer';\nimport { selectTooltipEventType } from './selectors/selectTooltipEventType';\nimport { DATA_ITEM_DATAKEY_ATTRIBUTE_NAME, DATA_ITEM_INDEX_ATTRIBUTE_NAME } from '../util/Constants';\nimport { selectTooltipCoordinate } from './selectors/touchSelectors';\nexport var touchEventAction = createAction('touchMove');\nexport var touchEventMiddleware = createListenerMiddleware();\ntouchEventMiddleware.startListening({\n  actionCreator: touchEventAction,\n  effect: (action, listenerApi) => {\n    var touchEvent = action.payload;\n    var state = listenerApi.getState();\n    var tooltipEventType = selectTooltipEventType(state, state.tooltip.settings.shared);\n    if (tooltipEventType === 'axis') {\n      var activeProps = selectActivePropsFromChartPointer(state, getChartPointer({\n        clientX: touchEvent.touches[0].clientX,\n        clientY: touchEvent.touches[0].clientY,\n        currentTarget: touchEvent.currentTarget\n      }));\n      if ((activeProps === null || activeProps === void 0 ? void 0 : activeProps.activeIndex) != null) {\n        listenerApi.dispatch(setMouseOverAxisIndex({\n          activeIndex: activeProps.activeIndex,\n          activeDataKey: undefined,\n          activeCoordinate: activeProps.activeCoordinate\n        }));\n      }\n    } else if (tooltipEventType === 'item') {\n      var _target$getAttribute;\n      var touch = touchEvent.touches[0];\n      var target = document.elementFromPoint(touch.clientX, touch.clientY);\n      if (!target || !target.getAttribute) {\n        return;\n      }\n      var itemIndex = target.getAttribute(DATA_ITEM_INDEX_ATTRIBUTE_NAME);\n      var dataKey = (_target$getAttribute = target.getAttribute(DATA_ITEM_DATAKEY_ATTRIBUTE_NAME)) !== null && _target$getAttribute !== void 0 ? _target$getAttribute : undefined;\n      var coordinate = selectTooltipCoordinate(listenerApi.getState(), itemIndex, dataKey);\n      listenerApi.dispatch(setActiveMouseOverItemIndex({\n        activeDataKey: dataKey,\n        activeIndex: itemIndex,\n        activeCoordinate: coordinate\n      }));\n    }\n  }\n});"], "mappings": "AAAA,SAASA,YAAY,EAAEC,wBAAwB,QAAQ,kBAAkB;AACzE,SAASC,2BAA2B,EAAEC,qBAAqB,QAAQ,gBAAgB;AACnF,SAASC,iCAAiC,QAAQ,+CAA+C;AACjG,SAASC,eAAe,QAAQ,yBAAyB;AACzD,SAASC,sBAAsB,QAAQ,oCAAoC;AAC3E,SAASC,gCAAgC,EAAEC,8BAA8B,QAAQ,mBAAmB;AACpG,SAASC,uBAAuB,QAAQ,4BAA4B;AACpE,OAAO,IAAIC,gBAAgB,GAAGV,YAAY,CAAC,WAAW,CAAC;AACvD,OAAO,IAAIW,oBAAoB,GAAGV,wBAAwB,CAAC,CAAC;AAC5DU,oBAAoB,CAACC,cAAc,CAAC;EAClCC,aAAa,EAAEH,gBAAgB;EAC/BI,MAAM,EAAEA,CAACC,MAAM,EAAEC,WAAW,KAAK;IAC/B,IAAIC,UAAU,GAAGF,MAAM,CAACG,OAAO;IAC/B,IAAIC,KAAK,GAAGH,WAAW,CAACI,QAAQ,CAAC,CAAC;IAClC,IAAIC,gBAAgB,GAAGf,sBAAsB,CAACa,KAAK,EAAEA,KAAK,CAACG,OAAO,CAACC,QAAQ,CAACC,MAAM,CAAC;IACnF,IAAIH,gBAAgB,KAAK,MAAM,EAAE;MAC/B,IAAII,WAAW,GAAGrB,iCAAiC,CAACe,KAAK,EAAEd,eAAe,CAAC;QACzEqB,OAAO,EAAET,UAAU,CAACU,OAAO,CAAC,CAAC,CAAC,CAACD,OAAO;QACtCE,OAAO,EAAEX,UAAU,CAACU,OAAO,CAAC,CAAC,CAAC,CAACC,OAAO;QACtCC,aAAa,EAAEZ,UAAU,CAACY;MAC5B,CAAC,CAAC,CAAC;MACH,IAAI,CAACJ,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACK,WAAW,KAAK,IAAI,EAAE;QAC/Fd,WAAW,CAACe,QAAQ,CAAC5B,qBAAqB,CAAC;UACzC2B,WAAW,EAAEL,WAAW,CAACK,WAAW;UACpCE,aAAa,EAAEC,SAAS;UACxBC,gBAAgB,EAAET,WAAW,CAACS;QAChC,CAAC,CAAC,CAAC;MACL;IACF,CAAC,MAAM,IAAIb,gBAAgB,KAAK,MAAM,EAAE;MACtC,IAAIc,oBAAoB;MACxB,IAAIC,KAAK,GAAGnB,UAAU,CAACU,OAAO,CAAC,CAAC,CAAC;MACjC,IAAIU,MAAM,GAAGC,QAAQ,CAACC,gBAAgB,CAACH,KAAK,CAACV,OAAO,EAAEU,KAAK,CAACR,OAAO,CAAC;MACpE,IAAI,CAACS,MAAM,IAAI,CAACA,MAAM,CAACG,YAAY,EAAE;QACnC;MACF;MACA,IAAIC,SAAS,GAAGJ,MAAM,CAACG,YAAY,CAAChC,8BAA8B,CAAC;MACnE,IAAIkC,OAAO,GAAG,CAACP,oBAAoB,GAAGE,MAAM,CAACG,YAAY,CAACjC,gCAAgC,CAAC,MAAM,IAAI,IAAI4B,oBAAoB,KAAK,KAAK,CAAC,GAAGA,oBAAoB,GAAGF,SAAS;MAC3K,IAAIU,UAAU,GAAGlC,uBAAuB,CAACO,WAAW,CAACI,QAAQ,CAAC,CAAC,EAAEqB,SAAS,EAAEC,OAAO,CAAC;MACpF1B,WAAW,CAACe,QAAQ,CAAC7B,2BAA2B,CAAC;QAC/C8B,aAAa,EAAEU,OAAO;QACtBZ,WAAW,EAAEW,SAAS;QACtBP,gBAAgB,EAAES;MACpB,CAAC,CAAC,CAAC;IACL;EACF;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}