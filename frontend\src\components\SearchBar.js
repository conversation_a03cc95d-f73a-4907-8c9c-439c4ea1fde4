import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { FiSearch, FiX, FiFilter } from 'react-icons/fi';
import { Input, Button } from './ui';

const SearchContainer = styled.div`
  position: relative;
  width: 100%;
  max-width: 500px;
`;

const SearchInputWrapper = styled.div`
  position: relative;
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
`;

const SearchInput = styled(Input)`
  flex: 1;
`;

const SearchSuggestions = styled(motion.div)`
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: ${({ theme }) => theme.colors.white};
  border: 1px solid ${({ theme }) => theme.colors.gray[200]};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  box-shadow: ${({ theme }) => theme.shadows.lg};
  margin-top: ${({ theme }) => theme.spacing[1]};
  max-height: 300px;
  overflow-y: auto;
  z-index: ${({ theme }) => theme.zIndex.dropdown};
`;

const SuggestionItem = styled(motion.div)`
  padding: ${({ theme }) => theme.spacing[3]} ${({ theme }) => theme.spacing[4]};
  cursor: pointer;
  border-bottom: 1px solid ${({ theme }) => theme.colors.gray[100]};
  transition: all ${({ theme }) => theme.transitions.fast};
  
  &:hover {
    background: ${({ theme }) => theme.colors.gray[50]};
  }
  
  &:last-child {
    border-bottom: none;
  }
`;

const SuggestionText = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.gray[700]};
  
  .highlight {
    background: ${({ theme }) => theme.colors.primary[100]};
    color: ${({ theme }) => theme.colors.primary[700]};
    font-weight: ${({ theme }) => theme.fontWeights.medium};
  }
`;

const SuggestionCategory = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.xs};
  color: ${({ theme }) => theme.colors.gray[500]};
  margin-top: ${({ theme }) => theme.spacing[1]};
`;

const RecentSearches = styled.div`
  padding: ${({ theme }) => theme.spacing[3]} ${({ theme }) => theme.spacing[4]};
  border-bottom: 1px solid ${({ theme }) => theme.colors.gray[100]};
`;

const RecentSearchTitle = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  color: ${({ theme }) => theme.colors.gray[700]};
  margin-bottom: ${({ theme }) => theme.spacing[2]};
`;

const RecentSearchItem = styled(motion.div)`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${({ theme }) => theme.spacing[1]} 0;
  cursor: pointer;
  
  &:hover {
    color: ${({ theme }) => theme.colors.primary[600]};
  }
`;

const ClearButton = styled(motion.button)`
  background: none;
  border: none;
  color: ${({ theme }) => theme.colors.gray[400]};
  cursor: pointer;
  padding: ${({ theme }) => theme.spacing[1]};
  border-radius: ${({ theme }) => theme.borderRadius.base};
  
  &:hover {
    color: ${({ theme }) => theme.colors.gray[600]};
    background: ${({ theme }) => theme.colors.gray[100]};
  }
`;

const SearchBar = ({ 
  value, 
  onChange, 
  onSearch, 
  placeholder = "البحث في الملفات...",
  suggestions = [],
  recentSearches = [],
  onClearRecent,
  showFilter = false,
  onFilterClick
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [localValue, setLocalValue] = useState(value || '');

  useEffect(() => {
    setLocalValue(value || '');
  }, [value]);

  const handleInputChange = (e) => {
    const newValue = e.target.value;
    setLocalValue(newValue);
    onChange?.(newValue);
    setShowSuggestions(newValue.length > 0 || recentSearches.length > 0);
  };

  const handleInputFocus = () => {
    setIsFocused(true);
    setShowSuggestions(localValue.length > 0 || recentSearches.length > 0);
  };

  const handleInputBlur = () => {
    // Delay hiding suggestions to allow clicking on them
    setTimeout(() => {
      setIsFocused(false);
      setShowSuggestions(false);
    }, 200);
  };

  const handleSuggestionClick = (suggestion) => {
    setLocalValue(suggestion.text);
    onChange?.(suggestion.text);
    onSearch?.(suggestion.text);
    setShowSuggestions(false);
  };

  const handleRecentSearchClick = (searchTerm) => {
    setLocalValue(searchTerm);
    onChange?.(searchTerm);
    onSearch?.(searchTerm);
    setShowSuggestions(false);
  };

  const handleClearInput = () => {
    setLocalValue('');
    onChange?.('');
    setShowSuggestions(false);
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      onSearch?.(localValue);
      setShowSuggestions(false);
    }
  };

  const highlightText = (text, query) => {
    if (!query) return text;
    
    const regex = new RegExp(`(${query})`, 'gi');
    return text.replace(regex, '<span class="highlight">$1</span>');
  };

  return (
    <SearchContainer>
      <SearchInputWrapper>
        <SearchInput
          value={localValue}
          onChange={handleInputChange}
          onFocus={handleInputFocus}
          onBlur={handleInputBlur}
          onKeyPress={handleKeyPress}
          placeholder={placeholder}
          leftIcon={<FiSearch size={18} />}
          rightIcon={
            localValue && (
              <ClearButton
                onClick={handleClearInput}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <FiX size={18} />
              </ClearButton>
            )
          }
        />
        
        {showFilter && (
          <Button
            variant="outline"
            size="md"
            leftIcon={<FiFilter size={18} />}
            onClick={onFilterClick}
          >
            فلترة
          </Button>
        )}
      </SearchInputWrapper>

      <AnimatePresence>
        {showSuggestions && (isFocused || showSuggestions) && (
          <SearchSuggestions
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
          >
            {recentSearches.length > 0 && localValue.length === 0 && (
              <RecentSearches>
                <RecentSearchTitle>عمليات البحث الأخيرة</RecentSearchTitle>
                {recentSearches.map((search, index) => (
                  <RecentSearchItem
                    key={index}
                    onClick={() => handleRecentSearchClick(search)}
                    whileHover={{ x: 5 }}
                  >
                    <span>{search}</span>
                    <ClearButton
                      onClick={(e) => {
                        e.stopPropagation();
                        onClearRecent?.(search);
                      }}
                    >
                      <FiX size={14} />
                    </ClearButton>
                  </RecentSearchItem>
                ))}
              </RecentSearches>
            )}
            
            {suggestions.map((suggestion, index) => (
              <SuggestionItem
                key={index}
                onClick={() => handleSuggestionClick(suggestion)}
                whileHover={{ x: 5 }}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: index * 0.05 }}
              >
                <SuggestionText
                  dangerouslySetInnerHTML={{
                    __html: highlightText(suggestion.text, localValue)
                  }}
                />
                {suggestion.category && (
                  <SuggestionCategory>{suggestion.category}</SuggestionCategory>
                )}
              </SuggestionItem>
            ))}
          </SearchSuggestions>
        )}
      </AnimatePresence>
    </SearchContainer>
  );
};

export default SearchBar;
