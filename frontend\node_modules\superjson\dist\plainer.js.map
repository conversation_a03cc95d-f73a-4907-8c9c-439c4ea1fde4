{"version": 3, "file": "plainer.js", "sourceRoot": "", "sources": ["../src/plainer.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA,2BAOc;AACd,qDAA6D;AAC7D,6CAKuB;AACvB,+BAA2C;AAC3C,qDAA8C;AAC9C,2CAAgD;AAShD,SAAS,QAAQ,CACf,IAAsB,EACtB,MAAsC,EACtC,MAAqB;IAArB,uBAAA,EAAA,WAAqB;IAErB,IAAI,CAAC,IAAI,EAAE;QACT,OAAO;KACR;IAED,IAAI,CAAC,YAAO,CAAC,IAAI,CAAC,EAAE;QAClB,cAAO,CAAC,IAAI,EAAE,UAAC,OAAO,EAAE,GAAG;YACzB,OAAA,QAAQ,CAAC,OAAO,EAAE,MAAM,yCAAM,MAAM,WAAK,2BAAS,CAAC,GAAG,CAAC,GAAE;QAAzD,CAAyD,CAC1D,CAAC;QACF,OAAO;KACR;IAEK,IAAA,KAAA,OAAwB,IAAI,IAAA,EAA3B,SAAS,QAAA,EAAE,QAAQ,QAAQ,CAAC;IACnC,IAAI,QAAQ,EAAE;QACZ,cAAO,CAAC,QAAQ,EAAE,UAAC,KAAK,EAAE,GAAG;YAC3B,QAAQ,CAAC,KAAK,EAAE,MAAM,yCAAM,MAAM,WAAK,2BAAS,CAAC,GAAG,CAAC,GAAE,CAAC;QAC1D,CAAC,CAAC,CAAC;KACJ;IAED,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;AAC5B,CAAC;AAED,SAAgB,qBAAqB,CACnC,KAAU,EACV,WAA0C,EAC1C,SAAoB;IAEpB,QAAQ,CAAC,WAAW,EAAE,UAAC,IAAI,EAAE,IAAI;QAC/B,KAAK,GAAG,oBAAO,CAAC,KAAK,EAAE,IAAI,EAAE,UAAA,CAAC,IAAI,OAAA,8BAAgB,CAAC,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,EAApC,CAAoC,CAAC,CAAC;IAC1E,CAAC,CAAC,CAAC;IAEH,OAAO,KAAK,CAAC;AACf,CAAC;AAVD,sDAUC;AAED,SAAgB,mCAAmC,CACjD,KAAU,EACV,WAA2C;IAE3C,SAAS,KAAK,CAAC,cAAwB,EAAE,IAAY;QACnD,IAAM,MAAM,GAAG,oBAAO,CAAC,KAAK,EAAE,2BAAS,CAAC,IAAI,CAAC,CAAC,CAAC;QAE/C,cAAc,CAAC,GAAG,CAAC,2BAAS,CAAC,CAAC,OAAO,CAAC,UAAA,mBAAmB;YACvD,KAAK,GAAG,oBAAO,CAAC,KAAK,EAAE,mBAAmB,EAAE,cAAM,OAAA,MAAM,EAAN,CAAM,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;IACL,CAAC;IAED,IAAI,YAAO,CAAC,WAAW,CAAC,EAAE;QAClB,IAAA,KAAA,OAAgB,WAAW,IAAA,EAA1B,IAAI,QAAA,EAAE,KAAK,QAAe,CAAC;QAClC,IAAI,CAAC,OAAO,CAAC,UAAA,aAAa;YACxB,KAAK,GAAG,oBAAO,CAAC,KAAK,EAAE,2BAAS,CAAC,aAAa,CAAC,EAAE,cAAM,OAAA,KAAK,EAAL,CAAK,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,IAAI,KAAK,EAAE;YACT,cAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;SACvB;KACF;SAAM;QACL,cAAO,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;KAC7B;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AA1BD,kFA0BC;AAED,IAAM,MAAM,GAAG,UAAC,MAAW,EAAE,SAAoB;IAC/C,OAAA,kBAAa,CAAC,MAAM,CAAC;QACrB,YAAO,CAAC,MAAM,CAAC;QACf,UAAK,CAAC,MAAM,CAAC;QACb,UAAK,CAAC,MAAM,CAAC;QACb,yCAA2B,CAAC,MAAM,EAAE,SAAS,CAAC;AAJ9C,CAI8C,CAAC;AAEjD,SAAS,WAAW,CAAC,MAAW,EAAE,IAAW,EAAE,UAA6B;IAC1E,IAAM,WAAW,GAAG,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IAE3C,IAAI,WAAW,EAAE;QACf,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KACxB;SAAM;QACL,UAAU,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;KAChC;AACH,CAAC;AAYD,SAAgB,sCAAsC,CACpD,WAA8B,EAC9B,MAAe;IAEf,IAAM,MAAM,GAA6B,EAAE,CAAC;IAC5C,IAAI,iBAAiB,GAAyB,SAAS,CAAC;IAExD,WAAW,CAAC,OAAO,CAAC,UAAA,KAAK;QACvB,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,EAAE;YACrB,OAAO;SACR;QAED,iEAAiE;QACjE,sEAAsE;QACtE,qGAAqG;QACrG,IAAI,CAAC,MAAM,EAAE;YACX,KAAK,GAAG,KAAK;iBACV,GAAG,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,EAAhB,CAAgB,CAAC;iBAC7B,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,EAAnB,CAAmB,CAAC,CAAC;SACxC;QAEK,IAAA,KAAA,OAA0C,KAAK,CAAA,EAA9C,kBAAkB,QAAA,EAAK,cAAc,cAAS,CAAC;QAEtD,IAAI,kBAAkB,CAAC,MAAM,KAAK,CAAC,EAAE;YACnC,iBAAiB,GAAG,cAAc,CAAC,GAAG,CAAC,+BAAa,CAAC,CAAC;SACvD;aAAM;YACL,MAAM,CAAC,+BAAa,CAAC,kBAAkB,CAAC,CAAC,GAAG,cAAc,CAAC,GAAG,CAC5D,+BAAa,CACd,CAAC;SACH;IACH,CAAC,CAAC,CAAC;IAEH,IAAI,iBAAiB,EAAE;QACrB,IAAI,kBAAa,CAAC,MAAM,CAAC,EAAE;YACzB,OAAO,CAAC,iBAAiB,CAAC,CAAC;SAC5B;aAAM;YACL,OAAO,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC;SACpC;KACF;SAAM;QACL,OAAO,kBAAa,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC;KACnD;AACH,CAAC;AAzCD,wFAyCC;AAEM,IAAM,MAAM,GAAG,UACpB,MAAW,EACX,UAA6B,EAC7B,SAAoB,EACpB,MAAe,EACf,IAAgB,EAChB,iBAA6B,EAC7B,WAAwC;;IAFxC,qBAAA,EAAA,SAAgB;IAChB,kCAAA,EAAA,sBAA6B;IAC7B,4BAAA,EAAA,kBAAkB,GAAG,EAAmB;IAExC,IAAM,SAAS,GAAG,gBAAW,CAAC,MAAM,CAAC,CAAC;IAEtC,IAAI,CAAC,SAAS,EAAE;QACd,WAAW,CAAC,MAAM,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;QAEtC,IAAM,IAAI,GAAG,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACrC,IAAI,IAAI,EAAE;YACR,wDAAwD;YACxD,OAAO,MAAM;gBACX,CAAC,CAAC;oBACE,gBAAgB,EAAE,IAAI;iBACvB;gBACH,CAAC,CAAC,IAAI,CAAC;SACV;KACF;IAED,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,EAAE;QAC9B,IAAM,aAAW,GAAG,4BAAc,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;QAEtD,IAAM,QAAM,GAAW,aAAW;YAChC,CAAC,CAAC;gBACE,gBAAgB,EAAE,aAAW,CAAC,KAAK;gBACnC,WAAW,EAAE,CAAC,aAAW,CAAC,IAAI,CAAC;aAChC;YACH,CAAC,CAAC;gBACE,gBAAgB,EAAE,MAAM;aACzB,CAAC;QACN,IAAI,CAAC,SAAS,EAAE;YACd,WAAW,CAAC,GAAG,CAAC,MAAM,EAAE,QAAM,CAAC,CAAC;SACjC;QACD,OAAO,QAAM,CAAC;KACf;IAED,IAAI,eAAQ,CAAC,iBAAiB,EAAE,MAAM,CAAC,EAAE;QACvC,8BAA8B;QAC9B,OAAO;YACL,gBAAgB,EAAE,IAAI;SACvB,CAAC;KACH;IAED,IAAM,oBAAoB,GAAG,4BAAc,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;IAC/D,IAAM,WAAW,GAAG,MAAA,oBAAoB,aAApB,oBAAoB,uBAApB,oBAAoB,CAAE,KAAK,mCAAI,MAAM,CAAC;IAE1D,IAAM,gBAAgB,GAAQ,YAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IAC7D,IAAM,gBAAgB,GAAyC,EAAE,CAAC;IAElE,cAAO,CAAC,WAAW,EAAE,UAAC,KAAK,EAAE,KAAK;QAChC,IAAM,eAAe,GAAG,cAAM,CAC5B,KAAK,EACL,UAAU,EACV,SAAS,EACT,MAAM,yCACF,IAAI,KAAE,KAAK,2CACX,iBAAiB,KAAE,MAAM,IAC7B,WAAW,CACZ,CAAC;QAEF,gBAAgB,CAAC,KAAK,CAAC,GAAG,eAAe,CAAC,gBAAgB,CAAC;QAE3D,IAAI,YAAO,CAAC,eAAe,CAAC,WAAW,CAAC,EAAE;YACxC,gBAAgB,CAAC,KAAK,CAAC,GAAG,eAAe,CAAC,WAAW,CAAC;SACvD;aAAM,IAAI,kBAAa,CAAC,eAAe,CAAC,WAAW,CAAC,EAAE;YACrD,cAAO,CAAC,eAAe,CAAC,WAAW,EAAE,UAAC,IAAI,EAAE,GAAG;gBAC7C,gBAAgB,CAAC,2BAAS,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC;YACxD,CAAC,CAAC,CAAC;SACJ;IACH,CAAC,CAAC,CAAC;IAEH,IAAM,MAAM,GAAW,kBAAa,CAAC,gBAAgB,CAAC;QACpD,CAAC,CAAC;YACE,gBAAgB,kBAAA;YAChB,WAAW,EAAE,CAAC,CAAC,oBAAoB;gBACjC,CAAC,CAAC,CAAC,oBAAoB,CAAC,IAAI,CAAC;gBAC7B,CAAC,CAAC,SAAS;SACd;QACH,CAAC,CAAC;YACE,gBAAgB,kBAAA;YAChB,WAAW,EAAE,CAAC,CAAC,oBAAoB;gBACjC,CAAC,CAAC,CAAC,oBAAoB,CAAC,IAAI,EAAE,gBAAgB,CAAC;gBAC/C,CAAC,CAAC,gBAAgB;SACrB,CAAC;IACN,IAAI,CAAC,SAAS,EAAE;QACd,WAAW,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;KACjC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC;AA/FW,QAAA,MAAM,UA+FjB"}