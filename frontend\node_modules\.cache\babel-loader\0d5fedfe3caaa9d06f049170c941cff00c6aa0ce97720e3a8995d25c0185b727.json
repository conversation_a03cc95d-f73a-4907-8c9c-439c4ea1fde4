{"ast": null, "code": "import { createAction, createListenerMiddleware } from '@reduxjs/toolkit';\nimport { mouseLeaveChart, setMouseClickAxisIndex, setMouseOverAxisIndex } from './tooltipSlice';\nimport { selectActivePropsFromChartPointer } from './selectors/selectActivePropsFromChartPointer';\nimport { selectTooltipEventType } from './selectors/selectTooltipEventType';\nimport { getChartPointer } from '../util/getChartPointer';\nexport var mouseClickAction = createAction('mouseClick');\nexport var mouseClickMiddleware = createListenerMiddleware();\n\n// TODO: there's a bug here when you click the chart the activeIndex resets to zero\nmouseClickMiddleware.startListening({\n  actionCreator: mouseClickAction,\n  effect: (action, listenerApi) => {\n    var mousePointer = action.payload;\n    var activeProps = selectActivePropsFromChartPointer(listenerApi.getState(), getChartPointer(mousePointer));\n    if ((activeProps === null || activeProps === void 0 ? void 0 : activeProps.activeIndex) != null) {\n      listenerApi.dispatch(setMouseClickAxisIndex({\n        activeIndex: activeProps.activeIndex,\n        activeDataKey: undefined,\n        activeCoordinate: activeProps.activeCoordinate\n      }));\n    }\n  }\n});\nexport var mouseMoveAction = createAction('mouseMove');\nexport var mouseMoveMiddleware = createListenerMiddleware();\nmouseMoveMiddleware.startListening({\n  actionCreator: mouseMoveAction,\n  effect: (action, listenerApi) => {\n    var mousePointer = action.payload;\n    var state = listenerApi.getState();\n    var tooltipEventType = selectTooltipEventType(state, state.tooltip.settings.shared);\n    var activeProps = selectActivePropsFromChartPointer(state, getChartPointer(mousePointer));\n\n    // this functionality only applies to charts that have axes\n    if (tooltipEventType === 'axis') {\n      if ((activeProps === null || activeProps === void 0 ? void 0 : activeProps.activeIndex) != null) {\n        listenerApi.dispatch(setMouseOverAxisIndex({\n          activeIndex: activeProps.activeIndex,\n          activeDataKey: undefined,\n          activeCoordinate: activeProps.activeCoordinate\n        }));\n      } else {\n        // this is needed to clear tooltip state when the mouse moves out of the inRange (svg - offset) function, but not yet out of the svg\n        listenerApi.dispatch(mouseLeaveChart());\n      }\n    }\n  }\n});", "map": {"version": 3, "names": ["createAction", "createListenerMiddleware", "mouseLeaveChart", "setMouseClickAxisIndex", "setMouseOverAxisIndex", "selectActivePropsFromChartPointer", "selectTooltipEventType", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mouseClickAction", "mouseClickMiddleware", "startListening", "actionCreator", "effect", "action", "listenerApi", "mousePointer", "payload", "activeProps", "getState", "activeIndex", "dispatch", "activeDataKey", "undefined", "activeCoordinate", "mouseMoveAction", "mouseMoveMiddleware", "state", "tooltipEventType", "tooltip", "settings", "shared"], "sources": ["D:/menasa/frontend/node_modules/recharts/es6/state/mouseEventsMiddleware.js"], "sourcesContent": ["import { createAction, createListenerMiddleware } from '@reduxjs/toolkit';\nimport { mouseLeaveChart, setMouseClickAxisIndex, setMouseOverAxisIndex } from './tooltipSlice';\nimport { selectActivePropsFromChartPointer } from './selectors/selectActivePropsFromChartPointer';\nimport { selectTooltipEventType } from './selectors/selectTooltipEventType';\nimport { getChartPointer } from '../util/getChartPointer';\nexport var mouseClickAction = createAction('mouseClick');\nexport var mouseClickMiddleware = createListenerMiddleware();\n\n// TODO: there's a bug here when you click the chart the activeIndex resets to zero\nmouseClickMiddleware.startListening({\n  actionCreator: mouseClickAction,\n  effect: (action, listenerApi) => {\n    var mousePointer = action.payload;\n    var activeProps = selectActivePropsFromChartPointer(listenerApi.getState(), getChartPointer(mousePointer));\n    if ((activeProps === null || activeProps === void 0 ? void 0 : activeProps.activeIndex) != null) {\n      listenerApi.dispatch(setMouseClickAxisIndex({\n        activeIndex: activeProps.activeIndex,\n        activeDataKey: undefined,\n        activeCoordinate: activeProps.activeCoordinate\n      }));\n    }\n  }\n});\nexport var mouseMoveAction = createAction('mouseMove');\nexport var mouseMoveMiddleware = createListenerMiddleware();\nmouseMoveMiddleware.startListening({\n  actionCreator: mouseMoveAction,\n  effect: (action, listenerApi) => {\n    var mousePointer = action.payload;\n    var state = listenerApi.getState();\n    var tooltipEventType = selectTooltipEventType(state, state.tooltip.settings.shared);\n    var activeProps = selectActivePropsFromChartPointer(state, getChartPointer(mousePointer));\n\n    // this functionality only applies to charts that have axes\n    if (tooltipEventType === 'axis') {\n      if ((activeProps === null || activeProps === void 0 ? void 0 : activeProps.activeIndex) != null) {\n        listenerApi.dispatch(setMouseOverAxisIndex({\n          activeIndex: activeProps.activeIndex,\n          activeDataKey: undefined,\n          activeCoordinate: activeProps.activeCoordinate\n        }));\n      } else {\n        // this is needed to clear tooltip state when the mouse moves out of the inRange (svg - offset) function, but not yet out of the svg\n        listenerApi.dispatch(mouseLeaveChart());\n      }\n    }\n  }\n});"], "mappings": "AAAA,SAASA,YAAY,EAAEC,wBAAwB,QAAQ,kBAAkB;AACzE,SAASC,eAAe,EAAEC,sBAAsB,EAAEC,qBAAqB,QAAQ,gBAAgB;AAC/F,SAASC,iCAAiC,QAAQ,+CAA+C;AACjG,SAASC,sBAAsB,QAAQ,oCAAoC;AAC3E,SAASC,eAAe,QAAQ,yBAAyB;AACzD,OAAO,IAAIC,gBAAgB,GAAGR,YAAY,CAAC,YAAY,CAAC;AACxD,OAAO,IAAIS,oBAAoB,GAAGR,wBAAwB,CAAC,CAAC;;AAE5D;AACAQ,oBAAoB,CAACC,cAAc,CAAC;EAClCC,aAAa,EAAEH,gBAAgB;EAC/BI,MAAM,EAAEA,CAACC,MAAM,EAAEC,WAAW,KAAK;IAC/B,IAAIC,YAAY,GAAGF,MAAM,CAACG,OAAO;IACjC,IAAIC,WAAW,GAAGZ,iCAAiC,CAACS,WAAW,CAACI,QAAQ,CAAC,CAAC,EAAEX,eAAe,CAACQ,YAAY,CAAC,CAAC;IAC1G,IAAI,CAACE,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACE,WAAW,KAAK,IAAI,EAAE;MAC/FL,WAAW,CAACM,QAAQ,CAACjB,sBAAsB,CAAC;QAC1CgB,WAAW,EAAEF,WAAW,CAACE,WAAW;QACpCE,aAAa,EAAEC,SAAS;QACxBC,gBAAgB,EAAEN,WAAW,CAACM;MAChC,CAAC,CAAC,CAAC;IACL;EACF;AACF,CAAC,CAAC;AACF,OAAO,IAAIC,eAAe,GAAGxB,YAAY,CAAC,WAAW,CAAC;AACtD,OAAO,IAAIyB,mBAAmB,GAAGxB,wBAAwB,CAAC,CAAC;AAC3DwB,mBAAmB,CAACf,cAAc,CAAC;EACjCC,aAAa,EAAEa,eAAe;EAC9BZ,MAAM,EAAEA,CAACC,MAAM,EAAEC,WAAW,KAAK;IAC/B,IAAIC,YAAY,GAAGF,MAAM,CAACG,OAAO;IACjC,IAAIU,KAAK,GAAGZ,WAAW,CAACI,QAAQ,CAAC,CAAC;IAClC,IAAIS,gBAAgB,GAAGrB,sBAAsB,CAACoB,KAAK,EAAEA,KAAK,CAACE,OAAO,CAACC,QAAQ,CAACC,MAAM,CAAC;IACnF,IAAIb,WAAW,GAAGZ,iCAAiC,CAACqB,KAAK,EAAEnB,eAAe,CAACQ,YAAY,CAAC,CAAC;;IAEzF;IACA,IAAIY,gBAAgB,KAAK,MAAM,EAAE;MAC/B,IAAI,CAACV,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACE,WAAW,KAAK,IAAI,EAAE;QAC/FL,WAAW,CAACM,QAAQ,CAAChB,qBAAqB,CAAC;UACzCe,WAAW,EAAEF,WAAW,CAACE,WAAW;UACpCE,aAAa,EAAEC,SAAS;UACxBC,gBAAgB,EAAEN,WAAW,CAACM;QAChC,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACL;QACAT,WAAW,CAACM,QAAQ,CAAClB,eAAe,CAAC,CAAC,CAAC;MACzC;IACF;EACF;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}