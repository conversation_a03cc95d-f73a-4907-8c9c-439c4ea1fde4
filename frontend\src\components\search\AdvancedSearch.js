import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  FiSearch, 
  FiFilter, 
  FiX, 
  FiCalendar,
  FiStar,
  FiDownload,
  FiUser,
  FiFileText,
  FiSliders
} from 'react-icons/fi';
import { Button, Input } from '../ui';

const SearchContainer = styled.div`
  position: relative;
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
`;

const SearchInputContainer = styled.div`
  position: relative;
  display: flex;
  align-items: center;
  background: white;
  border: 2px solid ${({ theme, focused }) => 
    focused ? theme.colors.primary[500] : theme.colors.gray[300]};
  border-radius: ${({ theme }) => theme.borderRadius.xl};
  padding: ${({ theme }) => theme.spacing[3]};
  transition: all 0.2s ease;
  box-shadow: ${({ focused, theme }) => 
    focused ? theme.shadows.lg : theme.shadows.sm};
`;

const SearchIcon = styled.div`
  color: ${({ theme }) => theme.colors.gray[400]};
  margin-left: ${({ theme }) => theme.spacing[2]};
`;

const SearchInput = styled.input`
  flex: 1;
  border: none;
  outline: none;
  font-size: ${({ theme }) => theme.fontSizes.lg};
  padding: ${({ theme }) => theme.spacing[2]};
  background: transparent;
  color: ${({ theme }) => theme.colors.gray[900]};
  
  &::placeholder {
    color: ${({ theme }) => theme.colors.gray[400]};
  }
`;

const FilterButton = styled(motion.button)`
  background: ${({ active, theme }) => 
    active ? theme.colors.primary[500] : theme.colors.gray[100]};
  color: ${({ active, theme }) => 
    active ? 'white' : theme.colors.gray[600]};
  border: none;
  padding: ${({ theme }) => theme.spacing[2]} ${({ theme }) => theme.spacing[3]};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  transition: all 0.2s ease;
  margin-right: ${({ theme }) => theme.spacing[2]};
`;

const FiltersPanel = styled(motion.div)`
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border-radius: ${({ theme }) => theme.borderRadius.xl};
  box-shadow: ${({ theme }) => theme.shadows.xl};
  border: 1px solid ${({ theme }) => theme.colors.gray[200]};
  padding: ${({ theme }) => theme.spacing[6]};
  margin-top: ${({ theme }) => theme.spacing[2]};
  z-index: 100;
`;

const FilterSection = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing[6]};
  
  &:last-child {
    margin-bottom: 0;
  }
`;

const FilterLabel = styled.h4`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  color: ${({ theme }) => theme.colors.gray[700]};
  margin-bottom: ${({ theme }) => theme.spacing[3]};
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
`;

const FilterOptions = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: ${({ theme }) => theme.spacing[2]};
`;

const FilterOption = styled(motion.button)`
  background: ${({ selected, theme }) => 
    selected ? theme.colors.primary[100] : theme.colors.gray[50]};
  color: ${({ selected, theme }) => 
    selected ? theme.colors.primary[700] : theme.colors.gray[600]};
  border: 1px solid ${({ selected, theme }) => 
    selected ? theme.colors.primary[300] : theme.colors.gray[200]};
  padding: ${({ theme }) => theme.spacing[2]} ${({ theme }) => theme.spacing[3]};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  cursor: pointer;
  font-size: ${({ theme }) => theme.fontSizes.sm};
  transition: all 0.2s ease;
  
  &:hover {
    background: ${({ selected, theme }) => 
      selected ? theme.colors.primary[200] : theme.colors.gray[100]};
  }
`;

const RangeContainer = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing[3]};
  align-items: center;
`;

const RangeInput = styled.input`
  flex: 1;
  padding: ${({ theme }) => theme.spacing[2]};
  border: 1px solid ${({ theme }) => theme.colors.gray[300]};
  border-radius: ${({ theme }) => theme.borderRadius.base};
  font-size: ${({ theme }) => theme.fontSizes.sm};
`;

const SortContainer = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing[2]};
  align-items: center;
  margin-top: ${({ theme }) => theme.spacing[4]};
  padding-top: ${({ theme }) => theme.spacing[4]};
  border-top: 1px solid ${({ theme }) => theme.colors.gray[200]};
`;

const SortSelect = styled.select`
  padding: ${({ theme }) => theme.spacing[2]} ${({ theme }) => theme.spacing[3]};
  border: 1px solid ${({ theme }) => theme.colors.gray[300]};
  border-radius: ${({ theme }) => theme.borderRadius.base};
  background: white;
  font-size: ${({ theme }) => theme.fontSizes.sm};
  cursor: pointer;
`;

const ActiveFiltersContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: ${({ theme }) => theme.spacing[2]};
  margin-top: ${({ theme }) => theme.spacing[3]};
`;

const ActiveFilter = styled(motion.div)`
  background: ${({ theme }) => theme.colors.primary[100]};
  color: ${({ theme }) => theme.colors.primary[700]};
  padding: ${({ theme }) => theme.spacing[1]} ${({ theme }) => theme.spacing[2]};
  border-radius: ${({ theme }) => theme.borderRadius.base};
  font-size: ${({ theme }) => theme.fontSizes.xs};
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[1]};
`;

const RemoveFilterButton = styled.button`
  background: none;
  border: none;
  color: ${({ theme }) => theme.colors.primary[600]};
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  
  &:hover {
    color: ${({ theme }) => theme.colors.primary[800]};
  }
`;

const AdvancedSearch = ({ 
  onSearch, 
  onFiltersChange, 
  placeholder = "ابحث في الملفات...",
  initialFilters = {}
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [focused, setFocused] = useState(false);
  const [filters, setFilters] = useState({
    subjects: [],
    semesters: [],
    fileTypes: [],
    ratingRange: [1, 5],
    dateRange: { from: '', to: '' },
    sortBy: 'relevance',
    sortOrder: 'desc',
    ...initialFilters
  });

  const subjects = [
    { value: 'math', label: 'الرياضيات' },
    { value: 'science', label: 'العلوم' },
    { value: 'arabic', label: 'اللغة العربية' },
    { value: 'english', label: 'اللغة الإنجليزية' },
    { value: 'history', label: 'التاريخ' },
    { value: 'geography', label: 'الجغرافيا' }
  ];

  const semesters = [
    { value: 'first', label: 'الفصل الأول' },
    { value: 'second', label: 'الفصل الثاني' },
    { value: 'summer', label: 'الفصل الصيفي' }
  ];

  const fileTypes = [
    { value: 'pdf', label: 'PDF' },
    { value: 'docx', label: 'Word' },
    { value: 'pptx', label: 'PowerPoint' },
    { value: 'mp4', label: 'فيديو' },
    { value: 'mp3', label: 'صوت' }
  ];

  const sortOptions = [
    { value: 'relevance', label: 'الأكثر صلة' },
    { value: 'date', label: 'الأحدث' },
    { value: 'downloads', label: 'الأكثر تحميلاً' },
    { value: 'rating', label: 'الأعلى تقييماً' },
    { value: 'title', label: 'الاسم' }
  ];

  useEffect(() => {
    onFiltersChange?.(filters);
  }, [filters, onFiltersChange]);

  const handleSearch = (e) => {
    e.preventDefault();
    onSearch?.(searchTerm, filters);
  };

  const handleFilterToggle = (filterType, value) => {
    setFilters(prev => ({
      ...prev,
      [filterType]: prev[filterType].includes(value)
        ? prev[filterType].filter(item => item !== value)
        : [...prev[filterType], value]
    }));
  };

  const handleRangeChange = (type, field, value) => {
    setFilters(prev => ({
      ...prev,
      [type]: {
        ...prev[type],
        [field]: value
      }
    }));
  };

  const clearFilters = () => {
    setFilters({
      subjects: [],
      semesters: [],
      fileTypes: [],
      ratingRange: [1, 5],
      dateRange: { from: '', to: '' },
      sortBy: 'relevance',
      sortOrder: 'desc'
    });
  };

  const getActiveFiltersCount = () => {
    return filters.subjects.length + 
           filters.semesters.length + 
           filters.fileTypes.length +
           (filters.dateRange.from || filters.dateRange.to ? 1 : 0) +
           (filters.ratingRange[0] !== 1 || filters.ratingRange[1] !== 5 ? 1 : 0);
  };

  const removeFilter = (filterType, value) => {
    if (filterType === 'dateRange') {
      setFilters(prev => ({
        ...prev,
        dateRange: { from: '', to: '' }
      }));
    } else if (filterType === 'ratingRange') {
      setFilters(prev => ({
        ...prev,
        ratingRange: [1, 5]
      }));
    } else {
      setFilters(prev => ({
        ...prev,
        [filterType]: prev[filterType].filter(item => item !== value)
      }));
    }
  };

  return (
    <SearchContainer>
      <form onSubmit={handleSearch}>
        <SearchInputContainer focused={focused}>
          <SearchIcon>
            <FiSearch size={20} />
          </SearchIcon>
          <SearchInput
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            onFocus={() => setFocused(true)}
            onBlur={() => setFocused(false)}
            placeholder={placeholder}
          />
          <FilterButton
            type="button"
            active={showFilters}
            onClick={() => setShowFilters(!showFilters)}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <FiSliders size={16} />
            فلاتر
            {getActiveFiltersCount() > 0 && (
              <span style={{ 
                background: 'white', 
                color: showFilters ? '#3B82F6' : '#6B7280',
                borderRadius: '50%',
                width: '20px',
                height: '20px',
                fontSize: '12px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                {getActiveFiltersCount()}
              </span>
            )}
          </FilterButton>
        </SearchInputContainer>
      </form>

      <AnimatePresence>
        {showFilters && (
          <FiltersPanel
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
          >
            <FilterSection>
              <FilterLabel>
                <FiFileText size={16} />
                المواد الدراسية
              </FilterLabel>
              <FilterOptions>
                {subjects.map(subject => (
                  <FilterOption
                    key={subject.value}
                    selected={filters.subjects.includes(subject.value)}
                    onClick={() => handleFilterToggle('subjects', subject.value)}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    {subject.label}
                  </FilterOption>
                ))}
              </FilterOptions>
            </FilterSection>

            <FilterSection>
              <FilterLabel>
                <FiCalendar size={16} />
                الفصل الدراسي
              </FilterLabel>
              <FilterOptions>
                {semesters.map(semester => (
                  <FilterOption
                    key={semester.value}
                    selected={filters.semesters.includes(semester.value)}
                    onClick={() => handleFilterToggle('semesters', semester.value)}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    {semester.label}
                  </FilterOption>
                ))}
              </FilterOptions>
            </FilterSection>

            <FilterSection>
              <FilterLabel>
                <FiFileText size={16} />
                نوع الملف
              </FilterLabel>
              <FilterOptions>
                {fileTypes.map(type => (
                  <FilterOption
                    key={type.value}
                    selected={filters.fileTypes.includes(type.value)}
                    onClick={() => handleFilterToggle('fileTypes', type.value)}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    {type.label}
                  </FilterOption>
                ))}
              </FilterOptions>
            </FilterSection>

            <SortContainer>
              <FilterLabel style={{ margin: 0 }}>
                <FiSliders size={16} />
                ترتيب النتائج:
              </FilterLabel>
              <SortSelect
                value={filters.sortBy}
                onChange={(e) => setFilters(prev => ({ ...prev, sortBy: e.target.value }))}
              >
                {sortOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </SortSelect>
              
              <Button
                variant="outline"
                size="sm"
                onClick={clearFilters}
              >
                مسح الفلاتر
              </Button>
            </SortContainer>
          </FiltersPanel>
        )}
      </AnimatePresence>

      {getActiveFiltersCount() > 0 && (
        <ActiveFiltersContainer>
          {filters.subjects.map(subject => (
            <ActiveFilter
              key={`subject-${subject}`}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
            >
              {subjects.find(s => s.value === subject)?.label}
              <RemoveFilterButton onClick={() => removeFilter('subjects', subject)}>
                <FiX size={12} />
              </RemoveFilterButton>
            </ActiveFilter>
          ))}
          
          {filters.semesters.map(semester => (
            <ActiveFilter
              key={`semester-${semester}`}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
            >
              {semesters.find(s => s.value === semester)?.label}
              <RemoveFilterButton onClick={() => removeFilter('semesters', semester)}>
                <FiX size={12} />
              </RemoveFilterButton>
            </ActiveFilter>
          ))}
          
          {filters.fileTypes.map(type => (
            <ActiveFilter
              key={`type-${type}`}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
            >
              {fileTypes.find(t => t.value === type)?.label}
              <RemoveFilterButton onClick={() => removeFilter('fileTypes', type)}>
                <FiX size={12} />
              </RemoveFilterButton>
            </ActiveFilter>
          ))}
        </ActiveFiltersContainer>
      )}
    </SearchContainer>
  );
};

export default AdvancedSearch;
