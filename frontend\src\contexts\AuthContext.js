import React, { createContext, useContext, useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import { auth } from '../services/api';
import { securityManager, trackLoginAttempt } from '../utils/security';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [session, setSession] = useState(null);

  useEffect(() => {
    // الحصول على الجلسة الحالية
    const getInitialSession = async () => {
      try {
        // التحقق من انتهاء الجلسة
        if (securityManager.checkSessionTimeout()) {
          await logout();
          return;
        }

        const { data: { session }, error } = await supabase.auth.getSession();
        if (error) throw error;

        if (session) {
          await handleAuthStateChange(session);
        }
      } catch (error) {
        console.error('Error getting initial session:', error);
      } finally {
        setLoading(false);
      }
    };

    getInitialSession();

    // الاستماع لتغييرات حالة المصادقة
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session);

        if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {
          await handleAuthStateChange(session);
        } else if (event === 'SIGNED_OUT') {
          handleSignOut();
        }

        setLoading(false);
      }
    );

    return () => {
      subscription?.unsubscribe();
    };
  }, []);

  const handleAuthStateChange = async (session) => {
    try {
      setSession(session);

      if (session?.user) {
        // الحصول على بيانات المستخدم الإضافية من جدول profiles
        const { data: profile, error } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', session.user.id)
          .single();

        if (error) {
          console.error('Error fetching profile:', error);
          // إذا لم يوجد ملف شخصي، قم بإنشاء واحد
          if (error.code === 'PGRST116') {
            const { data: newProfile, error: createError } = await supabase
              .from('profiles')
              .insert({
                id: session.user.id,
                email: session.user.email,
                full_name: session.user.user_metadata?.full_name || '',
                role: session.user.user_metadata?.role || 'student'
              })
              .select()
              .single();

            if (createError) {
              console.error('Error creating profile:', createError);
            } else {
              setUser({ ...session.user, ...newProfile });
            }
          }
        } else {
          setUser({ ...session.user, ...profile });
        }

        setIsAuthenticated(true);
      }
    } catch (error) {
      console.error('Error handling auth state change:', error);
      handleSignOut();
    }
  };

  const handleSignOut = () => {
    setUser(null);
    setSession(null);
    setIsAuthenticated(false);
  };

  const login = async (credentials) => {
    try {
      setLoading(true);

      // التحقق من محاولات تسجيل الدخول
      const loginAttemptResult = trackLoginAttempt(credentials.email);
      if (!loginAttemptResult.allowed) {
        return {
          success: false,
          error: `تم تجاوز عدد المحاولات المسموحة. حاول مرة أخرى بعد ${Math.ceil(loginAttemptResult.lockoutTime / 60000)} دقيقة`
        };
      }

      // تحديث وقت النشاط
      securityManager.updateLastActivity();

      const response = await auth.login(credentials);

      // تسجيل محاولة ناجحة
      trackLoginAttempt(credentials.email, true);

      return {
        success: true,
        user: response.data.user
      };
    } catch (error) {
      console.error('Login error:', error);

      // تسجيل محاولة فاشلة
      trackLoginAttempt(credentials.email, false);

      return {
        success: false,
        error: error.message || 'فشل في تسجيل الدخول'
      };
    } finally {
      setLoading(false);
    }
  };

  const register = async (userData) => {
    try {
      setLoading(true);
      const response = await auth.register(userData);

      return {
        success: true,
        data: response.data,
        message: 'تم إنشاء الحساب بنجاح. يرجى التحقق من بريدك الإلكتروني لتأكيد الحساب.'
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'فشل في إنشاء الحساب'
      };
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      setLoading(true);

      // تنظيف البيانات الحساسة
      securityManager.clearSensitiveData();
      securityManager.secureCleanup();

      await auth.logout();
      handleSignOut();
      return { success: true };
    } catch (error) {
      console.error('Error during logout:', error);
      // حتى لو فشل logout، قم بمسح البيانات المحلية
      securityManager.clearSensitiveData();
      securityManager.secureCleanup();
      handleSignOut();
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  };

  const updateProfile = async (updates) => {
    try {
      const response = await auth.updateProfile(updates);
      setUser(prev => ({ ...prev, ...response.data }));
      return { success: true, data: response.data };
    } catch (error) {
      return {
        success: false,
        error: error.message || 'فشل في تحديث الملف الشخصي'
      };
    }
  };

  const checkAuthStatus = async () => {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      if (error) throw error;

      if (session) {
        await handleAuthStateChange(session);
      } else {
        handleSignOut();
      }
    } catch (error) {
      console.error('Error checking auth status:', error);
      handleSignOut();
    }
  };

  const value = {
    user,
    session,
    loading,
    isAuthenticated,
    login,
    register,
    logout,
    updateProfile,
    checkAuthStatus,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthProvider;
