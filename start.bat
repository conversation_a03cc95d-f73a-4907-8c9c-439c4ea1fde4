@echo off
echo Starting Educational File Manager with Supabase...
echo.

echo Installing frontend dependencies...
cd frontend
call npm install
if %errorlevel% neq 0 (
    echo Error installing frontend dependencies
    pause
    exit /b 1
)

echo.
echo Checking environment variables...
if not exist .env (
    echo Warning: .env file not found!
    echo Please copy .env.example to .env and configure your Supabase credentials
    pause
)

echo.
echo Starting the application...
call npm start

pause
