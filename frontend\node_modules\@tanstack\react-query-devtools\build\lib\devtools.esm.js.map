{"version": 3, "file": "devtools.esm.js", "sources": ["../../src/devtools.tsx"], "sourcesContent": ["'use client'\nimport * as React from 'react'\nimport {\n  notify<PERSON>ana<PERSON>,\n  onlineManager,\n  useQueryClient,\n} from '@tanstack/react-query'\nimport { rankItem } from '@tanstack/match-sorter-utils'\nimport { useMemo } from 'react'\nimport { useSyncExternalStore } from './useSyncExternalStore'\nimport useLocalStorage from './useLocalStorage'\nimport {\n  defaultPanelSize,\n  displayValue,\n  getResizeHandleStyle,\n  getSidePanelStyle,\n  getSidedProp,\n  isVerticalSide,\n  minPanelSize,\n  sortFns,\n  useIsMounted,\n} from './utils'\nimport {\n  ActiveQueryPanel,\n  Button,\n  Code,\n  Input,\n  Panel,\n  QueryKey,\n  QueryKeys,\n  Select,\n} from './styledComponents'\nimport ScreenReader from './screenreader'\nimport { ThemeProvider, defaultTheme as theme } from './theme'\nimport { getQueryStatusColor, getQueryStatusLabel } from './utils'\nimport Explorer from './Explorer'\nimport Logo from './Logo'\nimport type { Corner, Side } from './utils'\nimport type {\n  ContextOptions,\n  Query,\n  QueryCache,\n  QueryClient,\n  QueryKey as QueryKeyType,\n} from '@tanstack/react-query'\n\nexport interface DevToolsErrorType {\n  /**\n   * The name of the error.\n   */\n  name: string\n  /**\n   * How the error is initialized. Whatever it returns MUST implement toString() so\n   * we can check against the current error.\n   */\n  initializer: (query: Query) => { toString(): string }\n}\n\nexport interface DevtoolsOptions extends ContextOptions {\n  /**\n   * Set this true if you want the dev tools to default to being open\n   */\n  initialIsOpen?: boolean\n  /**\n   * Use this to add props to the panel. For example, you can add className, style (merge and override default style), etc.\n   */\n  panelProps?: React.ComponentPropsWithoutRef<'div'>\n  /**\n   * Use this to add props to the close button. For example, you can add className, style (merge and override default style), onClick (extend default handler), etc.\n   */\n  closeButtonProps?: React.ComponentPropsWithoutRef<'button'>\n  /**\n   * Use this to add props to the toggle button. For example, you can add className, style (merge and override default style), onClick (extend default handler), etc.\n   */\n  toggleButtonProps?: React.ComponentPropsWithoutRef<'button'>\n  /**\n   * The position of the React Query logo to open and close the devtools panel.\n   * Defaults to 'bottom-left'.\n   */\n  position?: Corner\n  /**\n   * The position of the React Query devtools panel.\n   * Defaults to 'bottom'.\n   */\n  panelPosition?: Side\n  /**\n   * Use this to render the devtools inside a different type of container element for a11y purposes.\n   * Any string which corresponds to a valid intrinsic JSX element is allowed.\n   * Defaults to 'aside'.\n   */\n  containerElement?: string | any\n  /**\n   * nonce for style element for CSP\n   */\n  styleNonce?: string\n  /**\n   * Use this so you can define custom errors that can be shown in the devtools.\n   */\n  errorTypes?: DevToolsErrorType[]\n}\n\ninterface DevtoolsPanelOptions extends ContextOptions {\n  /**\n   * The standard React style object used to style a component with inline styles\n   */\n  style?: React.CSSProperties\n  /**\n   * The standard React className property used to style a component with classes\n   */\n  className?: string\n  /**\n   * A boolean variable indicating whether the panel is open or closed\n   */\n  isOpen?: boolean\n  /**\n   * nonce for style element for CSP\n   */\n  styleNonce?: string\n  /**\n   * A function that toggles the open and close state of the panel\n   */\n  setIsOpen: (isOpen: boolean) => void\n  /**\n   * Handles the opening and closing the devtools panel\n   */\n  onDragStart: (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => void\n  /**\n   * The position of the React Query devtools panel.\n   * Defaults to 'bottom'.\n   */\n  position?: Side\n  /**\n   * Handles the panel position select change\n   */\n  onPositionChange?: (side: Side) => void\n  /**\n   * Show a close button inside the panel\n   */\n  showCloseButton?: boolean\n  /**\n   * Use this to add props to the close button. For example, you can add className, style (merge and override default style), onClick (extend default handler), etc.\n   */\n  closeButtonProps?: React.ComponentPropsWithoutRef<'button'>\n  /**\n   * Use this so you can define custom errors that can be shown in the devtools.\n   */\n  errorTypes?: DevToolsErrorType[]\n}\n\nexport function ReactQueryDevtools({\n  initialIsOpen,\n  panelProps = {},\n  closeButtonProps = {},\n  toggleButtonProps = {},\n  position = 'bottom-left',\n  containerElement: Container = 'aside',\n  context,\n  styleNonce,\n  panelPosition: initialPanelPosition = 'bottom',\n  errorTypes = [],\n}: DevtoolsOptions): React.ReactElement | null {\n  const rootRef = React.useRef<HTMLDivElement>(null)\n  const panelRef = React.useRef<HTMLDivElement>(null)\n  const [isOpen, setIsOpen] = useLocalStorage(\n    'reactQueryDevtoolsOpen',\n    initialIsOpen,\n  )\n  const [devtoolsHeight, setDevtoolsHeight] = useLocalStorage<number>(\n    'reactQueryDevtoolsHeight',\n    defaultPanelSize,\n  )\n  const [devtoolsWidth, setDevtoolsWidth] = useLocalStorage<number>(\n    'reactQueryDevtoolsWidth',\n    defaultPanelSize,\n  )\n\n  const [panelPosition = 'bottom', setPanelPosition] = useLocalStorage<Side>(\n    'reactQueryDevtoolsPanelPosition',\n    initialPanelPosition,\n  )\n\n  const [isResolvedOpen, setIsResolvedOpen] = React.useState(false)\n  const [isResizing, setIsResizing] = React.useState(false)\n  const isMounted = useIsMounted()\n\n  const handleDragStart = (\n    panelElement: HTMLDivElement | null,\n    startEvent: React.MouseEvent<HTMLDivElement, MouseEvent>,\n  ) => {\n    if (!panelElement) return\n    if (startEvent.button !== 0) return // Only allow left click for drag\n    const isVertical = isVerticalSide(panelPosition)\n    setIsResizing(true)\n\n    const { height, width } = panelElement.getBoundingClientRect()\n    const startX = startEvent.clientX\n    const startY = startEvent.clientY\n    let newSize = 0\n\n    const run = (moveEvent: MouseEvent) => {\n      // prevent mouse selecting stuff with mouse drag\n      moveEvent.preventDefault()\n\n      // calculate the correct size based on mouse position and current panel position\n      // hint: it is different formula for the opposite sides\n      if (isVertical) {\n        newSize =\n          width +\n          (panelPosition === 'right'\n            ? startX - moveEvent.clientX\n            : moveEvent.clientX - startX)\n        setDevtoolsWidth(newSize)\n      } else {\n        newSize =\n          height +\n          (panelPosition === 'bottom'\n            ? startY - moveEvent.clientY\n            : moveEvent.clientY - startY)\n        setDevtoolsHeight(newSize)\n      }\n\n      if (newSize < minPanelSize) {\n        setIsOpen(false)\n      } else {\n        setIsOpen(true)\n      }\n    }\n\n    const unsub = () => {\n      if (isResizing) {\n        setIsResizing(false)\n      }\n\n      document.removeEventListener('mousemove', run, false)\n      document.removeEventListener('mouseUp', unsub, false)\n    }\n\n    document.addEventListener('mousemove', run, false)\n    document.addEventListener('mouseup', unsub, false)\n  }\n\n  React.useEffect(() => {\n    setIsResolvedOpen(isOpen ?? false)\n  }, [isOpen, isResolvedOpen, setIsResolvedOpen])\n\n  // Toggle panel visibility before/after transition (depending on direction).\n  // Prevents focusing in a closed panel.\n  React.useEffect(() => {\n    const ref = panelRef.current\n    if (ref) {\n      const handlePanelTransitionStart = () => {\n        if (isResolvedOpen) {\n          ref.style.visibility = 'visible'\n        }\n      }\n\n      const handlePanelTransitionEnd = () => {\n        if (!isResolvedOpen) {\n          ref.style.visibility = 'hidden'\n        }\n      }\n\n      ref.addEventListener('transitionstart', handlePanelTransitionStart)\n      ref.addEventListener('transitionend', handlePanelTransitionEnd)\n\n      return () => {\n        ref.removeEventListener('transitionstart', handlePanelTransitionStart)\n        ref.removeEventListener('transitionend', handlePanelTransitionEnd)\n      }\n    }\n    return\n  }, [isResolvedOpen])\n\n  React.useEffect(() => {\n    if (isResolvedOpen && rootRef.current?.parentElement) {\n      const { parentElement } = rootRef.current\n      const styleProp = getSidedProp('padding', panelPosition)\n      const isVertical = isVerticalSide(panelPosition)\n\n      const previousPaddings = (({\n        padding,\n        paddingTop,\n        paddingBottom,\n        paddingLeft,\n        paddingRight,\n      }) => ({\n        padding,\n        paddingTop,\n        paddingBottom,\n        paddingLeft,\n        paddingRight,\n      }))(parentElement.style)\n\n      const run = () => {\n        // reset the padding\n        parentElement.style.padding = '0px'\n        parentElement.style.paddingTop = '0px'\n        parentElement.style.paddingBottom = '0px'\n        parentElement.style.paddingLeft = '0px'\n        parentElement.style.paddingRight = '0px'\n        // set the new padding based on the new panel position\n\n        parentElement.style[styleProp] = `${\n          isVertical ? devtoolsWidth : devtoolsHeight\n        }px`\n      }\n\n      run()\n\n      if (typeof window !== 'undefined') {\n        window.addEventListener('resize', run)\n\n        return () => {\n          window.removeEventListener('resize', run)\n          Object.entries(previousPaddings).forEach(\n            ([property, previousValue]) => {\n              parentElement.style[property as keyof typeof previousPaddings] =\n                previousValue\n            },\n          )\n        }\n      }\n    }\n    return\n  }, [isResolvedOpen, panelPosition, devtoolsHeight, devtoolsWidth])\n\n  const { style: panelStyle = {}, ...otherPanelProps } = panelProps\n\n  const {\n    style: toggleButtonStyle = {},\n    onClick: onToggleClick,\n    ...otherToggleButtonProps\n  } = toggleButtonProps\n\n  // get computed style based on panel position\n  const style = getSidePanelStyle({\n    position: panelPosition,\n    devtoolsTheme: theme,\n    isOpen: isResolvedOpen,\n    height: devtoolsHeight,\n    width: devtoolsWidth,\n    isResizing,\n    panelStyle,\n  })\n\n  // Do not render on the server\n  if (!isMounted()) return null\n\n  return (\n    <Container\n      ref={rootRef}\n      className=\"ReactQueryDevtools\"\n      aria-label=\"React Query Devtools\"\n    >\n      <ThemeProvider theme={theme}>\n        <ReactQueryDevtoolsPanel\n          ref={panelRef as any}\n          context={context}\n          styleNonce={styleNonce}\n          position={panelPosition}\n          onPositionChange={setPanelPosition}\n          showCloseButton\n          closeButtonProps={closeButtonProps}\n          {...otherPanelProps}\n          style={style}\n          isOpen={isResolvedOpen}\n          setIsOpen={setIsOpen}\n          onDragStart={(e) => handleDragStart(panelRef.current, e)}\n          errorTypes={errorTypes}\n        />\n      </ThemeProvider>\n      {!isResolvedOpen ? (\n        <button\n          type=\"button\"\n          {...otherToggleButtonProps}\n          aria-label=\"Open React Query Devtools\"\n          aria-controls=\"ReactQueryDevtoolsPanel\"\n          aria-haspopup=\"true\"\n          aria-expanded=\"false\"\n          onClick={(e) => {\n            setIsOpen(true)\n            onToggleClick?.(e)\n          }}\n          style={{\n            background: 'none',\n            border: 0,\n            padding: 0,\n            position: 'fixed',\n            zIndex: 99999,\n            display: 'inline-flex',\n            fontSize: '1.5em',\n            margin: '.5em',\n            cursor: 'pointer',\n            width: 'fit-content',\n            ...(position === 'top-right'\n              ? {\n                  top: '0',\n                  right: '0',\n                }\n              : position === 'top-left'\n              ? {\n                  top: '0',\n                  left: '0',\n                }\n              : position === 'bottom-right'\n              ? {\n                  bottom: '0',\n                  right: '0',\n                }\n              : {\n                  bottom: '0',\n                  left: '0',\n                }),\n            ...toggleButtonStyle,\n          }}\n        >\n          <Logo aria-hidden />\n          <ScreenReader text=\"Open React Query Devtools\" />\n        </button>\n      ) : null}\n    </Container>\n  )\n}\n\nconst useSubscribeToQueryCache = <T,>(\n  queryCache: QueryCache,\n  getSnapshot: () => T,\n  skip: boolean = false,\n): T => {\n  return useSyncExternalStore(\n    React.useCallback(\n      (onStoreChange) => {\n        if (!skip)\n          return queryCache.subscribe(notifyManager.batchCalls(onStoreChange))\n        return () => {\n          return\n        }\n      },\n      [queryCache, skip],\n    ),\n    getSnapshot,\n    getSnapshot,\n  )\n}\n\nexport const ReactQueryDevtoolsPanel = React.forwardRef<\n  HTMLDivElement,\n  DevtoolsPanelOptions\n>(function ReactQueryDevtoolsPanel(props, ref): React.ReactElement {\n  const {\n    isOpen = true,\n    styleNonce,\n    setIsOpen,\n    context,\n    onDragStart,\n    onPositionChange,\n    showCloseButton,\n    position,\n    closeButtonProps = {},\n    errorTypes = [],\n    ...panelProps\n  } = props\n\n  const { onClick: onCloseClick, ...otherCloseButtonProps } = closeButtonProps\n\n  const queryClient = useQueryClient({ context })\n  const queryCache = queryClient.getQueryCache()\n\n  const [sort, setSort] = useLocalStorage(\n    'reactQueryDevtoolsSortFn',\n    Object.keys(sortFns)[0],\n  )\n\n  const [filter, setFilter] = useLocalStorage('reactQueryDevtoolsFilter', '')\n\n  const [baseSort, setBaseSort] = useLocalStorage(\n    'reactQueryDevtoolsBaseSort',\n    1,\n  )\n\n  const sortFn = React.useMemo(() => sortFns[sort as string], [sort])\n\n  const queriesCount = useSubscribeToQueryCache(\n    queryCache,\n    () => queryCache.getAll().length,\n    !isOpen,\n  )\n\n  const [activeQueryHash, setActiveQueryHash] = useLocalStorage(\n    'reactQueryDevtoolsActiveQueryHash',\n    '',\n  )\n\n  const queries = React.useMemo(() => {\n    const unsortedQueries = queryCache.getAll()\n\n    if (queriesCount === 0) {\n      return []\n    }\n\n    const filtered = filter\n      ? unsortedQueries.filter(\n          (item) => rankItem(item.queryHash, filter).passed,\n        )\n      : [...unsortedQueries]\n\n    const sorted = sortFn\n      ? filtered.sort((a, b) => sortFn(a, b) * (baseSort as number))\n      : filtered\n\n    return sorted\n  }, [baseSort, sortFn, filter, queriesCount, queryCache])\n\n  const [isMockOffline, setMockOffline] = React.useState(false)\n\n  return (\n    <ThemeProvider theme={theme}>\n      <Panel\n        ref={ref}\n        className=\"ReactQueryDevtoolsPanel\"\n        aria-label=\"React Query Devtools Panel\"\n        id=\"ReactQueryDevtoolsPanel\"\n        {...panelProps}\n        style={{\n          height: defaultPanelSize,\n          position: 'relative',\n          ...panelProps.style,\n        }}\n      >\n        <style\n          nonce={styleNonce}\n          dangerouslySetInnerHTML={{\n            __html: `\n            .ReactQueryDevtoolsPanel * {\n              scrollbar-color: ${theme.backgroundAlt} ${theme.gray};\n            }\n\n            .ReactQueryDevtoolsPanel *::-webkit-scrollbar, .ReactQueryDevtoolsPanel scrollbar {\n              width: 1em;\n              height: 1em;\n            }\n\n            .ReactQueryDevtoolsPanel *::-webkit-scrollbar-track, .ReactQueryDevtoolsPanel scrollbar-track {\n              background: ${theme.backgroundAlt};\n            }\n\n            .ReactQueryDevtoolsPanel *::-webkit-scrollbar-thumb, .ReactQueryDevtoolsPanel scrollbar-thumb {\n              background: ${theme.gray};\n              border-radius: .5em;\n              border: 3px solid ${theme.backgroundAlt};\n            }\n          `,\n          }}\n        />\n        <div\n          style={getResizeHandleStyle(position)}\n          onMouseDown={onDragStart}\n        ></div>\n\n        {isOpen && (\n          <div\n            style={{\n              flex: '1 1 500px',\n              minHeight: '40%',\n              maxHeight: '100%',\n              overflow: 'auto',\n              borderRight: `1px solid ${theme.grayAlt}`,\n              display: 'flex',\n              flexDirection: 'column',\n            }}\n          >\n            <div\n              style={{\n                padding: '.5em',\n                background: theme.backgroundAlt,\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center',\n              }}\n            >\n              <button\n                type=\"button\"\n                aria-label=\"Close React Query Devtools\"\n                aria-controls=\"ReactQueryDevtoolsPanel\"\n                aria-haspopup=\"true\"\n                aria-expanded=\"true\"\n                onClick={() => setIsOpen(false)}\n                style={{\n                  display: 'inline-flex',\n                  background: 'none',\n                  border: 0,\n                  padding: 0,\n                  marginRight: '.5em',\n                  cursor: 'pointer',\n                }}\n              >\n                <Logo aria-hidden />\n                <ScreenReader text=\"Close React Query Devtools\" />\n              </button>\n\n              <div\n                style={{\n                  display: 'flex',\n                  flexDirection: 'column',\n                }}\n              >\n                <div\n                  style={{\n                    display: 'flex',\n                    justifyContent: 'space-between',\n                    alignItems: 'center',\n                    marginBottom: '.5em',\n                  }}\n                >\n                  <QueryStatusCount queryCache={queryCache} />\n                  {position && onPositionChange ? (\n                    <Select\n                      aria-label=\"Panel position\"\n                      value={position}\n                      style={{ marginInlineStart: '.5em' }}\n                      onChange={(e) => onPositionChange(e.target.value as Side)}\n                    >\n                      <option value=\"left\">Left</option>\n                      <option value=\"right\">Right</option>\n                      <option value=\"top\">Top</option>\n                      <option value=\"bottom\">Bottom</option>\n                    </Select>\n                  ) : null}\n                </div>\n                <div\n                  style={{\n                    display: 'flex',\n                    alignItems: 'center',\n                    flexWrap: 'wrap',\n                    gap: '0.5em',\n                  }}\n                >\n                  <Input\n                    placeholder=\"Filter\"\n                    aria-label=\"Filter by queryhash\"\n                    value={filter ?? ''}\n                    onChange={(e) => setFilter(e.target.value)}\n                    onKeyDown={(e) => {\n                      if (e.key === 'Escape') setFilter('')\n                    }}\n                    style={{\n                      flex: '1',\n                      width: '100%',\n                    }}\n                  />\n                  <Select\n                    aria-label=\"Sort queries\"\n                    value={sort}\n                    onChange={(e) => setSort(e.target.value)}\n                    style={{\n                      flex: '1',\n                      minWidth: 75,\n                      marginRight: '.5em',\n                    }}\n                  >\n                    {Object.keys(sortFns).map((key) => (\n                      <option key={key} value={key}>\n                        Sort by {key}\n                      </option>\n                    ))}\n                  </Select>\n                  <Button\n                    type=\"button\"\n                    onClick={() => setBaseSort((old) => old * -1)}\n                    style={{\n                      padding: '.3em .4em',\n                      marginRight: '.5em',\n                    }}\n                  >\n                    {baseSort === 1 ? '⬆ Asc' : '⬇ Desc'}\n                  </Button>\n                  <Button\n                    title=\"Clear cache\"\n                    aria-label=\"Clear cache\"\n                    type=\"button\"\n                    onClick={() => queryCache.clear()}\n                    style={{\n                      padding: '.3em .4em',\n                      marginRight: '.5em',\n                    }}\n                  >\n                    Clear\n                  </Button>\n                  <Button\n                    type=\"button\"\n                    onClick={() => {\n                      if (isMockOffline) {\n                        onlineManager.setOnline(undefined)\n                        setMockOffline(false)\n                        window.dispatchEvent(new Event('online'))\n                      } else {\n                        onlineManager.setOnline(false)\n                        setMockOffline(true)\n                      }\n                    }}\n                    aria-label={\n                      isMockOffline\n                        ? 'Restore offline mock'\n                        : 'Mock offline behavior'\n                    }\n                    title={\n                      isMockOffline\n                        ? 'Restore offline mock'\n                        : 'Mock offline behavior'\n                    }\n                    style={{\n                      padding: '0',\n                      height: '2em',\n                    }}\n                  >\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      width=\"2em\"\n                      height=\"2em\"\n                      viewBox=\"0 0 24 24\"\n                      stroke={isMockOffline ? theme.danger : 'currentColor'}\n                      fill=\"none\"\n                    >\n                      {isMockOffline ? (\n                        <>\n                          <path stroke=\"none\" d=\"M0 0h24v24H0z\" fill=\"none\" />\n                          <line x1=\"12\" y1=\"18\" x2=\"12.01\" y2=\"18\" />\n                          <path d=\"M9.172 15.172a4 4 0 0 1 5.656 0\" />\n                          <path d=\"M6.343 12.343a7.963 7.963 0 0 1 3.864 -2.14m4.163 .155a7.965 7.965 0 0 1 3.287 2\" />\n                          <path d=\"M3.515 9.515a12 12 0 0 1 3.544 -2.455m3.101 -.92a12 12 0 0 1 10.325 3.374\" />\n                          <line x1=\"3\" y1=\"3\" x2=\"21\" y2=\"21\" />\n                        </>\n                      ) : (\n                        <>\n                          <path stroke=\"none\" d=\"M0 0h24v24H0z\" fill=\"none\" />\n                          <line x1=\"12\" y1=\"18\" x2=\"12.01\" y2=\"18\" />\n                          <path d=\"M9.172 15.172a4 4 0 0 1 5.656 0\" />\n                          <path d=\"M6.343 12.343a8 8 0 0 1 11.314 0\" />\n                          <path d=\"M3.515 9.515c4.686 -4.687 12.284 -4.687 17 0\" />\n                        </>\n                      )}\n                    </svg>\n                    <ScreenReader\n                      text={\n                        isMockOffline\n                          ? 'Restore offline mock'\n                          : 'Mock offline behavior'\n                      }\n                    />\n                  </Button>\n                </div>\n              </div>\n            </div>\n            <div\n              style={{\n                overflowY: 'auto',\n                flex: '1',\n              }}\n            >\n              {queries.map((query) => {\n                return (\n                  <QueryRow\n                    queryKey={query.queryKey}\n                    activeQueryHash={activeQueryHash}\n                    setActiveQueryHash={setActiveQueryHash}\n                    key={query.queryHash}\n                    queryCache={queryCache}\n                  />\n                )\n              })}\n            </div>\n          </div>\n        )}\n\n        {activeQueryHash && isOpen ? (\n          <ActiveQuery\n            activeQueryHash={activeQueryHash}\n            queryCache={queryCache}\n            queryClient={queryClient}\n            errorTypes={errorTypes}\n          />\n        ) : null}\n\n        {showCloseButton ? (\n          <Button\n            type=\"button\"\n            aria-controls=\"ReactQueryDevtoolsPanel\"\n            aria-haspopup=\"true\"\n            aria-expanded=\"true\"\n            {...(otherCloseButtonProps as Record<string, unknown>)}\n            style={{\n              position: 'absolute',\n              zIndex: 99999,\n              margin: '.5em',\n              bottom: 0,\n              left: 0,\n              ...otherCloseButtonProps.style,\n            }}\n            onClick={(e) => {\n              setIsOpen(false)\n              onCloseClick?.(e)\n            }}\n          >\n            Close\n          </Button>\n        ) : null}\n      </Panel>\n    </ThemeProvider>\n  )\n})\n\nconst ActiveQuery = ({\n  queryCache,\n  activeQueryHash,\n  queryClient,\n  errorTypes,\n}: {\n  queryCache: QueryCache\n  activeQueryHash: string\n  queryClient: QueryClient\n  errorTypes: DevToolsErrorType[]\n}) => {\n  const activeQuery = useSubscribeToQueryCache(queryCache, () =>\n    queryCache.getAll().find((query) => query.queryHash === activeQueryHash),\n  )\n\n  const activeQueryState = useSubscribeToQueryCache(\n    queryCache,\n    () =>\n      queryCache.getAll().find((query) => query.queryHash === activeQueryHash)\n        ?.state,\n  )\n\n  const isStale =\n    useSubscribeToQueryCache(queryCache, () =>\n      queryCache\n        .getAll()\n        .find((query) => query.queryHash === activeQueryHash)\n        ?.isStale(),\n    ) ?? false\n\n  const observerCount =\n    useSubscribeToQueryCache(queryCache, () =>\n      queryCache\n        .getAll()\n        .find((query) => query.queryHash === activeQueryHash)\n        ?.getObserversCount(),\n    ) ?? 0\n\n  const handleRefetch = () => {\n    const promise = activeQuery?.fetch()\n    promise?.catch(noop)\n  }\n\n  const currentErrorTypeName = useMemo(() => {\n    if (activeQuery && activeQueryState?.error) {\n      const errorType = errorTypes.find(\n        (type) =>\n          type.initializer(activeQuery).toString() ===\n          activeQueryState.error?.toString(),\n      )\n      return errorType?.name\n    }\n    return undefined\n  }, [activeQuery, activeQueryState?.error, errorTypes])\n\n  if (!activeQuery || !activeQueryState) {\n    return null\n  }\n\n  const triggerError = (errorType?: DevToolsErrorType) => {\n    const error =\n      errorType?.initializer(activeQuery) ??\n      new Error('Unknown error from devtools')\n\n    const __previousQueryOptions = activeQuery.options\n\n    activeQuery.setState({\n      status: 'error',\n      error,\n      fetchMeta: {\n        ...activeQuery.state.fetchMeta,\n        __previousQueryOptions,\n      },\n    })\n  }\n\n  const restoreQueryAfterLoadingOrError = () => {\n    activeQuery.fetch(activeQuery.state.fetchMeta.__previousQueryOptions, {\n      // Make sure this fetch will cancel the previous one\n      cancelRefetch: true,\n    })\n  }\n\n  return (\n    <ActiveQueryPanel>\n      <div\n        style={{\n          padding: '.5em',\n          background: theme.backgroundAlt,\n          position: 'sticky',\n          top: 0,\n          zIndex: 1,\n        }}\n      >\n        Query Details\n      </div>\n      <div\n        style={{\n          padding: '.5em',\n        }}\n      >\n        <div\n          style={{\n            marginBottom: '.5em',\n            display: 'flex',\n            alignItems: 'flex-start',\n            justifyContent: 'space-between',\n          }}\n        >\n          <Code\n            style={{\n              lineHeight: '1.8em',\n            }}\n          >\n            <pre\n              style={{\n                margin: 0,\n                padding: 0,\n                overflow: 'auto',\n              }}\n            >\n              {displayValue(activeQuery.queryKey, true)}\n            </pre>\n          </Code>\n          <span\n            style={{\n              padding: '0.3em .6em',\n              borderRadius: '0.4em',\n              fontWeight: 'bold',\n              textShadow: '0 2px 10px black',\n              background: getQueryStatusColor({\n                queryState: activeQueryState,\n                isStale: isStale,\n                observerCount: observerCount,\n                theme,\n              }),\n              flexShrink: 0,\n            }}\n          >\n            {getQueryStatusLabel(activeQuery)}\n          </span>\n        </div>\n        <div\n          style={{\n            marginBottom: '.5em',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between',\n          }}\n        >\n          Observers: <Code>{observerCount}</Code>\n        </div>\n        <div\n          style={{\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between',\n          }}\n        >\n          Last Updated:{' '}\n          <Code>\n            {new Date(activeQueryState.dataUpdatedAt).toLocaleTimeString()}\n          </Code>\n        </div>\n      </div>\n      <div\n        style={{\n          background: theme.backgroundAlt,\n          padding: '.5em',\n          position: 'sticky',\n          top: 0,\n          zIndex: 1,\n        }}\n      >\n        Actions\n      </div>\n      <div\n        style={{\n          padding: '0.5em',\n          display: 'flex',\n          flexWrap: 'wrap',\n          gap: '0.5em',\n          alignItems: 'flex-end',\n        }}\n      >\n        <Button\n          type=\"button\"\n          onClick={handleRefetch}\n          disabled={activeQueryState.fetchStatus === 'fetching'}\n          style={{\n            background: theme.active,\n          }}\n        >\n          Refetch\n        </Button>{' '}\n        <Button\n          type=\"button\"\n          onClick={() => queryClient.invalidateQueries(activeQuery)}\n          style={{\n            background: theme.warning,\n            color: theme.inputTextColor,\n          }}\n        >\n          Invalidate\n        </Button>{' '}\n        <Button\n          type=\"button\"\n          onClick={() => queryClient.resetQueries(activeQuery)}\n          style={{\n            background: theme.gray,\n          }}\n        >\n          Reset\n        </Button>{' '}\n        <Button\n          type=\"button\"\n          onClick={() => queryClient.removeQueries(activeQuery)}\n          style={{\n            background: theme.danger,\n          }}\n        >\n          Remove\n        </Button>{' '}\n        <Button\n          type=\"button\"\n          onClick={() => {\n            // Return early if the query is already restoring\n            if (\n              activeQuery.state.fetchStatus === 'fetching' &&\n              typeof activeQuery.state.fetchMeta?.__previousQueryOptions ===\n                'undefined'\n            ) {\n              return\n            }\n\n            if (activeQuery.state.data === undefined) {\n              restoreQueryAfterLoadingOrError()\n            } else {\n              const __previousQueryOptions = activeQuery.options\n              // Trigger a fetch in order to trigger suspense as well.\n              activeQuery.fetch({\n                ...__previousQueryOptions,\n                queryFn: () => {\n                  return new Promise(() => {\n                    // Never resolve\n                  })\n                },\n                cacheTime: -1,\n              })\n              activeQuery.setState({\n                data: undefined,\n                status: 'loading',\n                fetchMeta: {\n                  ...activeQuery.state.fetchMeta,\n                  __previousQueryOptions,\n                },\n              })\n            }\n          }}\n          style={{\n            background: theme.paused,\n          }}\n        >\n          {activeQuery.state.status === 'loading' ? 'Restore' : 'Trigger'}{' '}\n          loading\n        </Button>{' '}\n        {errorTypes.length === 0 || activeQuery.state.status === 'error' ? (\n          <Button\n            type=\"button\"\n            onClick={() => {\n              if (!activeQuery.state.error) {\n                triggerError()\n              } else {\n                queryClient.resetQueries(activeQuery)\n              }\n            }}\n            style={{\n              background: theme.danger,\n            }}\n          >\n            {activeQuery.state.status === 'error' ? 'Restore' : 'Trigger'} error\n          </Button>\n        ) : (\n          <label>\n            Trigger error:\n            <Select\n              value={currentErrorTypeName ?? ''}\n              style={{ marginInlineStart: '.5em' }}\n              onChange={(e) => {\n                const errorType = errorTypes.find(\n                  (t) => t.name === e.target.value,\n                )\n\n                triggerError(errorType)\n              }}\n            >\n              <option key=\"\" value=\"\" />\n              {errorTypes.map((errorType) => (\n                <option key={errorType.name} value={errorType.name}>\n                  {errorType.name}\n                </option>\n              ))}\n            </Select>\n          </label>\n        )}\n      </div>\n      <div\n        style={{\n          background: theme.backgroundAlt,\n          padding: '.5em',\n          position: 'sticky',\n          top: 0,\n          zIndex: 1,\n        }}\n      >\n        Data Explorer\n      </div>\n      <div\n        style={{\n          padding: '.5em',\n        }}\n      >\n        <Explorer\n          label=\"Data\"\n          value={activeQueryState.data}\n          defaultExpanded={{}}\n          copyable\n        />\n      </div>\n      <div\n        style={{\n          background: theme.backgroundAlt,\n          padding: '.5em',\n          position: 'sticky',\n          top: 0,\n          zIndex: 1,\n        }}\n      >\n        Query Explorer\n      </div>\n      <div\n        style={{\n          padding: '.5em',\n        }}\n      >\n        <Explorer\n          label=\"Query\"\n          value={activeQuery}\n          defaultExpanded={{\n            queryKey: true,\n          }}\n        />\n      </div>\n    </ActiveQueryPanel>\n  )\n}\n\nconst QueryStatusCount = ({ queryCache }: { queryCache: QueryCache }) => {\n  const hasFresh = useSubscribeToQueryCache(\n    queryCache,\n    () =>\n      queryCache.getAll().filter((q) => getQueryStatusLabel(q) === 'fresh')\n        .length,\n  )\n  const hasFetching = useSubscribeToQueryCache(\n    queryCache,\n    () =>\n      queryCache.getAll().filter((q) => getQueryStatusLabel(q) === 'fetching')\n        .length,\n  )\n  const hasPaused = useSubscribeToQueryCache(\n    queryCache,\n    () =>\n      queryCache.getAll().filter((q) => getQueryStatusLabel(q) === 'paused')\n        .length,\n  )\n  const hasStale = useSubscribeToQueryCache(\n    queryCache,\n    () =>\n      queryCache.getAll().filter((q) => getQueryStatusLabel(q) === 'stale')\n        .length,\n  )\n  const hasInactive = useSubscribeToQueryCache(\n    queryCache,\n    () =>\n      queryCache.getAll().filter((q) => getQueryStatusLabel(q) === 'inactive')\n        .length,\n  )\n  return (\n    <QueryKeys>\n      <QueryKey\n        style={{\n          background: theme.success,\n          opacity: hasFresh ? 1 : 0.3,\n        }}\n      >\n        fresh <Code>({hasFresh})</Code>\n      </QueryKey>{' '}\n      <QueryKey\n        style={{\n          background: theme.active,\n          opacity: hasFetching ? 1 : 0.3,\n        }}\n      >\n        fetching <Code>({hasFetching})</Code>\n      </QueryKey>{' '}\n      <QueryKey\n        style={{\n          background: theme.paused,\n          opacity: hasPaused ? 1 : 0.3,\n        }}\n      >\n        paused <Code>({hasPaused})</Code>\n      </QueryKey>{' '}\n      <QueryKey\n        style={{\n          background: theme.warning,\n          color: 'black',\n          textShadow: '0',\n          opacity: hasStale ? 1 : 0.3,\n        }}\n      >\n        stale <Code>({hasStale})</Code>\n      </QueryKey>{' '}\n      <QueryKey\n        style={{\n          background: theme.gray,\n          opacity: hasInactive ? 1 : 0.3,\n        }}\n      >\n        inactive <Code>({hasInactive})</Code>\n      </QueryKey>\n    </QueryKeys>\n  )\n}\n\ninterface QueryRowProps {\n  queryKey: QueryKeyType\n  setActiveQueryHash: (hash: string) => void\n  activeQueryHash?: string\n  queryCache: QueryCache\n}\n\nconst QueryRow = React.memo(\n  ({\n    queryKey,\n    setActiveQueryHash,\n    activeQueryHash,\n    queryCache,\n  }: QueryRowProps) => {\n    const queryHash =\n      useSubscribeToQueryCache(\n        queryCache,\n        () => queryCache.find(queryKey)?.queryHash,\n      ) ?? ''\n\n    const queryState = useSubscribeToQueryCache(\n      queryCache,\n      () => queryCache.find(queryKey)?.state,\n    )\n\n    const isStale =\n      useSubscribeToQueryCache(queryCache, () =>\n        queryCache.find(queryKey)?.isStale(),\n      ) ?? false\n\n    const isDisabled =\n      useSubscribeToQueryCache(queryCache, () =>\n        queryCache.find(queryKey)?.isDisabled(),\n      ) ?? false\n\n    const observerCount =\n      useSubscribeToQueryCache(queryCache, () =>\n        queryCache.find(queryKey)?.getObserversCount(),\n      ) ?? 0\n\n    if (!queryState) {\n      return null\n    }\n\n    return (\n      <div\n        role=\"button\"\n        aria-label={`Open query details for ${queryHash}`}\n        onClick={() =>\n          setActiveQueryHash(activeQueryHash === queryHash ? '' : queryHash)\n        }\n        style={{\n          display: 'flex',\n          borderBottom: `solid 1px ${theme.grayAlt}`,\n          cursor: 'pointer',\n          background:\n            queryHash === activeQueryHash ? 'rgba(255,255,255,.1)' : undefined,\n        }}\n      >\n        <div\n          style={{\n            flex: '0 0 auto',\n            width: '2em',\n            height: '2em',\n            background: getQueryStatusColor({\n              queryState,\n              isStale,\n              observerCount,\n              theme,\n            }),\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            fontWeight: 'bold',\n            textShadow: isStale ? '0' : '0 0 10px black',\n            color: isStale ? 'black' : 'white',\n          }}\n        >\n          {observerCount}\n        </div>\n        {isDisabled ? (\n          <div\n            style={{\n              flex: '0 0 auto',\n              height: '2em',\n              background: theme.gray,\n              display: 'flex',\n              alignItems: 'center',\n              fontWeight: 'bold',\n              padding: '0 0.5em',\n            }}\n          >\n            disabled\n          </div>\n        ) : null}\n        <Code\n          style={{\n            padding: '.5em',\n          }}\n        >\n          {`${queryHash}`}\n        </Code>\n      </div>\n    )\n  },\n)\n\nQueryRow.displayName = 'QueryRow'\n\n// eslint-disable-next-line @typescript-eslint/no-empty-function\nfunction noop() {}\n"], "names": ["panelProps", "closeButtonProps", "toggleButtonProps", "position", "errorTypes", "width", "newSize", "document", "setIsResolvedOpen", "ref", "parentElement", "paddingRight", "window", "Object", "onClick", "devtoolsTheme", "isOpen", "height", "panelStyle", "onToggleClick", "background", "border", "padding", "zIndex", "display", "fontSize", "margin", "cursor", "top", "right", "left", "bottom", "ReactQueryDevtoolsPanel", "context", "__html", "flex", "minHeight", "maxHeight", "overflow", "flexDirection", "justifyContent", "alignItems", "marginRight", "marginBottom", "marginInlineStart", "flexWrap", "gap", "min<PERSON><PERSON><PERSON>", "overflowY", "onCloseClick", "promise", "status", "fetchMeta", "__previousQueryOptions", "cancelRefetch", "lineHeight", "borderRadius", "fontWeight", "textShadow", "queryState", "isStale", "observerCount", "theme", "flexShrink", "activeQuery", "queryFn", "cacheTime", "data", "query<PERSON><PERSON>", "queryCache", "opacity", "color", "QueryRow"], "mappings": ";;;;;;;;;;;;;;;AAqJO;;AAELA;AACAC;AACAC;AACAC;;;;;AAKAC;AAViC;AAYjC;AACA;;;;AAcA;;;;;AASA;;AAKE;;AACA;;;;AAGgBC;;AAChB;AACA;;;;AAIE;;AAIA;;AACA;AACEC;;AAMD;AACCA;;AAMD;;;;AAIA;;AAEA;;;;AAID;;AAEC;;AAEDC;AACAA;;;AAGFA;AACAA;;;;AAIAC;;AAIF;;;AAEE;;AACA;;AAEI;AACEC;AACD;;;;;AAKCA;AACD;;;AAGHA;AACAA;AAEA;AACEA;AACAA;;AAEH;;AACD;;;AAGoB;;;;AAEVC;;AACR;AACA;;;;;;;AAOEC;AALyB;;;;;AAWzBA;AALK;;;AASL;AACAD;AACAA;AACAA;AACAA;AACAA;;;;;;;AAUF;AACEE;AAEA;AACEA;AACAC;AAEIH;;;AAKP;AACF;;AACD;;;;;AAGI;;;AAIJI;;;;;AAMAX;AACAY;AACAC;AACAC;AACAZ;;AAEAa;;;AAIF;AAEA;AAEI;AACA;;AAFF;AAKiB;AAAf;AAEI;AACA;AACA;AACA;AACA;AACA;AACA;AAPF;AASE;AACA;AACA;;AAEA;AAbF;AAkBE;AADF;AAGE;AACA;AACA;AACA;;;AAGEC;;AAEF;AACEC;AACAC;AACAC;AACAnB;AACAoB;AACAC;AACAC;AACAC;AACAC;AACAtB;;AAGMuB;AACAC;AAFF;AAMED;AACAE;AAFF;AAMEC;AACAF;AAFF;AAKEE;AACAD;AAFF;;AA1BC;AAXT;AA4CQ;;AACQ;;AAKvB;;AAED;AAKE;AAGM;AAEA;AACE;;;AAQT;;AAEYE;;AAKThB;;;;;;;;AAQAf;AACAG;;AAVI;;AAcEU;;AAAF;;AAE+BmB;AAAF;AACnC;AAEA;;;AAYA;AAEA;;AAWA;AACE;;;AAGE;AACD;;;;AAYD;AACD;;AAID;AACiB;AAAf;AAEI;AACA;AACA;AACA;AAJF;AAME;AACEhB;AACAd;AACA;AAHK;;AAOL;AACA;AACE+B;AADuB;AAF3B;AA0BE;AACA;;AAKE;AACEC;AACAC;AACAC;AACAC;;AAEAd;AACAe;AAPK;;AAWL;AACEjB;;AAEAE;AACAgB;AACAC;AALK;;AASL;AACA;AACA;AACA;AACA;AACA;AACA;AACEjB;AACAJ;AACAC;AACAC;AACAoB;AACAf;AANK;AAPT;AAgBQ;;AACQ;AAAd;AAIA;AACEH;AACAe;AAFK;;AAML;AACEf;AACAgB;AACAC;AACAE;AAJK;AADT;AAQoB;AAAlB;AAGI;AACA;AACA;AAASC;;;;AAGD;AAAR;AACQ;AAAR;AACQ;AAAR;AACQ;AAAR;AAKJ;AACEpB;AACAiB;AACAI;AACAC;AAJK;AADT;AASI;AACA;AACA;;;;;AAKA;AACEX;AACA9B;AAFK;;AAMP;AACA;;AAEA;AACE8B;AACAY;AACAL;AAHK;;AAOG;AAAU;AAAlB;AAMF;;AAEA;AACEpB;AACAoB;AAFK;;AAQP;AACA;AACA;AACA;AACA;AACEpB;AACAoB;AAFK;;AAQP;AACA;AACE;;;AAGE9B;AACD;;;AAGA;;AAEH;AAKA;AAKA;AACEU;AACAL;AAFK;;AAML;AACA;AACA;AACA;AACA;AACA;;AAIU;AAAc;AAAkB;AAAtC;AACM;AAAQ;AAAQ;AAAW;AAAjC;AACM;AAAN;AACM;AAAN;AACM;AAAN;AACM;AAAO;AAAO;AAAQ;;AAItB;AAAc;AAAkB;AAAtC;AACM;AAAQ;AAAQ;AAAW;AAAjC;AACM;AAAN;AACM;AAAN;AACM;AAAN;AAKJ;AADF;AAYN;AACE+B;AACAb;AAFK;AADT;AAOI;;AAGI;AACA;;AAEA;;;AAUR;AACA;AACA;AACA;AAJF;AAUE;AACA;AACA;;AAHF;AAME;AACEhC;AACAoB;AACAG;AACAK;AACAD;AACA;;;;AAIAmB;AACD;;AAQZ;;AAED;;;;AAIE7C;AAJmB;AAUf;;;AAKJ;AAEE;;AAAA;AAAA;AAKF;AACuC;;AAAA;;AAOvC;AACuC;;AAAA;;;;AAQrC;AACA8C;;;AAGF;AACE;AACE;AACE;;AAAA;AAAA;AAIF;AACD;;AACD;AACD;;AAED;AACE;AACD;;;AAEuD;;AACtD;AAIA;;AAGEC;;AAEAC;AAEEC;AAFS;;;;;;AASX;AACAC;;;;;AAOE;AACEhC;;AAEAnB;AACAyB;AACAL;AALK;AADT;AAYE;AACED;AADK;;AAKL;AACEqB;AACAnB;AACAiB;AACAD;AAJK;AADT;AASI;AACEe;AADK;;AAKL;AACE7B;AACAJ;AACAgB;AAHK;;AAUT;AACEhB;AACAkC;AACAC;AACAC;;AAEEC;AACAC;AACAC;AACAC;AAJ8B;AAMhCC;AAXK;AADT;AAmBA;AACEpB;AACAnB;AACAiB;AACAD;AAJK;AADT;AAWE;AACEhB;AACAiB;AACAD;AAHK;AADT;AAcA;;AAEElB;AACAnB;AACAyB;AACAL;AALK;AADT;AAYE;AACED;AACAE;AACAqB;AACAC;AACAL;AALK;AADT;AAUI;AACA;AACA;AACA;;AAAO;AAJT;AAWE;AACA;AACA;;;AAAO;AAHT;AAWE;AACA;AACA;;AAAO;AAHT;AAUE;AACA;AACA;;AAAO;AAHT;AAUE;AACA;AAAe;;AACb;AACA;AAKE;AACD;;AAED;;AAEC;AACC;;AAEAuB;AAEEC;AACE;AAEC;;AAEHC;;;AAGAC;AACAhB;AACAC;AAEEC;AAFS;;AAKd;;AAEH;;AAAO;AApCT;AA6CI;AACA;AACE;;AAEC;;AAEA;;AAEH;;AAAO;AATT;AAmBI;AACA;AAAST;;;AAEP;;AAKD;;AAEO;AAAO;AAAf;;;;AAWN;;AAEEtB;AACAnB;AACAyB;AACAL;AALK;AADT;AAYE;AACED;AADK;AADT;AAMI;;AAEA;;AAHF;AAQA;;AAEEA;AACAnB;AACAyB;AACAL;AALK;AADT;AAYE;AACED;AADK;AADT;AAMI;AACA;AACA;AACE8C;AADe;AAHnB;AAUP;;AAED;AAA4BC;AAAF;;;;;;AA+BxB;AAGM;;AAEEC;AAFK;;AAQP;;AAEEA;AAFK;;AAQP;;AAEEA;AAFK;;AAQP;;AAEEC;AACAb;AACAY;AAJK;;AAUP;;AAEEA;AAFK;AADT;AAUL;;AASD;;;;AAKID;AAJD;AAKoB;;AACnB;AAGI;;;;AAGJ;AAEE;;;AAAA;AAGF;AACuC;;;;AAIvC;AACuC;;;;AAIvC;AACuC;;;;;;AAKrC;AACD;;;AAIG;AACA;;AAIA;AACE7C;;AAEAG;AACAP;AAJK;;AASL;AACEe;AACA9B;AACAY;;;;;AAKE6C;AAJ8B;AAMhCtC;AACAiB;AACAD;AACAiB;AACAC;AACAa;AAfK;AADT;AAuBI;AACEpC;AACAlB;;AAEAO;AACAiB;AACAgB;AACAnC;AAPK;AADT;AAeA;AACEA;AADK;;AAQd;AAGHkD;;AAGA;;"}