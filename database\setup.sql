-- إعد<PERSON> قاعدة البيانات لنظام إدارة الملفات التعليمية
-- يجب تشغيل هذا الملف في Supabase SQL Editor

-- إن<PERSON>اء جدول profiles لتوسيع بيانات المستخدمين
CREATE TABLE IF NOT EXISTS public.profiles (
  id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  full_name TEXT,
  avatar_url TEXT,
  role TEXT DEFAULT 'student' CHECK (role IN ('student', 'admin')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إن<PERSON>اء فهارس على جدول profiles
CREATE INDEX IF NOT EXISTS profiles_email_idx ON public.profiles(email);
CREATE INDEX IF NOT EXISTS profiles_role_idx ON public.profiles(role);

-- <PERSON><PERSON><PERSON><PERSON><PERSON> جدول الملفات
CREATE TABLE IF NOT EXISTS public.files (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT,
  file_name TEXT NOT NULL,
  file_path TEXT NOT NULL,
  file_type TEXT NOT NULL,
  file_size BIGINT NOT NULL,
  subject TEXT,
  semester TEXT,
  uploaded_by UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
  download_count INTEGER DEFAULT 0,
  average_rating DECIMAL(3,2) DEFAULT 0.0,
  rating_count INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS files_title_idx ON public.files USING gin(to_tsvector('arabic', title));
CREATE INDEX IF NOT EXISTS files_subject_idx ON public.files(subject);
CREATE INDEX IF NOT EXISTS files_semester_idx ON public.files(semester);
CREATE INDEX IF NOT EXISTS files_uploaded_by_idx ON public.files(uploaded_by);
CREATE INDEX IF NOT EXISTS files_created_at_idx ON public.files(created_at DESC);
CREATE INDEX IF NOT EXISTS files_download_count_idx ON public.files(download_count DESC);
CREATE INDEX IF NOT EXISTS files_average_rating_idx ON public.files(average_rating DESC);

-- إنشاء جدول التقييمات
CREATE TABLE IF NOT EXISTS public.ratings (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  file_id UUID REFERENCES public.files(id) ON DELETE CASCADE,
  user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
  rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(file_id, user_id)
);

-- إنشاء فهارس لجدول التقييمات
CREATE INDEX IF NOT EXISTS ratings_file_id_idx ON public.ratings(file_id);
CREATE INDEX IF NOT EXISTS ratings_user_id_idx ON public.ratings(user_id);

-- إنشاء جدول التحميلات
CREATE TABLE IF NOT EXISTS public.downloads (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  file_id UUID REFERENCES public.files(id) ON DELETE CASCADE,
  user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
  downloaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء فهارس لجدول التحميلات
CREATE INDEX IF NOT EXISTS downloads_file_id_idx ON public.downloads(file_id);
CREATE INDEX IF NOT EXISTS downloads_user_id_idx ON public.downloads(user_id);
CREATE INDEX IF NOT EXISTS downloads_downloaded_at_idx ON public.downloads(downloaded_at DESC);

-- إنشاء جدول المفضلة
CREATE TABLE IF NOT EXISTS public.favorites (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  file_id UUID REFERENCES public.files(id) ON DELETE CASCADE,
  user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(file_id, user_id)
);

-- إنشاء فهارس لجدول المفضلة
CREATE INDEX IF NOT EXISTS favorites_file_id_idx ON public.favorites(file_id);
CREATE INDEX IF NOT EXISTS favorites_user_id_idx ON public.favorites(user_id);
CREATE INDEX IF NOT EXISTS favorites_created_at_idx ON public.favorites(created_at DESC);

-- إنشاء جدول الإشعارات
CREATE TABLE IF NOT EXISTS public.notifications (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
  type TEXT NOT NULL CHECK (type IN ('download', 'upload', 'rating', 'comment', 'user', 'system')),
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  file_id UUID REFERENCES public.files(id) ON DELETE CASCADE,
  related_user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
  is_read BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء فهارس لجدول الإشعارات
CREATE INDEX IF NOT EXISTS notifications_user_id_idx ON public.notifications(user_id);
CREATE INDEX IF NOT EXISTS notifications_type_idx ON public.notifications(type);
CREATE INDEX IF NOT EXISTS notifications_is_read_idx ON public.notifications(is_read);
CREATE INDEX IF NOT EXISTS notifications_created_at_idx ON public.notifications(created_at DESC);
CREATE INDEX IF NOT EXISTS notifications_file_id_idx ON public.notifications(file_id);

-- إنشاء جدول التعليقات
CREATE TABLE IF NOT EXISTS public.comments (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  file_id UUID REFERENCES public.files(id) ON DELETE CASCADE,
  user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء فهارس لجدول التعليقات
CREATE INDEX IF NOT EXISTS comments_file_id_idx ON public.comments(file_id);
CREATE INDEX IF NOT EXISTS comments_user_id_idx ON public.comments(user_id);
CREATE INDEX IF NOT EXISTS comments_created_at_idx ON public.comments(created_at DESC);

-- إنشاء جدول إعجابات التعليقات
CREATE TABLE IF NOT EXISTS public.comment_likes (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  comment_id UUID REFERENCES public.comments(id) ON DELETE CASCADE,
  user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(comment_id, user_id)
);

-- إنشاء فهارس لجدول إعجابات التعليقات
CREATE INDEX IF NOT EXISTS comment_likes_comment_id_idx ON public.comment_likes(comment_id);
CREATE INDEX IF NOT EXISTS comment_likes_user_id_idx ON public.comment_likes(user_id);

-- إنشاء جدول مشاركة الملفات
CREATE TABLE IF NOT EXISTS public.file_shares (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  file_id UUID REFERENCES public.files(id) ON DELETE CASCADE,
  created_by UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
  share_type TEXT DEFAULT 'public' CHECK (share_type IN ('public', 'private', 'users')),
  settings JSONB DEFAULT '{}',
  expires_at TIMESTAMP WITH TIME ZONE,
  access_count INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء فهارس لجدول مشاركة الملفات
CREATE INDEX IF NOT EXISTS file_shares_file_id_idx ON public.file_shares(file_id);
CREATE INDEX IF NOT EXISTS file_shares_created_by_idx ON public.file_shares(created_by);
CREATE INDEX IF NOT EXISTS file_shares_share_type_idx ON public.file_shares(share_type);
CREATE INDEX IF NOT EXISTS file_shares_expires_at_idx ON public.file_shares(expires_at);
CREATE INDEX IF NOT EXISTS file_shares_created_at_idx ON public.file_shares(created_at DESC);

-- تفعيل Row Level Security على جميع الجداول
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.files ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.ratings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.downloads ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.favorites ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.comment_likes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.file_shares ENABLE ROW LEVEL SECURITY;

-- سياسات RLS لجدول profiles
CREATE POLICY "Users can view own profile" ON public.profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.profiles
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON public.profiles
  FOR INSERT WITH CHECK (auth.uid() = id);

-- سياسات RLS لجدول files
CREATE POLICY "Anyone can view active files" ON public.files
  FOR SELECT USING (is_active = true);

CREATE POLICY "Only admins can insert files" ON public.files
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

CREATE POLICY "Admins and owners can update files" ON public.files
  FOR UPDATE USING (
    uploaded_by = auth.uid() OR
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

CREATE POLICY "Admins and owners can delete files" ON public.files
  FOR DELETE USING (
    uploaded_by = auth.uid() OR
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- سياسات RLS لجدول ratings
CREATE POLICY "Anyone can view ratings" ON public.ratings
  FOR SELECT USING (true);

CREATE POLICY "Users can insert own ratings" ON public.ratings
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own ratings" ON public.ratings
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own ratings" ON public.ratings
  FOR DELETE USING (auth.uid() = user_id);

-- سياسات RLS لجدول downloads
CREATE POLICY "Users can view own downloads" ON public.downloads
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own downloads" ON public.downloads
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Admins can view all downloads" ON public.downloads
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- سياسات RLS لجدول favorites
CREATE POLICY "Users can view own favorites" ON public.favorites
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own favorites" ON public.favorites
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete own favorites" ON public.favorites
  FOR DELETE USING (auth.uid() = user_id);

-- سياسات RLS لجدول notifications
CREATE POLICY "Users can view own notifications" ON public.notifications
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own notifications" ON public.notifications
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "System can insert notifications" ON public.notifications
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can delete own notifications" ON public.notifications
  FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Admins can view all notifications" ON public.notifications
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- سياسات RLS لجدول comments
CREATE POLICY "Anyone can view comments" ON public.comments
  FOR SELECT USING (true);

CREATE POLICY "Users can insert own comments" ON public.comments
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own comments" ON public.comments
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own comments" ON public.comments
  FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Admins can delete any comment" ON public.comments
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- سياسات RLS لجدول comment_likes
CREATE POLICY "Anyone can view comment likes" ON public.comment_likes
  FOR SELECT USING (true);

CREATE POLICY "Users can insert own comment likes" ON public.comment_likes
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete own comment likes" ON public.comment_likes
  FOR DELETE USING (auth.uid() = user_id);

-- سياسات RLS لجدول file_shares
CREATE POLICY "Anyone can view active public shares" ON public.file_shares
  FOR SELECT USING (is_active = true AND share_type = 'public');

CREATE POLICY "Users can view own shares" ON public.file_shares
  FOR SELECT USING (auth.uid() = created_by);

CREATE POLICY "Users can insert own shares" ON public.file_shares
  FOR INSERT WITH CHECK (auth.uid() = created_by);

CREATE POLICY "Users can update own shares" ON public.file_shares
  FOR UPDATE USING (auth.uid() = created_by);

CREATE POLICY "Users can delete own shares" ON public.file_shares
  FOR DELETE USING (auth.uid() = created_by);

CREATE POLICY "Admins can view all shares" ON public.file_shares
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- دالة لإنشاء ملف شخصي تلقائياً عند التسجيل
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, email, full_name)
  VALUES (NEW.id, NEW.email, NEW.raw_user_meta_data->>'full_name');
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- إنشاء trigger لتشغيل الدالة عند إنشاء مستخدم جديد
CREATE OR REPLACE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- دالة لتحديث متوسط التقييم في جدول الملفات
CREATE OR REPLACE FUNCTION update_file_rating()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE public.files
  SET 
    average_rating = (
      SELECT COALESCE(AVG(rating), 0)
      FROM public.ratings
      WHERE file_id = COALESCE(NEW.file_id, OLD.file_id)
    ),
    rating_count = (
      SELECT COUNT(*)
      FROM public.ratings
      WHERE file_id = COALESCE(NEW.file_id, OLD.file_id)
    )
  WHERE id = COALESCE(NEW.file_id, OLD.file_id);
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- إنشاء triggers لتحديث متوسط التقييم
CREATE TRIGGER rating_insert_trigger
  AFTER INSERT ON public.ratings
  FOR EACH ROW EXECUTE FUNCTION update_file_rating();

CREATE TRIGGER rating_update_trigger
  AFTER UPDATE ON public.ratings
  FOR EACH ROW EXECUTE FUNCTION update_file_rating();

CREATE TRIGGER rating_delete_trigger
  AFTER DELETE ON public.ratings
  FOR EACH ROW EXECUTE FUNCTION update_file_rating();

-- دالة لتحديث عداد التحميلات
CREATE OR REPLACE FUNCTION update_download_count()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE public.files
  SET download_count = (
    SELECT COUNT(*)
    FROM public.downloads
    WHERE file_id = NEW.file_id
  )
  WHERE id = NEW.file_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إنشاء trigger لتحديث عداد التحميلات
CREATE TRIGGER download_count_trigger
  AFTER INSERT ON public.downloads
  FOR EACH ROW EXECUTE FUNCTION update_download_count();

-- دالة لإنشاء إشعار جديد
CREATE OR REPLACE FUNCTION create_notification(
  p_user_id UUID,
  p_type TEXT,
  p_title TEXT,
  p_message TEXT,
  p_file_id UUID DEFAULT NULL,
  p_related_user_id UUID DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  notification_id UUID;
BEGIN
  INSERT INTO public.notifications (user_id, type, title, message, file_id, related_user_id)
  VALUES (p_user_id, p_type, p_title, p_message, p_file_id, p_related_user_id)
  RETURNING id INTO notification_id;

  RETURN notification_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- دالة لإنشاء إشعارات عند رفع ملف جديد
CREATE OR REPLACE FUNCTION notify_file_upload()
RETURNS TRIGGER AS $$
BEGIN
  -- إشعار للمديرين عند رفع ملف جديد
  INSERT INTO public.notifications (user_id, type, title, message, file_id, related_user_id)
  SELECT
    p.id,
    'upload',
    'ملف جديد تم رفعه',
    'تم رفع ملف جديد: ' || NEW.title,
    NEW.id,
    NEW.uploaded_by
  FROM public.profiles p
  WHERE p.role = 'admin' AND p.id != NEW.uploaded_by;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- دالة لإنشاء إشعارات عند تقييم ملف
CREATE OR REPLACE FUNCTION notify_file_rating()
RETURNS TRIGGER AS $$
DECLARE
  file_title TEXT;
  file_owner UUID;
BEGIN
  -- الحصول على معلومات الملف
  SELECT title, uploaded_by INTO file_title, file_owner
  FROM public.files
  WHERE id = NEW.file_id;

  -- إشعار لصاحب الملف (إذا لم يكن هو من قيم)
  IF file_owner != NEW.user_id THEN
    INSERT INTO public.notifications (user_id, type, title, message, file_id, related_user_id)
    VALUES (
      file_owner,
      'rating',
      'تقييم جديد لملفك',
      'تم تقييم ملفك "' || file_title || '" بـ ' || NEW.rating || ' نجوم',
      NEW.file_id,
      NEW.user_id
    );
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- دالة لإنشاء إشعارات عند تحميل ملف
CREATE OR REPLACE FUNCTION notify_file_download()
RETURNS TRIGGER AS $$
DECLARE
  file_title TEXT;
  file_owner UUID;
  downloader_name TEXT;
BEGIN
  -- الحصول على معلومات الملف والمحمل
  SELECT f.title, f.uploaded_by, p.full_name
  INTO file_title, file_owner, downloader_name
  FROM public.files f, public.profiles p
  WHERE f.id = NEW.file_id AND p.id = NEW.user_id;

  -- إشعار لصاحب الملف (إذا لم يكن هو من حمل)
  IF file_owner != NEW.user_id THEN
    INSERT INTO public.notifications (user_id, type, title, message, file_id, related_user_id)
    VALUES (
      file_owner,
      'download',
      'تم تحميل ملفك',
      'تم تحميل ملفك "' || file_title || '" بواسطة ' || COALESCE(downloader_name, 'مستخدم'),
      NEW.file_id,
      NEW.user_id
    );
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إنشاء triggers للإشعارات
CREATE TRIGGER file_upload_notification_trigger
  AFTER INSERT ON public.files
  FOR EACH ROW EXECUTE FUNCTION notify_file_upload();

CREATE TRIGGER file_rating_notification_trigger
  AFTER INSERT ON public.ratings
  FOR EACH ROW EXECUTE FUNCTION notify_file_rating();

CREATE TRIGGER file_download_notification_trigger
  AFTER INSERT ON public.downloads
  FOR EACH ROW EXECUTE FUNCTION notify_file_download();
