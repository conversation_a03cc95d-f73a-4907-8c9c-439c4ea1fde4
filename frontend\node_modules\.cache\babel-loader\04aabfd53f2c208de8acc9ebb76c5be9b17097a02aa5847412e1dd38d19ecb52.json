{"ast": null, "code": "import * as React from 'react';\nfunction useMediaQuery(query) {\n  // Keep track of the preference in state, start with the current match\n  const [isMatch, setIsMatch] = React.useState(() => {\n    if (typeof window !== 'undefined') {\n      return window.matchMedia(query).matches;\n    }\n    return;\n  }); // Watch for changes\n\n  React.useEffect(() => {\n    if (typeof window !== 'undefined') {\n      // Create a matcher\n      const matcher = window.matchMedia(query); // Create our handler\n\n      const onChange = ({\n        matches\n      }) => setIsMatch(matches); // Listen for changes\n\n      matcher.addListener(onChange);\n      return () => {\n        // Stop listening for changes\n        matcher.removeListener(onChange);\n      };\n    }\n    return;\n  }, [isMatch, query, setIsMatch]);\n  return isMatch;\n}\nexport { useMediaQuery as default };", "map": {"version": 3, "names": ["useMediaQuery", "query", "isMatch", "setIsMatch", "React", "useState", "window", "matchMedia", "matches", "useEffect", "matcher", "onChange", "addListener", "removeListener"], "sources": ["D:\\menasa\\frontend\\node_modules\\@tanstack\\react-query-devtools\\src\\useMediaQuery.ts"], "sourcesContent": ["import * as React from 'react'\n\nexport default function useMediaQuery(query: string): boolean | undefined {\n  // Keep track of the preference in state, start with the current match\n  const [isMatch, setIsMatch] = React.useState(() => {\n    if (typeof window !== 'undefined') {\n      return window.matchMedia(query).matches\n    }\n    return\n  })\n\n  // Watch for changes\n  React.useEffect(() => {\n    if (typeof window !== 'undefined') {\n      // Create a matcher\n      const matcher = window.matchMedia(query)\n\n      // Create our handler\n      const onChange = ({ matches }: { matches: boolean }) =>\n        setIsMatch(matches)\n\n      // Listen for changes\n      matcher.addListener(onChange)\n\n      return () => {\n        // Stop listening for changes\n        matcher.removeListener(onChange)\n      }\n    }\n    return\n  }, [isMatch, query, setIsMatch])\n\n  return isMatch\n}\n"], "mappings": ";AAEe,SAASA,aAATA,CAAuBC,KAAvB,EAA2D;EACxE;EACA,MAAM,CAACC,OAAD,EAAUC,UAAV,IAAwBC,KAAK,CAACC,QAAN,CAAe,MAAM;IACjD,IAAI,OAAOC,MAAP,KAAkB,WAAtB,EAAmC;MACjC,OAAOA,MAAM,CAACC,UAAP,CAAkBN,KAAlB,EAAyBO,OAAhC;IACD;IACD;GAJ4B,CAA9B,CAFwE;;EAUxEJ,KAAK,CAACK,SAAN,CAAgB,MAAM;IACpB,IAAI,OAAOH,MAAP,KAAkB,WAAtB,EAAmC;MACjC;MACA,MAAMI,OAAO,GAAGJ,MAAM,CAACC,UAAP,CAAkBN,KAAlB,CAAhB,CAFiC;;MAKjC,MAAMU,QAAQ,GAAGA,CAAC;QAAEH;MAAF,CAAD,KACfL,UAAU,CAACK,OAAD,CADZ,CALiC;;MASjCE,OAAO,CAACE,WAAR,CAAoBD,QAApB;MAEA,OAAO,MAAM;QACX;QACAD,OAAO,CAACG,cAAR,CAAuBF,QAAvB;OAFF;IAID;IACD;EACD,CAlBD,EAkBG,CAACT,OAAD,EAAUD,KAAV,EAAiBE,UAAjB,CAlBH;EAoBA,OAAOD,OAAP;AACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}