{"ast": null, "code": "import { createSelector } from 'reselect';\nimport { selectChartOffset } from './selectChartOffset';\nimport { selectChartHeight, selectChartWidth } from './containerSelectors';\nexport var selectPlotArea = createSelector([selectChartOffset, selectChartWidth, selectChartHeight], (offset, chartWidth, chartHeight) => {\n  if (!offset || chartWidth == null || chartHeight == null) {\n    return undefined;\n  }\n  return {\n    x: offset.left,\n    y: offset.top,\n    width: Math.max(0, chartWidth - offset.left - offset.right),\n    height: Math.max(0, chartHeight - offset.top - offset.bottom)\n  };\n});", "map": {"version": 3, "names": ["createSelector", "selectChartOffset", "selectChartHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectPlotArea", "offset", "chartWidth", "chartHeight", "undefined", "x", "left", "y", "top", "width", "Math", "max", "right", "height", "bottom"], "sources": ["D:/menasa/frontend/node_modules/recharts/es6/state/selectors/selectPlotArea.js"], "sourcesContent": ["import { createSelector } from 'reselect';\nimport { selectChartOffset } from './selectChartOffset';\nimport { selectChartHeight, selectChartWidth } from './containerSelectors';\nexport var selectPlotArea = createSelector([selectChartOffset, selectChartWidth, selectChartHeight], (offset, chartWidth, chartHeight) => {\n  if (!offset || chartWidth == null || chartHeight == null) {\n    return undefined;\n  }\n  return {\n    x: offset.left,\n    y: offset.top,\n    width: Math.max(0, chartWidth - offset.left - offset.right),\n    height: Math.max(0, chartHeight - offset.top - offset.bottom)\n  };\n});"], "mappings": "AAAA,SAASA,cAAc,QAAQ,UAAU;AACzC,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,iBAAiB,EAAEC,gBAAgB,QAAQ,sBAAsB;AAC1E,OAAO,IAAIC,cAAc,GAAGJ,cAAc,CAAC,CAACC,iBAAiB,EAAEE,gBAAgB,EAAED,iBAAiB,CAAC,EAAE,CAACG,MAAM,EAAEC,UAAU,EAAEC,WAAW,KAAK;EACxI,IAAI,CAACF,MAAM,IAAIC,UAAU,IAAI,IAAI,IAAIC,WAAW,IAAI,IAAI,EAAE;IACxD,OAAOC,SAAS;EAClB;EACA,OAAO;IACLC,CAAC,EAAEJ,MAAM,CAACK,IAAI;IACdC,CAAC,EAAEN,MAAM,CAACO,GAAG;IACbC,KAAK,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAET,UAAU,GAAGD,MAAM,CAACK,IAAI,GAAGL,MAAM,CAACW,KAAK,CAAC;IAC3DC,MAAM,EAAEH,IAAI,CAACC,GAAG,CAAC,CAAC,EAAER,WAAW,GAAGF,MAAM,CAACO,GAAG,GAAGP,MAAM,CAACa,MAAM;EAC9D,CAAC;AACH,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}