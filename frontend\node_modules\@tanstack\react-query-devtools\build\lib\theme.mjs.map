{"version": 3, "file": "theme.mjs", "sources": ["../../src/theme.tsx"], "sourcesContent": ["'use client'\nimport * as React from 'react'\n\nexport const defaultTheme = {\n  background: '#0b1521',\n  backgroundAlt: '#132337',\n  foreground: 'white',\n  gray: '#3f4e60',\n  grayAlt: '#222e3e',\n  inputBackgroundColor: '#fff',\n  inputTextColor: '#000',\n  success: '#00ab52',\n  danger: '#ff0085',\n  active: '#006bff',\n  paused: '#8c49eb',\n  warning: '#ffb200',\n} as const\n\nexport type Theme = typeof defaultTheme\ninterface ProviderProps {\n  theme: Theme\n  children?: React.ReactNode\n}\n\nconst ThemeContext = React.createContext(defaultTheme)\n\nexport function ThemeProvider({ theme, ...rest }: ProviderProps) {\n  return <ThemeContext.Provider value={theme} {...rest} />\n}\n\nexport function useTheme() {\n  return React.useContext(ThemeContext)\n}\n"], "names": ["background", "backgroundAlt", "foreground", "gray", "grayAlt", "inputBackgroundColor", "inputTextColor", "success", "danger", "active", "paused", "warning"], "mappings": ";;;;AAGO;AACLA;AACAC;AACAC;AACAC;AACAC;AACAC;AACAC;AACAC;AACAC;AACAC;AACAC;AACAC;AAZ0B;AAqB5B;AAEO;;;AAAuB;;AACE;AAAvB;AACR;AAEM;AACL;AACD;;"}