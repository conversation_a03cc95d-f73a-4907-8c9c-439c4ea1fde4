import React, { useState } from 'react';
import styled from 'styled-components';
import { useQuery } from 'react-query';
import Sidebar from '../components/admin/Sidebar';
import FileUpload from '../components/admin/FileUpload';
import UserManagement from '../components/admin/UserManagement';
import Statistics from '../components/admin/Statistics';
import FileManagement from '../components/admin/FileManagement';

const AdminLayout = styled.div`
  display: flex;
  min-height: 100vh;
`;

const Content = styled.div`
  flex: 1;
  padding: 20px;
  background: #f5f5f5;
`;

function AdminDashboard() {
  const [activeTab, setActiveTab] = useState('dashboard');

  const renderContent = () => {
    switch(activeTab) {
      case 'dashboard':
        return <Statistics />;
      case 'files':
        return <FileManagement />;
      case 'upload':
        return <FileUpload />;
      case 'users':
        return <UserManagement />;
      default:
        return <Statistics />;
    }
  };

  return (
    <AdminLayout>
      <Sidebar activeTab={activeTab} onTabChange={setActiveTab} />
      <Content>
        {renderContent()}
      </Content>
    </AdminLayout>
  );
}

export default AdminDashboard;