import React from 'react';
import styled, { css } from 'styled-components';
import { motion } from 'framer-motion';

const CardBase = styled(motion.div)`
  background: ${({ theme }) => theme.colors.white};
  border-radius: ${({ theme }) => theme.borderRadius.xl};
  box-shadow: ${({ theme }) => theme.shadows.base};
  overflow: hidden;
  transition: all ${({ theme }) => theme.transitions.normal};
  border: 1px solid ${({ theme }) => theme.colors.gray[100]};

  /* Hover Effect */
  ${({ hoverable, theme }) =>
    hoverable &&
    css`
      cursor: pointer;
      
      &:hover {
        box-shadow: ${theme.shadows.lg};
        transform: translateY(-2px);
      }
    `}

  /* Padding Variants */
  ${({ padding, theme }) => {
    switch (padding) {
      case 'none':
        return css`
          padding: 0;
        `;
      case 'sm':
        return css`
          padding: ${theme.spacing[4]};
        `;
      case 'lg':
        return css`
          padding: ${theme.spacing[8]};
        `;
      case 'xl':
        return css`
          padding: ${theme.spacing[10]};
        `;
      default:
        return css`
          padding: ${theme.spacing[6]};
        `;
    }
  }}

  /* Border Variants */
  ${({ variant, theme }) => {
    switch (variant) {
      case 'outlined':
        return css`
          border: 2px solid ${theme.colors.gray[200]};
          box-shadow: none;
        `;
      case 'elevated':
        return css`
          box-shadow: ${theme.shadows.xl};
        `;
      case 'flat':
        return css`
          box-shadow: none;
          border: none;
        `;
      default:
        return '';
    }
  }}
`;

const CardHeader = styled.div`
  padding: ${({ theme }) => theme.spacing[6]};
  border-bottom: 1px solid ${({ theme }) => theme.colors.gray[100]};
  
  ${({ noPadding, theme }) =>
    noPadding &&
    css`
      padding: 0;
      border-bottom: none;
    `}
`;

const CardTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes.xl};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  color: ${({ theme }) => theme.colors.gray[900]};
  margin: 0;
  line-height: ${({ theme }) => theme.lineHeights.tight};
`;

const CardSubtitle = styled.p`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.gray[600]};
  margin: ${({ theme }) => theme.spacing[1]} 0 0 0;
  line-height: ${({ theme }) => theme.lineHeights.normal};
`;

const CardBody = styled.div`
  padding: ${({ theme }) => theme.spacing[6]};
  
  ${({ noPadding }) =>
    noPadding &&
    css`
      padding: 0;
    `}
`;

const CardFooter = styled.div`
  padding: ${({ theme }) => theme.spacing[6]};
  border-top: 1px solid ${({ theme }) => theme.colors.gray[100]};
  background: ${({ theme }) => theme.colors.gray[25]};
  
  ${({ noPadding, theme }) =>
    noPadding &&
    css`
      padding: 0;
      border-top: none;
      background: transparent;
    `}
`;

const CardImage = styled.div`
  width: 100%;
  height: ${({ height }) => height || '200px'};
  background-image: url(${({ src }) => src});
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;

  ${({ rounded, theme }) =>
    rounded &&
    css`
      border-radius: ${theme.borderRadius.lg};
      margin: ${theme.spacing[2]};
      height: calc(${({ height }) => height || '200px'} - ${theme.spacing[4]});
    `}
`;

const Card = ({
  children,
  title,
  subtitle,
  image,
  imageHeight,
  imageRounded = false,
  header,
  footer,
  variant = 'default',
  padding = 'md',
  hoverable = false,
  onClick,
  className,
  ...props
}) => {
  const cardProps = {
    variant,
    padding: header || footer ? 'none' : padding,
    hoverable,
    onClick,
    className,
    ...props
  };

  const motionProps = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    whileHover: hoverable ? { y: -2 } : {},
    whileTap: hoverable ? { scale: 0.98 } : {},
  };

  return (
    <CardBase {...cardProps} {...motionProps}>
      {image && (
        <CardImage
          src={image}
          height={imageHeight}
          rounded={imageRounded}
        />
      )}
      
      {(title || subtitle || header) && (
        <CardHeader noPadding={!title && !subtitle}>
          {header || (
            <>
              {title && <CardTitle>{title}</CardTitle>}
              {subtitle && <CardSubtitle>{subtitle}</CardSubtitle>}
            </>
          )}
        </CardHeader>
      )}
      
      <CardBody noPadding={padding === 'none'}>
        {children}
      </CardBody>
      
      {footer && (
        <CardFooter noPadding={padding === 'none'}>
          {footer}
        </CardFooter>
      )}
    </CardBase>
  );
};

// Export sub-components for custom layouts
Card.Header = CardHeader;
Card.Title = CardTitle;
Card.Subtitle = CardSubtitle;
Card.Body = CardBody;
Card.Footer = CardFooter;
Card.Image = CardImage;

export default Card;
