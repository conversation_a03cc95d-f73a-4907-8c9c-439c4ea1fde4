# 🚀 دليل التشغيل السريع

## متطلبات النظام

- Node.js (الإصدار 16 أو أحدث)
- MongoDB
- Git

## التشغيل السريع

### 1. تحميل المشروع
```bash
git clone <repository-url>
cd educational-file-manager
```

### 2. التشغيل التلقائي

#### على Windows:
```bash
start.bat
```

#### على Linux/Mac:
```bash
chmod +x start.sh
./start.sh
```

### 3. التشغيل اليدوي

#### تثبيت التبعيات:
```bash
# تبعيات المشروع الرئيسي
npm install

# تبعيات Frontend
cd frontend
npm install

# تبعيات Backend
cd ../backend
npm install
```

#### إعداد قاعدة البيانات:
```bash
# تأكد من تشغيل MongoDB
mongod

# أو باستخدام Docker
docker run -d -p 27017:27017 --name mongodb mongo
```

#### إعداد متغيرات البيئة:
```bash
cd backend
cp .env.example .env
# قم بتحرير ملف .env حسب إعداداتك
```

#### تشغيل التطبيق:
```bash
# من المجلد الرئيسي
npm run dev
```

## الوصول للتطبيق

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:5000

## حسابات تجريبية

### مدير النظام:
- البريد الإلكتروني: <EMAIL>
- كلمة المرور: admin123

### طالب:
- البريد الإلكتروني: <EMAIL>
- كلمة المرور: student123

## الميزات الرئيسية

### للطلاب:
- ✅ تصفح الملفات التعليمية
- ✅ البحث والفلترة المتقدمة
- ✅ تحميل الملفات
- ✅ تقييم الملفات
- ✅ إضافة للمفضلة
- ✅ معاينة الملفات

### للمديرين:
- ✅ رفع الملفات الجديدة
- ✅ إدارة المستخدمين
- ✅ إحصائيات شاملة
- ✅ إدارة الملفات
- ✅ تحليلات الاستخدام

## استكشاف الأخطاء

### مشكلة في تثبيت التبعيات:
```bash
# مسح cache npm
npm cache clean --force

# حذف node_modules وإعادة التثبيت
rm -rf node_modules package-lock.json
npm install
```

### مشكلة في الاتصال بقاعدة البيانات:
- تأكد من تشغيل MongoDB
- تحقق من إعدادات MONGODB_URI في ملف .env

### مشكلة في تشغيل Frontend:
```bash
cd frontend
npm start
```

### مشكلة في تشغيل Backend:
```bash
cd backend
npm run dev
```

## الدعم

إذا واجهت أي مشاكل:
1. تحقق من ملف README.md للتفاصيل الكاملة
2. راجع ملفات الـ logs للأخطاء
3. تأكد من تثبيت جميع المتطلبات
4. تواصل مع فريق التطوير

## نصائح مفيدة

- استخدم `npm run dev` لتشغيل Frontend و Backend معاً
- استخدم `npm run build` لبناء النسخة النهائية
- راجع ملف package.json لمعرفة جميع الأوامر المتاحة

---

🎉 **مبروك! التطبيق جاهز للاستخدام**
