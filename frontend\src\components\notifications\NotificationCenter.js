import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { 
  FiBell, 
  FiX, 
  FiCheck, 
  FiTrash2, 
  FiSettings,
  FiDownload,
  FiStar,
  FiUpload,
  FiUser,
  FiMessageCircle
} from 'react-icons/fi';
import { useAuth } from '../../contexts/AuthContext';
import { notifications } from '../../services/api';
import { formatDistanceToNow } from 'date-fns';
import { ar } from 'date-fns/locale';

const NotificationContainer = styled.div`
  position: relative;
  display: inline-block;
`;

const NotificationButton = styled(motion.button)`
  position: relative;
  background: none;
  border: none;
  padding: ${({ theme }) => theme.spacing[2]};
  border-radius: 50%;
  cursor: pointer;
  color: ${({ theme }) => theme.colors.gray[600]};
  transition: all 0.2s ease;

  &:hover {
    background: ${({ theme }) => theme.colors.gray[100]};
    color: ${({ theme }) => theme.colors.primary[600]};
  }
`;

const NotificationBadge = styled(motion.span)`
  position: absolute;
  top: 0;
  right: 0;
  background: ${({ theme }) => theme.colors.error[500]};
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 10px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid white;
`;

const NotificationPanel = styled(motion.div)`
  position: absolute;
  top: 100%;
  right: 0;
  width: 380px;
  max-height: 500px;
  background: white;
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  box-shadow: ${({ theme }) => theme.shadows.xl};
  border: 1px solid ${({ theme }) => theme.colors.gray[200]};
  z-index: 1000;
  overflow: hidden;
  
  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    width: 320px;
    right: -20px;
  }
`;

const NotificationHeader = styled.div`
  padding: ${({ theme }) => theme.spacing[4]};
  border-bottom: 1px solid ${({ theme }) => theme.colors.gray[200]};
  display: flex;
  justify-content: between;
  align-items: center;
`;

const NotificationTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  color: ${({ theme }) => theme.colors.gray[900]};
  margin: 0;
`;

const NotificationActions = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing[2]};
`;

const ActionButton = styled(motion.button)`
  background: none;
  border: none;
  padding: ${({ theme }) => theme.spacing[1]};
  border-radius: ${({ theme }) => theme.borderRadius.base};
  cursor: pointer;
  color: ${({ theme }) => theme.colors.gray[500]};
  
  &:hover {
    background: ${({ theme }) => theme.colors.gray[100]};
    color: ${({ theme }) => theme.colors.gray[700]};
  }
`;

const NotificationList = styled.div`
  max-height: 400px;
  overflow-y: auto;
`;

const NotificationItem = styled(motion.div)`
  padding: ${({ theme }) => theme.spacing[4]};
  border-bottom: 1px solid ${({ theme }) => theme.colors.gray[100]};
  cursor: pointer;
  background: ${({ isRead, theme }) => 
    isRead ? 'transparent' : theme.colors.blue[50]};
  
  &:hover {
    background: ${({ theme }) => theme.colors.gray[50]};
  }
  
  &:last-child {
    border-bottom: none;
  }
`;

const NotificationContent = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing[3]};
`;

const NotificationIcon = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: ${({ type, theme }) => {
    switch (type) {
      case 'download': return theme.colors.blue[100];
      case 'upload': return theme.colors.green[100];
      case 'rating': return theme.colors.yellow[100];
      case 'comment': return theme.colors.purple[100];
      case 'user': return theme.colors.indigo[100];
      default: return theme.colors.gray[100];
    }
  }};
  color: ${({ type, theme }) => {
    switch (type) {
      case 'download': return theme.colors.blue[600];
      case 'upload': return theme.colors.green[600];
      case 'rating': return theme.colors.yellow[600];
      case 'comment': return theme.colors.purple[600];
      case 'user': return theme.colors.indigo[600];
      default: return theme.colors.gray[600];
    }
  }};
`;

const NotificationDetails = styled.div`
  flex: 1;
`;

const NotificationMessage = styled.p`
  margin: 0 0 ${({ theme }) => theme.spacing[1]} 0;
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.gray[900]};
  line-height: ${({ theme }) => theme.lineHeights.relaxed};
`;

const NotificationTime = styled.span`
  font-size: ${({ theme }) => theme.fontSizes.xs};
  color: ${({ theme }) => theme.colors.gray[500]};
`;

const EmptyState = styled.div`
  padding: ${({ theme }) => theme.spacing[8]};
  text-align: center;
  color: ${({ theme }) => theme.colors.gray[500]};
`;

const NotificationCenter = () => {
  const [isOpen, setIsOpen] = useState(false);
  const { user } = useAuth();
  const queryClient = useQueryClient();

  // جلب الإشعارات
  const { data: notificationsData, isLoading } = useQuery({
    queryKey: ['notifications', user?.id],
    queryFn: () => notifications.getAll(),
    enabled: !!user,
    refetchInterval: 30000, // تحديث كل 30 ثانية
  });

  const unreadCount = notificationsData?.filter(n => !n.is_read).length || 0;

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'download': return <FiDownload size={20} />;
      case 'upload': return <FiUpload size={20} />;
      case 'rating': return <FiStar size={20} />;
      case 'comment': return <FiMessageCircle size={20} />;
      case 'user': return <FiUser size={20} />;
      default: return <FiBell size={20} />;
    }
  };

  const handleNotificationClick = async (notification) => {
    if (!notification.is_read) {
      try {
        await notifications.markAsRead(notification.id);
        queryClient.invalidateQueries(['notifications']);
      } catch (error) {
        console.error('Error marking notification as read:', error);
      }
    }

    // التنقل حسب نوع الإشعار
    if (notification.file_id) {
      window.open(`/file/${notification.file_id}`, '_blank');
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      await notifications.markAllAsRead();
      queryClient.invalidateQueries(['notifications']);
    } catch (error) {
      console.error('Error marking all as read:', error);
    }
  };

  const handleClearAll = async () => {
    try {
      await notifications.clearAll();
      queryClient.invalidateQueries(['notifications']);
    } catch (error) {
      console.error('Error clearing notifications:', error);
    }
  };

  const formatTime = (dateString) => {
    return formatDistanceToNow(new Date(dateString), {
      addSuffix: true,
      locale: ar
    });
  };

  return (
    <NotificationContainer>
      <NotificationButton
        onClick={() => setIsOpen(!isOpen)}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        <FiBell size={20} />
        {unreadCount > 0 && (
          <NotificationBadge
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ type: "spring", stiffness: 500, damping: 30 }}
          >
            {unreadCount > 99 ? '99+' : unreadCount}
          </NotificationBadge>
        )}
      </NotificationButton>

      <AnimatePresence>
        {isOpen && (
          <NotificationPanel
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
          >
            <NotificationHeader>
              <NotificationTitle>الإشعارات</NotificationTitle>
              <NotificationActions>
                <ActionButton
                  onClick={handleMarkAllAsRead}
                  title="تحديد الكل كمقروء"
                >
                  <FiCheck size={16} />
                </ActionButton>
                <ActionButton
                  onClick={handleClearAll}
                  title="مسح الكل"
                >
                  <FiTrash2 size={16} />
                </ActionButton>
                <ActionButton
                  onClick={() => setIsOpen(false)}
                  title="إغلاق"
                >
                  <FiX size={16} />
                </ActionButton>
              </NotificationActions>
            </NotificationHeader>

            <NotificationList>
              {isLoading ? (
                <EmptyState>جاري تحميل الإشعارات...</EmptyState>
              ) : notificationsData?.length === 0 ? (
                <EmptyState>
                  <FiBell size={48} style={{ marginBottom: '16px', opacity: 0.3 }} />
                  <div>لا توجد إشعارات</div>
                </EmptyState>
              ) : (
                notificationsData?.map((notification, index) => (
                  <NotificationItem
                    key={notification.id}
                    isRead={notification.is_read}
                    onClick={() => handleNotificationClick(notification)}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.05 }}
                  >
                    <NotificationContent>
                      <NotificationIcon type={notification.type}>
                        {getNotificationIcon(notification.type)}
                      </NotificationIcon>
                      <NotificationDetails>
                        <NotificationMessage>
                          {notification.message}
                        </NotificationMessage>
                        <NotificationTime>
                          {formatTime(notification.created_at)}
                        </NotificationTime>
                      </NotificationDetails>
                    </NotificationContent>
                  </NotificationItem>
                ))
              )}
            </NotificationList>
          </NotificationPanel>
        )}
      </AnimatePresence>
    </NotificationContainer>
  );
};

export default NotificationCenter;
