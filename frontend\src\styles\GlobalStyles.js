import { createGlobalStyle } from 'styled-components';

export const GlobalStyles = createGlobalStyle`
  @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&family=Roboto:wght@300;400;500;700&display=swap');

  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  html {
    font-size: 16px;
    scroll-behavior: smooth;
  }

  body {
    font-family: ${({ theme }) => theme.fonts.primary};
    font-size: ${({ theme }) => theme.fontSizes.base};
    line-height: ${({ theme }) => theme.lineHeights.normal};
    color: ${({ theme }) => theme.colors.gray[800]};
    background-color: ${({ theme }) => theme.colors.gray[50]};
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    direction: rtl;
    text-align: right;
  }

  #root {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
  }

  /* Scrollbar Styling */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: ${({ theme }) => theme.colors.gray[100]};
    border-radius: ${({ theme }) => theme.borderRadius.base};
  }

  ::-webkit-scrollbar-thumb {
    background: ${({ theme }) => theme.colors.gray[300]};
    border-radius: ${({ theme }) => theme.borderRadius.base};
    
    &:hover {
      background: ${({ theme }) => theme.colors.gray[400]};
    }
  }

  /* Focus Styles */
  *:focus {
    outline: 2px solid ${({ theme }) => theme.colors.primary[500]};
    outline-offset: 2px;
  }

  /* Button Reset */
  button {
    border: none;
    background: none;
    cursor: pointer;
    font-family: inherit;
  }

  /* Link Reset */
  a {
    color: inherit;
    text-decoration: none;
  }

  /* Input Reset */
  input, textarea, select {
    font-family: inherit;
    font-size: inherit;
    border: none;
    outline: none;
  }

  /* Heading Styles */
  h1, h2, h3, h4, h5, h6 {
    font-weight: ${({ theme }) => theme.fontWeights.semibold};
    line-height: ${({ theme }) => theme.lineHeights.tight};
    color: ${({ theme }) => theme.colors.gray[900]};
  }

  h1 {
    font-size: ${({ theme }) => theme.fontSizes['4xl']};
    margin-bottom: ${({ theme }) => theme.spacing[6]};
  }

  h2 {
    font-size: ${({ theme }) => theme.fontSizes['3xl']};
    margin-bottom: ${({ theme }) => theme.spacing[5]};
  }

  h3 {
    font-size: ${({ theme }) => theme.fontSizes['2xl']};
    margin-bottom: ${({ theme }) => theme.spacing[4]};
  }

  h4 {
    font-size: ${({ theme }) => theme.fontSizes.xl};
    margin-bottom: ${({ theme }) => theme.spacing[3]};
  }

  h5 {
    font-size: ${({ theme }) => theme.fontSizes.lg};
    margin-bottom: ${({ theme }) => theme.spacing[2]};
  }

  h6 {
    font-size: ${({ theme }) => theme.fontSizes.base};
    margin-bottom: ${({ theme }) => theme.spacing[2]};
  }

  /* Paragraph Styles */
  p {
    margin-bottom: ${({ theme }) => theme.spacing[4]};
    line-height: ${({ theme }) => theme.lineHeights.relaxed};
  }

  /* List Styles */
  ul, ol {
    margin-bottom: ${({ theme }) => theme.spacing[4]};
    padding-right: ${({ theme }) => theme.spacing[6]};
  }

  li {
    margin-bottom: ${({ theme }) => theme.spacing[1]};
  }

  /* Utility Classes */
  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 ${({ theme }) => theme.spacing[4]};
  }

  .text-center {
    text-align: center;
  }

  .text-left {
    text-align: left;
  }

  .text-right {
    text-align: right;
  }

  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  /* Animation Classes */
  .fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  .slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .scale-in {
    animation: scaleIn 0.2s ease-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes slideUp {
    from {
      transform: translateY(20px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @keyframes scaleIn {
    from {
      transform: scale(0.95);
      opacity: 0;
    }
    to {
      transform: scale(1);
      opacity: 1;
    }
  }

  /* Responsive Design */
  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    html {
      font-size: 14px;
    }

    .container {
      padding: 0 ${({ theme }) => theme.spacing[3]};
    }

    /* Mobile-specific adjustments */
    body {
      font-size: ${({ theme }) => theme.fontSizes.sm};
    }

    /* Reduce spacing on mobile */
    h1 {
      font-size: ${({ theme }) => theme.fontSizes['2xl']};
      margin-bottom: ${({ theme }) => theme.spacing[4]};
    }

    h2 {
      font-size: ${({ theme }) => theme.fontSizes.xl};
      margin-bottom: ${({ theme }) => theme.spacing[3]};
    }

    h3 {
      font-size: ${({ theme }) => theme.fontSizes.lg};
      margin-bottom: ${({ theme }) => theme.spacing[2]};
    }

    /* Mobile navigation adjustments */
    .mobile-hidden {
      display: none !important;
    }

    .mobile-full-width {
      width: 100% !important;
    }

    /* Touch-friendly buttons */
    button {
      min-height: 44px;
      min-width: 44px;
    }

    /* Improved touch targets */
    a, button, input, select, textarea {
      min-height: 44px;
    }
  }

  @media (max-width: ${({ theme }) => theme.breakpoints.md}) {
    h1 {
      font-size: ${({ theme }) => theme.fontSizes['3xl']};
    }

    h2 {
      font-size: ${({ theme }) => theme.fontSizes['2xl']};
    }

    /* Tablet adjustments */
    .tablet-hidden {
      display: none !important;
    }

    .tablet-stack {
      flex-direction: column !important;
    }

    /* Grid adjustments for tablets */
    .responsive-grid {
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)) !important;
    }
  }

  @media (max-width: ${({ theme }) => theme.breakpoints.lg}) {
    /* Large tablet / small desktop adjustments */
    .lg-hidden {
      display: none !important;
    }

    .lg-stack {
      flex-direction: column !important;
    }
  }

  /* High DPI displays */
  @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    /* Sharper text rendering */
    body {
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }
  }

  /* Landscape orientation adjustments */
  @media (orientation: landscape) and (max-height: 500px) {
    /* Reduce header sizes in landscape mode on small screens */
    h1 {
      font-size: ${({ theme }) => theme.fontSizes.xl};
      margin-bottom: ${({ theme }) => theme.spacing[2]};
    }

    h2 {
      font-size: ${({ theme }) => theme.fontSizes.lg};
      margin-bottom: ${({ theme }) => theme.spacing[2]};
    }
  }

  /* Print styles */
  @media print {
    * {
      background: white !important;
      color: black !important;
      box-shadow: none !important;
    }

    .no-print {
      display: none !important;
    }

    a {
      text-decoration: underline;
    }

    h1, h2, h3, h4, h5, h6 {
      page-break-after: avoid;
    }

    img {
      max-width: 100% !important;
    }
  }

  /* Accessibility improvements */
  @media (prefers-reduced-motion: reduce) {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }

  @media (prefers-color-scheme: dark) {
    /* Dark mode preferences - can be overridden by theme */
    :root {
      color-scheme: dark;
    }
  }

  /* Focus improvements for keyboard navigation */
  @media (prefers-reduced-motion: no-preference) {
    :focus {
      transition: outline-offset 0.2s ease;
    }
  }

  /* Hover improvements for devices that support it */
  @media (hover: hover) {
    .hover-effect:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }

  /* Touch device optimizations */
  @media (hover: none) {
    .hover-effect {
      /* Remove hover effects on touch devices */
      transform: none !important;
    }

    /* Larger touch targets */
    button, a, input, select {
      min-height: 48px;
      min-width: 48px;
    }
  }
`;

export default GlobalStyles;
