{"name": "frontend", "version": "1.0.0", "dependencies": {"@supabase/supabase-js": "^2.38.0", "@tanstack/react-query": "^4.35.0", "@tanstack/react-query-devtools": "^4.40.1", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "framer-motion": "^10.16.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-icons": "^4.11.0", "react-router-dom": "^6.15.0", "react-scripts": "5.0.1", "react-toastify": "^9.1.3", "recharts": "^3.1.0", "styled-components": "^6.0.7"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}