{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nconst toNumber = require('./toNumber.js');\nfunction toFinite(value) {\n  if (!value) {\n    return value === 0 ? value : 0;\n  }\n  value = toNumber.toNumber(value);\n  if (value === Infinity || value === -Infinity) {\n    const sign = value < 0 ? -1 : 1;\n    return sign * Number.MAX_VALUE;\n  }\n  return value === value ? value : 0;\n}\nexports.toFinite = toFinite;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "toNumber", "require", "toFinite", "Infinity", "sign", "Number", "MAX_VALUE"], "sources": ["D:/menasa/frontend/node_modules/es-toolkit/dist/compat/util/toFinite.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nconst toNumber = require('./toNumber.js');\n\nfunction toFinite(value) {\n    if (!value) {\n        return value === 0 ? value : 0;\n    }\n    value = toNumber.toNumber(value);\n    if (value === Infinity || value === -Infinity) {\n        const sign = value < 0 ? -1 : 1;\n        return sign * Number.MAX_VALUE;\n    }\n    return value === value ? value : 0;\n}\n\nexports.toFinite = toFinite;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,MAAMC,QAAQ,GAAGC,OAAO,CAAC,eAAe,CAAC;AAEzC,SAASC,QAAQA,CAACH,KAAK,EAAE;EACrB,IAAI,CAACA,KAAK,EAAE;IACR,OAAOA,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAG,CAAC;EAClC;EACAA,KAAK,GAAGC,QAAQ,CAACA,QAAQ,CAACD,KAAK,CAAC;EAChC,IAAIA,KAAK,KAAKI,QAAQ,IAAIJ,KAAK,KAAK,CAACI,QAAQ,EAAE;IAC3C,MAAMC,IAAI,GAAGL,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;IAC/B,OAAOK,IAAI,GAAGC,MAAM,CAACC,SAAS;EAClC;EACA,OAAOP,KAAK,KAAKA,KAAK,GAAGA,KAAK,GAAG,CAAC;AACtC;AAEAH,OAAO,CAACM,QAAQ,GAAGA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}