{"ast": null, "code": "import * as React from 'react';\nimport { createContext, useContext, useState } from 'react';\nimport { uniqueId } from '../util/DataUtils';\nimport { usePlotArea } from '../hooks';\nvar ClipPathIdContext = /*#__PURE__*/createContext(undefined);\n\n/**\n * Generates a unique clip path ID for use in SVG elements,\n * and puts it in a context provider.\n *\n * To read the clip path ID, use the `useClipPathId` hook,\n * or render `<ClipPath>` component which will automatically use the ID from this context.\n *\n * @param props children - React children to be wrapped by the provider\n * @returns React Context Provider\n */\nexport var ClipPathProvider = _ref => {\n  var {\n    children\n  } = _ref;\n  var [clipPathId] = useState(\"\".concat(uniqueId('recharts'), \"-clip\"));\n  var plotArea = usePlotArea();\n  if (plotArea == null) {\n    return null;\n  }\n  var {\n    x,\n    y,\n    width,\n    height\n  } = plotArea;\n  return /*#__PURE__*/React.createElement(ClipPathIdContext.Provider, {\n    value: clipPathId\n  }, /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"clipPath\", {\n    id: clipPathId\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    x: x,\n    y: y,\n    height: height,\n    width: width\n  }))), children);\n};\nexport var useClipPathId = () => {\n  return useContext(ClipPathIdContext);\n};", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "uniqueId", "usePlotArea", "ClipPathIdContext", "undefined", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref", "children", "clipPathId", "concat", "<PERSON><PERSON><PERSON>", "x", "y", "width", "height", "createElement", "Provider", "value", "id", "useClipPathId"], "sources": ["D:/menasa/frontend/node_modules/recharts/es6/container/ClipPathProvider.js"], "sourcesContent": ["import * as React from 'react';\nimport { createContext, useContext, useState } from 'react';\nimport { uniqueId } from '../util/DataUtils';\nimport { usePlotArea } from '../hooks';\nvar ClipPathIdContext = /*#__PURE__*/createContext(undefined);\n\n/**\n * Generates a unique clip path ID for use in SVG elements,\n * and puts it in a context provider.\n *\n * To read the clip path ID, use the `useClipPathId` hook,\n * or render `<ClipPath>` component which will automatically use the ID from this context.\n *\n * @param props children - React children to be wrapped by the provider\n * @returns React Context Provider\n */\nexport var ClipPathProvider = _ref => {\n  var {\n    children\n  } = _ref;\n  var [clipPathId] = useState(\"\".concat(uniqueId('recharts'), \"-clip\"));\n  var plotArea = usePlotArea();\n  if (plotArea == null) {\n    return null;\n  }\n  var {\n    x,\n    y,\n    width,\n    height\n  } = plotArea;\n  return /*#__PURE__*/React.createElement(ClipPathIdContext.Provider, {\n    value: clipPathId\n  }, /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"clipPath\", {\n    id: clipPathId\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    x: x,\n    y: y,\n    height: height,\n    width: width\n  }))), children);\n};\nexport var useClipPathId = () => {\n  return useContext(ClipPathIdContext);\n};"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,QAAQ,OAAO;AAC3D,SAASC,QAAQ,QAAQ,mBAAmB;AAC5C,SAASC,WAAW,QAAQ,UAAU;AACtC,IAAIC,iBAAiB,GAAG,aAAaL,aAAa,CAACM,SAAS,CAAC;;AAE7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,gBAAgB,GAAGC,IAAI,IAAI;EACpC,IAAI;IACFC;EACF,CAAC,GAAGD,IAAI;EACR,IAAI,CAACE,UAAU,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAACS,MAAM,CAACR,QAAQ,CAAC,UAAU,CAAC,EAAE,OAAO,CAAC,CAAC;EACrE,IAAIS,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC5B,IAAIQ,QAAQ,IAAI,IAAI,EAAE;IACpB,OAAO,IAAI;EACb;EACA,IAAI;IACFC,CAAC;IACDC,CAAC;IACDC,KAAK;IACLC;EACF,CAAC,GAAGJ,QAAQ;EACZ,OAAO,aAAab,KAAK,CAACkB,aAAa,CAACZ,iBAAiB,CAACa,QAAQ,EAAE;IAClEC,KAAK,EAAET;EACT,CAAC,EAAE,aAAaX,KAAK,CAACkB,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,aAAalB,KAAK,CAACkB,aAAa,CAAC,UAAU,EAAE;IAC7FG,EAAE,EAAEV;EACN,CAAC,EAAE,aAAaX,KAAK,CAACkB,aAAa,CAAC,MAAM,EAAE;IAC1CJ,CAAC,EAAEA,CAAC;IACJC,CAAC,EAAEA,CAAC;IACJE,MAAM,EAAEA,MAAM;IACdD,KAAK,EAAEA;EACT,CAAC,CAAC,CAAC,CAAC,EAAEN,QAAQ,CAAC;AACjB,CAAC;AACD,OAAO,IAAIY,aAAa,GAAGA,CAAA,KAAM;EAC/B,OAAOpB,UAAU,CAACI,iBAAiB,CAAC;AACtC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}