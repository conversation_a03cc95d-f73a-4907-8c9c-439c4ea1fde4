{"ast": null, "code": "import { useEffect } from 'react';\nimport { useIsPanorama } from '../context/PanoramaContext';\nimport { setChartSize, setLayout, setMargin } from './layoutSlice';\nimport { useAppDispatch } from './hooks';\n\n/**\n * \"Main\" props are props that are only accepted on the main chart,\n * as opposed to the small panorama chart inside a Brush.\n */\n\nexport function ReportMainChartProps(_ref) {\n  var {\n    layout,\n    width,\n    height,\n    margin\n  } = _ref;\n  var dispatch = useAppDispatch();\n\n  /*\n   * Skip dispatching properties in panorama chart for two reasons:\n   * 1. The root chart should be deciding on these properties, and\n   * 2. Brush reads these properties from redux store, and so they must remain stable\n   *      to avoid circular dependency and infinite re-rendering.\n   */\n  var isPanorama = useIsPanorama();\n  /*\n   * useEffect here is required to avoid the \"Cannot update a component while rendering a different component\" error.\n   * https://github.com/facebook/react/issues/18178\n   *\n   * Reported in https://github.com/recharts/recharts/issues/5514\n   */\n  useEffect(() => {\n    if (!isPanorama) {\n      dispatch(setLayout(layout));\n      dispatch(setChartSize({\n        width,\n        height\n      }));\n      dispatch(setMargin(margin));\n    }\n  }, [dispatch, isPanorama, layout, width, height, margin]);\n  return null;\n}", "map": {"version": 3, "names": ["useEffect", "useIsPanorama", "setChartSize", "setLayout", "<PERSON><PERSON><PERSON><PERSON>", "useAppDispatch", "ReportMainChartProps", "_ref", "layout", "width", "height", "margin", "dispatch", "isPanorama"], "sources": ["D:/menasa/frontend/node_modules/recharts/es6/state/ReportMainChartProps.js"], "sourcesContent": ["import { useEffect } from 'react';\nimport { useIsPanorama } from '../context/PanoramaContext';\nimport { setChartSize, setLayout, setMargin } from './layoutSlice';\nimport { useAppDispatch } from './hooks';\n\n/**\n * \"Main\" props are props that are only accepted on the main chart,\n * as opposed to the small panorama chart inside a Brush.\n */\n\nexport function ReportMainChartProps(_ref) {\n  var {\n    layout,\n    width,\n    height,\n    margin\n  } = _ref;\n  var dispatch = useAppDispatch();\n\n  /*\n   * Skip dispatching properties in panorama chart for two reasons:\n   * 1. The root chart should be deciding on these properties, and\n   * 2. Brush reads these properties from redux store, and so they must remain stable\n   *      to avoid circular dependency and infinite re-rendering.\n   */\n  var isPanorama = useIsPanorama();\n  /*\n   * useEffect here is required to avoid the \"Cannot update a component while rendering a different component\" error.\n   * https://github.com/facebook/react/issues/18178\n   *\n   * Reported in https://github.com/recharts/recharts/issues/5514\n   */\n  useEffect(() => {\n    if (!isPanorama) {\n      dispatch(setLayout(layout));\n      dispatch(setChartSize({\n        width,\n        height\n      }));\n      dispatch(setMargin(margin));\n    }\n  }, [dispatch, isPanorama, layout, width, height, margin]);\n  return null;\n}"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,aAAa,QAAQ,4BAA4B;AAC1D,SAASC,YAAY,EAAEC,SAAS,EAAEC,SAAS,QAAQ,eAAe;AAClE,SAASC,cAAc,QAAQ,SAAS;;AAExC;AACA;AACA;AACA;;AAEA,OAAO,SAASC,oBAAoBA,CAACC,IAAI,EAAE;EACzC,IAAI;IACFC,MAAM;IACNC,KAAK;IACLC,MAAM;IACNC;EACF,CAAC,GAAGJ,IAAI;EACR,IAAIK,QAAQ,GAAGP,cAAc,CAAC,CAAC;;EAE/B;AACF;AACA;AACA;AACA;AACA;EACE,IAAIQ,UAAU,GAAGZ,aAAa,CAAC,CAAC;EAChC;AACF;AACA;AACA;AACA;AACA;EACED,SAAS,CAAC,MAAM;IACd,IAAI,CAACa,UAAU,EAAE;MACfD,QAAQ,CAACT,SAAS,CAACK,MAAM,CAAC,CAAC;MAC3BI,QAAQ,CAACV,YAAY,CAAC;QACpBO,KAAK;QACLC;MACF,CAAC,CAAC,CAAC;MACHE,QAAQ,CAACR,SAAS,CAACO,MAAM,CAAC,CAAC;IAC7B;EACF,CAAC,EAAE,CAACC,QAAQ,EAAEC,UAAU,EAAEL,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,MAAM,CAAC,CAAC;EACzD,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}