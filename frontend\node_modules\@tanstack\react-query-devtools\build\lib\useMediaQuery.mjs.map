{"version": 3, "file": "useMediaQuery.mjs", "sources": ["../../src/useMediaQuery.ts"], "sourcesContent": ["import * as React from 'react'\n\nexport default function useMediaQuery(query: string): boolean | undefined {\n  // Keep track of the preference in state, start with the current match\n  const [isMatch, setIsMatch] = React.useState(() => {\n    if (typeof window !== 'undefined') {\n      return window.matchMedia(query).matches\n    }\n    return\n  })\n\n  // Watch for changes\n  React.useEffect(() => {\n    if (typeof window !== 'undefined') {\n      // Create a matcher\n      const matcher = window.matchMedia(query)\n\n      // Create our handler\n      const onChange = ({ matches }: { matches: boolean }) =>\n        setIsMatch(matches)\n\n      // Listen for changes\n      matcher.addListener(onChange)\n\n      return () => {\n        // Stop listening for changes\n        matcher.removeListener(onChange)\n      }\n    }\n    return\n  }, [isMatch, query, setIsMatch])\n\n  return isMatch\n}\n"], "names": ["useMediaQuery", "query", "isMatch", "setIsMatch", "React", "useState", "window", "matchMedia", "matches", "useEffect", "matcher", "onChange", "addListener", "removeListener"], "mappings": ";;AAEe,SAASA,aAAT,CAAuBC,KAAvB,EAA2D;AACxE;EACA,MAAM,CAACC,OAAD,EAAUC,UAAV,IAAwBC,KAAK,CAACC,QAAN,CAAe,MAAM;AACjD,IAAA,IAAI,OAAOC,MAAP,KAAkB,WAAtB,EAAmC;AACjC,MAAA,OAAOA,MAAM,CAACC,UAAP,CAAkBN,KAAlB,EAAyBO,OAAhC,CAAA;AACD,KAAA;;AACD,IAAA,OAAA;GAJ4B,CAA9B,CAFwE;;EAUxEJ,KAAK,CAACK,SAAN,CAAgB,MAAM;AACpB,IAAA,IAAI,OAAOH,MAAP,KAAkB,WAAtB,EAAmC;AACjC;MACA,MAAMI,OAAO,GAAGJ,MAAM,CAACC,UAAP,CAAkBN,KAAlB,CAAhB,CAFiC;;MAKjC,MAAMU,QAAQ,GAAG,CAAC;AAAEH,QAAAA,OAAAA;AAAF,OAAD,KACfL,UAAU,CAACK,OAAD,CADZ,CALiC;;;MASjCE,OAAO,CAACE,WAAR,CAAoBD,QAApB,CAAA,CAAA;AAEA,MAAA,OAAO,MAAM;AACX;QACAD,OAAO,CAACG,cAAR,CAAuBF,QAAvB,CAAA,CAAA;OAFF,CAAA;AAID,KAAA;;AACD,IAAA,OAAA;AACD,GAlBD,EAkBG,CAACT,OAAD,EAAUD,KAAV,EAAiBE,UAAjB,CAlBH,CAAA,CAAA;AAoBA,EAAA,OAAOD,OAAP,CAAA;AACD;;;;"}