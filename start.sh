#!/bin/bash

echo "Starting Educational File Manager with Supabase..."
echo

echo "Installing frontend dependencies..."
cd frontend
npm install
if [ $? -ne 0 ]; then
    echo "Error installing frontend dependencies"
    exit 1
fi

echo
echo "Checking environment variables..."
if [ ! -f .env ]; then
    echo "Warning: .env file not found!"
    echo "Please copy .env.example to .env and configure your Supabase credentials"
    read -p "Press enter to continue..."
fi

echo
echo "Starting the application..."
npm start
