import React, { useState } from 'react';
import { useDropzone } from 'react-dropzone';
import styled from 'styled-components';
import { FiUpload, FiFile } from 'react-icons/fi';
import { uploadFile } from '../../services/api';
import { toast } from 'react-toastify';

const UploadContainer = styled.div`
  background: white;
  border-radius: 10px;
  padding: 30px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
`;

const DropZone = styled.div`
  border: 2px dashed #ddd;
  border-radius: 10px;
  padding: 40px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    border-color: #007bff;
    background: #f8f9fa;
  }
`;

const ProgressBar = styled.div`
  width: 100%;
  height: 6px;
  background: #e9ecef;
  border-radius: 3px;
  overflow: hidden;
  margin: 10px 0;
`;

const Progress = styled.div`
  height: 100%;
  background: #007bff;
  width: ${props => props.progress}%;
  transition: width 0.3s ease;
`;

function FileUpload() {
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploading, setUploading] = useState(false);

  const onDrop = async (acceptedFiles) => {
    const file = acceptedFiles[0];
    if (!file) return;

    setUploading(true);
    setUploadProgress(0);

    try {
      await uploadFile(file, {
        onUploadProgress: (progressEvent) => {
          const progress = Math.round(
            (progressEvent.loaded * 100) / progressEvent.total
          );
          setUploadProgress(progress);
        }
      });
      
      toast.success('تم رفع الملف بنجاح!');
      setUploadProgress(0);
    } catch (error) {
      toast.error('فشل في رفع الملف');
    } finally {
      setUploading(false);
    }
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    multiple: false
  });

  return (
    <UploadContainer>
      <h2>رفع ملف جديد</h2>
      
      <DropZone {...getRootProps()}>
        <input {...getInputProps()} />
        <FiUpload size={48} color="#007bff" />
        <p>اسحب الملف هنا أو انقر للاختيار</p>
        <small>يدعم: PDF, DOC, PPT, MP4, MP3</small>
      </DropZone>

      {uploading && (
        <div>
          <ProgressBar>
            <Progress progress={uploadProgress} />
          </ProgressBar>
          <p>{uploadProgress}% مكتمل</p>
        </div>
      )}
    </UploadContainer>
  );
}

export default FileUpload;