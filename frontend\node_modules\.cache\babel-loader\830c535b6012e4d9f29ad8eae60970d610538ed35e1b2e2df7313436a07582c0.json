{"ast": null, "code": "function ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nimport { createSelector } from 'reselect';\nimport range from 'es-toolkit/compat/range';\nimport * as d3Scales from 'victory-vendor/d3-scale';\nimport { selectChartLayout } from '../../context/chartLayoutContext';\nimport { checkDomainOfScale, getDomainOfStackGroups, getStackedData, getValueByDataKey, isCategoricalAxis } from '../../util/ChartUtils';\nimport { selectChartDataWithIndexes, selectChartDataWithIndexesIfNotInPanorama } from './dataSelectors';\nimport { isWellFormedNumberDomain, numericalDomainSpecifiedWithoutRequiringData, parseNumericalUserDomain } from '../../util/isDomainSpecifiedByUser';\nimport { getPercentValue, hasDuplicate, isNan, isNumber, isNumOrStr, mathSign, upperFirst } from '../../util/DataUtils';\nimport { isWellBehavedNumber } from '../../util/isWellBehavedNumber';\nimport { getNiceTickValues, getTickValuesFixedDomain } from '../../util/scale';\nimport { selectChartHeight, selectChartWidth } from './containerSelectors';\nimport { selectAllXAxes, selectAllYAxes } from './selectAllAxes';\nimport { selectChartOffsetInternal } from './selectChartOffsetInternal';\nimport { selectBrushDimensions, selectBrushSettings } from './brushSelectors';\nimport { selectBarCategoryGap, selectChartName, selectStackOffsetType } from './rootPropsSelectors';\nimport { selectAngleAxis, selectAngleAxisRange, selectRadiusAxis, selectRadiusAxisRange } from './polarAxisSelectors';\nimport { pickAxisType } from './pickAxisType';\nimport { pickAxisId } from './pickAxisId';\nimport { combineAxisRangeWithReverse } from './combiners/combineAxisRangeWithReverse';\nimport { DEFAULT_Y_AXIS_WIDTH } from '../../util/Constants';\nvar defaultNumericDomain = [0, 'auto'];\n\n/**\n * angle, radius, X, Y, and Z axes all have domain and range and scale and associated settings\n */\n\n/**\n * X and Y axes have ticks. Z axis is never displayed and so it lacks ticks\n * and tick settings.\n */\n\n/**\n * If an axis is not explicitly defined as an element,\n * we still need to render something in the chart and we need\n * some object to hold the domain and default settings.\n */\nexport var implicitXAxis = {\n  allowDataOverflow: false,\n  allowDecimals: true,\n  allowDuplicatedCategory: true,\n  angle: 0,\n  dataKey: undefined,\n  domain: undefined,\n  height: 30,\n  hide: true,\n  id: 0,\n  includeHidden: false,\n  interval: 'preserveEnd',\n  minTickGap: 5,\n  mirror: false,\n  name: undefined,\n  orientation: 'bottom',\n  padding: {\n    left: 0,\n    right: 0\n  },\n  reversed: false,\n  scale: 'auto',\n  tick: true,\n  tickCount: 5,\n  tickFormatter: undefined,\n  ticks: undefined,\n  type: 'category',\n  unit: undefined\n};\nexport var selectXAxisSettings = (state, axisId) => {\n  var axis = state.cartesianAxis.xAxis[axisId];\n  if (axis == null) {\n    return implicitXAxis;\n  }\n  return axis;\n};\n\n/**\n * If an axis is not explicitly defined as an element,\n * we still need to render something in the chart and we need\n * some object to hold the domain and default settings.\n */\nexport var implicitYAxis = {\n  allowDataOverflow: false,\n  allowDecimals: true,\n  allowDuplicatedCategory: true,\n  angle: 0,\n  dataKey: undefined,\n  domain: defaultNumericDomain,\n  hide: true,\n  id: 0,\n  includeHidden: false,\n  interval: 'preserveEnd',\n  minTickGap: 5,\n  mirror: false,\n  name: undefined,\n  orientation: 'left',\n  padding: {\n    top: 0,\n    bottom: 0\n  },\n  reversed: false,\n  scale: 'auto',\n  tick: true,\n  tickCount: 5,\n  tickFormatter: undefined,\n  ticks: undefined,\n  type: 'number',\n  unit: undefined,\n  width: DEFAULT_Y_AXIS_WIDTH\n};\nexport var selectYAxisSettings = (state, axisId) => {\n  var axis = state.cartesianAxis.yAxis[axisId];\n  if (axis == null) {\n    return implicitYAxis;\n  }\n  return axis;\n};\nexport var implicitZAxis = {\n  domain: [0, 'auto'],\n  includeHidden: false,\n  reversed: false,\n  allowDataOverflow: false,\n  allowDuplicatedCategory: false,\n  dataKey: undefined,\n  id: 0,\n  name: '',\n  range: [64, 64],\n  scale: 'auto',\n  type: 'number',\n  unit: ''\n};\nexport var selectZAxisSettings = (state, axisId) => {\n  var axis = state.cartesianAxis.zAxis[axisId];\n  if (axis == null) {\n    return implicitZAxis;\n  }\n  return axis;\n};\nexport var selectBaseAxis = (state, axisType, axisId) => {\n  switch (axisType) {\n    case 'xAxis':\n      {\n        return selectXAxisSettings(state, axisId);\n      }\n    case 'yAxis':\n      {\n        return selectYAxisSettings(state, axisId);\n      }\n    case 'zAxis':\n      {\n        return selectZAxisSettings(state, axisId);\n      }\n    case 'angleAxis':\n      {\n        return selectAngleAxis(state, axisId);\n      }\n    case 'radiusAxis':\n      {\n        return selectRadiusAxis(state, axisId);\n      }\n    default:\n      throw new Error(\"Unexpected axis type: \".concat(axisType));\n  }\n};\nvar selectCartesianAxisSettings = (state, axisType, axisId) => {\n  switch (axisType) {\n    case 'xAxis':\n      {\n        return selectXAxisSettings(state, axisId);\n      }\n    case 'yAxis':\n      {\n        return selectYAxisSettings(state, axisId);\n      }\n    default:\n      throw new Error(\"Unexpected axis type: \".concat(axisType));\n  }\n};\n\n/**\n * Selects either an X or Y axis. Doesn't work with Z axis - for that, instead use selectBaseAxis.\n * @param state Root state\n * @param axisType xAxis | yAxis\n * @param axisId xAxisId | yAxisId\n * @returns axis settings object\n */\nexport var selectAxisSettings = (state, axisType, axisId) => {\n  switch (axisType) {\n    case 'xAxis':\n      {\n        return selectXAxisSettings(state, axisId);\n      }\n    case 'yAxis':\n      {\n        return selectYAxisSettings(state, axisId);\n      }\n    case 'angleAxis':\n      {\n        return selectAngleAxis(state, axisId);\n      }\n    case 'radiusAxis':\n      {\n        return selectRadiusAxis(state, axisId);\n      }\n    default:\n      throw new Error(\"Unexpected axis type: \".concat(axisType));\n  }\n};\n\n/**\n * @param state RechartsRootState\n * @return boolean true if there is at least one Bar or RadialBar\n */\nexport var selectHasBar = state => state.graphicalItems.countOfBars > 0;\n\n/**\n * Filters CartesianGraphicalItemSettings by the relevant axis ID\n * @param axisType 'xAxis' | 'yAxis' | 'zAxis' | 'radiusAxis' | 'angleAxis'\n * @param axisId from props, defaults to 0\n *\n * @returns Predicate function that return true for CartesianGraphicalItemSettings that are relevant to the specified axis\n */\nexport function itemAxisPredicate(axisType, axisId) {\n  return item => {\n    switch (axisType) {\n      case 'xAxis':\n        // This is sensitive to the data type, as 0 !== '0'. I wonder if we should be more flexible. How does 2.x branch behave? TODO write test for that\n        return 'xAxisId' in item && item.xAxisId === axisId;\n      case 'yAxis':\n        return 'yAxisId' in item && item.yAxisId === axisId;\n      case 'zAxis':\n        return 'zAxisId' in item && item.zAxisId === axisId;\n      case 'angleAxis':\n        return 'angleAxisId' in item && item.angleAxisId === axisId;\n      case 'radiusAxis':\n        return 'radiusAxisId' in item && item.radiusAxisId === axisId;\n      default:\n        return false;\n    }\n  };\n}\nexport var selectUnfilteredCartesianItems = state => state.graphicalItems.cartesianItems;\nvar selectAxisPredicate = createSelector([pickAxisType, pickAxisId], itemAxisPredicate);\nexport var combineGraphicalItemsSettings = (graphicalItems, axisSettings, axisPredicate) => graphicalItems.filter(axisPredicate).filter(item => {\n  if ((axisSettings === null || axisSettings === void 0 ? void 0 : axisSettings.includeHidden) === true) {\n    return true;\n  }\n  return !item.hide;\n});\nexport var selectCartesianItemsSettings = createSelector([selectUnfilteredCartesianItems, selectBaseAxis, selectAxisPredicate], combineGraphicalItemsSettings);\nexport var filterGraphicalNotStackedItems = cartesianItems => cartesianItems.filter(item => item.stackId === undefined);\nvar selectCartesianItemsSettingsExceptStacked = createSelector([selectCartesianItemsSettings], filterGraphicalNotStackedItems);\nexport var combineGraphicalItemsData = cartesianItems => cartesianItems.map(item => item.data).filter(Boolean).flat(1);\n\n/**\n * This is a \"cheap\" selector - it returns the data but doesn't iterate them, so it is not sensitive on the array length.\n * Also does not apply dataKey yet.\n * @param state RechartsRootState\n * @returns data defined on the chart graphical items, such as Line or Scatter or Pie, and filtered with appropriate dataKey\n */\nexport var selectCartesianGraphicalItemsData = createSelector([selectCartesianItemsSettings], combineGraphicalItemsData);\nexport var combineDisplayedData = (graphicalItemsData, _ref) => {\n  var {\n    chartData = [],\n    dataStartIndex,\n    dataEndIndex\n  } = _ref;\n  if (graphicalItemsData.length > 0) {\n    /*\n     * There is no slicing when data is defined on graphical items. Why?\n     * Because Brush ignores data defined on graphical items,\n     * and does not render.\n     * So Brush will never show up in a Scatter chart for example.\n     * This is something we will need to fix.\n     *\n     * Now, when the root chart data is not defined, the dataEndIndex is 0,\n     * which means the itemsData will be sliced to an empty array anyway.\n     * But that's an implementation detail, and we can fix that too.\n     *\n     * Also, in absence of Axis dataKey, we use the dataKey from each item, respectively.\n     * This is the usual pattern for numerical axis, that is the one where bars go up:\n     * users don't specify any dataKey by default and expect the axis to \"just match the data\".\n     */\n    return graphicalItemsData;\n  }\n  return chartData.slice(dataStartIndex, dataEndIndex + 1);\n};\n\n/**\n * This selector will return all data there is in the chart: graphical items, chart root, all together.\n * Useful for figuring out an axis domain (because that needs to know of everything),\n * not useful for rendering individual graphical elements (because they need to know which data is theirs and which is not).\n *\n * This function will discard the original indexes, so it is also not useful for anything that depends on ordering.\n */\nexport var selectDisplayedData = createSelector([selectCartesianGraphicalItemsData, selectChartDataWithIndexesIfNotInPanorama], combineDisplayedData);\nexport var combineAppliedValues = (data, axisSettings, items) => {\n  if ((axisSettings === null || axisSettings === void 0 ? void 0 : axisSettings.dataKey) != null) {\n    return data.map(item => ({\n      value: getValueByDataKey(item, axisSettings.dataKey)\n    }));\n  }\n  if (items.length > 0) {\n    return items.map(item => item.dataKey).flatMap(dataKey => data.map(entry => ({\n      value: getValueByDataKey(entry, dataKey)\n    })));\n  }\n  return data.map(entry => ({\n    value: entry\n  }));\n};\n\n/**\n * This selector will return all values with the appropriate dataKey applied on them.\n * Which dataKey is appropriate depends on where it is defined.\n *\n * This is an expensive selector - it will iterate all data and compute their value using the provided dataKey.\n */\nexport var selectAllAppliedValues = createSelector([selectDisplayedData, selectBaseAxis, selectCartesianItemsSettings], combineAppliedValues);\nexport function isErrorBarRelevantForAxisType(axisType, errorBar) {\n  switch (axisType) {\n    case 'xAxis':\n      return errorBar.direction === 'x';\n    case 'yAxis':\n      return errorBar.direction === 'y';\n    default:\n      return false;\n  }\n}\n\n/**\n * This is type of \"error\" in chart. It is set by using ErrorBar, and it can represent confidence interval,\n * or gap in the data, or standard deviation, or quartiles in boxplot, or whiskers or whatever.\n *\n * We will internally represent it as a tuple of two numbers, where the first number is the lower bound and the second number is the upper bound.\n *\n * It is also true that the first number should be lower than or equal to the associated \"main value\",\n * and the second number should be higher than or equal to the associated \"main value\".\n */\n\nexport function fromMainValueToError(value) {\n  if (isNumber(value) && Number.isFinite(value)) {\n    return [value, value];\n  }\n  if (Array.isArray(value)) {\n    var minError = Math.min(...value);\n    var maxError = Math.max(...value);\n    if (!isNan(minError) && !isNan(maxError) && Number.isFinite(minError) && Number.isFinite(maxError)) {\n      return [minError, maxError];\n    }\n  }\n  return undefined;\n}\nfunction onlyAllowNumbers(data) {\n  return data.filter(v => isNumOrStr(v) || v instanceof Date).map(Number).filter(n => isNan(n) === false);\n}\n\n/**\n * @param entry One item in the 'data' array. Could be anything really - this is defined externally. This is the raw, before dataKey application\n * @param appliedValue This is the result of applying the 'main' dataKey on the `entry`.\n * @param relevantErrorBars Error bars that are relevant for the current axis and layout and all that.\n * @return either undefined or an array of ErrorValue\n */\nexport function getErrorDomainByDataKey(entry, appliedValue, relevantErrorBars) {\n  if (!relevantErrorBars || typeof appliedValue !== 'number' || isNan(appliedValue)) {\n    return [];\n  }\n  if (!relevantErrorBars.length) {\n    return [];\n  }\n  return onlyAllowNumbers(relevantErrorBars.flatMap(eb => {\n    var errorValue = getValueByDataKey(entry, eb.dataKey);\n    var lowBound, highBound;\n    if (Array.isArray(errorValue)) {\n      [lowBound, highBound] = errorValue;\n    } else {\n      lowBound = highBound = errorValue;\n    }\n    if (!isWellBehavedNumber(lowBound) || !isWellBehavedNumber(highBound)) {\n      return undefined;\n    }\n    return [appliedValue - lowBound, appliedValue + highBound];\n  }));\n}\nexport var combineStackGroups = (displayedData, items, stackOffsetType) => {\n  var initialItemsGroups = {};\n  var itemsGroup = items.reduce((acc, item) => {\n    if (item.stackId == null) {\n      return acc;\n    }\n    if (acc[item.stackId] == null) {\n      acc[item.stackId] = [];\n    }\n    acc[item.stackId].push(item);\n    return acc;\n  }, initialItemsGroups);\n  return Object.fromEntries(Object.entries(itemsGroup).map(_ref2 => {\n    var [stackId, graphicalItems] = _ref2;\n    var dataKeys = graphicalItems.map(i => i.dataKey);\n    return [stackId, {\n      // @ts-expect-error getStackedData requires that the input is array of objects, Recharts does not test for that\n      stackedData: getStackedData(displayedData, dataKeys, stackOffsetType),\n      graphicalItems\n    }];\n  }));\n};\n/**\n * Stack groups are groups of graphical items that stack on each other.\n * Stack is a function of axis type (X, Y), axis ID, and stack ID.\n * Graphical items that do not have a stack ID are not going to be present in stack groups.\n */\nexport var selectStackGroups = createSelector([selectDisplayedData, selectCartesianItemsSettings, selectStackOffsetType], combineStackGroups);\nexport var combineDomainOfStackGroups = (stackGroups, _ref3, axisType) => {\n  var {\n    dataStartIndex,\n    dataEndIndex\n  } = _ref3;\n  if (axisType === 'zAxis') {\n    // ZAxis ignores stacks\n    return undefined;\n  }\n  var domainOfStackGroups = getDomainOfStackGroups(stackGroups, dataStartIndex, dataEndIndex);\n  if (domainOfStackGroups != null && domainOfStackGroups[0] === 0 && domainOfStackGroups[1] === 0) {\n    return undefined;\n  }\n  return domainOfStackGroups;\n};\nexport var selectDomainOfStackGroups = createSelector([selectStackGroups, selectChartDataWithIndexes, pickAxisType], combineDomainOfStackGroups);\nexport var combineAppliedNumericalValuesIncludingErrorValues = (data, axisSettings, items, axisType) => {\n  if (items.length > 0) {\n    return data.flatMap(entry => {\n      return items.flatMap(item => {\n        var _item$errorBars, _axisSettings$dataKey;\n        var relevantErrorBars = (_item$errorBars = item.errorBars) === null || _item$errorBars === void 0 ? void 0 : _item$errorBars.filter(errorBar => isErrorBarRelevantForAxisType(axisType, errorBar));\n        var valueByDataKey = getValueByDataKey(entry, (_axisSettings$dataKey = axisSettings.dataKey) !== null && _axisSettings$dataKey !== void 0 ? _axisSettings$dataKey : item.dataKey);\n        return {\n          value: valueByDataKey,\n          errorDomain: getErrorDomainByDataKey(entry, valueByDataKey, relevantErrorBars)\n        };\n      });\n    }).filter(Boolean);\n  }\n  if ((axisSettings === null || axisSettings === void 0 ? void 0 : axisSettings.dataKey) != null) {\n    return data.map(item => ({\n      value: getValueByDataKey(item, axisSettings.dataKey),\n      errorDomain: []\n    }));\n  }\n  return data.map(entry => ({\n    value: entry,\n    errorDomain: []\n  }));\n};\nexport var selectAllAppliedNumericalValuesIncludingErrorValues = createSelector(selectDisplayedData, selectBaseAxis, selectCartesianItemsSettingsExceptStacked, pickAxisType, combineAppliedNumericalValuesIncludingErrorValues);\nfunction onlyAllowNumbersAndStringsAndDates(item) {\n  var {\n    value\n  } = item;\n  if (isNumOrStr(value) || value instanceof Date) {\n    return value;\n  }\n  return undefined;\n}\nvar computeNumericalDomain = dataWithErrorDomains => {\n  var allDataSquished = dataWithErrorDomains\n  // This flatMap has to be flat because we're creating a new array in the return value\n  .flatMap(d => [d.value, d.errorDomain])\n  // This flat is needed because a) errorDomain is an array, and b) value may be a number, or it may be a range (for Area, for example)\n  .flat(1);\n  var onlyNumbers = onlyAllowNumbers(allDataSquished);\n  if (onlyNumbers.length === 0) {\n    return undefined;\n  }\n  return [Math.min(...onlyNumbers), Math.max(...onlyNumbers)];\n};\nvar computeDomainOfTypeCategory = (allDataSquished, axisSettings, isCategorical) => {\n  var categoricalDomain = allDataSquished.map(onlyAllowNumbersAndStringsAndDates).filter(v => v != null);\n  if (isCategorical && (axisSettings.dataKey == null || axisSettings.allowDuplicatedCategory && hasDuplicate(categoricalDomain))) {\n    /*\n     * 1. In an absence of dataKey, Recharts will use array indexes as its categorical domain\n     * 2. When category axis has duplicated text, serial numbers are used to generate scale\n     */\n    return range(0, allDataSquished.length);\n  }\n  if (axisSettings.allowDuplicatedCategory) {\n    return categoricalDomain;\n  }\n  return Array.from(new Set(categoricalDomain));\n};\nexport var getDomainDefinition = axisSettings => {\n  var _axisSettings$domain;\n  if (axisSettings == null || !('domain' in axisSettings)) {\n    return defaultNumericDomain;\n  }\n  if (axisSettings.domain != null) {\n    return axisSettings.domain;\n  }\n  if (axisSettings.ticks != null) {\n    if (axisSettings.type === 'number') {\n      var allValues = onlyAllowNumbers(axisSettings.ticks);\n      return [Math.min(...allValues), Math.max(...allValues)];\n    }\n    if (axisSettings.type === 'category') {\n      return axisSettings.ticks.map(String);\n    }\n  }\n  return (_axisSettings$domain = axisSettings === null || axisSettings === void 0 ? void 0 : axisSettings.domain) !== null && _axisSettings$domain !== void 0 ? _axisSettings$domain : defaultNumericDomain;\n};\nexport var mergeDomains = function mergeDomains() {\n  for (var _len = arguments.length, domains = new Array(_len), _key = 0; _key < _len; _key++) {\n    domains[_key] = arguments[_key];\n  }\n  var allDomains = domains.filter(Boolean);\n  if (allDomains.length === 0) {\n    return undefined;\n  }\n  var allValues = allDomains.flat();\n  var min = Math.min(...allValues);\n  var max = Math.max(...allValues);\n  return [min, max];\n};\nexport var selectReferenceDots = state => state.referenceElements.dots;\nexport var filterReferenceElements = (elements, axisType, axisId) => {\n  return elements.filter(el => el.ifOverflow === 'extendDomain').filter(el => {\n    if (axisType === 'xAxis') {\n      return el.xAxisId === axisId;\n    }\n    return el.yAxisId === axisId;\n  });\n};\nexport var selectReferenceDotsByAxis = createSelector([selectReferenceDots, pickAxisType, pickAxisId], filterReferenceElements);\nexport var selectReferenceAreas = state => state.referenceElements.areas;\nexport var selectReferenceAreasByAxis = createSelector([selectReferenceAreas, pickAxisType, pickAxisId], filterReferenceElements);\nexport var selectReferenceLines = state => state.referenceElements.lines;\nexport var selectReferenceLinesByAxis = createSelector([selectReferenceLines, pickAxisType, pickAxisId], filterReferenceElements);\nexport var combineDotsDomain = (dots, axisType) => {\n  var allCoords = onlyAllowNumbers(dots.map(dot => axisType === 'xAxis' ? dot.x : dot.y));\n  if (allCoords.length === 0) {\n    return undefined;\n  }\n  return [Math.min(...allCoords), Math.max(...allCoords)];\n};\nvar selectReferenceDotsDomain = createSelector(selectReferenceDotsByAxis, pickAxisType, combineDotsDomain);\nexport var combineAreasDomain = (areas, axisType) => {\n  var allCoords = onlyAllowNumbers(areas.flatMap(area => [axisType === 'xAxis' ? area.x1 : area.y1, axisType === 'xAxis' ? area.x2 : area.y2]));\n  if (allCoords.length === 0) {\n    return undefined;\n  }\n  return [Math.min(...allCoords), Math.max(...allCoords)];\n};\nvar selectReferenceAreasDomain = createSelector([selectReferenceAreasByAxis, pickAxisType], combineAreasDomain);\nexport var combineLinesDomain = (lines, axisType) => {\n  var allCoords = onlyAllowNumbers(lines.map(line => axisType === 'xAxis' ? line.x : line.y));\n  if (allCoords.length === 0) {\n    return undefined;\n  }\n  return [Math.min(...allCoords), Math.max(...allCoords)];\n};\nvar selectReferenceLinesDomain = createSelector(selectReferenceLinesByAxis, pickAxisType, combineLinesDomain);\nvar selectReferenceElementsDomain = createSelector(selectReferenceDotsDomain, selectReferenceLinesDomain, selectReferenceAreasDomain, (dotsDomain, linesDomain, areasDomain) => {\n  return mergeDomains(dotsDomain, areasDomain, linesDomain);\n});\nexport var selectDomainDefinition = createSelector([selectBaseAxis], getDomainDefinition);\nexport var combineNumericalDomain = (axisSettings, domainDefinition, domainOfStackGroups, allDataWithErrorDomains, referenceElementsDomain) => {\n  var domainFromUserPreference = numericalDomainSpecifiedWithoutRequiringData(domainDefinition, axisSettings.allowDataOverflow);\n  if (domainFromUserPreference != null) {\n    // We're done! No need to compute anything else.\n    return domainFromUserPreference;\n  }\n  return parseNumericalUserDomain(domainDefinition, mergeDomains(domainOfStackGroups, referenceElementsDomain, computeNumericalDomain(allDataWithErrorDomains)), axisSettings.allowDataOverflow);\n};\nvar selectNumericalDomain = createSelector([selectBaseAxis, selectDomainDefinition, selectDomainOfStackGroups, selectAllAppliedNumericalValuesIncludingErrorValues, selectReferenceElementsDomain], combineNumericalDomain);\n\n/**\n * Expand by design maps everything between 0 and 1,\n * there is nothing to compute.\n * See https://d3js.org/d3-shape/stack#stack-offsets\n */\nvar expandDomain = [0, 1];\nexport var combineAxisDomain = (axisSettings, layout, displayedData, allAppliedValues, stackOffsetType, axisType, numericalDomain) => {\n  if (axisSettings == null || displayedData == null || displayedData.length === 0) {\n    return undefined;\n  }\n  var {\n    dataKey,\n    type\n  } = axisSettings;\n  var isCategorical = isCategoricalAxis(layout, axisType);\n  if (isCategorical && dataKey == null) {\n    return range(0, displayedData.length);\n  }\n  if (type === 'category') {\n    return computeDomainOfTypeCategory(allAppliedValues, axisSettings, isCategorical);\n  }\n  if (stackOffsetType === 'expand') {\n    return expandDomain;\n  }\n  return numericalDomain;\n};\nexport var selectAxisDomain = createSelector([selectBaseAxis, selectChartLayout, selectDisplayedData, selectAllAppliedValues, selectStackOffsetType, pickAxisType, selectNumericalDomain], combineAxisDomain);\nexport var combineRealScaleType = (axisConfig, layout, hasBar, chartType, axisType) => {\n  if (axisConfig == null) {\n    return undefined;\n  }\n  var {\n    scale,\n    type\n  } = axisConfig;\n  if (scale === 'auto') {\n    if (layout === 'radial' && axisType === 'radiusAxis') {\n      return 'band';\n    }\n    if (layout === 'radial' && axisType === 'angleAxis') {\n      return 'linear';\n    }\n    if (type === 'category' && chartType && (chartType.indexOf('LineChart') >= 0 || chartType.indexOf('AreaChart') >= 0 || chartType.indexOf('ComposedChart') >= 0 && !hasBar)) {\n      return 'point';\n    }\n    if (type === 'category') {\n      return 'band';\n    }\n    return 'linear';\n  }\n  if (typeof scale === 'string') {\n    var name = \"scale\".concat(upperFirst(scale));\n    return name in d3Scales ? name : 'point';\n  }\n  return undefined;\n};\nexport var selectRealScaleType = createSelector([selectBaseAxis, selectChartLayout, selectHasBar, selectChartName, pickAxisType], combineRealScaleType);\nfunction getD3ScaleFromType(realScaleType) {\n  if (realScaleType == null) {\n    return undefined;\n  }\n  if (realScaleType in d3Scales) {\n    // @ts-expect-error we should do better type verification here\n    return d3Scales[realScaleType]();\n  }\n  var name = \"scale\".concat(upperFirst(realScaleType));\n  if (name in d3Scales) {\n    // @ts-expect-error we should do better type verification here\n    return d3Scales[name]();\n  }\n  return undefined;\n}\nexport function combineScaleFunction(axis, realScaleType, axisDomain, axisRange) {\n  if (axisDomain == null || axisRange == null) {\n    return undefined;\n  }\n  if (typeof axis.scale === 'function') {\n    // @ts-expect-error we're going to assume here that if axis.scale is a function then it is a d3Scale function\n    return axis.scale.copy().domain(axisDomain).range(axisRange);\n  }\n  var d3ScaleFunction = getD3ScaleFromType(realScaleType);\n  if (d3ScaleFunction == null) {\n    return undefined;\n  }\n  var scale = d3ScaleFunction.domain(axisDomain).range(axisRange);\n  // I don't like this function because it mutates the scale. We should come up with a way to compute the domain up front.\n  checkDomainOfScale(scale);\n  return scale;\n}\nexport var combineNiceTicks = (axisDomain, axisSettings, realScaleType) => {\n  var domainDefinition = getDomainDefinition(axisSettings);\n  if (realScaleType !== 'auto' && realScaleType !== 'linear') {\n    return undefined;\n  }\n  if (axisSettings != null && axisSettings.tickCount && Array.isArray(domainDefinition) && (domainDefinition[0] === 'auto' || domainDefinition[1] === 'auto') && isWellFormedNumberDomain(axisDomain)) {\n    return getNiceTickValues(axisDomain, axisSettings.tickCount, axisSettings.allowDecimals);\n  }\n  if (axisSettings != null && axisSettings.tickCount && axisSettings.type === 'number' && isWellFormedNumberDomain(axisDomain)) {\n    return getTickValuesFixedDomain(axisDomain, axisSettings.tickCount, axisSettings.allowDecimals);\n  }\n  return undefined;\n};\nexport var selectNiceTicks = createSelector([selectAxisDomain, selectAxisSettings, selectRealScaleType], combineNiceTicks);\nexport var combineAxisDomainWithNiceTicks = (axisSettings, domain, niceTicks, axisType) => {\n  if (\n  /*\n   * Angle axis for some reason uses nice ticks when rendering axis tick labels,\n   * but doesn't use nice ticks for extending domain like all the other axes do.\n   * Not really sure why? Is there a good reason,\n   * or is it just because someone added support for nice ticks to the other axes and forgot this one?\n   */\n  axisType !== 'angleAxis' && (axisSettings === null || axisSettings === void 0 ? void 0 : axisSettings.type) === 'number' && isWellFormedNumberDomain(domain) && Array.isArray(niceTicks) && niceTicks.length > 0) {\n    var minFromDomain = domain[0];\n    var minFromTicks = niceTicks[0];\n    var maxFromDomain = domain[1];\n    var maxFromTicks = niceTicks[niceTicks.length - 1];\n    return [Math.min(minFromDomain, minFromTicks), Math.max(maxFromDomain, maxFromTicks)];\n  }\n  return domain;\n};\nexport var selectAxisDomainIncludingNiceTicks = createSelector([selectBaseAxis, selectAxisDomain, selectNiceTicks, pickAxisType], combineAxisDomainWithNiceTicks);\n\n/**\n * Returns the smallest gap, between two numbers in the data, as a ratio of the whole range (max - min).\n * Ignores domain provided by user and only considers domain from data.\n *\n * The result is a number between 0 and 1.\n */\nexport var selectSmallestDistanceBetweenValues = createSelector(selectAllAppliedValues, selectBaseAxis, (allDataSquished, axisSettings) => {\n  if (!axisSettings || axisSettings.type !== 'number') {\n    return undefined;\n  }\n  var smallestDistanceBetweenValues = Infinity;\n  var sortedValues = Array.from(onlyAllowNumbers(allDataSquished.map(d => d.value))).sort((a, b) => a - b);\n  if (sortedValues.length < 2) {\n    return Infinity;\n  }\n  var diff = sortedValues[sortedValues.length - 1] - sortedValues[0];\n  if (diff === 0) {\n    return Infinity;\n  }\n  // Only do n - 1 distance calculations because there's only n - 1 distances between n values.\n  for (var i = 0; i < sortedValues.length - 1; i++) {\n    var distance = sortedValues[i + 1] - sortedValues[i];\n    smallestDistanceBetweenValues = Math.min(smallestDistanceBetweenValues, distance);\n  }\n  return smallestDistanceBetweenValues / diff;\n});\nvar selectCalculatedPadding = createSelector(selectSmallestDistanceBetweenValues, selectChartLayout, selectBarCategoryGap, selectChartOffsetInternal, (_1, _2, _3, padding) => padding, (smallestDistanceInPercent, layout, barCategoryGap, offset, padding) => {\n  if (!isWellBehavedNumber(smallestDistanceInPercent)) {\n    return 0;\n  }\n  var rangeWidth = layout === 'vertical' ? offset.height : offset.width;\n  if (padding === 'gap') {\n    return smallestDistanceInPercent * rangeWidth / 2;\n  }\n  if (padding === 'no-gap') {\n    var gap = getPercentValue(barCategoryGap, smallestDistanceInPercent * rangeWidth);\n    var halfBand = smallestDistanceInPercent * rangeWidth / 2;\n    return halfBand - gap - (halfBand - gap) / rangeWidth * gap;\n  }\n  return 0;\n});\nexport var selectCalculatedXAxisPadding = (state, axisId) => {\n  var xAxisSettings = selectXAxisSettings(state, axisId);\n  if (xAxisSettings == null || typeof xAxisSettings.padding !== 'string') {\n    return 0;\n  }\n  return selectCalculatedPadding(state, 'xAxis', axisId, xAxisSettings.padding);\n};\nexport var selectCalculatedYAxisPadding = (state, axisId) => {\n  var yAxisSettings = selectYAxisSettings(state, axisId);\n  if (yAxisSettings == null || typeof yAxisSettings.padding !== 'string') {\n    return 0;\n  }\n  return selectCalculatedPadding(state, 'yAxis', axisId, yAxisSettings.padding);\n};\nvar selectXAxisPadding = createSelector(selectXAxisSettings, selectCalculatedXAxisPadding, (xAxisSettings, calculated) => {\n  var _padding$left, _padding$right;\n  if (xAxisSettings == null) {\n    return {\n      left: 0,\n      right: 0\n    };\n  }\n  var {\n    padding\n  } = xAxisSettings;\n  if (typeof padding === 'string') {\n    return {\n      left: calculated,\n      right: calculated\n    };\n  }\n  return {\n    left: ((_padding$left = padding.left) !== null && _padding$left !== void 0 ? _padding$left : 0) + calculated,\n    right: ((_padding$right = padding.right) !== null && _padding$right !== void 0 ? _padding$right : 0) + calculated\n  };\n});\nvar selectYAxisPadding = createSelector(selectYAxisSettings, selectCalculatedYAxisPadding, (yAxisSettings, calculated) => {\n  var _padding$top, _padding$bottom;\n  if (yAxisSettings == null) {\n    return {\n      top: 0,\n      bottom: 0\n    };\n  }\n  var {\n    padding\n  } = yAxisSettings;\n  if (typeof padding === 'string') {\n    return {\n      top: calculated,\n      bottom: calculated\n    };\n  }\n  return {\n    top: ((_padding$top = padding.top) !== null && _padding$top !== void 0 ? _padding$top : 0) + calculated,\n    bottom: ((_padding$bottom = padding.bottom) !== null && _padding$bottom !== void 0 ? _padding$bottom : 0) + calculated\n  };\n});\nexport var combineXAxisRange = createSelector([selectChartOffsetInternal, selectXAxisPadding, selectBrushDimensions, selectBrushSettings, (_state, _axisId, isPanorama) => isPanorama], (offset, padding, brushDimensions, _ref4, isPanorama) => {\n  var {\n    padding: brushPadding\n  } = _ref4;\n  if (isPanorama) {\n    return [brushPadding.left, brushDimensions.width - brushPadding.right];\n  }\n  return [offset.left + padding.left, offset.left + offset.width - padding.right];\n});\nexport var combineYAxisRange = createSelector([selectChartOffsetInternal, selectChartLayout, selectYAxisPadding, selectBrushDimensions, selectBrushSettings, (_state, _axisId, isPanorama) => isPanorama], (offset, layout, padding, brushDimensions, _ref5, isPanorama) => {\n  var {\n    padding: brushPadding\n  } = _ref5;\n  if (isPanorama) {\n    return [brushDimensions.height - brushPadding.bottom, brushPadding.top];\n  }\n  if (layout === 'horizontal') {\n    return [offset.top + offset.height - padding.bottom, offset.top + padding.top];\n  }\n  return [offset.top + padding.top, offset.top + offset.height - padding.bottom];\n});\nexport var selectAxisRange = (state, axisType, axisId, isPanorama) => {\n  var _selectZAxisSettings;\n  switch (axisType) {\n    case 'xAxis':\n      return combineXAxisRange(state, axisId, isPanorama);\n    case 'yAxis':\n      return combineYAxisRange(state, axisId, isPanorama);\n    case 'zAxis':\n      return (_selectZAxisSettings = selectZAxisSettings(state, axisId)) === null || _selectZAxisSettings === void 0 ? void 0 : _selectZAxisSettings.range;\n    case 'angleAxis':\n      return selectAngleAxisRange(state);\n    case 'radiusAxis':\n      return selectRadiusAxisRange(state, axisId);\n    default:\n      return undefined;\n  }\n};\nexport var selectAxisRangeWithReverse = createSelector([selectBaseAxis, selectAxisRange], combineAxisRangeWithReverse);\nexport var selectAxisScale = createSelector([selectBaseAxis, selectRealScaleType, selectAxisDomainIncludingNiceTicks, selectAxisRangeWithReverse], combineScaleFunction);\nexport var selectErrorBarsSettings = createSelector(selectCartesianItemsSettings, pickAxisType, (items, axisType) => {\n  return items.flatMap(item => {\n    var _item$errorBars2;\n    return (_item$errorBars2 = item.errorBars) !== null && _item$errorBars2 !== void 0 ? _item$errorBars2 : [];\n  }).filter(e => {\n    return isErrorBarRelevantForAxisType(axisType, e);\n  });\n});\nfunction compareIds(a, b) {\n  if (a.id < b.id) {\n    return -1;\n  }\n  if (a.id > b.id) {\n    return 1;\n  }\n  return 0;\n}\nvar pickAxisOrientation = (_state, orientation) => orientation;\nvar pickMirror = (_state, _orientation, mirror) => mirror;\nvar selectAllXAxesWithOffsetType = createSelector(selectAllXAxes, pickAxisOrientation, pickMirror, (allAxes, orientation, mirror) => allAxes.filter(axis => axis.orientation === orientation).filter(axis => axis.mirror === mirror).sort(compareIds));\nvar selectAllYAxesWithOffsetType = createSelector(selectAllYAxes, pickAxisOrientation, pickMirror, (allAxes, orientation, mirror) => allAxes.filter(axis => axis.orientation === orientation).filter(axis => axis.mirror === mirror).sort(compareIds));\nvar getXAxisSize = (offset, axisSettings) => {\n  return {\n    width: offset.width,\n    height: axisSettings.height\n  };\n};\nvar getYAxisSize = (offset, axisSettings) => {\n  var width = typeof axisSettings.width === 'number' ? axisSettings.width : DEFAULT_Y_AXIS_WIDTH;\n  return {\n    width,\n    height: offset.height\n  };\n};\nexport var selectXAxisSize = createSelector(selectChartOffsetInternal, selectXAxisSettings, getXAxisSize);\nvar combineXAxisPositionStartingPoint = (offset, orientation, chartHeight) => {\n  switch (orientation) {\n    case 'top':\n      return offset.top;\n    case 'bottom':\n      return chartHeight - offset.bottom;\n    default:\n      return 0;\n  }\n};\nvar combineYAxisPositionStartingPoint = (offset, orientation, chartWidth) => {\n  switch (orientation) {\n    case 'left':\n      return offset.left;\n    case 'right':\n      return chartWidth - offset.right;\n    default:\n      return 0;\n  }\n};\nexport var selectAllXAxesOffsetSteps = createSelector(selectChartHeight, selectChartOffsetInternal, selectAllXAxesWithOffsetType, pickAxisOrientation, pickMirror, (chartHeight, offset, allAxesWithSameOffsetType, orientation, mirror) => {\n  var steps = {};\n  var position;\n  allAxesWithSameOffsetType.forEach(axis => {\n    var axisSize = getXAxisSize(offset, axis);\n    if (position == null) {\n      position = combineXAxisPositionStartingPoint(offset, orientation, chartHeight);\n    }\n    var needSpace = orientation === 'top' && !mirror || orientation === 'bottom' && mirror;\n    steps[axis.id] = position - Number(needSpace) * axisSize.height;\n    position += (needSpace ? -1 : 1) * axisSize.height;\n  });\n  return steps;\n});\nexport var selectAllYAxesOffsetSteps = createSelector(selectChartWidth, selectChartOffsetInternal, selectAllYAxesWithOffsetType, pickAxisOrientation, pickMirror, (chartWidth, offset, allAxesWithSameOffsetType, orientation, mirror) => {\n  var steps = {};\n  var position;\n  allAxesWithSameOffsetType.forEach(axis => {\n    var axisSize = getYAxisSize(offset, axis);\n    if (position == null) {\n      position = combineYAxisPositionStartingPoint(offset, orientation, chartWidth);\n    }\n    var needSpace = orientation === 'left' && !mirror || orientation === 'right' && mirror;\n    steps[axis.id] = position - Number(needSpace) * axisSize.width;\n    position += (needSpace ? -1 : 1) * axisSize.width;\n  });\n  return steps;\n});\nexport var selectXAxisPosition = (state, axisId) => {\n  var offset = selectChartOffsetInternal(state);\n  var axisSettings = selectXAxisSettings(state, axisId);\n  if (axisSettings == null) {\n    return undefined;\n  }\n  var allSteps = selectAllXAxesOffsetSteps(state, axisSettings.orientation, axisSettings.mirror);\n  var stepOfThisAxis = allSteps[axisId];\n  if (stepOfThisAxis == null) {\n    return {\n      x: offset.left,\n      y: 0\n    };\n  }\n  return {\n    x: offset.left,\n    y: stepOfThisAxis\n  };\n};\nexport var selectYAxisPosition = (state, axisId) => {\n  var offset = selectChartOffsetInternal(state);\n  var axisSettings = selectYAxisSettings(state, axisId);\n  if (axisSettings == null) {\n    return undefined;\n  }\n  var allSteps = selectAllYAxesOffsetSteps(state, axisSettings.orientation, axisSettings.mirror);\n  var stepOfThisAxis = allSteps[axisId];\n  if (stepOfThisAxis == null) {\n    return {\n      x: 0,\n      y: offset.top\n    };\n  }\n  return {\n    x: stepOfThisAxis,\n    y: offset.top\n  };\n};\nexport var selectYAxisSize = createSelector(selectChartOffsetInternal, selectYAxisSettings, (offset, axisSettings) => {\n  var width = typeof axisSettings.width === 'number' ? axisSettings.width : DEFAULT_Y_AXIS_WIDTH;\n  return {\n    width,\n    height: offset.height\n  };\n});\nexport var selectCartesianAxisSize = (state, axisType, axisId) => {\n  switch (axisType) {\n    case 'xAxis':\n      {\n        return selectXAxisSize(state, axisId).width;\n      }\n    case 'yAxis':\n      {\n        return selectYAxisSize(state, axisId).height;\n      }\n    default:\n      {\n        return undefined;\n      }\n  }\n};\nexport var combineDuplicateDomain = (chartLayout, appliedValues, axis, axisType) => {\n  if (axis == null) {\n    return undefined;\n  }\n  var {\n    allowDuplicatedCategory,\n    type,\n    dataKey\n  } = axis;\n  var isCategorical = isCategoricalAxis(chartLayout, axisType);\n  var allData = appliedValues.map(av => av.value);\n  if (dataKey && isCategorical && type === 'category' && allowDuplicatedCategory && hasDuplicate(allData)) {\n    return allData;\n  }\n  return undefined;\n};\nexport var selectDuplicateDomain = createSelector([selectChartLayout, selectAllAppliedValues, selectBaseAxis, pickAxisType], combineDuplicateDomain);\nexport var combineCategoricalDomain = (layout, appliedValues, axis, axisType) => {\n  if (axis == null || axis.dataKey == null) {\n    return undefined;\n  }\n  var {\n    type,\n    scale\n  } = axis;\n  var isCategorical = isCategoricalAxis(layout, axisType);\n  if (isCategorical && (type === 'number' || scale !== 'auto')) {\n    return appliedValues.map(d => d.value);\n  }\n  return undefined;\n};\nexport var selectCategoricalDomain = createSelector([selectChartLayout, selectAllAppliedValues, selectAxisSettings, pickAxisType], combineCategoricalDomain);\nexport var selectAxisPropsNeededForCartesianGridTicksGenerator = createSelector([selectChartLayout, selectCartesianAxisSettings, selectRealScaleType, selectAxisScale, selectDuplicateDomain, selectCategoricalDomain, selectAxisRange, selectNiceTicks, pickAxisType], (layout, axis, realScaleType, scale, duplicateDomain, categoricalDomain, axisRange, niceTicks, axisType) => {\n  if (axis == null) {\n    return null;\n  }\n  var isCategorical = isCategoricalAxis(layout, axisType);\n  return {\n    angle: axis.angle,\n    interval: axis.interval,\n    minTickGap: axis.minTickGap,\n    orientation: axis.orientation,\n    tick: axis.tick,\n    tickCount: axis.tickCount,\n    tickFormatter: axis.tickFormatter,\n    ticks: axis.ticks,\n    type: axis.type,\n    unit: axis.unit,\n    axisType,\n    categoricalDomain,\n    duplicateDomain,\n    isCategorical,\n    niceTicks,\n    range: axisRange,\n    realScaleType,\n    scale\n  };\n});\nexport var combineAxisTicks = (layout, axis, realScaleType, scale, niceTicks, axisRange, duplicateDomain, categoricalDomain, axisType) => {\n  if (axis == null || scale == null) {\n    return undefined;\n  }\n  var isCategorical = isCategoricalAxis(layout, axisType);\n  var {\n    type,\n    ticks,\n    tickCount\n  } = axis;\n\n  // This is testing for `scaleBand` but for band axis the type is reported as `band` so this looks like a dead code with a workaround elsewhere?\n  var offsetForBand = realScaleType === 'scaleBand' && typeof scale.bandwidth === 'function' ? scale.bandwidth() / 2 : 2;\n  var offset = type === 'category' && scale.bandwidth ? scale.bandwidth() / offsetForBand : 0;\n  offset = axisType === 'angleAxis' && axisRange != null && axisRange.length >= 2 ? mathSign(axisRange[0] - axisRange[1]) * 2 * offset : offset;\n\n  // The ticks set by user should only affect the ticks adjacent to axis line\n  var ticksOrNiceTicks = ticks || niceTicks;\n  if (ticksOrNiceTicks) {\n    var result = ticksOrNiceTicks.map((entry, index) => {\n      var scaleContent = duplicateDomain ? duplicateDomain.indexOf(entry) : entry;\n      return {\n        index,\n        // If the scaleContent is not a number, the coordinate will be NaN.\n        // That could be the case for example with a PointScale and a string as domain.\n        coordinate: scale(scaleContent) + offset,\n        value: entry,\n        offset\n      };\n    });\n    return result.filter(row => !isNan(row.coordinate));\n  }\n\n  // When axis is a categorical axis, but the type of axis is number or the scale of axis is not \"auto\"\n  if (isCategorical && categoricalDomain) {\n    return categoricalDomain.map((entry, index) => ({\n      coordinate: scale(entry) + offset,\n      value: entry,\n      index,\n      offset\n    }));\n  }\n  if (scale.ticks) {\n    return scale.ticks(tickCount)\n    // @ts-expect-error why does the offset go here? The type does not require it\n    .map(entry => ({\n      coordinate: scale(entry) + offset,\n      value: entry,\n      offset\n    }));\n  }\n\n  // When axis has duplicated text, serial numbers are used to generate scale\n  return scale.domain().map((entry, index) => ({\n    coordinate: scale(entry) + offset,\n    value: duplicateDomain ? duplicateDomain[entry] : entry,\n    index,\n    offset\n  }));\n};\nexport var selectTicksOfAxis = createSelector([selectChartLayout, selectAxisSettings, selectRealScaleType, selectAxisScale, selectNiceTicks, selectAxisRange, selectDuplicateDomain, selectCategoricalDomain, pickAxisType], combineAxisTicks);\nexport var combineGraphicalItemTicks = (layout, axis, scale, axisRange, duplicateDomain, categoricalDomain, axisType) => {\n  if (axis == null || scale == null || axisRange == null || axisRange[0] === axisRange[1]) {\n    return undefined;\n  }\n  var isCategorical = isCategoricalAxis(layout, axisType);\n  var {\n    tickCount\n  } = axis;\n  var offset = 0;\n  offset = axisType === 'angleAxis' && (axisRange === null || axisRange === void 0 ? void 0 : axisRange.length) >= 2 ? mathSign(axisRange[0] - axisRange[1]) * 2 * offset : offset;\n\n  // When axis is a categorical axis, but the type of axis is number or the scale of axis is not \"auto\"\n  if (isCategorical && categoricalDomain) {\n    return categoricalDomain.map((entry, index) => ({\n      coordinate: scale(entry) + offset,\n      value: entry,\n      index,\n      offset\n    }));\n  }\n  if (scale.ticks) {\n    return scale.ticks(tickCount)\n    // @ts-expect-error why does the offset go here? The type does not require it\n    .map(entry => ({\n      coordinate: scale(entry) + offset,\n      value: entry,\n      offset\n    }));\n  }\n\n  // When axis has duplicated text, serial numbers are used to generate scale\n  return scale.domain().map((entry, index) => ({\n    coordinate: scale(entry) + offset,\n    value: duplicateDomain ? duplicateDomain[entry] : entry,\n    index,\n    offset\n  }));\n};\nexport var selectTicksOfGraphicalItem = createSelector([selectChartLayout, selectAxisSettings, selectAxisScale, selectAxisRange, selectDuplicateDomain, selectCategoricalDomain, pickAxisType], combineGraphicalItemTicks);\nexport var selectAxisWithScale = createSelector(selectBaseAxis, selectAxisScale, (axis, scale) => {\n  if (axis == null || scale == null) {\n    return undefined;\n  }\n  return _objectSpread(_objectSpread({}, axis), {}, {\n    scale\n  });\n});\nvar selectZAxisScale = createSelector([selectBaseAxis, selectRealScaleType, selectAxisDomain, selectAxisRangeWithReverse], combineScaleFunction);\nexport var selectZAxisWithScale = createSelector((state, _axisType, axisId) => selectZAxisSettings(state, axisId), selectZAxisScale, (axis, scale) => {\n  if (axis == null || scale == null) {\n    return undefined;\n  }\n  return _objectSpread(_objectSpread({}, axis), {}, {\n    scale\n  });\n});\n\n/**\n * We are also going to need to implement polar chart directions if we want to support keyboard controls for those.\n */\n\nexport var selectChartDirection = createSelector([selectChartLayout, selectAllXAxes, selectAllYAxes], (layout, allXAxes, allYAxes) => {\n  switch (layout) {\n    case 'horizontal':\n      {\n        return allXAxes.some(axis => axis.reversed) ? 'right-to-left' : 'left-to-right';\n      }\n    case 'vertical':\n      {\n        return allYAxes.some(axis => axis.reversed) ? 'bottom-to-top' : 'top-to-bottom';\n      }\n    // TODO: make this better. For now, right arrow triggers \"forward\", left arrow \"back\"\n    // however, the tooltip moves an unintuitive direction because of how the indices are rendered\n    case 'centric':\n    case 'radial':\n      {\n        return 'left-to-right';\n      }\n    default:\n      {\n        return undefined;\n      }\n  }\n});", "map": {"version": 3, "names": ["ownKeys", "e", "r", "t", "Object", "keys", "getOwnPropertySymbols", "o", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "length", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "configurable", "writable", "i", "_toPrimitive", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "createSelector", "range", "d3Scales", "selectChartLayout", "checkDomainOfScale", "getDomainOfStackGroups", "getStackedData", "getValueByDataKey", "isCategoricalAxis", "selectChartDataWithIndexes", "selectChartDataWithIndexesIfNotInPanorama", "isWellFormedNumberDomain", "numericalDomainSpecifiedWithoutRequiringData", "parseNumericalUserDomain", "getPercentValue", "hasDuplicate", "isNan", "isNumber", "isNumOrStr", "mathSign", "upperFirst", "isWellBehavedNumber", "getNiceTickValues", "getTickValuesFixedDomain", "selectChartHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectAllXAxes", "selectAllYAxes", "selectChartOffsetInternal", "selectBrushDimensions", "selectBrushSettings", "selectBarCategoryGap", "selectChartName", "selectStackOffsetType", "selectAngleAxis", "selectAngleAxisRange", "selectRadiusAxis", "selectRadiusAxisRange", "pickAxisType", "pickAxisId", "combineAxisRangeWithReverse", "DEFAULT_Y_AXIS_WIDTH", "defaultNumericDomain", "implicitXAxis", "allowDataOverflow", "allowDecimals", "allowDuplicatedCategory", "angle", "dataKey", "undefined", "domain", "height", "hide", "id", "includeHidden", "interval", "minTickGap", "mirror", "name", "orientation", "padding", "left", "right", "reversed", "scale", "tick", "tickCount", "tick<PERSON><PERSON><PERSON><PERSON>", "ticks", "type", "unit", "selectXAxisSettings", "state", "axisId", "axis", "cartesianAxis", "xAxis", "implicitYAxis", "top", "bottom", "width", "selectYAxisSettings", "yAxis", "implicitZAxis", "selectZAxisSettings", "zAxis", "selectBaseAxis", "axisType", "Error", "concat", "selectCartesianAxisSettings", "selectAxisSettings", "selectHasBar", "graphicalItems", "countOfBars", "itemAxisPredicate", "item", "xAxisId", "yAxisId", "zAxisId", "angleAxisId", "radiusAxisId", "selectUnfilteredCartesianItems", "cartesianItems", "selectAxisPredicate", "combineGraphicalItemsSettings", "axisSettings", "axisPredicate", "selectCartesianItemsSettings", "filterGraphicalNotStackedItems", "stackId", "selectCartesianItemsSettingsExceptStacked", "combineGraphicalItemsData", "map", "data", "Boolean", "flat", "selectCartesianGraphicalItemsData", "combineDisplayedData", "graphicalItemsData", "_ref", "chartData", "dataStartIndex", "dataEndIndex", "slice", "selectDisplayedData", "combineAppliedValues", "items", "flatMap", "entry", "selectAllAppliedValues", "isErrorBarRelevantForAxisType", "errorBar", "direction", "fromMainValueToError", "isFinite", "Array", "isArray", "minError", "Math", "min", "maxError", "max", "onlyAllow<PERSON>umbers", "v", "Date", "n", "getErrorDomainByDataKey", "appliedValue", "relevantErrorBars", "eb", "errorValue", "lowBound", "highBound", "combineStackGroups", "displayedData", "stackOffsetType", "initialItemsGroups", "itemsGroup", "reduce", "acc", "fromEntries", "entries", "_ref2", "dataKeys", "stackedData", "selectStackGroups", "combineDomainOfStackGroups", "stackGroups", "_ref3", "domainOfStackGroups", "selectDomainOfStackGroups", "combineAppliedNumericalValuesIncludingErrorValues", "_item$errorBars", "_axisSettings$dataKey", "errorBars", "valueByDataKey", "errorDomain", "selectAllAppliedNumericalValuesIncludingErrorValues", "onlyAllowNumbersAndStringsAndDates", "computeNumericalDomain", "dataWithErrorDomains", "allDataSquished", "d", "only<PERSON><PERSON>bers", "computeDomainOfTypeCategory", "isCategorical", "categoricalDomain", "from", "Set", "getDomainDefinition", "_axisSettings$domain", "allValues", "mergeDomains", "_len", "domains", "_key", "allDomains", "selectReferenceDots", "referenceElements", "dots", "filterReferenceElements", "elements", "el", "ifOverflow", "selectReferenceDotsByAxis", "selectReferenceAreas", "areas", "selectReferenceAreasByAxis", "selectReferenceLines", "lines", "selectReferenceLinesByAxis", "combineDotsDomain", "allCoords", "dot", "x", "y", "selectReferenceDotsDomain", "combineAreasDomain", "area", "x1", "y1", "x2", "y2", "selectReferenceAreasDomain", "combineLinesDomain", "line", "selectReferenceLinesDomain", "selectReferenceElementsDomain", "dotsDomain", "linesDomain", "areasDomain", "selectDomainDefinition", "combineNumericalDomain", "domainDefinition", "allDataWithErrorDomains", "referenceElementsDomain", "domainFromUserPreference", "selectNumericalDomain", "expandDomain", "combineAxisDomain", "layout", "allAppliedValues", "numericalDomain", "selectAxisDomain", "combineRealScaleType", "axisConfig", "<PERSON><PERSON><PERSON>", "chartType", "indexOf", "selectRealScaleType", "getD3ScaleFromType", "realScaleType", "combineScaleFunction", "axisDomain", "axisRange", "copy", "d3ScaleFunction", "combineNiceTicks", "selectNiceTicks", "combineAxisDomainWithNiceTicks", "niceTicks", "minFromDomain", "minFromTicks", "maxFromDomain", "maxFromTicks", "selectAxisDomainIncludingNiceTicks", "selectSmallestDistanceBetweenValues", "smallestDistanceBetweenValues", "Infinity", "sortedValues", "sort", "a", "b", "diff", "distance", "selectCalculatedPadding", "_1", "_2", "_3", "smallestDistanceInPercent", "barCategoryGap", "offset", "rangeWidth", "gap", "halfBand", "selectCalculatedXAxisPadding", "xAxisSettings", "selectCalculatedYAxisPadding", "yAxisSettings", "selectXAxisPadding", "calculated", "_padding$left", "_padding$right", "selectYAxisPadding", "_padding$top", "_padding$bottom", "combineXAxisRange", "_state", "_axisId", "isPanorama", "brushDimensions", "_ref4", "brushPadding", "combineYAxisRange", "_ref5", "selectAxisRange", "_selectZAxisSettings", "selectAxisRangeWithReverse", "selectAxisScale", "selectErrorBarsSettings", "_item$errorBars2", "compareIds", "pickAxisOrientation", "pick<PERSON><PERSON><PERSON>r", "_orientation", "selectAllXAxesWithOffsetType", "allAxes", "selectAllYAxesWithOffsetType", "getXAxisSize", "getYAxisSize", "selectXAxisSize", "combineXAxisPositionStartingPoint", "chartHeight", "combineYAxisPositionStartingPoint", "chartWidth", "selectAllXAxesOffsetSteps", "allAxesWithSameOffsetType", "steps", "position", "axisSize", "needSpace", "selectAllYAxesOffsetSteps", "selectXAxisPosition", "allSteps", "stepOfThisAxis", "selectYAxisPosition", "selectYAxisSize", "selectCartesianAxisSize", "combineDuplicateDomain", "chartLayout", "appliedValues", "allData", "av", "selectDuplicateDomain", "combineCategoricalDomain", "selectCategoricalDomain", "selectAxisPropsNeededForCartesianGridTicksGenerator", "duplicateDomain", "combineAxisTicks", "offsetForBand", "bandwidth", "ticksOrNiceTicks", "result", "index", "scaleContent", "coordinate", "row", "selectTicksOfAxis", "combineGraphicalItemTicks", "selectTicksOfGraphicalItem", "selectAxisWithScale", "selectZAxisScale", "selectZAxisWithScale", "_axisType", "selectChartDirection", "allXAxes", "allYAxes", "some"], "sources": ["D:/menasa/frontend/node_modules/recharts/es6/state/selectors/axisSelectors.js"], "sourcesContent": ["function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport { createSelector } from 'reselect';\nimport range from 'es-toolkit/compat/range';\nimport * as d3Scales from 'victory-vendor/d3-scale';\nimport { selectChartLayout } from '../../context/chartLayoutContext';\nimport { checkDomainOfScale, getDomainOfStackGroups, getStackedData, getValueByDataKey, isCategoricalAxis } from '../../util/ChartUtils';\nimport { selectChartDataWithIndexes, selectChartDataWithIndexesIfNotInPanorama } from './dataSelectors';\nimport { isWellFormedNumberDomain, numericalDomainSpecifiedWithoutRequiringData, parseNumericalUserDomain } from '../../util/isDomainSpecifiedByUser';\nimport { getPercentValue, hasDuplicate, isNan, isNumber, isNumOrStr, mathSign, upperFirst } from '../../util/DataUtils';\nimport { isWellBehavedNumber } from '../../util/isWellBehavedNumber';\nimport { getNiceTickValues, getTickValuesFixedDomain } from '../../util/scale';\nimport { selectChartHeight, selectChartWidth } from './containerSelectors';\nimport { selectAllXAxes, selectAllYAxes } from './selectAllAxes';\nimport { selectChartOffsetInternal } from './selectChartOffsetInternal';\nimport { selectBrushDimensions, selectBrushSettings } from './brushSelectors';\nimport { selectBarCategoryGap, selectChartName, selectStackOffsetType } from './rootPropsSelectors';\nimport { selectAngleAxis, selectAngleAxisRange, selectRadiusAxis, selectRadiusAxisRange } from './polarAxisSelectors';\nimport { pickAxisType } from './pickAxisType';\nimport { pickAxisId } from './pickAxisId';\nimport { combineAxisRangeWithReverse } from './combiners/combineAxisRangeWithReverse';\nimport { DEFAULT_Y_AXIS_WIDTH } from '../../util/Constants';\nvar defaultNumericDomain = [0, 'auto'];\n\n/**\n * angle, radius, X, Y, and Z axes all have domain and range and scale and associated settings\n */\n\n/**\n * X and Y axes have ticks. Z axis is never displayed and so it lacks ticks\n * and tick settings.\n */\n\n/**\n * If an axis is not explicitly defined as an element,\n * we still need to render something in the chart and we need\n * some object to hold the domain and default settings.\n */\nexport var implicitXAxis = {\n  allowDataOverflow: false,\n  allowDecimals: true,\n  allowDuplicatedCategory: true,\n  angle: 0,\n  dataKey: undefined,\n  domain: undefined,\n  height: 30,\n  hide: true,\n  id: 0,\n  includeHidden: false,\n  interval: 'preserveEnd',\n  minTickGap: 5,\n  mirror: false,\n  name: undefined,\n  orientation: 'bottom',\n  padding: {\n    left: 0,\n    right: 0\n  },\n  reversed: false,\n  scale: 'auto',\n  tick: true,\n  tickCount: 5,\n  tickFormatter: undefined,\n  ticks: undefined,\n  type: 'category',\n  unit: undefined\n};\nexport var selectXAxisSettings = (state, axisId) => {\n  var axis = state.cartesianAxis.xAxis[axisId];\n  if (axis == null) {\n    return implicitXAxis;\n  }\n  return axis;\n};\n\n/**\n * If an axis is not explicitly defined as an element,\n * we still need to render something in the chart and we need\n * some object to hold the domain and default settings.\n */\nexport var implicitYAxis = {\n  allowDataOverflow: false,\n  allowDecimals: true,\n  allowDuplicatedCategory: true,\n  angle: 0,\n  dataKey: undefined,\n  domain: defaultNumericDomain,\n  hide: true,\n  id: 0,\n  includeHidden: false,\n  interval: 'preserveEnd',\n  minTickGap: 5,\n  mirror: false,\n  name: undefined,\n  orientation: 'left',\n  padding: {\n    top: 0,\n    bottom: 0\n  },\n  reversed: false,\n  scale: 'auto',\n  tick: true,\n  tickCount: 5,\n  tickFormatter: undefined,\n  ticks: undefined,\n  type: 'number',\n  unit: undefined,\n  width: DEFAULT_Y_AXIS_WIDTH\n};\nexport var selectYAxisSettings = (state, axisId) => {\n  var axis = state.cartesianAxis.yAxis[axisId];\n  if (axis == null) {\n    return implicitYAxis;\n  }\n  return axis;\n};\nexport var implicitZAxis = {\n  domain: [0, 'auto'],\n  includeHidden: false,\n  reversed: false,\n  allowDataOverflow: false,\n  allowDuplicatedCategory: false,\n  dataKey: undefined,\n  id: 0,\n  name: '',\n  range: [64, 64],\n  scale: 'auto',\n  type: 'number',\n  unit: ''\n};\nexport var selectZAxisSettings = (state, axisId) => {\n  var axis = state.cartesianAxis.zAxis[axisId];\n  if (axis == null) {\n    return implicitZAxis;\n  }\n  return axis;\n};\nexport var selectBaseAxis = (state, axisType, axisId) => {\n  switch (axisType) {\n    case 'xAxis':\n      {\n        return selectXAxisSettings(state, axisId);\n      }\n    case 'yAxis':\n      {\n        return selectYAxisSettings(state, axisId);\n      }\n    case 'zAxis':\n      {\n        return selectZAxisSettings(state, axisId);\n      }\n    case 'angleAxis':\n      {\n        return selectAngleAxis(state, axisId);\n      }\n    case 'radiusAxis':\n      {\n        return selectRadiusAxis(state, axisId);\n      }\n    default:\n      throw new Error(\"Unexpected axis type: \".concat(axisType));\n  }\n};\nvar selectCartesianAxisSettings = (state, axisType, axisId) => {\n  switch (axisType) {\n    case 'xAxis':\n      {\n        return selectXAxisSettings(state, axisId);\n      }\n    case 'yAxis':\n      {\n        return selectYAxisSettings(state, axisId);\n      }\n    default:\n      throw new Error(\"Unexpected axis type: \".concat(axisType));\n  }\n};\n\n/**\n * Selects either an X or Y axis. Doesn't work with Z axis - for that, instead use selectBaseAxis.\n * @param state Root state\n * @param axisType xAxis | yAxis\n * @param axisId xAxisId | yAxisId\n * @returns axis settings object\n */\nexport var selectAxisSettings = (state, axisType, axisId) => {\n  switch (axisType) {\n    case 'xAxis':\n      {\n        return selectXAxisSettings(state, axisId);\n      }\n    case 'yAxis':\n      {\n        return selectYAxisSettings(state, axisId);\n      }\n    case 'angleAxis':\n      {\n        return selectAngleAxis(state, axisId);\n      }\n    case 'radiusAxis':\n      {\n        return selectRadiusAxis(state, axisId);\n      }\n    default:\n      throw new Error(\"Unexpected axis type: \".concat(axisType));\n  }\n};\n\n/**\n * @param state RechartsRootState\n * @return boolean true if there is at least one Bar or RadialBar\n */\nexport var selectHasBar = state => state.graphicalItems.countOfBars > 0;\n\n/**\n * Filters CartesianGraphicalItemSettings by the relevant axis ID\n * @param axisType 'xAxis' | 'yAxis' | 'zAxis' | 'radiusAxis' | 'angleAxis'\n * @param axisId from props, defaults to 0\n *\n * @returns Predicate function that return true for CartesianGraphicalItemSettings that are relevant to the specified axis\n */\nexport function itemAxisPredicate(axisType, axisId) {\n  return item => {\n    switch (axisType) {\n      case 'xAxis':\n        // This is sensitive to the data type, as 0 !== '0'. I wonder if we should be more flexible. How does 2.x branch behave? TODO write test for that\n        return 'xAxisId' in item && item.xAxisId === axisId;\n      case 'yAxis':\n        return 'yAxisId' in item && item.yAxisId === axisId;\n      case 'zAxis':\n        return 'zAxisId' in item && item.zAxisId === axisId;\n      case 'angleAxis':\n        return 'angleAxisId' in item && item.angleAxisId === axisId;\n      case 'radiusAxis':\n        return 'radiusAxisId' in item && item.radiusAxisId === axisId;\n      default:\n        return false;\n    }\n  };\n}\nexport var selectUnfilteredCartesianItems = state => state.graphicalItems.cartesianItems;\nvar selectAxisPredicate = createSelector([pickAxisType, pickAxisId], itemAxisPredicate);\nexport var combineGraphicalItemsSettings = (graphicalItems, axisSettings, axisPredicate) => graphicalItems.filter(axisPredicate).filter(item => {\n  if ((axisSettings === null || axisSettings === void 0 ? void 0 : axisSettings.includeHidden) === true) {\n    return true;\n  }\n  return !item.hide;\n});\nexport var selectCartesianItemsSettings = createSelector([selectUnfilteredCartesianItems, selectBaseAxis, selectAxisPredicate], combineGraphicalItemsSettings);\nexport var filterGraphicalNotStackedItems = cartesianItems => cartesianItems.filter(item => item.stackId === undefined);\nvar selectCartesianItemsSettingsExceptStacked = createSelector([selectCartesianItemsSettings], filterGraphicalNotStackedItems);\nexport var combineGraphicalItemsData = cartesianItems => cartesianItems.map(item => item.data).filter(Boolean).flat(1);\n\n/**\n * This is a \"cheap\" selector - it returns the data but doesn't iterate them, so it is not sensitive on the array length.\n * Also does not apply dataKey yet.\n * @param state RechartsRootState\n * @returns data defined on the chart graphical items, such as Line or Scatter or Pie, and filtered with appropriate dataKey\n */\nexport var selectCartesianGraphicalItemsData = createSelector([selectCartesianItemsSettings], combineGraphicalItemsData);\nexport var combineDisplayedData = (graphicalItemsData, _ref) => {\n  var {\n    chartData = [],\n    dataStartIndex,\n    dataEndIndex\n  } = _ref;\n  if (graphicalItemsData.length > 0) {\n    /*\n     * There is no slicing when data is defined on graphical items. Why?\n     * Because Brush ignores data defined on graphical items,\n     * and does not render.\n     * So Brush will never show up in a Scatter chart for example.\n     * This is something we will need to fix.\n     *\n     * Now, when the root chart data is not defined, the dataEndIndex is 0,\n     * which means the itemsData will be sliced to an empty array anyway.\n     * But that's an implementation detail, and we can fix that too.\n     *\n     * Also, in absence of Axis dataKey, we use the dataKey from each item, respectively.\n     * This is the usual pattern for numerical axis, that is the one where bars go up:\n     * users don't specify any dataKey by default and expect the axis to \"just match the data\".\n     */\n    return graphicalItemsData;\n  }\n  return chartData.slice(dataStartIndex, dataEndIndex + 1);\n};\n\n/**\n * This selector will return all data there is in the chart: graphical items, chart root, all together.\n * Useful for figuring out an axis domain (because that needs to know of everything),\n * not useful for rendering individual graphical elements (because they need to know which data is theirs and which is not).\n *\n * This function will discard the original indexes, so it is also not useful for anything that depends on ordering.\n */\nexport var selectDisplayedData = createSelector([selectCartesianGraphicalItemsData, selectChartDataWithIndexesIfNotInPanorama], combineDisplayedData);\nexport var combineAppliedValues = (data, axisSettings, items) => {\n  if ((axisSettings === null || axisSettings === void 0 ? void 0 : axisSettings.dataKey) != null) {\n    return data.map(item => ({\n      value: getValueByDataKey(item, axisSettings.dataKey)\n    }));\n  }\n  if (items.length > 0) {\n    return items.map(item => item.dataKey).flatMap(dataKey => data.map(entry => ({\n      value: getValueByDataKey(entry, dataKey)\n    })));\n  }\n  return data.map(entry => ({\n    value: entry\n  }));\n};\n\n/**\n * This selector will return all values with the appropriate dataKey applied on them.\n * Which dataKey is appropriate depends on where it is defined.\n *\n * This is an expensive selector - it will iterate all data and compute their value using the provided dataKey.\n */\nexport var selectAllAppliedValues = createSelector([selectDisplayedData, selectBaseAxis, selectCartesianItemsSettings], combineAppliedValues);\nexport function isErrorBarRelevantForAxisType(axisType, errorBar) {\n  switch (axisType) {\n    case 'xAxis':\n      return errorBar.direction === 'x';\n    case 'yAxis':\n      return errorBar.direction === 'y';\n    default:\n      return false;\n  }\n}\n\n/**\n * This is type of \"error\" in chart. It is set by using ErrorBar, and it can represent confidence interval,\n * or gap in the data, or standard deviation, or quartiles in boxplot, or whiskers or whatever.\n *\n * We will internally represent it as a tuple of two numbers, where the first number is the lower bound and the second number is the upper bound.\n *\n * It is also true that the first number should be lower than or equal to the associated \"main value\",\n * and the second number should be higher than or equal to the associated \"main value\".\n */\n\nexport function fromMainValueToError(value) {\n  if (isNumber(value) && Number.isFinite(value)) {\n    return [value, value];\n  }\n  if (Array.isArray(value)) {\n    var minError = Math.min(...value);\n    var maxError = Math.max(...value);\n    if (!isNan(minError) && !isNan(maxError) && Number.isFinite(minError) && Number.isFinite(maxError)) {\n      return [minError, maxError];\n    }\n  }\n  return undefined;\n}\nfunction onlyAllowNumbers(data) {\n  return data.filter(v => isNumOrStr(v) || v instanceof Date).map(Number).filter(n => isNan(n) === false);\n}\n\n/**\n * @param entry One item in the 'data' array. Could be anything really - this is defined externally. This is the raw, before dataKey application\n * @param appliedValue This is the result of applying the 'main' dataKey on the `entry`.\n * @param relevantErrorBars Error bars that are relevant for the current axis and layout and all that.\n * @return either undefined or an array of ErrorValue\n */\nexport function getErrorDomainByDataKey(entry, appliedValue, relevantErrorBars) {\n  if (!relevantErrorBars || typeof appliedValue !== 'number' || isNan(appliedValue)) {\n    return [];\n  }\n  if (!relevantErrorBars.length) {\n    return [];\n  }\n  return onlyAllowNumbers(relevantErrorBars.flatMap(eb => {\n    var errorValue = getValueByDataKey(entry, eb.dataKey);\n    var lowBound, highBound;\n    if (Array.isArray(errorValue)) {\n      [lowBound, highBound] = errorValue;\n    } else {\n      lowBound = highBound = errorValue;\n    }\n    if (!isWellBehavedNumber(lowBound) || !isWellBehavedNumber(highBound)) {\n      return undefined;\n    }\n    return [appliedValue - lowBound, appliedValue + highBound];\n  }));\n}\nexport var combineStackGroups = (displayedData, items, stackOffsetType) => {\n  var initialItemsGroups = {};\n  var itemsGroup = items.reduce((acc, item) => {\n    if (item.stackId == null) {\n      return acc;\n    }\n    if (acc[item.stackId] == null) {\n      acc[item.stackId] = [];\n    }\n    acc[item.stackId].push(item);\n    return acc;\n  }, initialItemsGroups);\n  return Object.fromEntries(Object.entries(itemsGroup).map(_ref2 => {\n    var [stackId, graphicalItems] = _ref2;\n    var dataKeys = graphicalItems.map(i => i.dataKey);\n    return [stackId, {\n      // @ts-expect-error getStackedData requires that the input is array of objects, Recharts does not test for that\n      stackedData: getStackedData(displayedData, dataKeys, stackOffsetType),\n      graphicalItems\n    }];\n  }));\n};\n/**\n * Stack groups are groups of graphical items that stack on each other.\n * Stack is a function of axis type (X, Y), axis ID, and stack ID.\n * Graphical items that do not have a stack ID are not going to be present in stack groups.\n */\nexport var selectStackGroups = createSelector([selectDisplayedData, selectCartesianItemsSettings, selectStackOffsetType], combineStackGroups);\nexport var combineDomainOfStackGroups = (stackGroups, _ref3, axisType) => {\n  var {\n    dataStartIndex,\n    dataEndIndex\n  } = _ref3;\n  if (axisType === 'zAxis') {\n    // ZAxis ignores stacks\n    return undefined;\n  }\n  var domainOfStackGroups = getDomainOfStackGroups(stackGroups, dataStartIndex, dataEndIndex);\n  if (domainOfStackGroups != null && domainOfStackGroups[0] === 0 && domainOfStackGroups[1] === 0) {\n    return undefined;\n  }\n  return domainOfStackGroups;\n};\nexport var selectDomainOfStackGroups = createSelector([selectStackGroups, selectChartDataWithIndexes, pickAxisType], combineDomainOfStackGroups);\nexport var combineAppliedNumericalValuesIncludingErrorValues = (data, axisSettings, items, axisType) => {\n  if (items.length > 0) {\n    return data.flatMap(entry => {\n      return items.flatMap(item => {\n        var _item$errorBars, _axisSettings$dataKey;\n        var relevantErrorBars = (_item$errorBars = item.errorBars) === null || _item$errorBars === void 0 ? void 0 : _item$errorBars.filter(errorBar => isErrorBarRelevantForAxisType(axisType, errorBar));\n        var valueByDataKey = getValueByDataKey(entry, (_axisSettings$dataKey = axisSettings.dataKey) !== null && _axisSettings$dataKey !== void 0 ? _axisSettings$dataKey : item.dataKey);\n        return {\n          value: valueByDataKey,\n          errorDomain: getErrorDomainByDataKey(entry, valueByDataKey, relevantErrorBars)\n        };\n      });\n    }).filter(Boolean);\n  }\n  if ((axisSettings === null || axisSettings === void 0 ? void 0 : axisSettings.dataKey) != null) {\n    return data.map(item => ({\n      value: getValueByDataKey(item, axisSettings.dataKey),\n      errorDomain: []\n    }));\n  }\n  return data.map(entry => ({\n    value: entry,\n    errorDomain: []\n  }));\n};\nexport var selectAllAppliedNumericalValuesIncludingErrorValues = createSelector(selectDisplayedData, selectBaseAxis, selectCartesianItemsSettingsExceptStacked, pickAxisType, combineAppliedNumericalValuesIncludingErrorValues);\nfunction onlyAllowNumbersAndStringsAndDates(item) {\n  var {\n    value\n  } = item;\n  if (isNumOrStr(value) || value instanceof Date) {\n    return value;\n  }\n  return undefined;\n}\nvar computeNumericalDomain = dataWithErrorDomains => {\n  var allDataSquished = dataWithErrorDomains\n  // This flatMap has to be flat because we're creating a new array in the return value\n  .flatMap(d => [d.value, d.errorDomain])\n  // This flat is needed because a) errorDomain is an array, and b) value may be a number, or it may be a range (for Area, for example)\n  .flat(1);\n  var onlyNumbers = onlyAllowNumbers(allDataSquished);\n  if (onlyNumbers.length === 0) {\n    return undefined;\n  }\n  return [Math.min(...onlyNumbers), Math.max(...onlyNumbers)];\n};\nvar computeDomainOfTypeCategory = (allDataSquished, axisSettings, isCategorical) => {\n  var categoricalDomain = allDataSquished.map(onlyAllowNumbersAndStringsAndDates).filter(v => v != null);\n  if (isCategorical && (axisSettings.dataKey == null || axisSettings.allowDuplicatedCategory && hasDuplicate(categoricalDomain))) {\n    /*\n     * 1. In an absence of dataKey, Recharts will use array indexes as its categorical domain\n     * 2. When category axis has duplicated text, serial numbers are used to generate scale\n     */\n    return range(0, allDataSquished.length);\n  }\n  if (axisSettings.allowDuplicatedCategory) {\n    return categoricalDomain;\n  }\n  return Array.from(new Set(categoricalDomain));\n};\nexport var getDomainDefinition = axisSettings => {\n  var _axisSettings$domain;\n  if (axisSettings == null || !('domain' in axisSettings)) {\n    return defaultNumericDomain;\n  }\n  if (axisSettings.domain != null) {\n    return axisSettings.domain;\n  }\n  if (axisSettings.ticks != null) {\n    if (axisSettings.type === 'number') {\n      var allValues = onlyAllowNumbers(axisSettings.ticks);\n      return [Math.min(...allValues), Math.max(...allValues)];\n    }\n    if (axisSettings.type === 'category') {\n      return axisSettings.ticks.map(String);\n    }\n  }\n  return (_axisSettings$domain = axisSettings === null || axisSettings === void 0 ? void 0 : axisSettings.domain) !== null && _axisSettings$domain !== void 0 ? _axisSettings$domain : defaultNumericDomain;\n};\nexport var mergeDomains = function mergeDomains() {\n  for (var _len = arguments.length, domains = new Array(_len), _key = 0; _key < _len; _key++) {\n    domains[_key] = arguments[_key];\n  }\n  var allDomains = domains.filter(Boolean);\n  if (allDomains.length === 0) {\n    return undefined;\n  }\n  var allValues = allDomains.flat();\n  var min = Math.min(...allValues);\n  var max = Math.max(...allValues);\n  return [min, max];\n};\nexport var selectReferenceDots = state => state.referenceElements.dots;\nexport var filterReferenceElements = (elements, axisType, axisId) => {\n  return elements.filter(el => el.ifOverflow === 'extendDomain').filter(el => {\n    if (axisType === 'xAxis') {\n      return el.xAxisId === axisId;\n    }\n    return el.yAxisId === axisId;\n  });\n};\nexport var selectReferenceDotsByAxis = createSelector([selectReferenceDots, pickAxisType, pickAxisId], filterReferenceElements);\nexport var selectReferenceAreas = state => state.referenceElements.areas;\nexport var selectReferenceAreasByAxis = createSelector([selectReferenceAreas, pickAxisType, pickAxisId], filterReferenceElements);\nexport var selectReferenceLines = state => state.referenceElements.lines;\nexport var selectReferenceLinesByAxis = createSelector([selectReferenceLines, pickAxisType, pickAxisId], filterReferenceElements);\nexport var combineDotsDomain = (dots, axisType) => {\n  var allCoords = onlyAllowNumbers(dots.map(dot => axisType === 'xAxis' ? dot.x : dot.y));\n  if (allCoords.length === 0) {\n    return undefined;\n  }\n  return [Math.min(...allCoords), Math.max(...allCoords)];\n};\nvar selectReferenceDotsDomain = createSelector(selectReferenceDotsByAxis, pickAxisType, combineDotsDomain);\nexport var combineAreasDomain = (areas, axisType) => {\n  var allCoords = onlyAllowNumbers(areas.flatMap(area => [axisType === 'xAxis' ? area.x1 : area.y1, axisType === 'xAxis' ? area.x2 : area.y2]));\n  if (allCoords.length === 0) {\n    return undefined;\n  }\n  return [Math.min(...allCoords), Math.max(...allCoords)];\n};\nvar selectReferenceAreasDomain = createSelector([selectReferenceAreasByAxis, pickAxisType], combineAreasDomain);\nexport var combineLinesDomain = (lines, axisType) => {\n  var allCoords = onlyAllowNumbers(lines.map(line => axisType === 'xAxis' ? line.x : line.y));\n  if (allCoords.length === 0) {\n    return undefined;\n  }\n  return [Math.min(...allCoords), Math.max(...allCoords)];\n};\nvar selectReferenceLinesDomain = createSelector(selectReferenceLinesByAxis, pickAxisType, combineLinesDomain);\nvar selectReferenceElementsDomain = createSelector(selectReferenceDotsDomain, selectReferenceLinesDomain, selectReferenceAreasDomain, (dotsDomain, linesDomain, areasDomain) => {\n  return mergeDomains(dotsDomain, areasDomain, linesDomain);\n});\nexport var selectDomainDefinition = createSelector([selectBaseAxis], getDomainDefinition);\nexport var combineNumericalDomain = (axisSettings, domainDefinition, domainOfStackGroups, allDataWithErrorDomains, referenceElementsDomain) => {\n  var domainFromUserPreference = numericalDomainSpecifiedWithoutRequiringData(domainDefinition, axisSettings.allowDataOverflow);\n  if (domainFromUserPreference != null) {\n    // We're done! No need to compute anything else.\n    return domainFromUserPreference;\n  }\n  return parseNumericalUserDomain(domainDefinition, mergeDomains(domainOfStackGroups, referenceElementsDomain, computeNumericalDomain(allDataWithErrorDomains)), axisSettings.allowDataOverflow);\n};\nvar selectNumericalDomain = createSelector([selectBaseAxis, selectDomainDefinition, selectDomainOfStackGroups, selectAllAppliedNumericalValuesIncludingErrorValues, selectReferenceElementsDomain], combineNumericalDomain);\n\n/**\n * Expand by design maps everything between 0 and 1,\n * there is nothing to compute.\n * See https://d3js.org/d3-shape/stack#stack-offsets\n */\nvar expandDomain = [0, 1];\nexport var combineAxisDomain = (axisSettings, layout, displayedData, allAppliedValues, stackOffsetType, axisType, numericalDomain) => {\n  if (axisSettings == null || displayedData == null || displayedData.length === 0) {\n    return undefined;\n  }\n  var {\n    dataKey,\n    type\n  } = axisSettings;\n  var isCategorical = isCategoricalAxis(layout, axisType);\n  if (isCategorical && dataKey == null) {\n    return range(0, displayedData.length);\n  }\n  if (type === 'category') {\n    return computeDomainOfTypeCategory(allAppliedValues, axisSettings, isCategorical);\n  }\n  if (stackOffsetType === 'expand') {\n    return expandDomain;\n  }\n  return numericalDomain;\n};\nexport var selectAxisDomain = createSelector([selectBaseAxis, selectChartLayout, selectDisplayedData, selectAllAppliedValues, selectStackOffsetType, pickAxisType, selectNumericalDomain], combineAxisDomain);\nexport var combineRealScaleType = (axisConfig, layout, hasBar, chartType, axisType) => {\n  if (axisConfig == null) {\n    return undefined;\n  }\n  var {\n    scale,\n    type\n  } = axisConfig;\n  if (scale === 'auto') {\n    if (layout === 'radial' && axisType === 'radiusAxis') {\n      return 'band';\n    }\n    if (layout === 'radial' && axisType === 'angleAxis') {\n      return 'linear';\n    }\n    if (type === 'category' && chartType && (chartType.indexOf('LineChart') >= 0 || chartType.indexOf('AreaChart') >= 0 || chartType.indexOf('ComposedChart') >= 0 && !hasBar)) {\n      return 'point';\n    }\n    if (type === 'category') {\n      return 'band';\n    }\n    return 'linear';\n  }\n  if (typeof scale === 'string') {\n    var name = \"scale\".concat(upperFirst(scale));\n    return name in d3Scales ? name : 'point';\n  }\n  return undefined;\n};\nexport var selectRealScaleType = createSelector([selectBaseAxis, selectChartLayout, selectHasBar, selectChartName, pickAxisType], combineRealScaleType);\nfunction getD3ScaleFromType(realScaleType) {\n  if (realScaleType == null) {\n    return undefined;\n  }\n  if (realScaleType in d3Scales) {\n    // @ts-expect-error we should do better type verification here\n    return d3Scales[realScaleType]();\n  }\n  var name = \"scale\".concat(upperFirst(realScaleType));\n  if (name in d3Scales) {\n    // @ts-expect-error we should do better type verification here\n    return d3Scales[name]();\n  }\n  return undefined;\n}\nexport function combineScaleFunction(axis, realScaleType, axisDomain, axisRange) {\n  if (axisDomain == null || axisRange == null) {\n    return undefined;\n  }\n  if (typeof axis.scale === 'function') {\n    // @ts-expect-error we're going to assume here that if axis.scale is a function then it is a d3Scale function\n    return axis.scale.copy().domain(axisDomain).range(axisRange);\n  }\n  var d3ScaleFunction = getD3ScaleFromType(realScaleType);\n  if (d3ScaleFunction == null) {\n    return undefined;\n  }\n  var scale = d3ScaleFunction.domain(axisDomain).range(axisRange);\n  // I don't like this function because it mutates the scale. We should come up with a way to compute the domain up front.\n  checkDomainOfScale(scale);\n  return scale;\n}\nexport var combineNiceTicks = (axisDomain, axisSettings, realScaleType) => {\n  var domainDefinition = getDomainDefinition(axisSettings);\n  if (realScaleType !== 'auto' && realScaleType !== 'linear') {\n    return undefined;\n  }\n  if (axisSettings != null && axisSettings.tickCount && Array.isArray(domainDefinition) && (domainDefinition[0] === 'auto' || domainDefinition[1] === 'auto') && isWellFormedNumberDomain(axisDomain)) {\n    return getNiceTickValues(axisDomain, axisSettings.tickCount, axisSettings.allowDecimals);\n  }\n  if (axisSettings != null && axisSettings.tickCount && axisSettings.type === 'number' && isWellFormedNumberDomain(axisDomain)) {\n    return getTickValuesFixedDomain(axisDomain, axisSettings.tickCount, axisSettings.allowDecimals);\n  }\n  return undefined;\n};\nexport var selectNiceTicks = createSelector([selectAxisDomain, selectAxisSettings, selectRealScaleType], combineNiceTicks);\nexport var combineAxisDomainWithNiceTicks = (axisSettings, domain, niceTicks, axisType) => {\n  if (\n  /*\n   * Angle axis for some reason uses nice ticks when rendering axis tick labels,\n   * but doesn't use nice ticks for extending domain like all the other axes do.\n   * Not really sure why? Is there a good reason,\n   * or is it just because someone added support for nice ticks to the other axes and forgot this one?\n   */\n  axisType !== 'angleAxis' && (axisSettings === null || axisSettings === void 0 ? void 0 : axisSettings.type) === 'number' && isWellFormedNumberDomain(domain) && Array.isArray(niceTicks) && niceTicks.length > 0) {\n    var minFromDomain = domain[0];\n    var minFromTicks = niceTicks[0];\n    var maxFromDomain = domain[1];\n    var maxFromTicks = niceTicks[niceTicks.length - 1];\n    return [Math.min(minFromDomain, minFromTicks), Math.max(maxFromDomain, maxFromTicks)];\n  }\n  return domain;\n};\nexport var selectAxisDomainIncludingNiceTicks = createSelector([selectBaseAxis, selectAxisDomain, selectNiceTicks, pickAxisType], combineAxisDomainWithNiceTicks);\n\n/**\n * Returns the smallest gap, between two numbers in the data, as a ratio of the whole range (max - min).\n * Ignores domain provided by user and only considers domain from data.\n *\n * The result is a number between 0 and 1.\n */\nexport var selectSmallestDistanceBetweenValues = createSelector(selectAllAppliedValues, selectBaseAxis, (allDataSquished, axisSettings) => {\n  if (!axisSettings || axisSettings.type !== 'number') {\n    return undefined;\n  }\n  var smallestDistanceBetweenValues = Infinity;\n  var sortedValues = Array.from(onlyAllowNumbers(allDataSquished.map(d => d.value))).sort((a, b) => a - b);\n  if (sortedValues.length < 2) {\n    return Infinity;\n  }\n  var diff = sortedValues[sortedValues.length - 1] - sortedValues[0];\n  if (diff === 0) {\n    return Infinity;\n  }\n  // Only do n - 1 distance calculations because there's only n - 1 distances between n values.\n  for (var i = 0; i < sortedValues.length - 1; i++) {\n    var distance = sortedValues[i + 1] - sortedValues[i];\n    smallestDistanceBetweenValues = Math.min(smallestDistanceBetweenValues, distance);\n  }\n  return smallestDistanceBetweenValues / diff;\n});\nvar selectCalculatedPadding = createSelector(selectSmallestDistanceBetweenValues, selectChartLayout, selectBarCategoryGap, selectChartOffsetInternal, (_1, _2, _3, padding) => padding, (smallestDistanceInPercent, layout, barCategoryGap, offset, padding) => {\n  if (!isWellBehavedNumber(smallestDistanceInPercent)) {\n    return 0;\n  }\n  var rangeWidth = layout === 'vertical' ? offset.height : offset.width;\n  if (padding === 'gap') {\n    return smallestDistanceInPercent * rangeWidth / 2;\n  }\n  if (padding === 'no-gap') {\n    var gap = getPercentValue(barCategoryGap, smallestDistanceInPercent * rangeWidth);\n    var halfBand = smallestDistanceInPercent * rangeWidth / 2;\n    return halfBand - gap - (halfBand - gap) / rangeWidth * gap;\n  }\n  return 0;\n});\nexport var selectCalculatedXAxisPadding = (state, axisId) => {\n  var xAxisSettings = selectXAxisSettings(state, axisId);\n  if (xAxisSettings == null || typeof xAxisSettings.padding !== 'string') {\n    return 0;\n  }\n  return selectCalculatedPadding(state, 'xAxis', axisId, xAxisSettings.padding);\n};\nexport var selectCalculatedYAxisPadding = (state, axisId) => {\n  var yAxisSettings = selectYAxisSettings(state, axisId);\n  if (yAxisSettings == null || typeof yAxisSettings.padding !== 'string') {\n    return 0;\n  }\n  return selectCalculatedPadding(state, 'yAxis', axisId, yAxisSettings.padding);\n};\nvar selectXAxisPadding = createSelector(selectXAxisSettings, selectCalculatedXAxisPadding, (xAxisSettings, calculated) => {\n  var _padding$left, _padding$right;\n  if (xAxisSettings == null) {\n    return {\n      left: 0,\n      right: 0\n    };\n  }\n  var {\n    padding\n  } = xAxisSettings;\n  if (typeof padding === 'string') {\n    return {\n      left: calculated,\n      right: calculated\n    };\n  }\n  return {\n    left: ((_padding$left = padding.left) !== null && _padding$left !== void 0 ? _padding$left : 0) + calculated,\n    right: ((_padding$right = padding.right) !== null && _padding$right !== void 0 ? _padding$right : 0) + calculated\n  };\n});\nvar selectYAxisPadding = createSelector(selectYAxisSettings, selectCalculatedYAxisPadding, (yAxisSettings, calculated) => {\n  var _padding$top, _padding$bottom;\n  if (yAxisSettings == null) {\n    return {\n      top: 0,\n      bottom: 0\n    };\n  }\n  var {\n    padding\n  } = yAxisSettings;\n  if (typeof padding === 'string') {\n    return {\n      top: calculated,\n      bottom: calculated\n    };\n  }\n  return {\n    top: ((_padding$top = padding.top) !== null && _padding$top !== void 0 ? _padding$top : 0) + calculated,\n    bottom: ((_padding$bottom = padding.bottom) !== null && _padding$bottom !== void 0 ? _padding$bottom : 0) + calculated\n  };\n});\nexport var combineXAxisRange = createSelector([selectChartOffsetInternal, selectXAxisPadding, selectBrushDimensions, selectBrushSettings, (_state, _axisId, isPanorama) => isPanorama], (offset, padding, brushDimensions, _ref4, isPanorama) => {\n  var {\n    padding: brushPadding\n  } = _ref4;\n  if (isPanorama) {\n    return [brushPadding.left, brushDimensions.width - brushPadding.right];\n  }\n  return [offset.left + padding.left, offset.left + offset.width - padding.right];\n});\nexport var combineYAxisRange = createSelector([selectChartOffsetInternal, selectChartLayout, selectYAxisPadding, selectBrushDimensions, selectBrushSettings, (_state, _axisId, isPanorama) => isPanorama], (offset, layout, padding, brushDimensions, _ref5, isPanorama) => {\n  var {\n    padding: brushPadding\n  } = _ref5;\n  if (isPanorama) {\n    return [brushDimensions.height - brushPadding.bottom, brushPadding.top];\n  }\n  if (layout === 'horizontal') {\n    return [offset.top + offset.height - padding.bottom, offset.top + padding.top];\n  }\n  return [offset.top + padding.top, offset.top + offset.height - padding.bottom];\n});\nexport var selectAxisRange = (state, axisType, axisId, isPanorama) => {\n  var _selectZAxisSettings;\n  switch (axisType) {\n    case 'xAxis':\n      return combineXAxisRange(state, axisId, isPanorama);\n    case 'yAxis':\n      return combineYAxisRange(state, axisId, isPanorama);\n    case 'zAxis':\n      return (_selectZAxisSettings = selectZAxisSettings(state, axisId)) === null || _selectZAxisSettings === void 0 ? void 0 : _selectZAxisSettings.range;\n    case 'angleAxis':\n      return selectAngleAxisRange(state);\n    case 'radiusAxis':\n      return selectRadiusAxisRange(state, axisId);\n    default:\n      return undefined;\n  }\n};\nexport var selectAxisRangeWithReverse = createSelector([selectBaseAxis, selectAxisRange], combineAxisRangeWithReverse);\nexport var selectAxisScale = createSelector([selectBaseAxis, selectRealScaleType, selectAxisDomainIncludingNiceTicks, selectAxisRangeWithReverse], combineScaleFunction);\nexport var selectErrorBarsSettings = createSelector(selectCartesianItemsSettings, pickAxisType, (items, axisType) => {\n  return items.flatMap(item => {\n    var _item$errorBars2;\n    return (_item$errorBars2 = item.errorBars) !== null && _item$errorBars2 !== void 0 ? _item$errorBars2 : [];\n  }).filter(e => {\n    return isErrorBarRelevantForAxisType(axisType, e);\n  });\n});\nfunction compareIds(a, b) {\n  if (a.id < b.id) {\n    return -1;\n  }\n  if (a.id > b.id) {\n    return 1;\n  }\n  return 0;\n}\nvar pickAxisOrientation = (_state, orientation) => orientation;\nvar pickMirror = (_state, _orientation, mirror) => mirror;\nvar selectAllXAxesWithOffsetType = createSelector(selectAllXAxes, pickAxisOrientation, pickMirror, (allAxes, orientation, mirror) => allAxes.filter(axis => axis.orientation === orientation).filter(axis => axis.mirror === mirror).sort(compareIds));\nvar selectAllYAxesWithOffsetType = createSelector(selectAllYAxes, pickAxisOrientation, pickMirror, (allAxes, orientation, mirror) => allAxes.filter(axis => axis.orientation === orientation).filter(axis => axis.mirror === mirror).sort(compareIds));\nvar getXAxisSize = (offset, axisSettings) => {\n  return {\n    width: offset.width,\n    height: axisSettings.height\n  };\n};\nvar getYAxisSize = (offset, axisSettings) => {\n  var width = typeof axisSettings.width === 'number' ? axisSettings.width : DEFAULT_Y_AXIS_WIDTH;\n  return {\n    width,\n    height: offset.height\n  };\n};\nexport var selectXAxisSize = createSelector(selectChartOffsetInternal, selectXAxisSettings, getXAxisSize);\nvar combineXAxisPositionStartingPoint = (offset, orientation, chartHeight) => {\n  switch (orientation) {\n    case 'top':\n      return offset.top;\n    case 'bottom':\n      return chartHeight - offset.bottom;\n    default:\n      return 0;\n  }\n};\nvar combineYAxisPositionStartingPoint = (offset, orientation, chartWidth) => {\n  switch (orientation) {\n    case 'left':\n      return offset.left;\n    case 'right':\n      return chartWidth - offset.right;\n    default:\n      return 0;\n  }\n};\nexport var selectAllXAxesOffsetSteps = createSelector(selectChartHeight, selectChartOffsetInternal, selectAllXAxesWithOffsetType, pickAxisOrientation, pickMirror, (chartHeight, offset, allAxesWithSameOffsetType, orientation, mirror) => {\n  var steps = {};\n  var position;\n  allAxesWithSameOffsetType.forEach(axis => {\n    var axisSize = getXAxisSize(offset, axis);\n    if (position == null) {\n      position = combineXAxisPositionStartingPoint(offset, orientation, chartHeight);\n    }\n    var needSpace = orientation === 'top' && !mirror || orientation === 'bottom' && mirror;\n    steps[axis.id] = position - Number(needSpace) * axisSize.height;\n    position += (needSpace ? -1 : 1) * axisSize.height;\n  });\n  return steps;\n});\nexport var selectAllYAxesOffsetSteps = createSelector(selectChartWidth, selectChartOffsetInternal, selectAllYAxesWithOffsetType, pickAxisOrientation, pickMirror, (chartWidth, offset, allAxesWithSameOffsetType, orientation, mirror) => {\n  var steps = {};\n  var position;\n  allAxesWithSameOffsetType.forEach(axis => {\n    var axisSize = getYAxisSize(offset, axis);\n    if (position == null) {\n      position = combineYAxisPositionStartingPoint(offset, orientation, chartWidth);\n    }\n    var needSpace = orientation === 'left' && !mirror || orientation === 'right' && mirror;\n    steps[axis.id] = position - Number(needSpace) * axisSize.width;\n    position += (needSpace ? -1 : 1) * axisSize.width;\n  });\n  return steps;\n});\nexport var selectXAxisPosition = (state, axisId) => {\n  var offset = selectChartOffsetInternal(state);\n  var axisSettings = selectXAxisSettings(state, axisId);\n  if (axisSettings == null) {\n    return undefined;\n  }\n  var allSteps = selectAllXAxesOffsetSteps(state, axisSettings.orientation, axisSettings.mirror);\n  var stepOfThisAxis = allSteps[axisId];\n  if (stepOfThisAxis == null) {\n    return {\n      x: offset.left,\n      y: 0\n    };\n  }\n  return {\n    x: offset.left,\n    y: stepOfThisAxis\n  };\n};\nexport var selectYAxisPosition = (state, axisId) => {\n  var offset = selectChartOffsetInternal(state);\n  var axisSettings = selectYAxisSettings(state, axisId);\n  if (axisSettings == null) {\n    return undefined;\n  }\n  var allSteps = selectAllYAxesOffsetSteps(state, axisSettings.orientation, axisSettings.mirror);\n  var stepOfThisAxis = allSteps[axisId];\n  if (stepOfThisAxis == null) {\n    return {\n      x: 0,\n      y: offset.top\n    };\n  }\n  return {\n    x: stepOfThisAxis,\n    y: offset.top\n  };\n};\nexport var selectYAxisSize = createSelector(selectChartOffsetInternal, selectYAxisSettings, (offset, axisSettings) => {\n  var width = typeof axisSettings.width === 'number' ? axisSettings.width : DEFAULT_Y_AXIS_WIDTH;\n  return {\n    width,\n    height: offset.height\n  };\n});\nexport var selectCartesianAxisSize = (state, axisType, axisId) => {\n  switch (axisType) {\n    case 'xAxis':\n      {\n        return selectXAxisSize(state, axisId).width;\n      }\n    case 'yAxis':\n      {\n        return selectYAxisSize(state, axisId).height;\n      }\n    default:\n      {\n        return undefined;\n      }\n  }\n};\nexport var combineDuplicateDomain = (chartLayout, appliedValues, axis, axisType) => {\n  if (axis == null) {\n    return undefined;\n  }\n  var {\n    allowDuplicatedCategory,\n    type,\n    dataKey\n  } = axis;\n  var isCategorical = isCategoricalAxis(chartLayout, axisType);\n  var allData = appliedValues.map(av => av.value);\n  if (dataKey && isCategorical && type === 'category' && allowDuplicatedCategory && hasDuplicate(allData)) {\n    return allData;\n  }\n  return undefined;\n};\nexport var selectDuplicateDomain = createSelector([selectChartLayout, selectAllAppliedValues, selectBaseAxis, pickAxisType], combineDuplicateDomain);\nexport var combineCategoricalDomain = (layout, appliedValues, axis, axisType) => {\n  if (axis == null || axis.dataKey == null) {\n    return undefined;\n  }\n  var {\n    type,\n    scale\n  } = axis;\n  var isCategorical = isCategoricalAxis(layout, axisType);\n  if (isCategorical && (type === 'number' || scale !== 'auto')) {\n    return appliedValues.map(d => d.value);\n  }\n  return undefined;\n};\nexport var selectCategoricalDomain = createSelector([selectChartLayout, selectAllAppliedValues, selectAxisSettings, pickAxisType], combineCategoricalDomain);\nexport var selectAxisPropsNeededForCartesianGridTicksGenerator = createSelector([selectChartLayout, selectCartesianAxisSettings, selectRealScaleType, selectAxisScale, selectDuplicateDomain, selectCategoricalDomain, selectAxisRange, selectNiceTicks, pickAxisType], (layout, axis, realScaleType, scale, duplicateDomain, categoricalDomain, axisRange, niceTicks, axisType) => {\n  if (axis == null) {\n    return null;\n  }\n  var isCategorical = isCategoricalAxis(layout, axisType);\n  return {\n    angle: axis.angle,\n    interval: axis.interval,\n    minTickGap: axis.minTickGap,\n    orientation: axis.orientation,\n    tick: axis.tick,\n    tickCount: axis.tickCount,\n    tickFormatter: axis.tickFormatter,\n    ticks: axis.ticks,\n    type: axis.type,\n    unit: axis.unit,\n    axisType,\n    categoricalDomain,\n    duplicateDomain,\n    isCategorical,\n    niceTicks,\n    range: axisRange,\n    realScaleType,\n    scale\n  };\n});\nexport var combineAxisTicks = (layout, axis, realScaleType, scale, niceTicks, axisRange, duplicateDomain, categoricalDomain, axisType) => {\n  if (axis == null || scale == null) {\n    return undefined;\n  }\n  var isCategorical = isCategoricalAxis(layout, axisType);\n  var {\n    type,\n    ticks,\n    tickCount\n  } = axis;\n\n  // This is testing for `scaleBand` but for band axis the type is reported as `band` so this looks like a dead code with a workaround elsewhere?\n  var offsetForBand = realScaleType === 'scaleBand' && typeof scale.bandwidth === 'function' ? scale.bandwidth() / 2 : 2;\n  var offset = type === 'category' && scale.bandwidth ? scale.bandwidth() / offsetForBand : 0;\n  offset = axisType === 'angleAxis' && axisRange != null && axisRange.length >= 2 ? mathSign(axisRange[0] - axisRange[1]) * 2 * offset : offset;\n\n  // The ticks set by user should only affect the ticks adjacent to axis line\n  var ticksOrNiceTicks = ticks || niceTicks;\n  if (ticksOrNiceTicks) {\n    var result = ticksOrNiceTicks.map((entry, index) => {\n      var scaleContent = duplicateDomain ? duplicateDomain.indexOf(entry) : entry;\n      return {\n        index,\n        // If the scaleContent is not a number, the coordinate will be NaN.\n        // That could be the case for example with a PointScale and a string as domain.\n        coordinate: scale(scaleContent) + offset,\n        value: entry,\n        offset\n      };\n    });\n    return result.filter(row => !isNan(row.coordinate));\n  }\n\n  // When axis is a categorical axis, but the type of axis is number or the scale of axis is not \"auto\"\n  if (isCategorical && categoricalDomain) {\n    return categoricalDomain.map((entry, index) => ({\n      coordinate: scale(entry) + offset,\n      value: entry,\n      index,\n      offset\n    }));\n  }\n  if (scale.ticks) {\n    return scale.ticks(tickCount)\n    // @ts-expect-error why does the offset go here? The type does not require it\n    .map(entry => ({\n      coordinate: scale(entry) + offset,\n      value: entry,\n      offset\n    }));\n  }\n\n  // When axis has duplicated text, serial numbers are used to generate scale\n  return scale.domain().map((entry, index) => ({\n    coordinate: scale(entry) + offset,\n    value: duplicateDomain ? duplicateDomain[entry] : entry,\n    index,\n    offset\n  }));\n};\nexport var selectTicksOfAxis = createSelector([selectChartLayout, selectAxisSettings, selectRealScaleType, selectAxisScale, selectNiceTicks, selectAxisRange, selectDuplicateDomain, selectCategoricalDomain, pickAxisType], combineAxisTicks);\nexport var combineGraphicalItemTicks = (layout, axis, scale, axisRange, duplicateDomain, categoricalDomain, axisType) => {\n  if (axis == null || scale == null || axisRange == null || axisRange[0] === axisRange[1]) {\n    return undefined;\n  }\n  var isCategorical = isCategoricalAxis(layout, axisType);\n  var {\n    tickCount\n  } = axis;\n  var offset = 0;\n  offset = axisType === 'angleAxis' && (axisRange === null || axisRange === void 0 ? void 0 : axisRange.length) >= 2 ? mathSign(axisRange[0] - axisRange[1]) * 2 * offset : offset;\n\n  // When axis is a categorical axis, but the type of axis is number or the scale of axis is not \"auto\"\n  if (isCategorical && categoricalDomain) {\n    return categoricalDomain.map((entry, index) => ({\n      coordinate: scale(entry) + offset,\n      value: entry,\n      index,\n      offset\n    }));\n  }\n  if (scale.ticks) {\n    return scale.ticks(tickCount)\n    // @ts-expect-error why does the offset go here? The type does not require it\n    .map(entry => ({\n      coordinate: scale(entry) + offset,\n      value: entry,\n      offset\n    }));\n  }\n\n  // When axis has duplicated text, serial numbers are used to generate scale\n  return scale.domain().map((entry, index) => ({\n    coordinate: scale(entry) + offset,\n    value: duplicateDomain ? duplicateDomain[entry] : entry,\n    index,\n    offset\n  }));\n};\nexport var selectTicksOfGraphicalItem = createSelector([selectChartLayout, selectAxisSettings, selectAxisScale, selectAxisRange, selectDuplicateDomain, selectCategoricalDomain, pickAxisType], combineGraphicalItemTicks);\nexport var selectAxisWithScale = createSelector(selectBaseAxis, selectAxisScale, (axis, scale) => {\n  if (axis == null || scale == null) {\n    return undefined;\n  }\n  return _objectSpread(_objectSpread({}, axis), {}, {\n    scale\n  });\n});\nvar selectZAxisScale = createSelector([selectBaseAxis, selectRealScaleType, selectAxisDomain, selectAxisRangeWithReverse], combineScaleFunction);\nexport var selectZAxisWithScale = createSelector((state, _axisType, axisId) => selectZAxisSettings(state, axisId), selectZAxisScale, (axis, scale) => {\n  if (axis == null || scale == null) {\n    return undefined;\n  }\n  return _objectSpread(_objectSpread({}, axis), {}, {\n    scale\n  });\n});\n\n/**\n * We are also going to need to implement polar chart directions if we want to support keyboard controls for those.\n */\n\nexport var selectChartDirection = createSelector([selectChartLayout, selectAllXAxes, selectAllYAxes], (layout, allXAxes, allYAxes) => {\n  switch (layout) {\n    case 'horizontal':\n      {\n        return allXAxes.some(axis => axis.reversed) ? 'right-to-left' : 'left-to-right';\n      }\n    case 'vertical':\n      {\n        return allYAxes.some(axis => axis.reversed) ? 'bottom-to-top' : 'top-to-bottom';\n      }\n    // TODO: make this better. For now, right arrow triggers \"forward\", left arrow \"back\"\n    // however, the tooltip moves an unintuitive direction because of how the indices are rendered\n    case 'centric':\n    case 'radial':\n      {\n        return 'left-to-right';\n      }\n    default:\n      {\n        return undefined;\n      }\n  }\n});"], "mappings": "AAAA,SAASA,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAIC,CAAC,GAAGC,MAAM,CAACC,IAAI,CAACJ,CAAC,CAAC;EAAE,IAAIG,MAAM,CAACE,qBAAqB,EAAE;IAAE,IAAIC,CAAC,GAAGH,MAAM,CAACE,qBAAqB,CAACL,CAAC,CAAC;IAAEC,CAAC,KAAKK,CAAC,GAAGA,CAAC,CAACC,MAAM,CAAC,UAAUN,CAAC,EAAE;MAAE,OAAOE,MAAM,CAACK,wBAAwB,CAACR,CAAC,EAAEC,CAAC,CAAC,CAACQ,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEP,CAAC,CAACQ,IAAI,CAACC,KAAK,CAACT,CAAC,EAAEI,CAAC,CAAC;EAAE;EAAE,OAAOJ,CAAC;AAAE;AAC9P,SAASU,aAAaA,CAACZ,CAAC,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,SAAS,CAACC,MAAM,EAAEb,CAAC,EAAE,EAAE;IAAE,IAAIC,CAAC,GAAG,IAAI,IAAIW,SAAS,CAACZ,CAAC,CAAC,GAAGY,SAAS,CAACZ,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGF,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACa,OAAO,CAAC,UAAUd,CAAC,EAAE;MAAEe,eAAe,CAAChB,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGE,MAAM,CAACc,yBAAyB,GAAGd,MAAM,CAACe,gBAAgB,CAAClB,CAAC,EAAEG,MAAM,CAACc,yBAAyB,CAACf,CAAC,CAAC,CAAC,GAAGH,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,CAAC,CAACa,OAAO,CAAC,UAAUd,CAAC,EAAE;MAAEE,MAAM,CAACgB,cAAc,CAACnB,CAAC,EAAEC,CAAC,EAAEE,MAAM,CAACK,wBAAwB,CAACN,CAAC,EAAED,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AACtb,SAASgB,eAAeA,CAAChB,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAAE,OAAO,CAACD,CAAC,GAAGmB,cAAc,CAACnB,CAAC,CAAC,KAAKD,CAAC,GAAGG,MAAM,CAACgB,cAAc,CAACnB,CAAC,EAAEC,CAAC,EAAE;IAAEoB,KAAK,EAAEnB,CAAC;IAAEO,UAAU,EAAE,CAAC,CAAC;IAAEa,YAAY,EAAE,CAAC,CAAC;IAAEC,QAAQ,EAAE,CAAC;EAAE,CAAC,CAAC,GAAGvB,CAAC,CAACC,CAAC,CAAC,GAAGC,CAAC,EAAEF,CAAC;AAAE;AACnL,SAASoB,cAAcA,CAAClB,CAAC,EAAE;EAAE,IAAIsB,CAAC,GAAGC,YAAY,CAACvB,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAI,OAAOsB,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC1G,SAASC,YAAYA,CAACvB,CAAC,EAAED,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAI,OAAOC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIF,CAAC,GAAGE,CAAC,CAACwB,MAAM,CAACC,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAK3B,CAAC,EAAE;IAAE,IAAIwB,CAAC,GAAGxB,CAAC,CAAC4B,IAAI,CAAC1B,CAAC,EAAED,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAI,OAAOuB,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAIK,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAK5B,CAAC,GAAG6B,MAAM,GAAGC,MAAM,EAAE7B,CAAC,CAAC;AAAE;AACvT,SAAS8B,cAAc,QAAQ,UAAU;AACzC,OAAOC,KAAK,MAAM,yBAAyB;AAC3C,OAAO,KAAKC,QAAQ,MAAM,yBAAyB;AACnD,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,kBAAkB,EAAEC,sBAAsB,EAAEC,cAAc,EAAEC,iBAAiB,EAAEC,iBAAiB,QAAQ,uBAAuB;AACxI,SAASC,0BAA0B,EAAEC,yCAAyC,QAAQ,iBAAiB;AACvG,SAASC,wBAAwB,EAAEC,4CAA4C,EAAEC,wBAAwB,QAAQ,oCAAoC;AACrJ,SAASC,eAAe,EAAEC,YAAY,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,sBAAsB;AACvH,SAASC,mBAAmB,QAAQ,gCAAgC;AACpE,SAASC,iBAAiB,EAAEC,wBAAwB,QAAQ,kBAAkB;AAC9E,SAASC,iBAAiB,EAAEC,gBAAgB,QAAQ,sBAAsB;AAC1E,SAASC,cAAc,EAAEC,cAAc,QAAQ,iBAAiB;AAChE,SAASC,yBAAyB,QAAQ,6BAA6B;AACvE,SAASC,qBAAqB,EAAEC,mBAAmB,QAAQ,kBAAkB;AAC7E,SAASC,oBAAoB,EAAEC,eAAe,EAAEC,qBAAqB,QAAQ,sBAAsB;AACnG,SAASC,eAAe,EAAEC,oBAAoB,EAAEC,gBAAgB,EAAEC,qBAAqB,QAAQ,sBAAsB;AACrH,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,2BAA2B,QAAQ,yCAAyC;AACrF,SAASC,oBAAoB,QAAQ,sBAAsB;AAC3D,IAAIC,oBAAoB,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC;;AAEtC;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,aAAa,GAAG;EACzBC,iBAAiB,EAAE,KAAK;EACxBC,aAAa,EAAE,IAAI;EACnBC,uBAAuB,EAAE,IAAI;EAC7BC,KAAK,EAAE,CAAC;EACRC,OAAO,EAAEC,SAAS;EAClBC,MAAM,EAAED,SAAS;EACjBE,MAAM,EAAE,EAAE;EACVC,IAAI,EAAE,IAAI;EACVC,EAAE,EAAE,CAAC;EACLC,aAAa,EAAE,KAAK;EACpBC,QAAQ,EAAE,aAAa;EACvBC,UAAU,EAAE,CAAC;EACbC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAET,SAAS;EACfU,WAAW,EAAE,QAAQ;EACrBC,OAAO,EAAE;IACPC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE,KAAK;EACfC,KAAK,EAAE,MAAM;EACbC,IAAI,EAAE,IAAI;EACVC,SAAS,EAAE,CAAC;EACZC,aAAa,EAAElB,SAAS;EACxBmB,KAAK,EAAEnB,SAAS;EAChBoB,IAAI,EAAE,UAAU;EAChBC,IAAI,EAAErB;AACR,CAAC;AACD,OAAO,IAAIsB,mBAAmB,GAAGA,CAACC,KAAK,EAAEC,MAAM,KAAK;EAClD,IAAIC,IAAI,GAAGF,KAAK,CAACG,aAAa,CAACC,KAAK,CAACH,MAAM,CAAC;EAC5C,IAAIC,IAAI,IAAI,IAAI,EAAE;IAChB,OAAO/B,aAAa;EACtB;EACA,OAAO+B,IAAI;AACb,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIG,aAAa,GAAG;EACzBjC,iBAAiB,EAAE,KAAK;EACxBC,aAAa,EAAE,IAAI;EACnBC,uBAAuB,EAAE,IAAI;EAC7BC,KAAK,EAAE,CAAC;EACRC,OAAO,EAAEC,SAAS;EAClBC,MAAM,EAAER,oBAAoB;EAC5BU,IAAI,EAAE,IAAI;EACVC,EAAE,EAAE,CAAC;EACLC,aAAa,EAAE,KAAK;EACpBC,QAAQ,EAAE,aAAa;EACvBC,UAAU,EAAE,CAAC;EACbC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAET,SAAS;EACfU,WAAW,EAAE,MAAM;EACnBC,OAAO,EAAE;IACPkB,GAAG,EAAE,CAAC;IACNC,MAAM,EAAE;EACV,CAAC;EACDhB,QAAQ,EAAE,KAAK;EACfC,KAAK,EAAE,MAAM;EACbC,IAAI,EAAE,IAAI;EACVC,SAAS,EAAE,CAAC;EACZC,aAAa,EAAElB,SAAS;EACxBmB,KAAK,EAAEnB,SAAS;EAChBoB,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAErB,SAAS;EACf+B,KAAK,EAAEvC;AACT,CAAC;AACD,OAAO,IAAIwC,mBAAmB,GAAGA,CAACT,KAAK,EAAEC,MAAM,KAAK;EAClD,IAAIC,IAAI,GAAGF,KAAK,CAACG,aAAa,CAACO,KAAK,CAACT,MAAM,CAAC;EAC5C,IAAIC,IAAI,IAAI,IAAI,EAAE;IAChB,OAAOG,aAAa;EACtB;EACA,OAAOH,IAAI;AACb,CAAC;AACD,OAAO,IAAIS,aAAa,GAAG;EACzBjC,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC;EACnBI,aAAa,EAAE,KAAK;EACpBS,QAAQ,EAAE,KAAK;EACfnB,iBAAiB,EAAE,KAAK;EACxBE,uBAAuB,EAAE,KAAK;EAC9BE,OAAO,EAAEC,SAAS;EAClBI,EAAE,EAAE,CAAC;EACLK,IAAI,EAAE,EAAE;EACRzD,KAAK,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;EACf+D,KAAK,EAAE,MAAM;EACbK,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE;AACR,CAAC;AACD,OAAO,IAAIc,mBAAmB,GAAGA,CAACZ,KAAK,EAAEC,MAAM,KAAK;EAClD,IAAIC,IAAI,GAAGF,KAAK,CAACG,aAAa,CAACU,KAAK,CAACZ,MAAM,CAAC;EAC5C,IAAIC,IAAI,IAAI,IAAI,EAAE;IAChB,OAAOS,aAAa;EACtB;EACA,OAAOT,IAAI;AACb,CAAC;AACD,OAAO,IAAIY,cAAc,GAAGA,CAACd,KAAK,EAAEe,QAAQ,EAAEd,MAAM,KAAK;EACvD,QAAQc,QAAQ;IACd,KAAK,OAAO;MACV;QACE,OAAOhB,mBAAmB,CAACC,KAAK,EAAEC,MAAM,CAAC;MAC3C;IACF,KAAK,OAAO;MACV;QACE,OAAOQ,mBAAmB,CAACT,KAAK,EAAEC,MAAM,CAAC;MAC3C;IACF,KAAK,OAAO;MACV;QACE,OAAOW,mBAAmB,CAACZ,KAAK,EAAEC,MAAM,CAAC;MAC3C;IACF,KAAK,WAAW;MACd;QACE,OAAOvC,eAAe,CAACsC,KAAK,EAAEC,MAAM,CAAC;MACvC;IACF,KAAK,YAAY;MACf;QACE,OAAOrC,gBAAgB,CAACoC,KAAK,EAAEC,MAAM,CAAC;MACxC;IACF;MACE,MAAM,IAAIe,KAAK,CAAC,wBAAwB,CAACC,MAAM,CAACF,QAAQ,CAAC,CAAC;EAC9D;AACF,CAAC;AACD,IAAIG,2BAA2B,GAAGA,CAAClB,KAAK,EAAEe,QAAQ,EAAEd,MAAM,KAAK;EAC7D,QAAQc,QAAQ;IACd,KAAK,OAAO;MACV;QACE,OAAOhB,mBAAmB,CAACC,KAAK,EAAEC,MAAM,CAAC;MAC3C;IACF,KAAK,OAAO;MACV;QACE,OAAOQ,mBAAmB,CAACT,KAAK,EAAEC,MAAM,CAAC;MAC3C;IACF;MACE,MAAM,IAAIe,KAAK,CAAC,wBAAwB,CAACC,MAAM,CAACF,QAAQ,CAAC,CAAC;EAC9D;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAII,kBAAkB,GAAGA,CAACnB,KAAK,EAAEe,QAAQ,EAAEd,MAAM,KAAK;EAC3D,QAAQc,QAAQ;IACd,KAAK,OAAO;MACV;QACE,OAAOhB,mBAAmB,CAACC,KAAK,EAAEC,MAAM,CAAC;MAC3C;IACF,KAAK,OAAO;MACV;QACE,OAAOQ,mBAAmB,CAACT,KAAK,EAAEC,MAAM,CAAC;MAC3C;IACF,KAAK,WAAW;MACd;QACE,OAAOvC,eAAe,CAACsC,KAAK,EAAEC,MAAM,CAAC;MACvC;IACF,KAAK,YAAY;MACf;QACE,OAAOrC,gBAAgB,CAACoC,KAAK,EAAEC,MAAM,CAAC;MACxC;IACF;MACE,MAAM,IAAIe,KAAK,CAAC,wBAAwB,CAACC,MAAM,CAACF,QAAQ,CAAC,CAAC;EAC9D;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,IAAIK,YAAY,GAAGpB,KAAK,IAAIA,KAAK,CAACqB,cAAc,CAACC,WAAW,GAAG,CAAC;;AAEvE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,iBAAiBA,CAACR,QAAQ,EAAEd,MAAM,EAAE;EAClD,OAAOuB,IAAI,IAAI;IACb,QAAQT,QAAQ;MACd,KAAK,OAAO;QACV;QACA,OAAO,SAAS,IAAIS,IAAI,IAAIA,IAAI,CAACC,OAAO,KAAKxB,MAAM;MACrD,KAAK,OAAO;QACV,OAAO,SAAS,IAAIuB,IAAI,IAAIA,IAAI,CAACE,OAAO,KAAKzB,MAAM;MACrD,KAAK,OAAO;QACV,OAAO,SAAS,IAAIuB,IAAI,IAAIA,IAAI,CAACG,OAAO,KAAK1B,MAAM;MACrD,KAAK,WAAW;QACd,OAAO,aAAa,IAAIuB,IAAI,IAAIA,IAAI,CAACI,WAAW,KAAK3B,MAAM;MAC7D,KAAK,YAAY;QACf,OAAO,cAAc,IAAIuB,IAAI,IAAIA,IAAI,CAACK,YAAY,KAAK5B,MAAM;MAC/D;QACE,OAAO,KAAK;IAChB;EACF,CAAC;AACH;AACA,OAAO,IAAI6B,8BAA8B,GAAG9B,KAAK,IAAIA,KAAK,CAACqB,cAAc,CAACU,cAAc;AACxF,IAAIC,mBAAmB,GAAGxG,cAAc,CAAC,CAACsC,YAAY,EAAEC,UAAU,CAAC,EAAEwD,iBAAiB,CAAC;AACvF,OAAO,IAAIU,6BAA6B,GAAGA,CAACZ,cAAc,EAAEa,YAAY,EAAEC,aAAa,KAAKd,cAAc,CAACtH,MAAM,CAACoI,aAAa,CAAC,CAACpI,MAAM,CAACyH,IAAI,IAAI;EAC9I,IAAI,CAACU,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACpD,aAAa,MAAM,IAAI,EAAE;IACrG,OAAO,IAAI;EACb;EACA,OAAO,CAAC0C,IAAI,CAAC5C,IAAI;AACnB,CAAC,CAAC;AACF,OAAO,IAAIwD,4BAA4B,GAAG5G,cAAc,CAAC,CAACsG,8BAA8B,EAAEhB,cAAc,EAAEkB,mBAAmB,CAAC,EAAEC,6BAA6B,CAAC;AAC9J,OAAO,IAAII,8BAA8B,GAAGN,cAAc,IAAIA,cAAc,CAAChI,MAAM,CAACyH,IAAI,IAAIA,IAAI,CAACc,OAAO,KAAK7D,SAAS,CAAC;AACvH,IAAI8D,yCAAyC,GAAG/G,cAAc,CAAC,CAAC4G,4BAA4B,CAAC,EAAEC,8BAA8B,CAAC;AAC9H,OAAO,IAAIG,yBAAyB,GAAGT,cAAc,IAAIA,cAAc,CAACU,GAAG,CAACjB,IAAI,IAAIA,IAAI,CAACkB,IAAI,CAAC,CAAC3I,MAAM,CAAC4I,OAAO,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC;;AAEtH;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,iCAAiC,GAAGrH,cAAc,CAAC,CAAC4G,4BAA4B,CAAC,EAAEI,yBAAyB,CAAC;AACxH,OAAO,IAAIM,oBAAoB,GAAGA,CAACC,kBAAkB,EAAEC,IAAI,KAAK;EAC9D,IAAI;IACFC,SAAS,GAAG,EAAE;IACdC,cAAc;IACdC;EACF,CAAC,GAAGH,IAAI;EACR,IAAID,kBAAkB,CAACzI,MAAM,GAAG,CAAC,EAAE;IACjC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,OAAOyI,kBAAkB;EAC3B;EACA,OAAOE,SAAS,CAACG,KAAK,CAACF,cAAc,EAAEC,YAAY,GAAG,CAAC,CAAC;AAC1D,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIE,mBAAmB,GAAG7H,cAAc,CAAC,CAACqH,iCAAiC,EAAE3G,yCAAyC,CAAC,EAAE4G,oBAAoB,CAAC;AACrJ,OAAO,IAAIQ,oBAAoB,GAAGA,CAACZ,IAAI,EAAER,YAAY,EAAEqB,KAAK,KAAK;EAC/D,IAAI,CAACrB,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAAC1D,OAAO,KAAK,IAAI,EAAE;IAC9F,OAAOkE,IAAI,CAACD,GAAG,CAACjB,IAAI,KAAK;MACvB3G,KAAK,EAAEkB,iBAAiB,CAACyF,IAAI,EAAEU,YAAY,CAAC1D,OAAO;IACrD,CAAC,CAAC,CAAC;EACL;EACA,IAAI+E,KAAK,CAACjJ,MAAM,GAAG,CAAC,EAAE;IACpB,OAAOiJ,KAAK,CAACd,GAAG,CAACjB,IAAI,IAAIA,IAAI,CAAChD,OAAO,CAAC,CAACgF,OAAO,CAAChF,OAAO,IAAIkE,IAAI,CAACD,GAAG,CAACgB,KAAK,KAAK;MAC3E5I,KAAK,EAAEkB,iBAAiB,CAAC0H,KAAK,EAAEjF,OAAO;IACzC,CAAC,CAAC,CAAC,CAAC;EACN;EACA,OAAOkE,IAAI,CAACD,GAAG,CAACgB,KAAK,KAAK;IACxB5I,KAAK,EAAE4I;EACT,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,sBAAsB,GAAGlI,cAAc,CAAC,CAAC6H,mBAAmB,EAAEvC,cAAc,EAAEsB,4BAA4B,CAAC,EAAEkB,oBAAoB,CAAC;AAC7I,OAAO,SAASK,6BAA6BA,CAAC5C,QAAQ,EAAE6C,QAAQ,EAAE;EAChE,QAAQ7C,QAAQ;IACd,KAAK,OAAO;MACV,OAAO6C,QAAQ,CAACC,SAAS,KAAK,GAAG;IACnC,KAAK,OAAO;MACV,OAAOD,QAAQ,CAACC,SAAS,KAAK,GAAG;IACnC;MACE,OAAO,KAAK;EAChB;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,oBAAoBA,CAACjJ,KAAK,EAAE;EAC1C,IAAI4B,QAAQ,CAAC5B,KAAK,CAAC,IAAIU,MAAM,CAACwI,QAAQ,CAAClJ,KAAK,CAAC,EAAE;IAC7C,OAAO,CAACA,KAAK,EAAEA,KAAK,CAAC;EACvB;EACA,IAAImJ,KAAK,CAACC,OAAO,CAACpJ,KAAK,CAAC,EAAE;IACxB,IAAIqJ,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAGvJ,KAAK,CAAC;IACjC,IAAIwJ,QAAQ,GAAGF,IAAI,CAACG,GAAG,CAAC,GAAGzJ,KAAK,CAAC;IACjC,IAAI,CAAC2B,KAAK,CAAC0H,QAAQ,CAAC,IAAI,CAAC1H,KAAK,CAAC6H,QAAQ,CAAC,IAAI9I,MAAM,CAACwI,QAAQ,CAACG,QAAQ,CAAC,IAAI3I,MAAM,CAACwI,QAAQ,CAACM,QAAQ,CAAC,EAAE;MAClG,OAAO,CAACH,QAAQ,EAAEG,QAAQ,CAAC;IAC7B;EACF;EACA,OAAO5F,SAAS;AAClB;AACA,SAAS8F,gBAAgBA,CAAC7B,IAAI,EAAE;EAC9B,OAAOA,IAAI,CAAC3I,MAAM,CAACyK,CAAC,IAAI9H,UAAU,CAAC8H,CAAC,CAAC,IAAIA,CAAC,YAAYC,IAAI,CAAC,CAAChC,GAAG,CAAClH,MAAM,CAAC,CAACxB,MAAM,CAAC2K,CAAC,IAAIlI,KAAK,CAACkI,CAAC,CAAC,KAAK,KAAK,CAAC;AACzG;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,uBAAuBA,CAAClB,KAAK,EAAEmB,YAAY,EAAEC,iBAAiB,EAAE;EAC9E,IAAI,CAACA,iBAAiB,IAAI,OAAOD,YAAY,KAAK,QAAQ,IAAIpI,KAAK,CAACoI,YAAY,CAAC,EAAE;IACjF,OAAO,EAAE;EACX;EACA,IAAI,CAACC,iBAAiB,CAACvK,MAAM,EAAE;IAC7B,OAAO,EAAE;EACX;EACA,OAAOiK,gBAAgB,CAACM,iBAAiB,CAACrB,OAAO,CAACsB,EAAE,IAAI;IACtD,IAAIC,UAAU,GAAGhJ,iBAAiB,CAAC0H,KAAK,EAAEqB,EAAE,CAACtG,OAAO,CAAC;IACrD,IAAIwG,QAAQ,EAAEC,SAAS;IACvB,IAAIjB,KAAK,CAACC,OAAO,CAACc,UAAU,CAAC,EAAE;MAC7B,CAACC,QAAQ,EAAEC,SAAS,CAAC,GAAGF,UAAU;IACpC,CAAC,MAAM;MACLC,QAAQ,GAAGC,SAAS,GAAGF,UAAU;IACnC;IACA,IAAI,CAAClI,mBAAmB,CAACmI,QAAQ,CAAC,IAAI,CAACnI,mBAAmB,CAACoI,SAAS,CAAC,EAAE;MACrE,OAAOxG,SAAS;IAClB;IACA,OAAO,CAACmG,YAAY,GAAGI,QAAQ,EAAEJ,YAAY,GAAGK,SAAS,CAAC;EAC5D,CAAC,CAAC,CAAC;AACL;AACA,OAAO,IAAIC,kBAAkB,GAAGA,CAACC,aAAa,EAAE5B,KAAK,EAAE6B,eAAe,KAAK;EACzE,IAAIC,kBAAkB,GAAG,CAAC,CAAC;EAC3B,IAAIC,UAAU,GAAG/B,KAAK,CAACgC,MAAM,CAAC,CAACC,GAAG,EAAEhE,IAAI,KAAK;IAC3C,IAAIA,IAAI,CAACc,OAAO,IAAI,IAAI,EAAE;MACxB,OAAOkD,GAAG;IACZ;IACA,IAAIA,GAAG,CAAChE,IAAI,CAACc,OAAO,CAAC,IAAI,IAAI,EAAE;MAC7BkD,GAAG,CAAChE,IAAI,CAACc,OAAO,CAAC,GAAG,EAAE;IACxB;IACAkD,GAAG,CAAChE,IAAI,CAACc,OAAO,CAAC,CAACpI,IAAI,CAACsH,IAAI,CAAC;IAC5B,OAAOgE,GAAG;EACZ,CAAC,EAAEH,kBAAkB,CAAC;EACtB,OAAO1L,MAAM,CAAC8L,WAAW,CAAC9L,MAAM,CAAC+L,OAAO,CAACJ,UAAU,CAAC,CAAC7C,GAAG,CAACkD,KAAK,IAAI;IAChE,IAAI,CAACrD,OAAO,EAAEjB,cAAc,CAAC,GAAGsE,KAAK;IACrC,IAAIC,QAAQ,GAAGvE,cAAc,CAACoB,GAAG,CAACzH,CAAC,IAAIA,CAAC,CAACwD,OAAO,CAAC;IACjD,OAAO,CAAC8D,OAAO,EAAE;MACf;MACAuD,WAAW,EAAE/J,cAAc,CAACqJ,aAAa,EAAES,QAAQ,EAAER,eAAe,CAAC;MACrE/D;IACF,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;AACL,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIyE,iBAAiB,GAAGtK,cAAc,CAAC,CAAC6H,mBAAmB,EAAEjB,4BAA4B,EAAE3E,qBAAqB,CAAC,EAAEyH,kBAAkB,CAAC;AAC7I,OAAO,IAAIa,0BAA0B,GAAGA,CAACC,WAAW,EAAEC,KAAK,EAAElF,QAAQ,KAAK;EACxE,IAAI;IACFmC,cAAc;IACdC;EACF,CAAC,GAAG8C,KAAK;EACT,IAAIlF,QAAQ,KAAK,OAAO,EAAE;IACxB;IACA,OAAOtC,SAAS;EAClB;EACA,IAAIyH,mBAAmB,GAAGrK,sBAAsB,CAACmK,WAAW,EAAE9C,cAAc,EAAEC,YAAY,CAAC;EAC3F,IAAI+C,mBAAmB,IAAI,IAAI,IAAIA,mBAAmB,CAAC,CAAC,CAAC,KAAK,CAAC,IAAIA,mBAAmB,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;IAC/F,OAAOzH,SAAS;EAClB;EACA,OAAOyH,mBAAmB;AAC5B,CAAC;AACD,OAAO,IAAIC,yBAAyB,GAAG3K,cAAc,CAAC,CAACsK,iBAAiB,EAAE7J,0BAA0B,EAAE6B,YAAY,CAAC,EAAEiI,0BAA0B,CAAC;AAChJ,OAAO,IAAIK,iDAAiD,GAAGA,CAAC1D,IAAI,EAAER,YAAY,EAAEqB,KAAK,EAAExC,QAAQ,KAAK;EACtG,IAAIwC,KAAK,CAACjJ,MAAM,GAAG,CAAC,EAAE;IACpB,OAAOoI,IAAI,CAACc,OAAO,CAACC,KAAK,IAAI;MAC3B,OAAOF,KAAK,CAACC,OAAO,CAAChC,IAAI,IAAI;QAC3B,IAAI6E,eAAe,EAAEC,qBAAqB;QAC1C,IAAIzB,iBAAiB,GAAG,CAACwB,eAAe,GAAG7E,IAAI,CAAC+E,SAAS,MAAM,IAAI,IAAIF,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACtM,MAAM,CAAC6J,QAAQ,IAAID,6BAA6B,CAAC5C,QAAQ,EAAE6C,QAAQ,CAAC,CAAC;QAClM,IAAI4C,cAAc,GAAGzK,iBAAiB,CAAC0H,KAAK,EAAE,CAAC6C,qBAAqB,GAAGpE,YAAY,CAAC1D,OAAO,MAAM,IAAI,IAAI8H,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAG9E,IAAI,CAAChD,OAAO,CAAC;QACjL,OAAO;UACL3D,KAAK,EAAE2L,cAAc;UACrBC,WAAW,EAAE9B,uBAAuB,CAAClB,KAAK,EAAE+C,cAAc,EAAE3B,iBAAiB;QAC/E,CAAC;MACH,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC9K,MAAM,CAAC4I,OAAO,CAAC;EACpB;EACA,IAAI,CAACT,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAAC1D,OAAO,KAAK,IAAI,EAAE;IAC9F,OAAOkE,IAAI,CAACD,GAAG,CAACjB,IAAI,KAAK;MACvB3G,KAAK,EAAEkB,iBAAiB,CAACyF,IAAI,EAAEU,YAAY,CAAC1D,OAAO,CAAC;MACpDiI,WAAW,EAAE;IACf,CAAC,CAAC,CAAC;EACL;EACA,OAAO/D,IAAI,CAACD,GAAG,CAACgB,KAAK,KAAK;IACxB5I,KAAK,EAAE4I,KAAK;IACZgD,WAAW,EAAE;EACf,CAAC,CAAC,CAAC;AACL,CAAC;AACD,OAAO,IAAIC,mDAAmD,GAAGlL,cAAc,CAAC6H,mBAAmB,EAAEvC,cAAc,EAAEyB,yCAAyC,EAAEzE,YAAY,EAAEsI,iDAAiD,CAAC;AAChO,SAASO,kCAAkCA,CAACnF,IAAI,EAAE;EAChD,IAAI;IACF3G;EACF,CAAC,GAAG2G,IAAI;EACR,IAAI9E,UAAU,CAAC7B,KAAK,CAAC,IAAIA,KAAK,YAAY4J,IAAI,EAAE;IAC9C,OAAO5J,KAAK;EACd;EACA,OAAO4D,SAAS;AAClB;AACA,IAAImI,sBAAsB,GAAGC,oBAAoB,IAAI;EACnD,IAAIC,eAAe,GAAGD;EACtB;EAAA,CACCrD,OAAO,CAACuD,CAAC,IAAI,CAACA,CAAC,CAAClM,KAAK,EAAEkM,CAAC,CAACN,WAAW,CAAC;EACtC;EAAA,CACC7D,IAAI,CAAC,CAAC,CAAC;EACR,IAAIoE,WAAW,GAAGzC,gBAAgB,CAACuC,eAAe,CAAC;EACnD,IAAIE,WAAW,CAAC1M,MAAM,KAAK,CAAC,EAAE;IAC5B,OAAOmE,SAAS;EAClB;EACA,OAAO,CAAC0F,IAAI,CAACC,GAAG,CAAC,GAAG4C,WAAW,CAAC,EAAE7C,IAAI,CAACG,GAAG,CAAC,GAAG0C,WAAW,CAAC,CAAC;AAC7D,CAAC;AACD,IAAIC,2BAA2B,GAAGA,CAACH,eAAe,EAAE5E,YAAY,EAAEgF,aAAa,KAAK;EAClF,IAAIC,iBAAiB,GAAGL,eAAe,CAACrE,GAAG,CAACkE,kCAAkC,CAAC,CAAC5M,MAAM,CAACyK,CAAC,IAAIA,CAAC,IAAI,IAAI,CAAC;EACtG,IAAI0C,aAAa,KAAKhF,YAAY,CAAC1D,OAAO,IAAI,IAAI,IAAI0D,YAAY,CAAC5D,uBAAuB,IAAI/B,YAAY,CAAC4K,iBAAiB,CAAC,CAAC,EAAE;IAC9H;AACJ;AACA;AACA;IACI,OAAO1L,KAAK,CAAC,CAAC,EAAEqL,eAAe,CAACxM,MAAM,CAAC;EACzC;EACA,IAAI4H,YAAY,CAAC5D,uBAAuB,EAAE;IACxC,OAAO6I,iBAAiB;EAC1B;EACA,OAAOnD,KAAK,CAACoD,IAAI,CAAC,IAAIC,GAAG,CAACF,iBAAiB,CAAC,CAAC;AAC/C,CAAC;AACD,OAAO,IAAIG,mBAAmB,GAAGpF,YAAY,IAAI;EAC/C,IAAIqF,oBAAoB;EACxB,IAAIrF,YAAY,IAAI,IAAI,IAAI,EAAE,QAAQ,IAAIA,YAAY,CAAC,EAAE;IACvD,OAAOhE,oBAAoB;EAC7B;EACA,IAAIgE,YAAY,CAACxD,MAAM,IAAI,IAAI,EAAE;IAC/B,OAAOwD,YAAY,CAACxD,MAAM;EAC5B;EACA,IAAIwD,YAAY,CAACtC,KAAK,IAAI,IAAI,EAAE;IAC9B,IAAIsC,YAAY,CAACrC,IAAI,KAAK,QAAQ,EAAE;MAClC,IAAI2H,SAAS,GAAGjD,gBAAgB,CAACrC,YAAY,CAACtC,KAAK,CAAC;MACpD,OAAO,CAACuE,IAAI,CAACC,GAAG,CAAC,GAAGoD,SAAS,CAAC,EAAErD,IAAI,CAACG,GAAG,CAAC,GAAGkD,SAAS,CAAC,CAAC;IACzD;IACA,IAAItF,YAAY,CAACrC,IAAI,KAAK,UAAU,EAAE;MACpC,OAAOqC,YAAY,CAACtC,KAAK,CAAC6C,GAAG,CAACnH,MAAM,CAAC;IACvC;EACF;EACA,OAAO,CAACiM,oBAAoB,GAAGrF,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACxD,MAAM,MAAM,IAAI,IAAI6I,oBAAoB,KAAK,KAAK,CAAC,GAAGA,oBAAoB,GAAGrJ,oBAAoB;AAC3M,CAAC;AACD,OAAO,IAAIuJ,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;EAChD,KAAK,IAAIC,IAAI,GAAGrN,SAAS,CAACC,MAAM,EAAEqN,OAAO,GAAG,IAAI3D,KAAK,CAAC0D,IAAI,CAAC,EAAEE,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGF,IAAI,EAAEE,IAAI,EAAE,EAAE;IAC1FD,OAAO,CAACC,IAAI,CAAC,GAAGvN,SAAS,CAACuN,IAAI,CAAC;EACjC;EACA,IAAIC,UAAU,GAAGF,OAAO,CAAC5N,MAAM,CAAC4I,OAAO,CAAC;EACxC,IAAIkF,UAAU,CAACvN,MAAM,KAAK,CAAC,EAAE;IAC3B,OAAOmE,SAAS;EAClB;EACA,IAAI+I,SAAS,GAAGK,UAAU,CAACjF,IAAI,CAAC,CAAC;EACjC,IAAIwB,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,GAAGoD,SAAS,CAAC;EAChC,IAAIlD,GAAG,GAAGH,IAAI,CAACG,GAAG,CAAC,GAAGkD,SAAS,CAAC;EAChC,OAAO,CAACpD,GAAG,EAAEE,GAAG,CAAC;AACnB,CAAC;AACD,OAAO,IAAIwD,mBAAmB,GAAG9H,KAAK,IAAIA,KAAK,CAAC+H,iBAAiB,CAACC,IAAI;AACtE,OAAO,IAAIC,uBAAuB,GAAGA,CAACC,QAAQ,EAAEnH,QAAQ,EAAEd,MAAM,KAAK;EACnE,OAAOiI,QAAQ,CAACnO,MAAM,CAACoO,EAAE,IAAIA,EAAE,CAACC,UAAU,KAAK,cAAc,CAAC,CAACrO,MAAM,CAACoO,EAAE,IAAI;IAC1E,IAAIpH,QAAQ,KAAK,OAAO,EAAE;MACxB,OAAOoH,EAAE,CAAC1G,OAAO,KAAKxB,MAAM;IAC9B;IACA,OAAOkI,EAAE,CAACzG,OAAO,KAAKzB,MAAM;EAC9B,CAAC,CAAC;AACJ,CAAC;AACD,OAAO,IAAIoI,yBAAyB,GAAG7M,cAAc,CAAC,CAACsM,mBAAmB,EAAEhK,YAAY,EAAEC,UAAU,CAAC,EAAEkK,uBAAuB,CAAC;AAC/H,OAAO,IAAIK,oBAAoB,GAAGtI,KAAK,IAAIA,KAAK,CAAC+H,iBAAiB,CAACQ,KAAK;AACxE,OAAO,IAAIC,0BAA0B,GAAGhN,cAAc,CAAC,CAAC8M,oBAAoB,EAAExK,YAAY,EAAEC,UAAU,CAAC,EAAEkK,uBAAuB,CAAC;AACjI,OAAO,IAAIQ,oBAAoB,GAAGzI,KAAK,IAAIA,KAAK,CAAC+H,iBAAiB,CAACW,KAAK;AACxE,OAAO,IAAIC,0BAA0B,GAAGnN,cAAc,CAAC,CAACiN,oBAAoB,EAAE3K,YAAY,EAAEC,UAAU,CAAC,EAAEkK,uBAAuB,CAAC;AACjI,OAAO,IAAIW,iBAAiB,GAAGA,CAACZ,IAAI,EAAEjH,QAAQ,KAAK;EACjD,IAAI8H,SAAS,GAAGtE,gBAAgB,CAACyD,IAAI,CAACvF,GAAG,CAACqG,GAAG,IAAI/H,QAAQ,KAAK,OAAO,GAAG+H,GAAG,CAACC,CAAC,GAAGD,GAAG,CAACE,CAAC,CAAC,CAAC;EACvF,IAAIH,SAAS,CAACvO,MAAM,KAAK,CAAC,EAAE;IAC1B,OAAOmE,SAAS;EAClB;EACA,OAAO,CAAC0F,IAAI,CAACC,GAAG,CAAC,GAAGyE,SAAS,CAAC,EAAE1E,IAAI,CAACG,GAAG,CAAC,GAAGuE,SAAS,CAAC,CAAC;AACzD,CAAC;AACD,IAAII,yBAAyB,GAAGzN,cAAc,CAAC6M,yBAAyB,EAAEvK,YAAY,EAAE8K,iBAAiB,CAAC;AAC1G,OAAO,IAAIM,kBAAkB,GAAGA,CAACX,KAAK,EAAExH,QAAQ,KAAK;EACnD,IAAI8H,SAAS,GAAGtE,gBAAgB,CAACgE,KAAK,CAAC/E,OAAO,CAAC2F,IAAI,IAAI,CAACpI,QAAQ,KAAK,OAAO,GAAGoI,IAAI,CAACC,EAAE,GAAGD,IAAI,CAACE,EAAE,EAAEtI,QAAQ,KAAK,OAAO,GAAGoI,IAAI,CAACG,EAAE,GAAGH,IAAI,CAACI,EAAE,CAAC,CAAC,CAAC;EAC7I,IAAIV,SAAS,CAACvO,MAAM,KAAK,CAAC,EAAE;IAC1B,OAAOmE,SAAS;EAClB;EACA,OAAO,CAAC0F,IAAI,CAACC,GAAG,CAAC,GAAGyE,SAAS,CAAC,EAAE1E,IAAI,CAACG,GAAG,CAAC,GAAGuE,SAAS,CAAC,CAAC;AACzD,CAAC;AACD,IAAIW,0BAA0B,GAAGhO,cAAc,CAAC,CAACgN,0BAA0B,EAAE1K,YAAY,CAAC,EAAEoL,kBAAkB,CAAC;AAC/G,OAAO,IAAIO,kBAAkB,GAAGA,CAACf,KAAK,EAAE3H,QAAQ,KAAK;EACnD,IAAI8H,SAAS,GAAGtE,gBAAgB,CAACmE,KAAK,CAACjG,GAAG,CAACiH,IAAI,IAAI3I,QAAQ,KAAK,OAAO,GAAG2I,IAAI,CAACX,CAAC,GAAGW,IAAI,CAACV,CAAC,CAAC,CAAC;EAC3F,IAAIH,SAAS,CAACvO,MAAM,KAAK,CAAC,EAAE;IAC1B,OAAOmE,SAAS;EAClB;EACA,OAAO,CAAC0F,IAAI,CAACC,GAAG,CAAC,GAAGyE,SAAS,CAAC,EAAE1E,IAAI,CAACG,GAAG,CAAC,GAAGuE,SAAS,CAAC,CAAC;AACzD,CAAC;AACD,IAAIc,0BAA0B,GAAGnO,cAAc,CAACmN,0BAA0B,EAAE7K,YAAY,EAAE2L,kBAAkB,CAAC;AAC7G,IAAIG,6BAA6B,GAAGpO,cAAc,CAACyN,yBAAyB,EAAEU,0BAA0B,EAAEH,0BAA0B,EAAE,CAACK,UAAU,EAAEC,WAAW,EAAEC,WAAW,KAAK;EAC9K,OAAOtC,YAAY,CAACoC,UAAU,EAAEE,WAAW,EAAED,WAAW,CAAC;AAC3D,CAAC,CAAC;AACF,OAAO,IAAIE,sBAAsB,GAAGxO,cAAc,CAAC,CAACsF,cAAc,CAAC,EAAEwG,mBAAmB,CAAC;AACzF,OAAO,IAAI2C,sBAAsB,GAAGA,CAAC/H,YAAY,EAAEgI,gBAAgB,EAAEhE,mBAAmB,EAAEiE,uBAAuB,EAAEC,uBAAuB,KAAK;EAC7I,IAAIC,wBAAwB,GAAGjO,4CAA4C,CAAC8N,gBAAgB,EAAEhI,YAAY,CAAC9D,iBAAiB,CAAC;EAC7H,IAAIiM,wBAAwB,IAAI,IAAI,EAAE;IACpC;IACA,OAAOA,wBAAwB;EACjC;EACA,OAAOhO,wBAAwB,CAAC6N,gBAAgB,EAAEzC,YAAY,CAACvB,mBAAmB,EAAEkE,uBAAuB,EAAExD,sBAAsB,CAACuD,uBAAuB,CAAC,CAAC,EAAEjI,YAAY,CAAC9D,iBAAiB,CAAC;AAChM,CAAC;AACD,IAAIkM,qBAAqB,GAAG9O,cAAc,CAAC,CAACsF,cAAc,EAAEkJ,sBAAsB,EAAE7D,yBAAyB,EAAEO,mDAAmD,EAAEkD,6BAA6B,CAAC,EAAEK,sBAAsB,CAAC;;AAE3N;AACA;AACA;AACA;AACA;AACA,IAAIM,YAAY,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;AACzB,OAAO,IAAIC,iBAAiB,GAAGA,CAACtI,YAAY,EAAEuI,MAAM,EAAEtF,aAAa,EAAEuF,gBAAgB,EAAEtF,eAAe,EAAErE,QAAQ,EAAE4J,eAAe,KAAK;EACpI,IAAIzI,YAAY,IAAI,IAAI,IAAIiD,aAAa,IAAI,IAAI,IAAIA,aAAa,CAAC7K,MAAM,KAAK,CAAC,EAAE;IAC/E,OAAOmE,SAAS;EAClB;EACA,IAAI;IACFD,OAAO;IACPqB;EACF,CAAC,GAAGqC,YAAY;EAChB,IAAIgF,aAAa,GAAGlL,iBAAiB,CAACyO,MAAM,EAAE1J,QAAQ,CAAC;EACvD,IAAImG,aAAa,IAAI1I,OAAO,IAAI,IAAI,EAAE;IACpC,OAAO/C,KAAK,CAAC,CAAC,EAAE0J,aAAa,CAAC7K,MAAM,CAAC;EACvC;EACA,IAAIuF,IAAI,KAAK,UAAU,EAAE;IACvB,OAAOoH,2BAA2B,CAACyD,gBAAgB,EAAExI,YAAY,EAAEgF,aAAa,CAAC;EACnF;EACA,IAAI9B,eAAe,KAAK,QAAQ,EAAE;IAChC,OAAOmF,YAAY;EACrB;EACA,OAAOI,eAAe;AACxB,CAAC;AACD,OAAO,IAAIC,gBAAgB,GAAGpP,cAAc,CAAC,CAACsF,cAAc,EAAEnF,iBAAiB,EAAE0H,mBAAmB,EAAEK,sBAAsB,EAAEjG,qBAAqB,EAAEK,YAAY,EAAEwM,qBAAqB,CAAC,EAAEE,iBAAiB,CAAC;AAC7M,OAAO,IAAIK,oBAAoB,GAAGA,CAACC,UAAU,EAAEL,MAAM,EAAEM,MAAM,EAAEC,SAAS,EAAEjK,QAAQ,KAAK;EACrF,IAAI+J,UAAU,IAAI,IAAI,EAAE;IACtB,OAAOrM,SAAS;EAClB;EACA,IAAI;IACFe,KAAK;IACLK;EACF,CAAC,GAAGiL,UAAU;EACd,IAAItL,KAAK,KAAK,MAAM,EAAE;IACpB,IAAIiL,MAAM,KAAK,QAAQ,IAAI1J,QAAQ,KAAK,YAAY,EAAE;MACpD,OAAO,MAAM;IACf;IACA,IAAI0J,MAAM,KAAK,QAAQ,IAAI1J,QAAQ,KAAK,WAAW,EAAE;MACnD,OAAO,QAAQ;IACjB;IACA,IAAIlB,IAAI,KAAK,UAAU,IAAImL,SAAS,KAAKA,SAAS,CAACC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,IAAID,SAAS,CAACC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,IAAID,SAAS,CAACC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAACF,MAAM,CAAC,EAAE;MAC1K,OAAO,OAAO;IAChB;IACA,IAAIlL,IAAI,KAAK,UAAU,EAAE;MACvB,OAAO,MAAM;IACf;IACA,OAAO,QAAQ;EACjB;EACA,IAAI,OAAOL,KAAK,KAAK,QAAQ,EAAE;IAC7B,IAAIN,IAAI,GAAG,OAAO,CAAC+B,MAAM,CAACrE,UAAU,CAAC4C,KAAK,CAAC,CAAC;IAC5C,OAAON,IAAI,IAAIxD,QAAQ,GAAGwD,IAAI,GAAG,OAAO;EAC1C;EACA,OAAOT,SAAS;AAClB,CAAC;AACD,OAAO,IAAIyM,mBAAmB,GAAG1P,cAAc,CAAC,CAACsF,cAAc,EAAEnF,iBAAiB,EAAEyF,YAAY,EAAE5D,eAAe,EAAEM,YAAY,CAAC,EAAE+M,oBAAoB,CAAC;AACvJ,SAASM,kBAAkBA,CAACC,aAAa,EAAE;EACzC,IAAIA,aAAa,IAAI,IAAI,EAAE;IACzB,OAAO3M,SAAS;EAClB;EACA,IAAI2M,aAAa,IAAI1P,QAAQ,EAAE;IAC7B;IACA,OAAOA,QAAQ,CAAC0P,aAAa,CAAC,CAAC,CAAC;EAClC;EACA,IAAIlM,IAAI,GAAG,OAAO,CAAC+B,MAAM,CAACrE,UAAU,CAACwO,aAAa,CAAC,CAAC;EACpD,IAAIlM,IAAI,IAAIxD,QAAQ,EAAE;IACpB;IACA,OAAOA,QAAQ,CAACwD,IAAI,CAAC,CAAC,CAAC;EACzB;EACA,OAAOT,SAAS;AAClB;AACA,OAAO,SAAS4M,oBAAoBA,CAACnL,IAAI,EAAEkL,aAAa,EAAEE,UAAU,EAAEC,SAAS,EAAE;EAC/E,IAAID,UAAU,IAAI,IAAI,IAAIC,SAAS,IAAI,IAAI,EAAE;IAC3C,OAAO9M,SAAS;EAClB;EACA,IAAI,OAAOyB,IAAI,CAACV,KAAK,KAAK,UAAU,EAAE;IACpC;IACA,OAAOU,IAAI,CAACV,KAAK,CAACgM,IAAI,CAAC,CAAC,CAAC9M,MAAM,CAAC4M,UAAU,CAAC,CAAC7P,KAAK,CAAC8P,SAAS,CAAC;EAC9D;EACA,IAAIE,eAAe,GAAGN,kBAAkB,CAACC,aAAa,CAAC;EACvD,IAAIK,eAAe,IAAI,IAAI,EAAE;IAC3B,OAAOhN,SAAS;EAClB;EACA,IAAIe,KAAK,GAAGiM,eAAe,CAAC/M,MAAM,CAAC4M,UAAU,CAAC,CAAC7P,KAAK,CAAC8P,SAAS,CAAC;EAC/D;EACA3P,kBAAkB,CAAC4D,KAAK,CAAC;EACzB,OAAOA,KAAK;AACd;AACA,OAAO,IAAIkM,gBAAgB,GAAGA,CAACJ,UAAU,EAAEpJ,YAAY,EAAEkJ,aAAa,KAAK;EACzE,IAAIlB,gBAAgB,GAAG5C,mBAAmB,CAACpF,YAAY,CAAC;EACxD,IAAIkJ,aAAa,KAAK,MAAM,IAAIA,aAAa,KAAK,QAAQ,EAAE;IAC1D,OAAO3M,SAAS;EAClB;EACA,IAAIyD,YAAY,IAAI,IAAI,IAAIA,YAAY,CAACxC,SAAS,IAAIsE,KAAK,CAACC,OAAO,CAACiG,gBAAgB,CAAC,KAAKA,gBAAgB,CAAC,CAAC,CAAC,KAAK,MAAM,IAAIA,gBAAgB,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,IAAI/N,wBAAwB,CAACmP,UAAU,CAAC,EAAE;IACnM,OAAOxO,iBAAiB,CAACwO,UAAU,EAAEpJ,YAAY,CAACxC,SAAS,EAAEwC,YAAY,CAAC7D,aAAa,CAAC;EAC1F;EACA,IAAI6D,YAAY,IAAI,IAAI,IAAIA,YAAY,CAACxC,SAAS,IAAIwC,YAAY,CAACrC,IAAI,KAAK,QAAQ,IAAI1D,wBAAwB,CAACmP,UAAU,CAAC,EAAE;IAC5H,OAAOvO,wBAAwB,CAACuO,UAAU,EAAEpJ,YAAY,CAACxC,SAAS,EAAEwC,YAAY,CAAC7D,aAAa,CAAC;EACjG;EACA,OAAOI,SAAS;AAClB,CAAC;AACD,OAAO,IAAIkN,eAAe,GAAGnQ,cAAc,CAAC,CAACoP,gBAAgB,EAAEzJ,kBAAkB,EAAE+J,mBAAmB,CAAC,EAAEQ,gBAAgB,CAAC;AAC1H,OAAO,IAAIE,8BAA8B,GAAGA,CAAC1J,YAAY,EAAExD,MAAM,EAAEmN,SAAS,EAAE9K,QAAQ,KAAK;EACzF;EACA;AACF;AACA;AACA;AACA;AACA;EACEA,QAAQ,KAAK,WAAW,IAAI,CAACmB,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACrC,IAAI,MAAM,QAAQ,IAAI1D,wBAAwB,CAACuC,MAAM,CAAC,IAAIsF,KAAK,CAACC,OAAO,CAAC4H,SAAS,CAAC,IAAIA,SAAS,CAACvR,MAAM,GAAG,CAAC,EAAE;IAChN,IAAIwR,aAAa,GAAGpN,MAAM,CAAC,CAAC,CAAC;IAC7B,IAAIqN,YAAY,GAAGF,SAAS,CAAC,CAAC,CAAC;IAC/B,IAAIG,aAAa,GAAGtN,MAAM,CAAC,CAAC,CAAC;IAC7B,IAAIuN,YAAY,GAAGJ,SAAS,CAACA,SAAS,CAACvR,MAAM,GAAG,CAAC,CAAC;IAClD,OAAO,CAAC6J,IAAI,CAACC,GAAG,CAAC0H,aAAa,EAAEC,YAAY,CAAC,EAAE5H,IAAI,CAACG,GAAG,CAAC0H,aAAa,EAAEC,YAAY,CAAC,CAAC;EACvF;EACA,OAAOvN,MAAM;AACf,CAAC;AACD,OAAO,IAAIwN,kCAAkC,GAAG1Q,cAAc,CAAC,CAACsF,cAAc,EAAE8J,gBAAgB,EAAEe,eAAe,EAAE7N,YAAY,CAAC,EAAE8N,8BAA8B,CAAC;;AAEjK;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIO,mCAAmC,GAAG3Q,cAAc,CAACkI,sBAAsB,EAAE5C,cAAc,EAAE,CAACgG,eAAe,EAAE5E,YAAY,KAAK;EACzI,IAAI,CAACA,YAAY,IAAIA,YAAY,CAACrC,IAAI,KAAK,QAAQ,EAAE;IACnD,OAAOpB,SAAS;EAClB;EACA,IAAI2N,6BAA6B,GAAGC,QAAQ;EAC5C,IAAIC,YAAY,GAAGtI,KAAK,CAACoD,IAAI,CAAC7C,gBAAgB,CAACuC,eAAe,CAACrE,GAAG,CAACsE,CAAC,IAAIA,CAAC,CAAClM,KAAK,CAAC,CAAC,CAAC,CAAC0R,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC;EACxG,IAAIH,YAAY,CAAChS,MAAM,GAAG,CAAC,EAAE;IAC3B,OAAO+R,QAAQ;EACjB;EACA,IAAIK,IAAI,GAAGJ,YAAY,CAACA,YAAY,CAAChS,MAAM,GAAG,CAAC,CAAC,GAAGgS,YAAY,CAAC,CAAC,CAAC;EAClE,IAAII,IAAI,KAAK,CAAC,EAAE;IACd,OAAOL,QAAQ;EACjB;EACA;EACA,KAAK,IAAIrR,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsR,YAAY,CAAChS,MAAM,GAAG,CAAC,EAAEU,CAAC,EAAE,EAAE;IAChD,IAAI2R,QAAQ,GAAGL,YAAY,CAACtR,CAAC,GAAG,CAAC,CAAC,GAAGsR,YAAY,CAACtR,CAAC,CAAC;IACpDoR,6BAA6B,GAAGjI,IAAI,CAACC,GAAG,CAACgI,6BAA6B,EAAEO,QAAQ,CAAC;EACnF;EACA,OAAOP,6BAA6B,GAAGM,IAAI;AAC7C,CAAC,CAAC;AACF,IAAIE,uBAAuB,GAAGpR,cAAc,CAAC2Q,mCAAmC,EAAExQ,iBAAiB,EAAE4B,oBAAoB,EAAEH,yBAAyB,EAAE,CAACyP,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE3N,OAAO,KAAKA,OAAO,EAAE,CAAC4N,yBAAyB,EAAEvC,MAAM,EAAEwC,cAAc,EAAEC,MAAM,EAAE9N,OAAO,KAAK;EAC9P,IAAI,CAACvC,mBAAmB,CAACmQ,yBAAyB,CAAC,EAAE;IACnD,OAAO,CAAC;EACV;EACA,IAAIG,UAAU,GAAG1C,MAAM,KAAK,UAAU,GAAGyC,MAAM,CAACvO,MAAM,GAAGuO,MAAM,CAAC1M,KAAK;EACrE,IAAIpB,OAAO,KAAK,KAAK,EAAE;IACrB,OAAO4N,yBAAyB,GAAGG,UAAU,GAAG,CAAC;EACnD;EACA,IAAI/N,OAAO,KAAK,QAAQ,EAAE;IACxB,IAAIgO,GAAG,GAAG9Q,eAAe,CAAC2Q,cAAc,EAAED,yBAAyB,GAAGG,UAAU,CAAC;IACjF,IAAIE,QAAQ,GAAGL,yBAAyB,GAAGG,UAAU,GAAG,CAAC;IACzD,OAAOE,QAAQ,GAAGD,GAAG,GAAG,CAACC,QAAQ,GAAGD,GAAG,IAAID,UAAU,GAAGC,GAAG;EAC7D;EACA,OAAO,CAAC;AACV,CAAC,CAAC;AACF,OAAO,IAAIE,4BAA4B,GAAGA,CAACtN,KAAK,EAAEC,MAAM,KAAK;EAC3D,IAAIsN,aAAa,GAAGxN,mBAAmB,CAACC,KAAK,EAAEC,MAAM,CAAC;EACtD,IAAIsN,aAAa,IAAI,IAAI,IAAI,OAAOA,aAAa,CAACnO,OAAO,KAAK,QAAQ,EAAE;IACtE,OAAO,CAAC;EACV;EACA,OAAOwN,uBAAuB,CAAC5M,KAAK,EAAE,OAAO,EAAEC,MAAM,EAAEsN,aAAa,CAACnO,OAAO,CAAC;AAC/E,CAAC;AACD,OAAO,IAAIoO,4BAA4B,GAAGA,CAACxN,KAAK,EAAEC,MAAM,KAAK;EAC3D,IAAIwN,aAAa,GAAGhN,mBAAmB,CAACT,KAAK,EAAEC,MAAM,CAAC;EACtD,IAAIwN,aAAa,IAAI,IAAI,IAAI,OAAOA,aAAa,CAACrO,OAAO,KAAK,QAAQ,EAAE;IACtE,OAAO,CAAC;EACV;EACA,OAAOwN,uBAAuB,CAAC5M,KAAK,EAAE,OAAO,EAAEC,MAAM,EAAEwN,aAAa,CAACrO,OAAO,CAAC;AAC/E,CAAC;AACD,IAAIsO,kBAAkB,GAAGlS,cAAc,CAACuE,mBAAmB,EAAEuN,4BAA4B,EAAE,CAACC,aAAa,EAAEI,UAAU,KAAK;EACxH,IAAIC,aAAa,EAAEC,cAAc;EACjC,IAAIN,aAAa,IAAI,IAAI,EAAE;IACzB,OAAO;MACLlO,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE;IACT,CAAC;EACH;EACA,IAAI;IACFF;EACF,CAAC,GAAGmO,aAAa;EACjB,IAAI,OAAOnO,OAAO,KAAK,QAAQ,EAAE;IAC/B,OAAO;MACLC,IAAI,EAAEsO,UAAU;MAChBrO,KAAK,EAAEqO;IACT,CAAC;EACH;EACA,OAAO;IACLtO,IAAI,EAAE,CAAC,CAACuO,aAAa,GAAGxO,OAAO,CAACC,IAAI,MAAM,IAAI,IAAIuO,aAAa,KAAK,KAAK,CAAC,GAAGA,aAAa,GAAG,CAAC,IAAID,UAAU;IAC5GrO,KAAK,EAAE,CAAC,CAACuO,cAAc,GAAGzO,OAAO,CAACE,KAAK,MAAM,IAAI,IAAIuO,cAAc,KAAK,KAAK,CAAC,GAAGA,cAAc,GAAG,CAAC,IAAIF;EACzG,CAAC;AACH,CAAC,CAAC;AACF,IAAIG,kBAAkB,GAAGtS,cAAc,CAACiF,mBAAmB,EAAE+M,4BAA4B,EAAE,CAACC,aAAa,EAAEE,UAAU,KAAK;EACxH,IAAII,YAAY,EAAEC,eAAe;EACjC,IAAIP,aAAa,IAAI,IAAI,EAAE;IACzB,OAAO;MACLnN,GAAG,EAAE,CAAC;MACNC,MAAM,EAAE;IACV,CAAC;EACH;EACA,IAAI;IACFnB;EACF,CAAC,GAAGqO,aAAa;EACjB,IAAI,OAAOrO,OAAO,KAAK,QAAQ,EAAE;IAC/B,OAAO;MACLkB,GAAG,EAAEqN,UAAU;MACfpN,MAAM,EAAEoN;IACV,CAAC;EACH;EACA,OAAO;IACLrN,GAAG,EAAE,CAAC,CAACyN,YAAY,GAAG3O,OAAO,CAACkB,GAAG,MAAM,IAAI,IAAIyN,YAAY,KAAK,KAAK,CAAC,GAAGA,YAAY,GAAG,CAAC,IAAIJ,UAAU;IACvGpN,MAAM,EAAE,CAAC,CAACyN,eAAe,GAAG5O,OAAO,CAACmB,MAAM,MAAM,IAAI,IAAIyN,eAAe,KAAK,KAAK,CAAC,GAAGA,eAAe,GAAG,CAAC,IAAIL;EAC9G,CAAC;AACH,CAAC,CAAC;AACF,OAAO,IAAIM,iBAAiB,GAAGzS,cAAc,CAAC,CAAC4B,yBAAyB,EAAEsQ,kBAAkB,EAAErQ,qBAAqB,EAAEC,mBAAmB,EAAE,CAAC4Q,MAAM,EAAEC,OAAO,EAAEC,UAAU,KAAKA,UAAU,CAAC,EAAE,CAAClB,MAAM,EAAE9N,OAAO,EAAEiP,eAAe,EAAEC,KAAK,EAAEF,UAAU,KAAK;EAC/O,IAAI;IACFhP,OAAO,EAAEmP;EACX,CAAC,GAAGD,KAAK;EACT,IAAIF,UAAU,EAAE;IACd,OAAO,CAACG,YAAY,CAAClP,IAAI,EAAEgP,eAAe,CAAC7N,KAAK,GAAG+N,YAAY,CAACjP,KAAK,CAAC;EACxE;EACA,OAAO,CAAC4N,MAAM,CAAC7N,IAAI,GAAGD,OAAO,CAACC,IAAI,EAAE6N,MAAM,CAAC7N,IAAI,GAAG6N,MAAM,CAAC1M,KAAK,GAAGpB,OAAO,CAACE,KAAK,CAAC;AACjF,CAAC,CAAC;AACF,OAAO,IAAIkP,iBAAiB,GAAGhT,cAAc,CAAC,CAAC4B,yBAAyB,EAAEzB,iBAAiB,EAAEmS,kBAAkB,EAAEzQ,qBAAqB,EAAEC,mBAAmB,EAAE,CAAC4Q,MAAM,EAAEC,OAAO,EAAEC,UAAU,KAAKA,UAAU,CAAC,EAAE,CAAClB,MAAM,EAAEzC,MAAM,EAAErL,OAAO,EAAEiP,eAAe,EAAEI,KAAK,EAAEL,UAAU,KAAK;EAC1Q,IAAI;IACFhP,OAAO,EAAEmP;EACX,CAAC,GAAGE,KAAK;EACT,IAAIL,UAAU,EAAE;IACd,OAAO,CAACC,eAAe,CAAC1P,MAAM,GAAG4P,YAAY,CAAChO,MAAM,EAAEgO,YAAY,CAACjO,GAAG,CAAC;EACzE;EACA,IAAImK,MAAM,KAAK,YAAY,EAAE;IAC3B,OAAO,CAACyC,MAAM,CAAC5M,GAAG,GAAG4M,MAAM,CAACvO,MAAM,GAAGS,OAAO,CAACmB,MAAM,EAAE2M,MAAM,CAAC5M,GAAG,GAAGlB,OAAO,CAACkB,GAAG,CAAC;EAChF;EACA,OAAO,CAAC4M,MAAM,CAAC5M,GAAG,GAAGlB,OAAO,CAACkB,GAAG,EAAE4M,MAAM,CAAC5M,GAAG,GAAG4M,MAAM,CAACvO,MAAM,GAAGS,OAAO,CAACmB,MAAM,CAAC;AAChF,CAAC,CAAC;AACF,OAAO,IAAImO,eAAe,GAAGA,CAAC1O,KAAK,EAAEe,QAAQ,EAAEd,MAAM,EAAEmO,UAAU,KAAK;EACpE,IAAIO,oBAAoB;EACxB,QAAQ5N,QAAQ;IACd,KAAK,OAAO;MACV,OAAOkN,iBAAiB,CAACjO,KAAK,EAAEC,MAAM,EAAEmO,UAAU,CAAC;IACrD,KAAK,OAAO;MACV,OAAOI,iBAAiB,CAACxO,KAAK,EAAEC,MAAM,EAAEmO,UAAU,CAAC;IACrD,KAAK,OAAO;MACV,OAAO,CAACO,oBAAoB,GAAG/N,mBAAmB,CAACZ,KAAK,EAAEC,MAAM,CAAC,MAAM,IAAI,IAAI0O,oBAAoB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,oBAAoB,CAAClT,KAAK;IACtJ,KAAK,WAAW;MACd,OAAOkC,oBAAoB,CAACqC,KAAK,CAAC;IACpC,KAAK,YAAY;MACf,OAAOnC,qBAAqB,CAACmC,KAAK,EAAEC,MAAM,CAAC;IAC7C;MACE,OAAOxB,SAAS;EACpB;AACF,CAAC;AACD,OAAO,IAAImQ,0BAA0B,GAAGpT,cAAc,CAAC,CAACsF,cAAc,EAAE4N,eAAe,CAAC,EAAE1Q,2BAA2B,CAAC;AACtH,OAAO,IAAI6Q,eAAe,GAAGrT,cAAc,CAAC,CAACsF,cAAc,EAAEoK,mBAAmB,EAAEgB,kCAAkC,EAAE0C,0BAA0B,CAAC,EAAEvD,oBAAoB,CAAC;AACxK,OAAO,IAAIyD,uBAAuB,GAAGtT,cAAc,CAAC4G,4BAA4B,EAAEtE,YAAY,EAAE,CAACyF,KAAK,EAAExC,QAAQ,KAAK;EACnH,OAAOwC,KAAK,CAACC,OAAO,CAAChC,IAAI,IAAI;IAC3B,IAAIuN,gBAAgB;IACpB,OAAO,CAACA,gBAAgB,GAAGvN,IAAI,CAAC+E,SAAS,MAAM,IAAI,IAAIwI,gBAAgB,KAAK,KAAK,CAAC,GAAGA,gBAAgB,GAAG,EAAE;EAC5G,CAAC,CAAC,CAAChV,MAAM,CAACP,CAAC,IAAI;IACb,OAAOmK,6BAA6B,CAAC5C,QAAQ,EAAEvH,CAAC,CAAC;EACnD,CAAC,CAAC;AACJ,CAAC,CAAC;AACF,SAASwV,UAAUA,CAACxC,CAAC,EAAEC,CAAC,EAAE;EACxB,IAAID,CAAC,CAAC3N,EAAE,GAAG4N,CAAC,CAAC5N,EAAE,EAAE;IACf,OAAO,CAAC,CAAC;EACX;EACA,IAAI2N,CAAC,CAAC3N,EAAE,GAAG4N,CAAC,CAAC5N,EAAE,EAAE;IACf,OAAO,CAAC;EACV;EACA,OAAO,CAAC;AACV;AACA,IAAIoQ,mBAAmB,GAAGA,CAACf,MAAM,EAAE/O,WAAW,KAAKA,WAAW;AAC9D,IAAI+P,UAAU,GAAGA,CAAChB,MAAM,EAAEiB,YAAY,EAAElQ,MAAM,KAAKA,MAAM;AACzD,IAAImQ,4BAA4B,GAAG5T,cAAc,CAAC0B,cAAc,EAAE+R,mBAAmB,EAAEC,UAAU,EAAE,CAACG,OAAO,EAAElQ,WAAW,EAAEF,MAAM,KAAKoQ,OAAO,CAACtV,MAAM,CAACmG,IAAI,IAAIA,IAAI,CAACf,WAAW,KAAKA,WAAW,CAAC,CAACpF,MAAM,CAACmG,IAAI,IAAIA,IAAI,CAACjB,MAAM,KAAKA,MAAM,CAAC,CAACsN,IAAI,CAACyC,UAAU,CAAC,CAAC;AACtP,IAAIM,4BAA4B,GAAG9T,cAAc,CAAC2B,cAAc,EAAE8R,mBAAmB,EAAEC,UAAU,EAAE,CAACG,OAAO,EAAElQ,WAAW,EAAEF,MAAM,KAAKoQ,OAAO,CAACtV,MAAM,CAACmG,IAAI,IAAIA,IAAI,CAACf,WAAW,KAAKA,WAAW,CAAC,CAACpF,MAAM,CAACmG,IAAI,IAAIA,IAAI,CAACjB,MAAM,KAAKA,MAAM,CAAC,CAACsN,IAAI,CAACyC,UAAU,CAAC,CAAC;AACtP,IAAIO,YAAY,GAAGA,CAACrC,MAAM,EAAEhL,YAAY,KAAK;EAC3C,OAAO;IACL1B,KAAK,EAAE0M,MAAM,CAAC1M,KAAK;IACnB7B,MAAM,EAAEuD,YAAY,CAACvD;EACvB,CAAC;AACH,CAAC;AACD,IAAI6Q,YAAY,GAAGA,CAACtC,MAAM,EAAEhL,YAAY,KAAK;EAC3C,IAAI1B,KAAK,GAAG,OAAO0B,YAAY,CAAC1B,KAAK,KAAK,QAAQ,GAAG0B,YAAY,CAAC1B,KAAK,GAAGvC,oBAAoB;EAC9F,OAAO;IACLuC,KAAK;IACL7B,MAAM,EAAEuO,MAAM,CAACvO;EACjB,CAAC;AACH,CAAC;AACD,OAAO,IAAI8Q,eAAe,GAAGjU,cAAc,CAAC4B,yBAAyB,EAAE2C,mBAAmB,EAAEwP,YAAY,CAAC;AACzG,IAAIG,iCAAiC,GAAGA,CAACxC,MAAM,EAAE/N,WAAW,EAAEwQ,WAAW,KAAK;EAC5E,QAAQxQ,WAAW;IACjB,KAAK,KAAK;MACR,OAAO+N,MAAM,CAAC5M,GAAG;IACnB,KAAK,QAAQ;MACX,OAAOqP,WAAW,GAAGzC,MAAM,CAAC3M,MAAM;IACpC;MACE,OAAO,CAAC;EACZ;AACF,CAAC;AACD,IAAIqP,iCAAiC,GAAGA,CAAC1C,MAAM,EAAE/N,WAAW,EAAE0Q,UAAU,KAAK;EAC3E,QAAQ1Q,WAAW;IACjB,KAAK,MAAM;MACT,OAAO+N,MAAM,CAAC7N,IAAI;IACpB,KAAK,OAAO;MACV,OAAOwQ,UAAU,GAAG3C,MAAM,CAAC5N,KAAK;IAClC;MACE,OAAO,CAAC;EACZ;AACF,CAAC;AACD,OAAO,IAAIwQ,yBAAyB,GAAGtU,cAAc,CAACwB,iBAAiB,EAAEI,yBAAyB,EAAEgS,4BAA4B,EAAEH,mBAAmB,EAAEC,UAAU,EAAE,CAACS,WAAW,EAAEzC,MAAM,EAAE6C,yBAAyB,EAAE5Q,WAAW,EAAEF,MAAM,KAAK;EAC1O,IAAI+Q,KAAK,GAAG,CAAC,CAAC;EACd,IAAIC,QAAQ;EACZF,yBAAyB,CAACxV,OAAO,CAAC2F,IAAI,IAAI;IACxC,IAAIgQ,QAAQ,GAAGX,YAAY,CAACrC,MAAM,EAAEhN,IAAI,CAAC;IACzC,IAAI+P,QAAQ,IAAI,IAAI,EAAE;MACpBA,QAAQ,GAAGP,iCAAiC,CAACxC,MAAM,EAAE/N,WAAW,EAAEwQ,WAAW,CAAC;IAChF;IACA,IAAIQ,SAAS,GAAGhR,WAAW,KAAK,KAAK,IAAI,CAACF,MAAM,IAAIE,WAAW,KAAK,QAAQ,IAAIF,MAAM;IACtF+Q,KAAK,CAAC9P,IAAI,CAACrB,EAAE,CAAC,GAAGoR,QAAQ,GAAG1U,MAAM,CAAC4U,SAAS,CAAC,GAAGD,QAAQ,CAACvR,MAAM;IAC/DsR,QAAQ,IAAI,CAACE,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,IAAID,QAAQ,CAACvR,MAAM;EACpD,CAAC,CAAC;EACF,OAAOqR,KAAK;AACd,CAAC,CAAC;AACF,OAAO,IAAII,yBAAyB,GAAG5U,cAAc,CAACyB,gBAAgB,EAAEG,yBAAyB,EAAEkS,4BAA4B,EAAEL,mBAAmB,EAAEC,UAAU,EAAE,CAACW,UAAU,EAAE3C,MAAM,EAAE6C,yBAAyB,EAAE5Q,WAAW,EAAEF,MAAM,KAAK;EACxO,IAAI+Q,KAAK,GAAG,CAAC,CAAC;EACd,IAAIC,QAAQ;EACZF,yBAAyB,CAACxV,OAAO,CAAC2F,IAAI,IAAI;IACxC,IAAIgQ,QAAQ,GAAGV,YAAY,CAACtC,MAAM,EAAEhN,IAAI,CAAC;IACzC,IAAI+P,QAAQ,IAAI,IAAI,EAAE;MACpBA,QAAQ,GAAGL,iCAAiC,CAAC1C,MAAM,EAAE/N,WAAW,EAAE0Q,UAAU,CAAC;IAC/E;IACA,IAAIM,SAAS,GAAGhR,WAAW,KAAK,MAAM,IAAI,CAACF,MAAM,IAAIE,WAAW,KAAK,OAAO,IAAIF,MAAM;IACtF+Q,KAAK,CAAC9P,IAAI,CAACrB,EAAE,CAAC,GAAGoR,QAAQ,GAAG1U,MAAM,CAAC4U,SAAS,CAAC,GAAGD,QAAQ,CAAC1P,KAAK;IAC9DyP,QAAQ,IAAI,CAACE,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,IAAID,QAAQ,CAAC1P,KAAK;EACnD,CAAC,CAAC;EACF,OAAOwP,KAAK;AACd,CAAC,CAAC;AACF,OAAO,IAAIK,mBAAmB,GAAGA,CAACrQ,KAAK,EAAEC,MAAM,KAAK;EAClD,IAAIiN,MAAM,GAAG9P,yBAAyB,CAAC4C,KAAK,CAAC;EAC7C,IAAIkC,YAAY,GAAGnC,mBAAmB,CAACC,KAAK,EAAEC,MAAM,CAAC;EACrD,IAAIiC,YAAY,IAAI,IAAI,EAAE;IACxB,OAAOzD,SAAS;EAClB;EACA,IAAI6R,QAAQ,GAAGR,yBAAyB,CAAC9P,KAAK,EAAEkC,YAAY,CAAC/C,WAAW,EAAE+C,YAAY,CAACjD,MAAM,CAAC;EAC9F,IAAIsR,cAAc,GAAGD,QAAQ,CAACrQ,MAAM,CAAC;EACrC,IAAIsQ,cAAc,IAAI,IAAI,EAAE;IAC1B,OAAO;MACLxH,CAAC,EAAEmE,MAAM,CAAC7N,IAAI;MACd2J,CAAC,EAAE;IACL,CAAC;EACH;EACA,OAAO;IACLD,CAAC,EAAEmE,MAAM,CAAC7N,IAAI;IACd2J,CAAC,EAAEuH;EACL,CAAC;AACH,CAAC;AACD,OAAO,IAAIC,mBAAmB,GAAGA,CAACxQ,KAAK,EAAEC,MAAM,KAAK;EAClD,IAAIiN,MAAM,GAAG9P,yBAAyB,CAAC4C,KAAK,CAAC;EAC7C,IAAIkC,YAAY,GAAGzB,mBAAmB,CAACT,KAAK,EAAEC,MAAM,CAAC;EACrD,IAAIiC,YAAY,IAAI,IAAI,EAAE;IACxB,OAAOzD,SAAS;EAClB;EACA,IAAI6R,QAAQ,GAAGF,yBAAyB,CAACpQ,KAAK,EAAEkC,YAAY,CAAC/C,WAAW,EAAE+C,YAAY,CAACjD,MAAM,CAAC;EAC9F,IAAIsR,cAAc,GAAGD,QAAQ,CAACrQ,MAAM,CAAC;EACrC,IAAIsQ,cAAc,IAAI,IAAI,EAAE;IAC1B,OAAO;MACLxH,CAAC,EAAE,CAAC;MACJC,CAAC,EAAEkE,MAAM,CAAC5M;IACZ,CAAC;EACH;EACA,OAAO;IACLyI,CAAC,EAAEwH,cAAc;IACjBvH,CAAC,EAAEkE,MAAM,CAAC5M;EACZ,CAAC;AACH,CAAC;AACD,OAAO,IAAImQ,eAAe,GAAGjV,cAAc,CAAC4B,yBAAyB,EAAEqD,mBAAmB,EAAE,CAACyM,MAAM,EAAEhL,YAAY,KAAK;EACpH,IAAI1B,KAAK,GAAG,OAAO0B,YAAY,CAAC1B,KAAK,KAAK,QAAQ,GAAG0B,YAAY,CAAC1B,KAAK,GAAGvC,oBAAoB;EAC9F,OAAO;IACLuC,KAAK;IACL7B,MAAM,EAAEuO,MAAM,CAACvO;EACjB,CAAC;AACH,CAAC,CAAC;AACF,OAAO,IAAI+R,uBAAuB,GAAGA,CAAC1Q,KAAK,EAAEe,QAAQ,EAAEd,MAAM,KAAK;EAChE,QAAQc,QAAQ;IACd,KAAK,OAAO;MACV;QACE,OAAO0O,eAAe,CAACzP,KAAK,EAAEC,MAAM,CAAC,CAACO,KAAK;MAC7C;IACF,KAAK,OAAO;MACV;QACE,OAAOiQ,eAAe,CAACzQ,KAAK,EAAEC,MAAM,CAAC,CAACtB,MAAM;MAC9C;IACF;MACE;QACE,OAAOF,SAAS;MAClB;EACJ;AACF,CAAC;AACD,OAAO,IAAIkS,sBAAsB,GAAGA,CAACC,WAAW,EAAEC,aAAa,EAAE3Q,IAAI,EAAEa,QAAQ,KAAK;EAClF,IAAIb,IAAI,IAAI,IAAI,EAAE;IAChB,OAAOzB,SAAS;EAClB;EACA,IAAI;IACFH,uBAAuB;IACvBuB,IAAI;IACJrB;EACF,CAAC,GAAG0B,IAAI;EACR,IAAIgH,aAAa,GAAGlL,iBAAiB,CAAC4U,WAAW,EAAE7P,QAAQ,CAAC;EAC5D,IAAI+P,OAAO,GAAGD,aAAa,CAACpO,GAAG,CAACsO,EAAE,IAAIA,EAAE,CAAClW,KAAK,CAAC;EAC/C,IAAI2D,OAAO,IAAI0I,aAAa,IAAIrH,IAAI,KAAK,UAAU,IAAIvB,uBAAuB,IAAI/B,YAAY,CAACuU,OAAO,CAAC,EAAE;IACvG,OAAOA,OAAO;EAChB;EACA,OAAOrS,SAAS;AAClB,CAAC;AACD,OAAO,IAAIuS,qBAAqB,GAAGxV,cAAc,CAAC,CAACG,iBAAiB,EAAE+H,sBAAsB,EAAE5C,cAAc,EAAEhD,YAAY,CAAC,EAAE6S,sBAAsB,CAAC;AACpJ,OAAO,IAAIM,wBAAwB,GAAGA,CAACxG,MAAM,EAAEoG,aAAa,EAAE3Q,IAAI,EAAEa,QAAQ,KAAK;EAC/E,IAAIb,IAAI,IAAI,IAAI,IAAIA,IAAI,CAAC1B,OAAO,IAAI,IAAI,EAAE;IACxC,OAAOC,SAAS;EAClB;EACA,IAAI;IACFoB,IAAI;IACJL;EACF,CAAC,GAAGU,IAAI;EACR,IAAIgH,aAAa,GAAGlL,iBAAiB,CAACyO,MAAM,EAAE1J,QAAQ,CAAC;EACvD,IAAImG,aAAa,KAAKrH,IAAI,KAAK,QAAQ,IAAIL,KAAK,KAAK,MAAM,CAAC,EAAE;IAC5D,OAAOqR,aAAa,CAACpO,GAAG,CAACsE,CAAC,IAAIA,CAAC,CAAClM,KAAK,CAAC;EACxC;EACA,OAAO4D,SAAS;AAClB,CAAC;AACD,OAAO,IAAIyS,uBAAuB,GAAG1V,cAAc,CAAC,CAACG,iBAAiB,EAAE+H,sBAAsB,EAAEvC,kBAAkB,EAAErD,YAAY,CAAC,EAAEmT,wBAAwB,CAAC;AAC5J,OAAO,IAAIE,mDAAmD,GAAG3V,cAAc,CAAC,CAACG,iBAAiB,EAAEuF,2BAA2B,EAAEgK,mBAAmB,EAAE2D,eAAe,EAAEmC,qBAAqB,EAAEE,uBAAuB,EAAExC,eAAe,EAAE/C,eAAe,EAAE7N,YAAY,CAAC,EAAE,CAAC2M,MAAM,EAAEvK,IAAI,EAAEkL,aAAa,EAAE5L,KAAK,EAAE4R,eAAe,EAAEjK,iBAAiB,EAAEoE,SAAS,EAAEM,SAAS,EAAE9K,QAAQ,KAAK;EAClX,IAAIb,IAAI,IAAI,IAAI,EAAE;IAChB,OAAO,IAAI;EACb;EACA,IAAIgH,aAAa,GAAGlL,iBAAiB,CAACyO,MAAM,EAAE1J,QAAQ,CAAC;EACvD,OAAO;IACLxC,KAAK,EAAE2B,IAAI,CAAC3B,KAAK;IACjBQ,QAAQ,EAAEmB,IAAI,CAACnB,QAAQ;IACvBC,UAAU,EAAEkB,IAAI,CAAClB,UAAU;IAC3BG,WAAW,EAAEe,IAAI,CAACf,WAAW;IAC7BM,IAAI,EAAES,IAAI,CAACT,IAAI;IACfC,SAAS,EAAEQ,IAAI,CAACR,SAAS;IACzBC,aAAa,EAAEO,IAAI,CAACP,aAAa;IACjCC,KAAK,EAAEM,IAAI,CAACN,KAAK;IACjBC,IAAI,EAAEK,IAAI,CAACL,IAAI;IACfC,IAAI,EAAEI,IAAI,CAACJ,IAAI;IACfiB,QAAQ;IACRoG,iBAAiB;IACjBiK,eAAe;IACflK,aAAa;IACb2E,SAAS;IACTpQ,KAAK,EAAE8P,SAAS;IAChBH,aAAa;IACb5L;EACF,CAAC;AACH,CAAC,CAAC;AACF,OAAO,IAAI6R,gBAAgB,GAAGA,CAAC5G,MAAM,EAAEvK,IAAI,EAAEkL,aAAa,EAAE5L,KAAK,EAAEqM,SAAS,EAAEN,SAAS,EAAE6F,eAAe,EAAEjK,iBAAiB,EAAEpG,QAAQ,KAAK;EACxI,IAAIb,IAAI,IAAI,IAAI,IAAIV,KAAK,IAAI,IAAI,EAAE;IACjC,OAAOf,SAAS;EAClB;EACA,IAAIyI,aAAa,GAAGlL,iBAAiB,CAACyO,MAAM,EAAE1J,QAAQ,CAAC;EACvD,IAAI;IACFlB,IAAI;IACJD,KAAK;IACLF;EACF,CAAC,GAAGQ,IAAI;;EAER;EACA,IAAIoR,aAAa,GAAGlG,aAAa,KAAK,WAAW,IAAI,OAAO5L,KAAK,CAAC+R,SAAS,KAAK,UAAU,GAAG/R,KAAK,CAAC+R,SAAS,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;EACtH,IAAIrE,MAAM,GAAGrN,IAAI,KAAK,UAAU,IAAIL,KAAK,CAAC+R,SAAS,GAAG/R,KAAK,CAAC+R,SAAS,CAAC,CAAC,GAAGD,aAAa,GAAG,CAAC;EAC3FpE,MAAM,GAAGnM,QAAQ,KAAK,WAAW,IAAIwK,SAAS,IAAI,IAAI,IAAIA,SAAS,CAACjR,MAAM,IAAI,CAAC,GAAGqC,QAAQ,CAAC4O,SAAS,CAAC,CAAC,CAAC,GAAGA,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG2B,MAAM,GAAGA,MAAM;;EAE7I;EACA,IAAIsE,gBAAgB,GAAG5R,KAAK,IAAIiM,SAAS;EACzC,IAAI2F,gBAAgB,EAAE;IACpB,IAAIC,MAAM,GAAGD,gBAAgB,CAAC/O,GAAG,CAAC,CAACgB,KAAK,EAAEiO,KAAK,KAAK;MAClD,IAAIC,YAAY,GAAGP,eAAe,GAAGA,eAAe,CAACnG,OAAO,CAACxH,KAAK,CAAC,GAAGA,KAAK;MAC3E,OAAO;QACLiO,KAAK;QACL;QACA;QACAE,UAAU,EAAEpS,KAAK,CAACmS,YAAY,CAAC,GAAGzE,MAAM;QACxCrS,KAAK,EAAE4I,KAAK;QACZyJ;MACF,CAAC;IACH,CAAC,CAAC;IACF,OAAOuE,MAAM,CAAC1X,MAAM,CAAC8X,GAAG,IAAI,CAACrV,KAAK,CAACqV,GAAG,CAACD,UAAU,CAAC,CAAC;EACrD;;EAEA;EACA,IAAI1K,aAAa,IAAIC,iBAAiB,EAAE;IACtC,OAAOA,iBAAiB,CAAC1E,GAAG,CAAC,CAACgB,KAAK,EAAEiO,KAAK,MAAM;MAC9CE,UAAU,EAAEpS,KAAK,CAACiE,KAAK,CAAC,GAAGyJ,MAAM;MACjCrS,KAAK,EAAE4I,KAAK;MACZiO,KAAK;MACLxE;IACF,CAAC,CAAC,CAAC;EACL;EACA,IAAI1N,KAAK,CAACI,KAAK,EAAE;IACf,OAAOJ,KAAK,CAACI,KAAK,CAACF,SAAS;IAC5B;IAAA,CACC+C,GAAG,CAACgB,KAAK,KAAK;MACbmO,UAAU,EAAEpS,KAAK,CAACiE,KAAK,CAAC,GAAGyJ,MAAM;MACjCrS,KAAK,EAAE4I,KAAK;MACZyJ;IACF,CAAC,CAAC,CAAC;EACL;;EAEA;EACA,OAAO1N,KAAK,CAACd,MAAM,CAAC,CAAC,CAAC+D,GAAG,CAAC,CAACgB,KAAK,EAAEiO,KAAK,MAAM;IAC3CE,UAAU,EAAEpS,KAAK,CAACiE,KAAK,CAAC,GAAGyJ,MAAM;IACjCrS,KAAK,EAAEuW,eAAe,GAAGA,eAAe,CAAC3N,KAAK,CAAC,GAAGA,KAAK;IACvDiO,KAAK;IACLxE;EACF,CAAC,CAAC,CAAC;AACL,CAAC;AACD,OAAO,IAAI4E,iBAAiB,GAAGtW,cAAc,CAAC,CAACG,iBAAiB,EAAEwF,kBAAkB,EAAE+J,mBAAmB,EAAE2D,eAAe,EAAElD,eAAe,EAAE+C,eAAe,EAAEsC,qBAAqB,EAAEE,uBAAuB,EAAEpT,YAAY,CAAC,EAAEuT,gBAAgB,CAAC;AAC9O,OAAO,IAAIU,yBAAyB,GAAGA,CAACtH,MAAM,EAAEvK,IAAI,EAAEV,KAAK,EAAE+L,SAAS,EAAE6F,eAAe,EAAEjK,iBAAiB,EAAEpG,QAAQ,KAAK;EACvH,IAAIb,IAAI,IAAI,IAAI,IAAIV,KAAK,IAAI,IAAI,IAAI+L,SAAS,IAAI,IAAI,IAAIA,SAAS,CAAC,CAAC,CAAC,KAAKA,SAAS,CAAC,CAAC,CAAC,EAAE;IACvF,OAAO9M,SAAS;EAClB;EACA,IAAIyI,aAAa,GAAGlL,iBAAiB,CAACyO,MAAM,EAAE1J,QAAQ,CAAC;EACvD,IAAI;IACFrB;EACF,CAAC,GAAGQ,IAAI;EACR,IAAIgN,MAAM,GAAG,CAAC;EACdA,MAAM,GAAGnM,QAAQ,KAAK,WAAW,IAAI,CAACwK,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACjR,MAAM,KAAK,CAAC,GAAGqC,QAAQ,CAAC4O,SAAS,CAAC,CAAC,CAAC,GAAGA,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG2B,MAAM,GAAGA,MAAM;;EAEhL;EACA,IAAIhG,aAAa,IAAIC,iBAAiB,EAAE;IACtC,OAAOA,iBAAiB,CAAC1E,GAAG,CAAC,CAACgB,KAAK,EAAEiO,KAAK,MAAM;MAC9CE,UAAU,EAAEpS,KAAK,CAACiE,KAAK,CAAC,GAAGyJ,MAAM;MACjCrS,KAAK,EAAE4I,KAAK;MACZiO,KAAK;MACLxE;IACF,CAAC,CAAC,CAAC;EACL;EACA,IAAI1N,KAAK,CAACI,KAAK,EAAE;IACf,OAAOJ,KAAK,CAACI,KAAK,CAACF,SAAS;IAC5B;IAAA,CACC+C,GAAG,CAACgB,KAAK,KAAK;MACbmO,UAAU,EAAEpS,KAAK,CAACiE,KAAK,CAAC,GAAGyJ,MAAM;MACjCrS,KAAK,EAAE4I,KAAK;MACZyJ;IACF,CAAC,CAAC,CAAC;EACL;;EAEA;EACA,OAAO1N,KAAK,CAACd,MAAM,CAAC,CAAC,CAAC+D,GAAG,CAAC,CAACgB,KAAK,EAAEiO,KAAK,MAAM;IAC3CE,UAAU,EAAEpS,KAAK,CAACiE,KAAK,CAAC,GAAGyJ,MAAM;IACjCrS,KAAK,EAAEuW,eAAe,GAAGA,eAAe,CAAC3N,KAAK,CAAC,GAAGA,KAAK;IACvDiO,KAAK;IACLxE;EACF,CAAC,CAAC,CAAC;AACL,CAAC;AACD,OAAO,IAAI8E,0BAA0B,GAAGxW,cAAc,CAAC,CAACG,iBAAiB,EAAEwF,kBAAkB,EAAE0N,eAAe,EAAEH,eAAe,EAAEsC,qBAAqB,EAAEE,uBAAuB,EAAEpT,YAAY,CAAC,EAAEiU,yBAAyB,CAAC;AAC1N,OAAO,IAAIE,mBAAmB,GAAGzW,cAAc,CAACsF,cAAc,EAAE+N,eAAe,EAAE,CAAC3O,IAAI,EAAEV,KAAK,KAAK;EAChG,IAAIU,IAAI,IAAI,IAAI,IAAIV,KAAK,IAAI,IAAI,EAAE;IACjC,OAAOf,SAAS;EAClB;EACA,OAAOrE,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE8F,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;IAChDV;EACF,CAAC,CAAC;AACJ,CAAC,CAAC;AACF,IAAI0S,gBAAgB,GAAG1W,cAAc,CAAC,CAACsF,cAAc,EAAEoK,mBAAmB,EAAEN,gBAAgB,EAAEgE,0BAA0B,CAAC,EAAEvD,oBAAoB,CAAC;AAChJ,OAAO,IAAI8G,oBAAoB,GAAG3W,cAAc,CAAC,CAACwE,KAAK,EAAEoS,SAAS,EAAEnS,MAAM,KAAKW,mBAAmB,CAACZ,KAAK,EAAEC,MAAM,CAAC,EAAEiS,gBAAgB,EAAE,CAAChS,IAAI,EAAEV,KAAK,KAAK;EACpJ,IAAIU,IAAI,IAAI,IAAI,IAAIV,KAAK,IAAI,IAAI,EAAE;IACjC,OAAOf,SAAS;EAClB;EACA,OAAOrE,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE8F,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;IAChDV;EACF,CAAC,CAAC;AACJ,CAAC,CAAC;;AAEF;AACA;AACA;;AAEA,OAAO,IAAI6S,oBAAoB,GAAG7W,cAAc,CAAC,CAACG,iBAAiB,EAAEuB,cAAc,EAAEC,cAAc,CAAC,EAAE,CAACsN,MAAM,EAAE6H,QAAQ,EAAEC,QAAQ,KAAK;EACpI,QAAQ9H,MAAM;IACZ,KAAK,YAAY;MACf;QACE,OAAO6H,QAAQ,CAACE,IAAI,CAACtS,IAAI,IAAIA,IAAI,CAACX,QAAQ,CAAC,GAAG,eAAe,GAAG,eAAe;MACjF;IACF,KAAK,UAAU;MACb;QACE,OAAOgT,QAAQ,CAACC,IAAI,CAACtS,IAAI,IAAIA,IAAI,CAACX,QAAQ,CAAC,GAAG,eAAe,GAAG,eAAe;MACjF;IACF;IACA;IACA,KAAK,SAAS;IACd,KAAK,QAAQ;MACX;QACE,OAAO,eAAe;MACxB;IACF;MACE;QACE,OAAOd,SAAS;MAClB;EACJ;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}