{"ast": null, "code": "function ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nimport { createSelector } from 'reselect';\nimport { computePieSectors } from '../../polar/Pie';\nimport { selectChartDataAndAlwaysIgnoreIndexes } from './dataSelectors';\nimport { selectChartOffsetInternal } from './selectChartOffsetInternal';\nimport { getTooltipNameProp, getValueByDataKey } from '../../util/ChartUtils';\nimport { selectUnfilteredPolarItems } from './polarSelectors';\nvar pickPieSettings = (_state, pieSettings) => pieSettings;\n\n// Keep stable reference to an empty array to prevent re-renders\nvar emptyArray = [];\nvar pickCells = (_state, _pieSettings, cells) => {\n  if ((cells === null || cells === void 0 ? void 0 : cells.length) === 0) {\n    return emptyArray;\n  }\n  return cells;\n};\nexport var selectDisplayedData = createSelector([selectChartDataAndAlwaysIgnoreIndexes, pickPieSettings, pickCells], (_ref, pieSettings, cells) => {\n  var {\n    chartData\n  } = _ref;\n  var displayedData;\n  if ((pieSettings === null || pieSettings === void 0 ? void 0 : pieSettings.data) != null && pieSettings.data.length > 0) {\n    displayedData = pieSettings.data;\n  } else {\n    displayedData = chartData;\n  }\n  if ((!displayedData || !displayedData.length) && cells != null) {\n    displayedData = cells.map(cell => _objectSpread(_objectSpread({}, pieSettings.presentationProps), cell.props));\n  }\n  if (displayedData == null) {\n    return undefined;\n  }\n  return displayedData;\n});\nexport var selectPieLegend = createSelector([selectDisplayedData, pickPieSettings, pickCells], (displayedData, pieSettings, cells) => {\n  if (displayedData == null) {\n    return undefined;\n  }\n  return displayedData.map((entry, i) => {\n    var _cells$i;\n    var name = getValueByDataKey(entry, pieSettings.nameKey, pieSettings.name);\n    var color;\n    if (cells !== null && cells !== void 0 && (_cells$i = cells[i]) !== null && _cells$i !== void 0 && (_cells$i = _cells$i.props) !== null && _cells$i !== void 0 && _cells$i.fill) {\n      color = cells[i].props.fill;\n    } else if (typeof entry === 'object' && entry != null && 'fill' in entry) {\n      color = entry.fill;\n    } else {\n      color = pieSettings.fill;\n    }\n    return {\n      value: getTooltipNameProp(name, pieSettings.dataKey),\n      color,\n      payload: entry,\n      type: pieSettings.legendType\n    };\n  });\n});\nvar selectSynchronisedPieSettings = createSelector([selectUnfilteredPolarItems, pickPieSettings], (graphicalItems, pieSettingsFromProps) => {\n  if (graphicalItems.some(pgis => pgis.type === 'pie' && pieSettingsFromProps.dataKey === pgis.dataKey && pieSettingsFromProps.data === pgis.data)) {\n    return pieSettingsFromProps;\n  }\n  return undefined;\n});\nexport var selectPieSectors = createSelector([selectDisplayedData, selectSynchronisedPieSettings, pickCells, selectChartOffsetInternal], (displayedData, pieSettings, cells, offset) => {\n  if (pieSettings == null || displayedData == null) {\n    return undefined;\n  }\n  return computePieSectors({\n    offset,\n    pieSettings,\n    displayedData,\n    cells\n  });\n});", "map": {"version": 3, "names": ["ownKeys", "e", "r", "t", "Object", "keys", "getOwnPropertySymbols", "o", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "length", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "configurable", "writable", "i", "_toPrimitive", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "createSelector", "computePieSectors", "selectChartDataAndAlwaysIgnoreIndexes", "selectChartOffsetInternal", "getTooltipNameProp", "getValueByDataKey", "selectUnfilteredPolarItems", "pickPieSettings", "_state", "pieSettings", "emptyArray", "pick<PERSON>ells", "_pieSettings", "cells", "selectDisplayedData", "_ref", "chartData", "displayedData", "data", "map", "cell", "presentationProps", "props", "undefined", "selectPieLegend", "entry", "_cells$i", "name", "<PERSON><PERSON><PERSON>", "color", "fill", "dataKey", "payload", "type", "legendType", "selectSynchronisedPieSettings", "graphicalItems", "pieSettingsFromProps", "some", "pgis", "selectPieSectors", "offset"], "sources": ["D:/menasa/frontend/node_modules/recharts/es6/state/selectors/pieSelectors.js"], "sourcesContent": ["function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport { createSelector } from 'reselect';\nimport { computePieSectors } from '../../polar/Pie';\nimport { selectChartDataAndAlwaysIgnoreIndexes } from './dataSelectors';\nimport { selectChartOffsetInternal } from './selectChartOffsetInternal';\nimport { getTooltipNameProp, getValueByDataKey } from '../../util/ChartUtils';\nimport { selectUnfilteredPolarItems } from './polarSelectors';\nvar pickPieSettings = (_state, pieSettings) => pieSettings;\n\n// Keep stable reference to an empty array to prevent re-renders\nvar emptyArray = [];\nvar pickCells = (_state, _pieSettings, cells) => {\n  if ((cells === null || cells === void 0 ? void 0 : cells.length) === 0) {\n    return emptyArray;\n  }\n  return cells;\n};\nexport var selectDisplayedData = createSelector([selectChartDataAndAlwaysIgnoreIndexes, pickPieSettings, pickCells], (_ref, pieSettings, cells) => {\n  var {\n    chartData\n  } = _ref;\n  var displayedData;\n  if ((pieSettings === null || pieSettings === void 0 ? void 0 : pieSettings.data) != null && pieSettings.data.length > 0) {\n    displayedData = pieSettings.data;\n  } else {\n    displayedData = chartData;\n  }\n  if ((!displayedData || !displayedData.length) && cells != null) {\n    displayedData = cells.map(cell => _objectSpread(_objectSpread({}, pieSettings.presentationProps), cell.props));\n  }\n  if (displayedData == null) {\n    return undefined;\n  }\n  return displayedData;\n});\nexport var selectPieLegend = createSelector([selectDisplayedData, pickPieSettings, pickCells], (displayedData, pieSettings, cells) => {\n  if (displayedData == null) {\n    return undefined;\n  }\n  return displayedData.map((entry, i) => {\n    var _cells$i;\n    var name = getValueByDataKey(entry, pieSettings.nameKey, pieSettings.name);\n    var color;\n    if (cells !== null && cells !== void 0 && (_cells$i = cells[i]) !== null && _cells$i !== void 0 && (_cells$i = _cells$i.props) !== null && _cells$i !== void 0 && _cells$i.fill) {\n      color = cells[i].props.fill;\n    } else if (typeof entry === 'object' && entry != null && 'fill' in entry) {\n      color = entry.fill;\n    } else {\n      color = pieSettings.fill;\n    }\n    return {\n      value: getTooltipNameProp(name, pieSettings.dataKey),\n      color,\n      payload: entry,\n      type: pieSettings.legendType\n    };\n  });\n});\nvar selectSynchronisedPieSettings = createSelector([selectUnfilteredPolarItems, pickPieSettings], (graphicalItems, pieSettingsFromProps) => {\n  if (graphicalItems.some(pgis => pgis.type === 'pie' && pieSettingsFromProps.dataKey === pgis.dataKey && pieSettingsFromProps.data === pgis.data)) {\n    return pieSettingsFromProps;\n  }\n  return undefined;\n});\nexport var selectPieSectors = createSelector([selectDisplayedData, selectSynchronisedPieSettings, pickCells, selectChartOffsetInternal], (displayedData, pieSettings, cells, offset) => {\n  if (pieSettings == null || displayedData == null) {\n    return undefined;\n  }\n  return computePieSectors({\n    offset,\n    pieSettings,\n    displayedData,\n    cells\n  });\n});"], "mappings": "AAAA,SAASA,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAIC,CAAC,GAAGC,MAAM,CAACC,IAAI,CAACJ,CAAC,CAAC;EAAE,IAAIG,MAAM,CAACE,qBAAqB,EAAE;IAAE,IAAIC,CAAC,GAAGH,MAAM,CAACE,qBAAqB,CAACL,CAAC,CAAC;IAAEC,CAAC,KAAKK,CAAC,GAAGA,CAAC,CAACC,MAAM,CAAC,UAAUN,CAAC,EAAE;MAAE,OAAOE,MAAM,CAACK,wBAAwB,CAACR,CAAC,EAAEC,CAAC,CAAC,CAACQ,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEP,CAAC,CAACQ,IAAI,CAACC,KAAK,CAACT,CAAC,EAAEI,CAAC,CAAC;EAAE;EAAE,OAAOJ,CAAC;AAAE;AAC9P,SAASU,aAAaA,CAACZ,CAAC,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,SAAS,CAACC,MAAM,EAAEb,CAAC,EAAE,EAAE;IAAE,IAAIC,CAAC,GAAG,IAAI,IAAIW,SAAS,CAACZ,CAAC,CAAC,GAAGY,SAAS,CAACZ,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGF,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACa,OAAO,CAAC,UAAUd,CAAC,EAAE;MAAEe,eAAe,CAAChB,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGE,MAAM,CAACc,yBAAyB,GAAGd,MAAM,CAACe,gBAAgB,CAAClB,CAAC,EAAEG,MAAM,CAACc,yBAAyB,CAACf,CAAC,CAAC,CAAC,GAAGH,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,CAAC,CAACa,OAAO,CAAC,UAAUd,CAAC,EAAE;MAAEE,MAAM,CAACgB,cAAc,CAACnB,CAAC,EAAEC,CAAC,EAAEE,MAAM,CAACK,wBAAwB,CAACN,CAAC,EAAED,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AACtb,SAASgB,eAAeA,CAAChB,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAAE,OAAO,CAACD,CAAC,GAAGmB,cAAc,CAACnB,CAAC,CAAC,KAAKD,CAAC,GAAGG,MAAM,CAACgB,cAAc,CAACnB,CAAC,EAAEC,CAAC,EAAE;IAAEoB,KAAK,EAAEnB,CAAC;IAAEO,UAAU,EAAE,CAAC,CAAC;IAAEa,YAAY,EAAE,CAAC,CAAC;IAAEC,QAAQ,EAAE,CAAC;EAAE,CAAC,CAAC,GAAGvB,CAAC,CAACC,CAAC,CAAC,GAAGC,CAAC,EAAEF,CAAC;AAAE;AACnL,SAASoB,cAAcA,CAAClB,CAAC,EAAE;EAAE,IAAIsB,CAAC,GAAGC,YAAY,CAACvB,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAI,OAAOsB,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC1G,SAASC,YAAYA,CAACvB,CAAC,EAAED,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAI,OAAOC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIF,CAAC,GAAGE,CAAC,CAACwB,MAAM,CAACC,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAK3B,CAAC,EAAE;IAAE,IAAIwB,CAAC,GAAGxB,CAAC,CAAC4B,IAAI,CAAC1B,CAAC,EAAED,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAI,OAAOuB,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAIK,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAK5B,CAAC,GAAG6B,MAAM,GAAGC,MAAM,EAAE7B,CAAC,CAAC;AAAE;AACvT,SAAS8B,cAAc,QAAQ,UAAU;AACzC,SAASC,iBAAiB,QAAQ,iBAAiB;AACnD,SAASC,qCAAqC,QAAQ,iBAAiB;AACvE,SAASC,yBAAyB,QAAQ,6BAA6B;AACvE,SAASC,kBAAkB,EAAEC,iBAAiB,QAAQ,uBAAuB;AAC7E,SAASC,0BAA0B,QAAQ,kBAAkB;AAC7D,IAAIC,eAAe,GAAGA,CAACC,MAAM,EAAEC,WAAW,KAAKA,WAAW;;AAE1D;AACA,IAAIC,UAAU,GAAG,EAAE;AACnB,IAAIC,SAAS,GAAGA,CAACH,MAAM,EAAEI,YAAY,EAAEC,KAAK,KAAK;EAC/C,IAAI,CAACA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAAC/B,MAAM,MAAM,CAAC,EAAE;IACtE,OAAO4B,UAAU;EACnB;EACA,OAAOG,KAAK;AACd,CAAC;AACD,OAAO,IAAIC,mBAAmB,GAAGd,cAAc,CAAC,CAACE,qCAAqC,EAAEK,eAAe,EAAEI,SAAS,CAAC,EAAE,CAACI,IAAI,EAAEN,WAAW,EAAEI,KAAK,KAAK;EACjJ,IAAI;IACFG;EACF,CAAC,GAAGD,IAAI;EACR,IAAIE,aAAa;EACjB,IAAI,CAACR,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACS,IAAI,KAAK,IAAI,IAAIT,WAAW,CAACS,IAAI,CAACpC,MAAM,GAAG,CAAC,EAAE;IACvHmC,aAAa,GAAGR,WAAW,CAACS,IAAI;EAClC,CAAC,MAAM;IACLD,aAAa,GAAGD,SAAS;EAC3B;EACA,IAAI,CAAC,CAACC,aAAa,IAAI,CAACA,aAAa,CAACnC,MAAM,KAAK+B,KAAK,IAAI,IAAI,EAAE;IAC9DI,aAAa,GAAGJ,KAAK,CAACM,GAAG,CAACC,IAAI,IAAIxC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6B,WAAW,CAACY,iBAAiB,CAAC,EAAED,IAAI,CAACE,KAAK,CAAC,CAAC;EAChH;EACA,IAAIL,aAAa,IAAI,IAAI,EAAE;IACzB,OAAOM,SAAS;EAClB;EACA,OAAON,aAAa;AACtB,CAAC,CAAC;AACF,OAAO,IAAIO,eAAe,GAAGxB,cAAc,CAAC,CAACc,mBAAmB,EAAEP,eAAe,EAAEI,SAAS,CAAC,EAAE,CAACM,aAAa,EAAER,WAAW,EAAEI,KAAK,KAAK;EACpI,IAAII,aAAa,IAAI,IAAI,EAAE;IACzB,OAAOM,SAAS;EAClB;EACA,OAAON,aAAa,CAACE,GAAG,CAAC,CAACM,KAAK,EAAEjC,CAAC,KAAK;IACrC,IAAIkC,QAAQ;IACZ,IAAIC,IAAI,GAAGtB,iBAAiB,CAACoB,KAAK,EAAEhB,WAAW,CAACmB,OAAO,EAAEnB,WAAW,CAACkB,IAAI,CAAC;IAC1E,IAAIE,KAAK;IACT,IAAIhB,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,IAAI,CAACa,QAAQ,GAAGb,KAAK,CAACrB,CAAC,CAAC,MAAM,IAAI,IAAIkC,QAAQ,KAAK,KAAK,CAAC,IAAI,CAACA,QAAQ,GAAGA,QAAQ,CAACJ,KAAK,MAAM,IAAI,IAAII,QAAQ,KAAK,KAAK,CAAC,IAAIA,QAAQ,CAACI,IAAI,EAAE;MAC/KD,KAAK,GAAGhB,KAAK,CAACrB,CAAC,CAAC,CAAC8B,KAAK,CAACQ,IAAI;IAC7B,CAAC,MAAM,IAAI,OAAOL,KAAK,KAAK,QAAQ,IAAIA,KAAK,IAAI,IAAI,IAAI,MAAM,IAAIA,KAAK,EAAE;MACxEI,KAAK,GAAGJ,KAAK,CAACK,IAAI;IACpB,CAAC,MAAM;MACLD,KAAK,GAAGpB,WAAW,CAACqB,IAAI;IAC1B;IACA,OAAO;MACLzC,KAAK,EAAEe,kBAAkB,CAACuB,IAAI,EAAElB,WAAW,CAACsB,OAAO,CAAC;MACpDF,KAAK;MACLG,OAAO,EAAEP,KAAK;MACdQ,IAAI,EAAExB,WAAW,CAACyB;IACpB,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACF,IAAIC,6BAA6B,GAAGnC,cAAc,CAAC,CAACM,0BAA0B,EAAEC,eAAe,CAAC,EAAE,CAAC6B,cAAc,EAAEC,oBAAoB,KAAK;EAC1I,IAAID,cAAc,CAACE,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACN,IAAI,KAAK,KAAK,IAAII,oBAAoB,CAACN,OAAO,KAAKQ,IAAI,CAACR,OAAO,IAAIM,oBAAoB,CAACnB,IAAI,KAAKqB,IAAI,CAACrB,IAAI,CAAC,EAAE;IAChJ,OAAOmB,oBAAoB;EAC7B;EACA,OAAOd,SAAS;AAClB,CAAC,CAAC;AACF,OAAO,IAAIiB,gBAAgB,GAAGxC,cAAc,CAAC,CAACc,mBAAmB,EAAEqB,6BAA6B,EAAExB,SAAS,EAAER,yBAAyB,CAAC,EAAE,CAACc,aAAa,EAAER,WAAW,EAAEI,KAAK,EAAE4B,MAAM,KAAK;EACtL,IAAIhC,WAAW,IAAI,IAAI,IAAIQ,aAAa,IAAI,IAAI,EAAE;IAChD,OAAOM,SAAS;EAClB;EACA,OAAOtB,iBAAiB,CAAC;IACvBwC,MAAM;IACNhC,WAAW;IACXQ,aAAa;IACbJ;EACF,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}