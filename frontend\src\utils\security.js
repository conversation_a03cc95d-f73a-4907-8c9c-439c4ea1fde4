// مكتبة الأمان المتقدمة

import CryptoJS from 'crypto-js';

// مفتاح التشفير (يجب أن يكون في متغيرات البيئة في الإنتاج)
const ENCRYPTION_KEY = process.env.REACT_APP_ENCRYPTION_KEY || 'default-key-change-in-production';

// فئة إدارة الأمان
export class SecurityManager {
  constructor() {
    this.sessionTimeout = 30 * 60 * 1000; // 30 دقيقة
    this.maxLoginAttempts = 5;
    this.lockoutDuration = 15 * 60 * 1000; // 15 دقيقة
    this.initializeSecurityMonitoring();
  }

  // تشفير البيانات الحساسة
  encrypt(data) {
    try {
      const encrypted = CryptoJS.AES.encrypt(JSON.stringify(data), ENCRYPTION_KEY).toString();
      return encrypted;
    } catch (error) {
      console.error('Encryption failed:', error);
      throw new Error('فشل في تشفير البيانات');
    }
  }

  // فك تشفير البيانات
  decrypt(encryptedData) {
    try {
      const bytes = CryptoJS.AES.decrypt(encryptedData, ENCRYPTION_KEY);
      const decrypted = bytes.toString(CryptoJS.enc.Utf8);
      return JSON.parse(decrypted);
    } catch (error) {
      console.error('Decryption failed:', error);
      throw new Error('فشل في فك تشفير البيانات');
    }
  }

  // تشفير كلمات المرور
  hashPassword(password, salt = null) {
    const saltToUse = salt || CryptoJS.lib.WordArray.random(128/8).toString();
    const hash = CryptoJS.PBKDF2(password, saltToUse, {
      keySize: 256/32,
      iterations: 10000
    }).toString();
    
    return {
      hash: hash,
      salt: saltToUse
    };
  }

  // التحقق من كلمة المرور
  verifyPassword(password, hash, salt) {
    const verifyHash = CryptoJS.PBKDF2(password, salt, {
      keySize: 256/32,
      iterations: 10000
    }).toString();
    
    return verifyHash === hash;
  }

  // إنشاء رمز مميز آمن
  generateSecureToken(length = 32) {
    const array = new Uint8Array(length);
    window.crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  // التحقق من قوة كلمة المرور
  checkPasswordStrength(password) {
    const checks = {
      length: password.length >= 8,
      lowercase: /[a-z]/.test(password),
      uppercase: /[A-Z]/.test(password),
      numbers: /\d/.test(password),
      symbols: /[^A-Za-z0-9]/.test(password),
      noCommonPatterns: !this.hasCommonPatterns(password)
    };

    const score = Object.values(checks).filter(Boolean).length;
    
    return {
      score,
      checks,
      strength: this.getPasswordStrengthLevel(score),
      suggestions: this.getPasswordSuggestions(checks)
    };
  }

  // التحقق من الأنماط الشائعة في كلمات المرور
  hasCommonPatterns(password) {
    const commonPatterns = [
      /123456/,
      /password/i,
      /qwerty/i,
      /admin/i,
      /letmein/i,
      /welcome/i,
      /monkey/i,
      /dragon/i
    ];

    return commonPatterns.some(pattern => pattern.test(password));
  }

  // تحديد مستوى قوة كلمة المرور
  getPasswordStrengthLevel(score) {
    if (score <= 2) return { level: 'ضعيف جداً', color: '#ef4444' };
    if (score <= 3) return { level: 'ضعيف', color: '#f97316' };
    if (score <= 4) return { level: 'متوسط', color: '#eab308' };
    if (score <= 5) return { level: 'جيد', color: '#22c55e' };
    return { level: 'قوي جداً', color: '#16a34a' };
  }

  // اقتراحات لتحسين كلمة المرور
  getPasswordSuggestions(checks) {
    const suggestions = [];
    
    if (!checks.length) suggestions.push('استخدم 8 أحرف على الأقل');
    if (!checks.lowercase) suggestions.push('أضف أحرف صغيرة');
    if (!checks.uppercase) suggestions.push('أضف أحرف كبيرة');
    if (!checks.numbers) suggestions.push('أضف أرقام');
    if (!checks.symbols) suggestions.push('أضف رموز خاصة');
    if (!checks.noCommonPatterns) suggestions.push('تجنب الكلمات الشائعة');
    
    return suggestions;
  }

  // إدارة محاولات تسجيل الدخول
  trackLoginAttempt(email, success = false) {
    const key = `login_attempts_${email}`;
    const attempts = JSON.parse(localStorage.getItem(key) || '[]');
    
    if (success) {
      // مسح المحاولات عند النجاح
      localStorage.removeItem(key);
      return { allowed: true, remainingAttempts: this.maxLoginAttempts };
    }

    // إضافة محاولة فاشلة
    attempts.push(Date.now());
    
    // إزالة المحاولات القديمة (أكثر من ساعة)
    const oneHourAgo = Date.now() - (60 * 60 * 1000);
    const recentAttempts = attempts.filter(time => time > oneHourAgo);
    
    localStorage.setItem(key, JSON.stringify(recentAttempts));
    
    const remainingAttempts = this.maxLoginAttempts - recentAttempts.length;
    const isLocked = recentAttempts.length >= this.maxLoginAttempts;
    
    return {
      allowed: !isLocked,
      remainingAttempts: Math.max(0, remainingAttempts),
      lockoutTime: isLocked ? this.lockoutDuration : 0
    };
  }

  // التحقق من انتهاء الجلسة
  checkSessionTimeout() {
    const lastActivity = localStorage.getItem('lastActivity');
    if (!lastActivity) return false;
    
    const timeSinceLastActivity = Date.now() - parseInt(lastActivity);
    return timeSinceLastActivity > this.sessionTimeout;
  }

  // تحديث وقت النشاط الأخير
  updateLastActivity() {
    localStorage.setItem('lastActivity', Date.now().toString());
  }

  // تنظيف البيانات الحساسة عند تسجيل الخروج
  clearSensitiveData() {
    const keysToRemove = [
      'lastActivity',
      'userPreferences',
      'tempData'
    ];
    
    keysToRemove.forEach(key => {
      localStorage.removeItem(key);
      sessionStorage.removeItem(key);
    });
  }

  // التحقق من سلامة البيانات
  validateDataIntegrity(data, expectedHash) {
    const calculatedHash = CryptoJS.SHA256(JSON.stringify(data)).toString();
    return calculatedHash === expectedHash;
  }

  // إنشاء بصمة للجهاز
  generateDeviceFingerprint() {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    ctx.textBaseline = 'top';
    ctx.font = '14px Arial';
    ctx.fillText('Device fingerprint', 2, 2);
    
    const fingerprint = {
      userAgent: navigator.userAgent,
      language: navigator.language,
      platform: navigator.platform,
      screen: `${window.screen.width}x${window.screen.height}`,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      canvas: canvas.toDataURL(),
      webgl: this.getWebGLFingerprint()
    };
    
    return CryptoJS.SHA256(JSON.stringify(fingerprint)).toString();
  }

  // الحصول على بصمة WebGL
  getWebGLFingerprint() {
    try {
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
      
      if (!gl) return 'no-webgl';
      
      const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
      const vendor = gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL);
      const renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL);
      
      return `${vendor}~${renderer}`;
    } catch (error) {
      return 'webgl-error';
    }
  }

  // مراقبة الأنشطة المشبوهة
  initializeSecurityMonitoring() {
    // مراقبة محاولات الوصول المتكررة
    this.monitorRapidRequests();
    
    // مراقبة تغييرات الجلسة
    this.monitorSessionChanges();
    
    // مراقبة أدوات المطور
    this.monitorDevTools();
  }

  // مراقبة الطلبات السريعة المتتالية
  monitorRapidRequests() {
    let requestCount = 0;
    const resetInterval = 60000; // دقيقة واحدة
    
    setInterval(() => {
      requestCount = 0;
    }, resetInterval);
    
    // يمكن استخدامها في interceptors
    window.securityManager = {
      ...window.securityManager,
      checkRateLimit: () => {
        requestCount++;
        if (requestCount > 100) { // 100 طلب في الدقيقة
          console.warn('Rate limit exceeded');
          return false;
        }
        return true;
      }
    };
  }

  // مراقبة تغييرات الجلسة
  monitorSessionChanges() {
    let lastFingerprint = this.generateDeviceFingerprint();
    
    setInterval(() => {
      const currentFingerprint = this.generateDeviceFingerprint();
      if (currentFingerprint !== lastFingerprint) {
        console.warn('Device fingerprint changed - possible session hijacking');
        // يمكن إضافة إجراءات أمنية هنا
      }
      lastFingerprint = currentFingerprint;
    }, 30000); // كل 30 ثانية
  }

  // مراقبة أدوات المطور
  monitorDevTools() {
    let devtools = { open: false, orientation: null };
    
    setInterval(() => {
      if (window.outerHeight - window.innerHeight > 200 || 
          window.outerWidth - window.innerWidth > 200) {
        if (!devtools.open) {
          devtools.open = true;
          console.warn('Developer tools detected');
          // يمكن إضافة إجراءات أمنية هنا
        }
      } else {
        devtools.open = false;
      }
    }, 1000);
  }

  // تنظيف الذاكرة من البيانات الحساسة
  secureCleanup() {
    // مسح المتغيرات الحساسة
    if (window.sensitiveData) {
      for (let key in window.sensitiveData) {
        delete window.sensitiveData[key];
      }
    }
    
    // تشغيل garbage collection إذا كان متاحاً
    if (window.gc) {
      window.gc();
    }
  }
}

// إنشاء مثيل واحد من مدير الأمان
export const securityManager = new SecurityManager();

// دوال مساعدة للاستخدام السريع
export const encrypt = (data) => securityManager.encrypt(data);
export const decrypt = (data) => securityManager.decrypt(data);
export const generateToken = (length) => securityManager.generateSecureToken(length);
export const checkPasswordStrength = (password) => securityManager.checkPasswordStrength(password);
export const trackLoginAttempt = (email, success) => securityManager.trackLoginAttempt(email, success);

// تصدير افتراضي
export default securityManager;
