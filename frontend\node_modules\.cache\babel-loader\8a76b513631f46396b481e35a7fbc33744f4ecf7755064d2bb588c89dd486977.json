{"ast": null, "code": "import { numericPatterns } from \"../constants.js\";\nimport { Parser } from \"../Parser.js\";\nimport { mapValue, parseNDigits, parseNumericPattern } from \"../utils.js\";\nexport class StandAloneMonthParser extends Parser {\n  priority = 110;\n  parse(dateString, token, match) {\n    const valueCallback = value => value - 1;\n    switch (token) {\n      // 1, 2, ..., 12\n      case \"L\":\n        return mapValue(parseNumericPattern(numericPatterns.month, dateString), valueCallback);\n      // 01, 02, ..., 12\n      case \"LL\":\n        return mapValue(parseNDigits(2, dateString), valueCallback);\n      // 1st, 2nd, ..., 12th\n      case \"Lo\":\n        return mapValue(match.ordinalNumber(dateString, {\n          unit: \"month\"\n        }), valueCallback);\n      // Jan, Feb, ..., Dec\n      case \"LLL\":\n        return match.month(dateString, {\n          width: \"abbreviated\",\n          context: \"standalone\"\n        }) || match.month(dateString, {\n          width: \"narrow\",\n          context: \"standalone\"\n        });\n\n      // J, F, ..., D\n      case \"LLLLL\":\n        return match.month(dateString, {\n          width: \"narrow\",\n          context: \"standalone\"\n        });\n      // January, February, ..., December\n      case \"LLLL\":\n      default:\n        return match.month(dateString, {\n          width: \"wide\",\n          context: \"standalone\"\n        }) || match.month(dateString, {\n          width: \"abbreviated\",\n          context: \"standalone\"\n        }) || match.month(dateString, {\n          width: \"narrow\",\n          context: \"standalone\"\n        });\n    }\n  }\n  validate(_date, value) {\n    return value >= 0 && value <= 11;\n  }\n  set(date, _flags, value) {\n    date.setMonth(value, 1);\n    date.setHours(0, 0, 0, 0);\n    return date;\n  }\n  incompatibleTokens = [\"Y\", \"R\", \"q\", \"Q\", \"M\", \"w\", \"I\", \"D\", \"i\", \"e\", \"c\", \"t\", \"T\"];\n}", "map": {"version": 3, "names": ["numericPatterns", "<PERSON><PERSON><PERSON>", "mapValue", "parseNDigits", "parseNumericPattern", "StandAloneMonthParser", "priority", "parse", "dateString", "token", "match", "valueCallback", "value", "month", "ordinalNumber", "unit", "width", "context", "validate", "_date", "set", "date", "_flags", "setMonth", "setHours", "incompatibleTokens"], "sources": ["D:/menasa/frontend/node_modules/date-fns/parse/_lib/parsers/StandAloneMonthParser.js"], "sourcesContent": ["import { numericPatterns } from \"../constants.js\";\nimport { Parser } from \"../Parser.js\";\n\nimport { mapValue, parseNDigits, parseNumericPattern } from \"../utils.js\";\n\nexport class StandAloneMonthParser extends Parser {\n  priority = 110;\n\n  parse(dateString, token, match) {\n    const valueCallback = (value) => value - 1;\n\n    switch (token) {\n      // 1, 2, ..., 12\n      case \"L\":\n        return mapValue(\n          parseNumericPattern(numericPatterns.month, dateString),\n          valueCallback,\n        );\n      // 01, 02, ..., 12\n      case \"LL\":\n        return mapValue(parseNDigits(2, dateString), valueCallback);\n      // 1st, 2nd, ..., 12th\n      case \"Lo\":\n        return mapValue(\n          match.ordinalNumber(dateString, {\n            unit: \"month\",\n          }),\n          valueCallback,\n        );\n      // Jan, Feb, ..., Dec\n      case \"LLL\":\n        return (\n          match.month(dateString, {\n            width: \"abbreviated\",\n            context: \"standalone\",\n          }) ||\n          match.month(dateString, { width: \"narrow\", context: \"standalone\" })\n        );\n\n      // J, F, ..., D\n      case \"LLLLL\":\n        return match.month(dateString, {\n          width: \"narrow\",\n          context: \"standalone\",\n        });\n      // January, February, ..., December\n      case \"LLLL\":\n      default:\n        return (\n          match.month(dateString, { width: \"wide\", context: \"standalone\" }) ||\n          match.month(dateString, {\n            width: \"abbreviated\",\n            context: \"standalone\",\n          }) ||\n          match.month(dateString, { width: \"narrow\", context: \"standalone\" })\n        );\n    }\n  }\n\n  validate(_date, value) {\n    return value >= 0 && value <= 11;\n  }\n\n  set(date, _flags, value) {\n    date.setMonth(value, 1);\n    date.setHours(0, 0, 0, 0);\n    return date;\n  }\n\n  incompatibleTokens = [\n    \"Y\",\n    \"R\",\n    \"q\",\n    \"Q\",\n    \"M\",\n    \"w\",\n    \"I\",\n    \"D\",\n    \"i\",\n    \"e\",\n    \"c\",\n    \"t\",\n    \"T\",\n  ];\n}\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,iBAAiB;AACjD,SAASC,MAAM,QAAQ,cAAc;AAErC,SAASC,QAAQ,EAAEC,YAAY,EAAEC,mBAAmB,QAAQ,aAAa;AAEzE,OAAO,MAAMC,qBAAqB,SAASJ,MAAM,CAAC;EAChDK,QAAQ,GAAG,GAAG;EAEdC,KAAKA,CAACC,UAAU,EAAEC,KAAK,EAAEC,KAAK,EAAE;IAC9B,MAAMC,aAAa,GAAIC,KAAK,IAAKA,KAAK,GAAG,CAAC;IAE1C,QAAQH,KAAK;MACX;MACA,KAAK,GAAG;QACN,OAAOP,QAAQ,CACbE,mBAAmB,CAACJ,eAAe,CAACa,KAAK,EAAEL,UAAU,CAAC,EACtDG,aACF,CAAC;MACH;MACA,KAAK,IAAI;QACP,OAAOT,QAAQ,CAACC,YAAY,CAAC,CAAC,EAAEK,UAAU,CAAC,EAAEG,aAAa,CAAC;MAC7D;MACA,KAAK,IAAI;QACP,OAAOT,QAAQ,CACbQ,KAAK,CAACI,aAAa,CAACN,UAAU,EAAE;UAC9BO,IAAI,EAAE;QACR,CAAC,CAAC,EACFJ,aACF,CAAC;MACH;MACA,KAAK,KAAK;QACR,OACED,KAAK,CAACG,KAAK,CAACL,UAAU,EAAE;UACtBQ,KAAK,EAAE,aAAa;UACpBC,OAAO,EAAE;QACX,CAAC,CAAC,IACFP,KAAK,CAACG,KAAK,CAACL,UAAU,EAAE;UAAEQ,KAAK,EAAE,QAAQ;UAAEC,OAAO,EAAE;QAAa,CAAC,CAAC;;MAGvE;MACA,KAAK,OAAO;QACV,OAAOP,KAAK,CAACG,KAAK,CAACL,UAAU,EAAE;UAC7BQ,KAAK,EAAE,QAAQ;UACfC,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;MACA,KAAK,MAAM;MACX;QACE,OACEP,KAAK,CAACG,KAAK,CAACL,UAAU,EAAE;UAAEQ,KAAK,EAAE,MAAM;UAAEC,OAAO,EAAE;QAAa,CAAC,CAAC,IACjEP,KAAK,CAACG,KAAK,CAACL,UAAU,EAAE;UACtBQ,KAAK,EAAE,aAAa;UACpBC,OAAO,EAAE;QACX,CAAC,CAAC,IACFP,KAAK,CAACG,KAAK,CAACL,UAAU,EAAE;UAAEQ,KAAK,EAAE,QAAQ;UAAEC,OAAO,EAAE;QAAa,CAAC,CAAC;IAEzE;EACF;EAEAC,QAAQA,CAACC,KAAK,EAAEP,KAAK,EAAE;IACrB,OAAOA,KAAK,IAAI,CAAC,IAAIA,KAAK,IAAI,EAAE;EAClC;EAEAQ,GAAGA,CAACC,IAAI,EAAEC,MAAM,EAAEV,KAAK,EAAE;IACvBS,IAAI,CAACE,QAAQ,CAACX,KAAK,EAAE,CAAC,CAAC;IACvBS,IAAI,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACzB,OAAOH,IAAI;EACb;EAEAI,kBAAkB,GAAG,CACnB,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,CACJ;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}