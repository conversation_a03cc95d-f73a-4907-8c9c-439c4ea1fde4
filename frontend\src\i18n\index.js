import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

const resources = {
  ar: {
    translation: {
      "dashboard": "لوحة التحكم",
      "files": "الملفات",
      "upload": "رفع ملف",
      "users": "المستخدمين",
      "search": "بحث...",
      "download": "تحميل",
      "preview": "معاينة",
      "favorite": "مفضل",
      "rating": "التقييم",
      "subject": "المادة",
      "semester": "الفصل",
      "type": "النوع"
    }
  },
  en: {
    translation: {
      "dashboard": "Dashboard",
      "files": "Files",
      "upload": "Upload",
      "users": "Users",
      "search": "Search...",
      "download": "Download",
      "preview": "Preview",
      "favorite": "Favorite",
      "rating": "Rating",
      "subject": "Subject",
      "semester": "Semester",
      "type": "Type"
    }
  }
};

i18n
  .use(initReactI18next)
  .init({
    resources,
    lng: 'ar',
    fallbackLng: 'en',
    interpolation: {
      escapeValue: false
    }
  });

export default i18n;