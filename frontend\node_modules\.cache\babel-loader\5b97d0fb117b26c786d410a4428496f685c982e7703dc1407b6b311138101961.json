{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nfunction getPriority(a) {\n  if (typeof a === 'symbol') {\n    return 1;\n  }\n  if (a === null) {\n    return 2;\n  }\n  if (a === undefined) {\n    return 3;\n  }\n  if (a !== a) {\n    return 4;\n  }\n  return 0;\n}\nconst compareValues = (a, b, order) => {\n  if (a !== b) {\n    const aPriority = getPriority(a);\n    const bPriority = getPriority(b);\n    if (aPriority === bPriority && aPriority === 0) {\n      if (a < b) {\n        return order === 'desc' ? 1 : -1;\n      }\n      if (a > b) {\n        return order === 'desc' ? -1 : 1;\n      }\n    }\n    return order === 'desc' ? bPriority - aPriority : aPriority - bPriority;\n  }\n  return 0;\n};\nexports.compareValues = compareValues;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "getPriority", "a", "undefined", "compareValues", "b", "order", "aPriority", "bPriority"], "sources": ["D:/menasa/frontend/node_modules/es-toolkit/dist/compat/_internal/compareValues.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nfunction getPriority(a) {\n    if (typeof a === 'symbol') {\n        return 1;\n    }\n    if (a === null) {\n        return 2;\n    }\n    if (a === undefined) {\n        return 3;\n    }\n    if (a !== a) {\n        return 4;\n    }\n    return 0;\n}\nconst compareValues = (a, b, order) => {\n    if (a !== b) {\n        const aPriority = getPriority(a);\n        const bPriority = getPriority(b);\n        if (aPriority === bPriority && aPriority === 0) {\n            if (a < b) {\n                return order === 'desc' ? 1 : -1;\n            }\n            if (a > b) {\n                return order === 'desc' ? -1 : 1;\n            }\n        }\n        return order === 'desc' ? bPriority - aPriority : aPriority - bPriority;\n    }\n    return 0;\n};\n\nexports.compareValues = compareValues;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,SAASC,WAAWA,CAACC,CAAC,EAAE;EACpB,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;IACvB,OAAO,CAAC;EACZ;EACA,IAAIA,CAAC,KAAK,IAAI,EAAE;IACZ,OAAO,CAAC;EACZ;EACA,IAAIA,CAAC,KAAKC,SAAS,EAAE;IACjB,OAAO,CAAC;EACZ;EACA,IAAID,CAAC,KAAKA,CAAC,EAAE;IACT,OAAO,CAAC;EACZ;EACA,OAAO,CAAC;AACZ;AACA,MAAME,aAAa,GAAGA,CAACF,CAAC,EAAEG,CAAC,EAAEC,KAAK,KAAK;EACnC,IAAIJ,CAAC,KAAKG,CAAC,EAAE;IACT,MAAME,SAAS,GAAGN,WAAW,CAACC,CAAC,CAAC;IAChC,MAAMM,SAAS,GAAGP,WAAW,CAACI,CAAC,CAAC;IAChC,IAAIE,SAAS,KAAKC,SAAS,IAAID,SAAS,KAAK,CAAC,EAAE;MAC5C,IAAIL,CAAC,GAAGG,CAAC,EAAE;QACP,OAAOC,KAAK,KAAK,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MACpC;MACA,IAAIJ,CAAC,GAAGG,CAAC,EAAE;QACP,OAAOC,KAAK,KAAK,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC;MACpC;IACJ;IACA,OAAOA,KAAK,KAAK,MAAM,GAAGE,SAAS,GAAGD,SAAS,GAAGA,SAAS,GAAGC,SAAS;EAC3E;EACA,OAAO,CAAC;AACZ,CAAC;AAEDX,OAAO,CAACO,aAAa,GAAGA,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}