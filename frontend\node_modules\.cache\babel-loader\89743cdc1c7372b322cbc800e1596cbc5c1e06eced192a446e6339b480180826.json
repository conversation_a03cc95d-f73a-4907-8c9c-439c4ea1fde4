{"ast": null, "code": "import { createAction, createListenerMiddleware } from '@reduxjs/toolkit';\nimport { selectActiveLabel, selectActiveTooltipCoordinate, selectActiveTooltipDataKey, selectActiveTooltipIndex, selectIsTooltipActive } from './selectors/tooltipSelectors';\nexport var externalEventAction = createAction('externalEvent');\nexport var externalEventsMiddleware = createListenerMiddleware();\nexternalEventsMiddleware.startListening({\n  actionCreator: externalEventAction,\n  effect: (action, listenerApi) => {\n    if (action.payload.handler == null) {\n      return;\n    }\n    var state = listenerApi.getState();\n    var nextState = {\n      activeCoordinate: selectActiveTooltipCoordinate(state),\n      activeDataKey: selectActiveTooltipDataKey(state),\n      activeIndex: selectActiveTooltipIndex(state),\n      activeLabel: selectActiveLabel(state),\n      activeTooltipIndex: selectActiveTooltipIndex(state),\n      isTooltipActive: selectIsTooltipActive(state)\n    };\n    action.payload.handler(nextState, action.payload.reactEvent);\n  }\n});", "map": {"version": 3, "names": ["createAction", "createListenerMiddleware", "selectActiveLabel", "selectActiveTooltipCoordinate", "selectActiveTooltipDataKey", "selectActiveTooltipIndex", "selectIsTooltipActive", "externalEventAction", "externalEventsMiddleware", "startListening", "actionCreator", "effect", "action", "listenerApi", "payload", "handler", "state", "getState", "nextState", "activeCoordinate", "activeDataKey", "activeIndex", "activeLabel", "activeTooltipIndex", "isTooltipActive", "reactEvent"], "sources": ["D:/menasa/frontend/node_modules/recharts/es6/state/externalEventsMiddleware.js"], "sourcesContent": ["import { createAction, createListenerMiddleware } from '@reduxjs/toolkit';\nimport { selectActiveLabel, selectActiveTooltipCoordinate, selectActiveTooltipDataKey, selectActiveTooltipIndex, selectIsTooltipActive } from './selectors/tooltipSelectors';\nexport var externalEventAction = createAction('externalEvent');\nexport var externalEventsMiddleware = createListenerMiddleware();\nexternalEventsMiddleware.startListening({\n  actionCreator: externalEventAction,\n  effect: (action, listenerApi) => {\n    if (action.payload.handler == null) {\n      return;\n    }\n    var state = listenerApi.getState();\n    var nextState = {\n      activeCoordinate: selectActiveTooltipCoordinate(state),\n      activeDataKey: selectActiveTooltipDataKey(state),\n      activeIndex: selectActiveTooltipIndex(state),\n      activeLabel: selectActiveLabel(state),\n      activeTooltipIndex: selectActiveTooltipIndex(state),\n      isTooltipActive: selectIsTooltipActive(state)\n    };\n    action.payload.handler(nextState, action.payload.reactEvent);\n  }\n});"], "mappings": "AAAA,SAASA,YAAY,EAAEC,wBAAwB,QAAQ,kBAAkB;AACzE,SAASC,iBAAiB,EAAEC,6BAA6B,EAAEC,0BAA0B,EAAEC,wBAAwB,EAAEC,qBAAqB,QAAQ,8BAA8B;AAC5K,OAAO,IAAIC,mBAAmB,GAAGP,YAAY,CAAC,eAAe,CAAC;AAC9D,OAAO,IAAIQ,wBAAwB,GAAGP,wBAAwB,CAAC,CAAC;AAChEO,wBAAwB,CAACC,cAAc,CAAC;EACtCC,aAAa,EAAEH,mBAAmB;EAClCI,MAAM,EAAEA,CAACC,MAAM,EAAEC,WAAW,KAAK;IAC/B,IAAID,MAAM,CAACE,OAAO,CAACC,OAAO,IAAI,IAAI,EAAE;MAClC;IACF;IACA,IAAIC,KAAK,GAAGH,WAAW,CAACI,QAAQ,CAAC,CAAC;IAClC,IAAIC,SAAS,GAAG;MACdC,gBAAgB,EAAEhB,6BAA6B,CAACa,KAAK,CAAC;MACtDI,aAAa,EAAEhB,0BAA0B,CAACY,KAAK,CAAC;MAChDK,WAAW,EAAEhB,wBAAwB,CAACW,KAAK,CAAC;MAC5CM,WAAW,EAAEpB,iBAAiB,CAACc,KAAK,CAAC;MACrCO,kBAAkB,EAAElB,wBAAwB,CAACW,KAAK,CAAC;MACnDQ,eAAe,EAAElB,qBAAqB,CAACU,KAAK;IAC9C,CAAC;IACDJ,MAAM,CAACE,OAAO,CAACC,OAAO,CAACG,SAAS,EAAEN,MAAM,CAACE,OAAO,CAACW,UAAU,CAAC;EAC9D;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}