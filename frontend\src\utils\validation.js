// مكتبة التحقق من صحة البيانات

export const validators = {
  // التحقق من البريد الإلكتروني
  email: (value) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!value) return 'البريد الإلكتروني مطلوب';
    if (!emailRegex.test(value)) return 'البريد الإلكتروني غير صحيح';
    return null;
  },

  // التحقق من كلمة المرور
  password: (value) => {
    if (!value) return 'كلمة المرور مطلوبة';
    if (value.length < 8) return 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';
    if (!/(?=.*[a-z])/.test(value)) return 'كلمة المرور يجب أن تحتوي على حرف صغير';
    if (!/(?=.*[A-Z])/.test(value)) return 'كلمة المرور يجب أن تحتوي على حرف كبير';
    if (!/(?=.*\d)/.test(value)) return 'كلمة المرور يجب أن تحتوي على رقم';
    return null;
  },

  // التحقق من الاسم
  name: (value) => {
    if (!value) return 'الاسم مطلوب';
    if (value.length < 2) return 'الاسم يجب أن يكون حرفين على الأقل';
    if (value.length > 50) return 'الاسم يجب أن يكون أقل من 50 حرف';
    return null;
  },

  // التحقق من العنوان
  title: (value) => {
    if (!value) return 'العنوان مطلوب';
    if (value.length < 3) return 'العنوان يجب أن يكون 3 أحرف على الأقل';
    if (value.length > 100) return 'العنوان يجب أن يكون أقل من 100 حرف';
    return null;
  },

  // التحقق من الوصف
  description: (value) => {
    if (value && value.length > 500) return 'الوصف يجب أن يكون أقل من 500 حرف';
    return null;
  },

  // التحقق من المادة
  subject: (value) => {
    if (!value) return 'المادة مطلوبة';
    const validSubjects = ['math', 'science', 'arabic', 'english', 'history', 'geography'];
    if (!validSubjects.includes(value)) return 'المادة غير صحيحة';
    return null;
  },

  // التحقق من الفصل الدراسي
  semester: (value) => {
    if (!value) return 'الفصل الدراسي مطلوب';
    const validSemesters = ['first', 'second', 'summer'];
    if (!validSemesters.includes(value)) return 'الفصل الدراسي غير صحيح';
    return null;
  },

  // التحقق من الملف
  file: (file) => {
    if (!file) return 'الملف مطلوب';
    
    const maxSize = 100 * 1024 * 1024; // 100MB
    if (file.size > maxSize) return 'حجم الملف يجب أن يكون أقل من 100 ميجابايت';
    
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'video/mp4',
      'audio/mpeg',
      'image/jpeg',
      'image/png',
      'image/gif'
    ];
    
    if (!allowedTypes.includes(file.type)) {
      return 'نوع الملف غير مدعوم. الأنواع المدعومة: PDF, Word, PowerPoint, MP4, MP3, صور';
    }
    
    return null;
  },

  // التحقق من التقييم
  rating: (value) => {
    const rating = Number(value);
    if (isNaN(rating)) return 'التقييم يجب أن يكون رقم';
    if (rating < 1 || rating > 5) return 'التقييم يجب أن يكون بين 1 و 5';
    return null;
  },

  // التحقق من الدور
  role: (value) => {
    if (!value) return 'الدور مطلوب';
    const validRoles = ['student', 'admin'];
    if (!validRoles.includes(value)) return 'الدور غير صحيح';
    return null;
  }
};

// دالة للتحقق من صحة كائن كامل
export const validateObject = (data, rules) => {
  const errors = {};
  
  for (const [field, rule] of Object.entries(rules)) {
    if (typeof rule === 'function') {
      const error = rule(data[field]);
      if (error) errors[field] = error;
    } else if (Array.isArray(rule)) {
      // تطبيق عدة قواعد
      for (const validator of rule) {
        const error = validator(data[field]);
        if (error) {
          errors[field] = error;
          break; // توقف عند أول خطأ
        }
      }
    }
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};

// قواعد التحقق المحددة مسبقاً
export const validationRules = {
  login: {
    email: validators.email,
    password: (value) => value ? null : 'كلمة المرور مطلوبة'
  },
  
  register: {
    email: validators.email,
    password: validators.password,
    full_name: validators.name
  },
  
  fileUpload: {
    title: validators.title,
    description: validators.description,
    subject: validators.subject,
    semester: validators.semester,
    file: validators.file
  },
  
  profile: {
    full_name: validators.name,
    email: validators.email
  },
  
  rating: {
    rating: validators.rating
  }
};

// دالة مساعدة للتحقق من البيانات مع رسائل خطأ مخصصة
export const createValidator = (rules) => {
  return (data) => validateObject(data, rules);
};

// دالة للتحقق من صحة حقل واحد
export const validateField = (value, validator) => {
  if (typeof validator === 'function') {
    return validator(value);
  }
  return null;
};

// دالة للتحقق من صحة البيانات بشكل تدريجي (للنماذج التفاعلية)
export const validateFieldsLive = (data, rules, touchedFields = {}) => {
  const errors = {};
  
  for (const [field, rule] of Object.entries(rules)) {
    if (touchedFields[field]) {
      const error = validateField(data[field], rule);
      if (error) errors[field] = error;
    }
  }
  
  return errors;
};

// دالة للتحقق من قوة كلمة المرور
export const getPasswordStrength = (password) => {
  if (!password) return { score: 0, text: 'ضعيف جداً' };
  
  let score = 0;
  const checks = {
    length: password.length >= 8,
    lowercase: /[a-z]/.test(password),
    uppercase: /[A-Z]/.test(password),
    numbers: /\d/.test(password),
    symbols: /[^A-Za-z0-9]/.test(password)
  };
  
  score = Object.values(checks).filter(Boolean).length;
  
  const strength = {
    0: { score: 0, text: 'ضعيف جداً', color: '#ef4444' },
    1: { score: 1, text: 'ضعيف', color: '#f97316' },
    2: { score: 2, text: 'متوسط', color: '#eab308' },
    3: { score: 3, text: 'جيد', color: '#22c55e' },
    4: { score: 4, text: 'قوي', color: '#16a34a' },
    5: { score: 5, text: 'قوي جداً', color: '#15803d' }
  };
  
  return strength[score] || strength[0];
};

export default {
  validators,
  validateObject,
  validationRules,
  createValidator,
  validateField,
  validateFieldsLive,
  getPasswordStrength
};
