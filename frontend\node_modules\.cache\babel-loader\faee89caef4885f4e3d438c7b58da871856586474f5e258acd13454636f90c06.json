{"ast": null, "code": "import { hue } from \"./color.js\";\nexport default function (a, b) {\n  var i = hue(+a, +b);\n  return function (t) {\n    var x = i(t);\n    return x - 360 * Math.floor(x / 360);\n  };\n}", "map": {"version": 3, "names": ["hue", "a", "b", "i", "t", "x", "Math", "floor"], "sources": ["D:/menasa/frontend/node_modules/d3-interpolate/src/hue.js"], "sourcesContent": ["import {hue} from \"./color.js\";\n\nexport default function(a, b) {\n  var i = hue(+a, +b);\n  return function(t) {\n    var x = i(t);\n    return x - 360 * Math.floor(x / 360);\n  };\n}\n"], "mappings": "AAAA,SAAQA,GAAG,QAAO,YAAY;AAE9B,eAAe,UAASC,CAAC,EAAEC,CAAC,EAAE;EAC5B,IAAIC,CAAC,GAAGH,GAAG,CAAC,CAACC,CAAC,EAAE,CAACC,CAAC,CAAC;EACnB,OAAO,UAASE,CAAC,EAAE;IACjB,IAAIC,CAAC,GAAGF,CAAC,CAACC,CAAC,CAAC;IACZ,OAAOC,CAAC,GAAG,GAAG,GAAGC,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,GAAG,CAAC;EACtC,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}