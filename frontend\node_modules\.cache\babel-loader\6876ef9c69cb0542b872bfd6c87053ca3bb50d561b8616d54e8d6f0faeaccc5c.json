{"ast": null, "code": "var _jsxFileName = \"D:\\\\menasa\\\\frontend\\\\src\\\\components\\\\admin\\\\Sidebar.js\";\nimport React from 'react';\nimport styled from 'styled-components';\nimport { motion } from 'framer-motion';\nimport { FiHome, FiFile, FiUpload, FiUsers, FiSettings, FiBarChart, FiBook, FiTrendingUp } from 'react-icons/fi';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SidebarContainer = styled(motion.div)`\n  width: 280px;\n  min-height: 100vh;\n  background: linear-gradient(180deg, \n    ${({\n  theme\n}) => theme.colors.gray[900]} 0%, \n    ${({\n  theme\n}) => theme.colors.gray[800]} 100%);\n  color: ${({\n  theme\n}) => theme.colors.white};\n  padding: ${({\n  theme\n}) => theme.spacing[6]} 0;\n  position: relative;\n  overflow: hidden;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: linear-gradient(135deg, \n      ${({\n  theme\n}) => theme.colors.primary[600]}20 0%, \n      transparent 50%);\n    pointer-events: none;\n  }\n`;\n_c = SidebarContainer;\nconst SidebarHeader = styled.div`\n  padding: 0 ${({\n  theme\n}) => theme.spacing[6]};\n  margin-bottom: ${({\n  theme\n}) => theme.spacing[8]};\n  position: relative;\n  z-index: 1;\n`;\n_c2 = SidebarHeader;\nconst Logo = styled.div`\n  display: flex;\n  align-items: center;\n  gap: ${({\n  theme\n}) => theme.spacing[3]};\n  margin-bottom: ${({\n  theme\n}) => theme.spacing[2]};\n`;\n_c3 = Logo;\nconst LogoIcon = styled.div`\n  width: 48px;\n  height: 48px;\n  background: linear-gradient(135deg, ${({\n  theme\n}) => theme.colors.primary[500]}, ${({\n  theme\n}) => theme.colors.primary[600]});\n  border-radius: ${({\n  theme\n}) => theme.borderRadius.xl};\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: ${({\n  theme\n}) => theme.fontSizes.xl};\n  box-shadow: ${({\n  theme\n}) => theme.shadows.lg};\n`;\n_c4 = LogoIcon;\nconst LogoText = styled.div`\n  font-size: ${({\n  theme\n}) => theme.fontSizes.xl};\n  font-weight: ${({\n  theme\n}) => theme.fontWeights.bold};\n  color: ${({\n  theme\n}) => theme.colors.white};\n`;\n_c5 = LogoText;\nconst AdminTitle = styled.div`\n  font-size: ${({\n  theme\n}) => theme.fontSizes.sm};\n  color: ${({\n  theme\n}) => theme.colors.gray[300]};\n  margin-top: ${({\n  theme\n}) => theme.spacing[1]};\n`;\n_c6 = AdminTitle;\nconst Navigation = styled.nav`\n  position: relative;\n  z-index: 1;\n`;\n_c7 = Navigation;\nconst NavSection = styled.div`\n  margin-bottom: ${({\n  theme\n}) => theme.spacing[6]};\n`;\n_c8 = NavSection;\nconst SectionTitle = styled.div`\n  padding: 0 ${({\n  theme\n}) => theme.spacing[6]};\n  margin-bottom: ${({\n  theme\n}) => theme.spacing[3]};\n  font-size: ${({\n  theme\n}) => theme.fontSizes.xs};\n  font-weight: ${({\n  theme\n}) => theme.fontWeights.semibold};\n  color: ${({\n  theme\n}) => theme.colors.gray[400]};\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n`;\n_c9 = SectionTitle;\nconst NavItem = styled(motion.button)`\n  width: 100%;\n  display: flex;\n  align-items: center;\n  gap: ${({\n  theme\n}) => theme.spacing[3]};\n  padding: ${({\n  theme\n}) => theme.spacing[3]} ${({\n  theme\n}) => theme.spacing[6]};\n  background: none;\n  border: none;\n  color: ${({\n  theme\n}) => theme.colors.gray[300]};\n  font-size: ${({\n  theme\n}) => theme.fontSizes.base};\n  font-weight: ${({\n  theme\n}) => theme.fontWeights.medium};\n  text-align: right;\n  cursor: pointer;\n  transition: all ${({\n  theme\n}) => theme.transitions.fast};\n  position: relative;\n  \n  &:hover {\n    background: ${({\n  theme\n}) => theme.colors.gray[800]};\n    color: ${({\n  theme\n}) => theme.colors.white};\n  }\n  \n  ${({\n  active,\n  theme\n}) => active && `\n    background: linear-gradient(90deg, \n      ${theme.colors.primary[600]}40 0%, \n      ${theme.colors.primary[500]}20 100%);\n    color: ${theme.colors.white};\n    border-right: 3px solid ${theme.colors.primary[500]};\n    \n    &::before {\n      content: '';\n      position: absolute;\n      left: 0;\n      top: 0;\n      bottom: 0;\n      width: 3px;\n      background: ${theme.colors.primary[500]};\n    }\n  `}\n`;\n_c0 = NavItem;\nconst NavIcon = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 20px;\n  height: 20px;\n  font-size: 18px;\n`;\n_c1 = NavIcon;\nconst NavText = styled.span`\n  flex: 1;\n`;\n_c10 = NavText;\nconst Badge = styled.div`\n  background: ${({\n  theme\n}) => theme.colors.primary[500]};\n  color: ${({\n  theme\n}) => theme.colors.white};\n  font-size: ${({\n  theme\n}) => theme.fontSizes.xs};\n  font-weight: ${({\n  theme\n}) => theme.fontWeights.semibold};\n  padding: 2px 6px;\n  border-radius: ${({\n  theme\n}) => theme.borderRadius.full};\n  min-width: 18px;\n  height: 18px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n`;\n_c11 = Badge;\nconst SidebarFooter = styled.div`\n  position: absolute;\n  bottom: ${({\n  theme\n}) => theme.spacing[6]};\n  left: ${({\n  theme\n}) => theme.spacing[6]};\n  right: ${({\n  theme\n}) => theme.spacing[6]};\n  padding-top: ${({\n  theme\n}) => theme.spacing[6]};\n  border-top: 1px solid ${({\n  theme\n}) => theme.colors.gray[700]};\n  z-index: 1;\n`;\n_c12 = SidebarFooter;\nconst FooterText = styled.div`\n  font-size: ${({\n  theme\n}) => theme.fontSizes.xs};\n  color: ${({\n  theme\n}) => theme.colors.gray[400]};\n  text-align: center;\n`;\n_c13 = FooterText;\nconst Sidebar = ({\n  activeTab,\n  onTabChange\n}) => {\n  const menuItems = [{\n    section: 'الرئيسية',\n    items: [{\n      id: 'dashboard',\n      label: 'لوحة التحكم',\n      icon: /*#__PURE__*/_jsxDEV(FiHome, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 17\n      }, this)\n    }, {\n      id: 'analytics',\n      label: 'التحليلات',\n      icon: /*#__PURE__*/_jsxDEV(FiBarChart3, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 17\n      }, this),\n      badge: 'جديد'\n    }]\n  }, {\n    section: 'إدارة المحتوى',\n    items: [{\n      id: 'files',\n      label: 'إدارة الملفات',\n      icon: /*#__PURE__*/_jsxDEV(FiFile, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 17\n      }, this)\n    }, {\n      id: 'upload',\n      label: 'رفع ملف جديد',\n      icon: /*#__PURE__*/_jsxDEV(FiUpload, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 17\n      }, this)\n    }]\n  }, {\n    section: 'إدارة النظام',\n    items: [{\n      id: 'users',\n      label: 'إدارة المستخدمين',\n      icon: /*#__PURE__*/_jsxDEV(FiUsers, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 17\n      }, this)\n    }, {\n      id: 'settings',\n      label: 'الإعدادات',\n      icon: /*#__PURE__*/_jsxDEV(FiSettings, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 17\n      }, this)\n    }]\n  }];\n  return /*#__PURE__*/_jsxDEV(SidebarContainer, {\n    initial: {\n      x: -280\n    },\n    animate: {\n      x: 0\n    },\n    transition: {\n      duration: 0.3,\n      ease: 'easeOut'\n    },\n    children: [/*#__PURE__*/_jsxDEV(SidebarHeader, {\n      children: /*#__PURE__*/_jsxDEV(Logo, {\n        children: [/*#__PURE__*/_jsxDEV(LogoIcon, {\n          children: /*#__PURE__*/_jsxDEV(FiBook, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(LogoText, {\n            children: \"\\u0627\\u0644\\u0645\\u0643\\u062A\\u0628\\u0629 \\u0627\\u0644\\u062A\\u0639\\u0644\\u064A\\u0645\\u064A\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(AdminTitle, {\n            children: \"\\u0644\\u0648\\u062D\\u0629 \\u0627\\u0644\\u0625\\u062F\\u0627\\u0631\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Navigation, {\n      children: menuItems.map((section, sectionIndex) => /*#__PURE__*/_jsxDEV(NavSection, {\n        children: [/*#__PURE__*/_jsxDEV(SectionTitle, {\n          children: section.section\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 13\n        }, this), section.items.map((item, itemIndex) => /*#__PURE__*/_jsxDEV(NavItem, {\n          active: activeTab === item.id,\n          onClick: () => onTabChange(item.id),\n          whileHover: {\n            x: 5\n          },\n          whileTap: {\n            scale: 0.98\n          },\n          initial: {\n            opacity: 0,\n            x: -20\n          },\n          animate: {\n            opacity: 1,\n            x: 0\n          },\n          transition: {\n            delay: sectionIndex * 0.1 + itemIndex * 0.05,\n            duration: 0.3\n          },\n          children: [/*#__PURE__*/_jsxDEV(NavIcon, {\n            children: item.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(NavText, {\n            children: item.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 17\n          }, this), item.badge && /*#__PURE__*/_jsxDEV(Badge, {\n            children: item.badge\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 32\n          }, this)]\n        }, item.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 15\n        }, this))]\n      }, section.section, true, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 251,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(SidebarFooter, {\n      children: /*#__PURE__*/_jsxDEV(FooterText, {\n        children: [\"\\u0646\\u0638\\u0627\\u0645 \\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0645\\u0644\\u0641\\u0627\\u062A \\u0627\\u0644\\u062A\\u0639\\u0644\\u064A\\u0645\\u064A\\u0629\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 11\n        }, this), \"\\u0627\\u0644\\u0625\\u0635\\u062F\\u0627\\u0631 1.0.0\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 278,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 234,\n    columnNumber: 5\n  }, this);\n};\n_c14 = Sidebar;\nexport default Sidebar;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14;\n$RefreshReg$(_c, \"SidebarContainer\");\n$RefreshReg$(_c2, \"SidebarHeader\");\n$RefreshReg$(_c3, \"Logo\");\n$RefreshReg$(_c4, \"LogoIcon\");\n$RefreshReg$(_c5, \"LogoText\");\n$RefreshReg$(_c6, \"AdminTitle\");\n$RefreshReg$(_c7, \"Navigation\");\n$RefreshReg$(_c8, \"NavSection\");\n$RefreshReg$(_c9, \"SectionTitle\");\n$RefreshReg$(_c0, \"NavItem\");\n$RefreshReg$(_c1, \"NavIcon\");\n$RefreshReg$(_c10, \"NavText\");\n$RefreshReg$(_c11, \"Badge\");\n$RefreshReg$(_c12, \"SidebarFooter\");\n$RefreshReg$(_c13, \"FooterText\");\n$RefreshReg$(_c14, \"Sidebar\");", "map": {"version": 3, "names": ["React", "styled", "motion", "FiHome", "FiFile", "FiUpload", "FiUsers", "FiSettings", "Fi<PERSON>ar<PERSON><PERSON>", "FiBook", "FiTrendingUp", "jsxDEV", "_jsxDEV", "SidebarContainer", "div", "theme", "colors", "gray", "white", "spacing", "primary", "_c", "SidebarHeader", "_c2", "Logo", "_c3", "LogoIcon", "borderRadius", "xl", "fontSizes", "shadows", "lg", "_c4", "LogoText", "fontWeights", "bold", "_c5", "AdminTitle", "sm", "_c6", "Navigation", "nav", "_c7", "NavSection", "_c8", "SectionTitle", "xs", "semibold", "_c9", "NavItem", "button", "base", "medium", "transitions", "fast", "active", "_c0", "NavIcon", "_c1", "NavText", "span", "_c10", "Badge", "full", "_c11", "SidebarFooter", "_c12", "FooterText", "_c13", "Sidebar", "activeTab", "onTabChange", "menuItems", "section", "items", "id", "label", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "FiBarChart3", "badge", "initial", "x", "animate", "transition", "duration", "ease", "children", "map", "sectionIndex", "item", "itemIndex", "onClick", "whileHover", "whileTap", "scale", "opacity", "delay", "_c14", "$RefreshReg$"], "sources": ["D:/menasa/frontend/src/components/admin/Sidebar.js"], "sourcesContent": ["import React from 'react';\nimport styled from 'styled-components';\nimport { motion } from 'framer-motion';\nimport { \n  FiHome, \n  FiFile, \n  FiUpload, \n  FiUsers, \n  FiSettings,\n  FiBarChart,\n  FiBook,\n  FiTrendingUp\n} from 'react-icons/fi';\n\nconst SidebarContainer = styled(motion.div)`\n  width: 280px;\n  min-height: 100vh;\n  background: linear-gradient(180deg, \n    ${({ theme }) => theme.colors.gray[900]} 0%, \n    ${({ theme }) => theme.colors.gray[800]} 100%);\n  color: ${({ theme }) => theme.colors.white};\n  padding: ${({ theme }) => theme.spacing[6]} 0;\n  position: relative;\n  overflow: hidden;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: linear-gradient(135deg, \n      ${({ theme }) => theme.colors.primary[600]}20 0%, \n      transparent 50%);\n    pointer-events: none;\n  }\n`;\n\nconst SidebarHeader = styled.div`\n  padding: 0 ${({ theme }) => theme.spacing[6]};\n  margin-bottom: ${({ theme }) => theme.spacing[8]};\n  position: relative;\n  z-index: 1;\n`;\n\nconst Logo = styled.div`\n  display: flex;\n  align-items: center;\n  gap: ${({ theme }) => theme.spacing[3]};\n  margin-bottom: ${({ theme }) => theme.spacing[2]};\n`;\n\nconst LogoIcon = styled.div`\n  width: 48px;\n  height: 48px;\n  background: linear-gradient(135deg, ${({ theme }) => theme.colors.primary[500]}, ${({ theme }) => theme.colors.primary[600]});\n  border-radius: ${({ theme }) => theme.borderRadius.xl};\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: ${({ theme }) => theme.fontSizes.xl};\n  box-shadow: ${({ theme }) => theme.shadows.lg};\n`;\n\nconst LogoText = styled.div`\n  font-size: ${({ theme }) => theme.fontSizes.xl};\n  font-weight: ${({ theme }) => theme.fontWeights.bold};\n  color: ${({ theme }) => theme.colors.white};\n`;\n\nconst AdminTitle = styled.div`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme }) => theme.colors.gray[300]};\n  margin-top: ${({ theme }) => theme.spacing[1]};\n`;\n\nconst Navigation = styled.nav`\n  position: relative;\n  z-index: 1;\n`;\n\nconst NavSection = styled.div`\n  margin-bottom: ${({ theme }) => theme.spacing[6]};\n`;\n\nconst SectionTitle = styled.div`\n  padding: 0 ${({ theme }) => theme.spacing[6]};\n  margin-bottom: ${({ theme }) => theme.spacing[3]};\n  font-size: ${({ theme }) => theme.fontSizes.xs};\n  font-weight: ${({ theme }) => theme.fontWeights.semibold};\n  color: ${({ theme }) => theme.colors.gray[400]};\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n`;\n\nconst NavItem = styled(motion.button)`\n  width: 100%;\n  display: flex;\n  align-items: center;\n  gap: ${({ theme }) => theme.spacing[3]};\n  padding: ${({ theme }) => theme.spacing[3]} ${({ theme }) => theme.spacing[6]};\n  background: none;\n  border: none;\n  color: ${({ theme }) => theme.colors.gray[300]};\n  font-size: ${({ theme }) => theme.fontSizes.base};\n  font-weight: ${({ theme }) => theme.fontWeights.medium};\n  text-align: right;\n  cursor: pointer;\n  transition: all ${({ theme }) => theme.transitions.fast};\n  position: relative;\n  \n  &:hover {\n    background: ${({ theme }) => theme.colors.gray[800]};\n    color: ${({ theme }) => theme.colors.white};\n  }\n  \n  ${({ active, theme }) =>\n    active &&\n    `\n    background: linear-gradient(90deg, \n      ${theme.colors.primary[600]}40 0%, \n      ${theme.colors.primary[500]}20 100%);\n    color: ${theme.colors.white};\n    border-right: 3px solid ${theme.colors.primary[500]};\n    \n    &::before {\n      content: '';\n      position: absolute;\n      left: 0;\n      top: 0;\n      bottom: 0;\n      width: 3px;\n      background: ${theme.colors.primary[500]};\n    }\n  `}\n`;\n\nconst NavIcon = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 20px;\n  height: 20px;\n  font-size: 18px;\n`;\n\nconst NavText = styled.span`\n  flex: 1;\n`;\n\nconst Badge = styled.div`\n  background: ${({ theme }) => theme.colors.primary[500]};\n  color: ${({ theme }) => theme.colors.white};\n  font-size: ${({ theme }) => theme.fontSizes.xs};\n  font-weight: ${({ theme }) => theme.fontWeights.semibold};\n  padding: 2px 6px;\n  border-radius: ${({ theme }) => theme.borderRadius.full};\n  min-width: 18px;\n  height: 18px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n`;\n\nconst SidebarFooter = styled.div`\n  position: absolute;\n  bottom: ${({ theme }) => theme.spacing[6]};\n  left: ${({ theme }) => theme.spacing[6]};\n  right: ${({ theme }) => theme.spacing[6]};\n  padding-top: ${({ theme }) => theme.spacing[6]};\n  border-top: 1px solid ${({ theme }) => theme.colors.gray[700]};\n  z-index: 1;\n`;\n\nconst FooterText = styled.div`\n  font-size: ${({ theme }) => theme.fontSizes.xs};\n  color: ${({ theme }) => theme.colors.gray[400]};\n  text-align: center;\n`;\n\nconst Sidebar = ({ activeTab, onTabChange }) => {\n  const menuItems = [\n    {\n      section: 'الرئيسية',\n      items: [\n        {\n          id: 'dashboard',\n          label: 'لوحة التحكم',\n          icon: <FiHome />,\n        },\n        {\n          id: 'analytics',\n          label: 'التحليلات',\n          icon: <FiBarChart3 />,\n          badge: 'جديد'\n        }\n      ]\n    },\n    {\n      section: 'إدارة المحتوى',\n      items: [\n        {\n          id: 'files',\n          label: 'إدارة الملفات',\n          icon: <FiFile />,\n        },\n        {\n          id: 'upload',\n          label: 'رفع ملف جديد',\n          icon: <FiUpload />,\n        }\n      ]\n    },\n    {\n      section: 'إدارة النظام',\n      items: [\n        {\n          id: 'users',\n          label: 'إدارة المستخدمين',\n          icon: <FiUsers />,\n        },\n        {\n          id: 'settings',\n          label: 'الإعدادات',\n          icon: <FiSettings />,\n        }\n      ]\n    }\n  ];\n\n  return (\n    <SidebarContainer\n      initial={{ x: -280 }}\n      animate={{ x: 0 }}\n      transition={{ duration: 0.3, ease: 'easeOut' }}\n    >\n      <SidebarHeader>\n        <Logo>\n          <LogoIcon>\n            <FiBook />\n          </LogoIcon>\n          <div>\n            <LogoText>المكتبة التعليمية</LogoText>\n            <AdminTitle>لوحة الإدارة</AdminTitle>\n          </div>\n        </Logo>\n      </SidebarHeader>\n\n      <Navigation>\n        {menuItems.map((section, sectionIndex) => (\n          <NavSection key={section.section}>\n            <SectionTitle>{section.section}</SectionTitle>\n            {section.items.map((item, itemIndex) => (\n              <NavItem\n                key={item.id}\n                active={activeTab === item.id}\n                onClick={() => onTabChange(item.id)}\n                whileHover={{ x: 5 }}\n                whileTap={{ scale: 0.98 }}\n                initial={{ opacity: 0, x: -20 }}\n                animate={{ opacity: 1, x: 0 }}\n                transition={{ \n                  delay: (sectionIndex * 0.1) + (itemIndex * 0.05),\n                  duration: 0.3 \n                }}\n              >\n                <NavIcon>{item.icon}</NavIcon>\n                <NavText>{item.label}</NavText>\n                {item.badge && <Badge>{item.badge}</Badge>}\n              </NavItem>\n            ))}\n          </NavSection>\n        ))}\n      </Navigation>\n\n      <SidebarFooter>\n        <FooterText>\n          نظام إدارة الملفات التعليمية\n          <br />\n          الإصدار 1.0.0\n        </FooterText>\n      </SidebarFooter>\n    </SidebarContainer>\n  );\n};\n\nexport default Sidebar;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,MAAM,EACNC,MAAM,EACNC,QAAQ,EACRC,OAAO,EACPC,UAAU,EACVC,UAAU,EACVC,MAAM,EACNC,YAAY,QACP,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,gBAAgB,GAAGZ,MAAM,CAACC,MAAM,CAACY,GAAG,CAAC;AAC3C;AACA;AACA;AACA,MAAM,CAAC;EAAEC;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACC,IAAI,CAAC,GAAG,CAAC;AAC3C,MAAM,CAAC;EAAEF;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACC,IAAI,CAAC,GAAG,CAAC;AAC3C,WAAW,CAAC;EAAEF;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACE,KAAK;AAC5C,aAAa,CAAC;EAAEH;AAAM,CAAC,KAAKA,KAAK,CAACI,OAAO,CAAC,CAAC,CAAC;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,CAAC;EAAEJ;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACI,OAAO,CAAC,GAAG,CAAC;AAChD;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAvBIR,gBAAgB;AAyBtB,MAAMS,aAAa,GAAGrB,MAAM,CAACa,GAAG;AAChC,eAAe,CAAC;EAAEC;AAAM,CAAC,KAAKA,KAAK,CAACI,OAAO,CAAC,CAAC,CAAC;AAC9C,mBAAmB,CAAC;EAAEJ;AAAM,CAAC,KAAKA,KAAK,CAACI,OAAO,CAAC,CAAC,CAAC;AAClD;AACA;AACA,CAAC;AAACI,GAAA,GALID,aAAa;AAOnB,MAAME,IAAI,GAAGvB,MAAM,CAACa,GAAG;AACvB;AACA;AACA,SAAS,CAAC;EAAEC;AAAM,CAAC,KAAKA,KAAK,CAACI,OAAO,CAAC,CAAC,CAAC;AACxC,mBAAmB,CAAC;EAAEJ;AAAM,CAAC,KAAKA,KAAK,CAACI,OAAO,CAAC,CAAC,CAAC;AAClD,CAAC;AAACM,GAAA,GALID,IAAI;AAOV,MAAME,QAAQ,GAAGzB,MAAM,CAACa,GAAG;AAC3B;AACA;AACA,wCAAwC,CAAC;EAAEC;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACI,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC;EAAEL;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACI,OAAO,CAAC,GAAG,CAAC;AAC7H,mBAAmB,CAAC;EAAEL;AAAM,CAAC,KAAKA,KAAK,CAACY,YAAY,CAACC,EAAE;AACvD;AACA;AACA;AACA;AACA,eAAe,CAAC;EAAEb;AAAM,CAAC,KAAKA,KAAK,CAACc,SAAS,CAACD,EAAE;AAChD,gBAAgB,CAAC;EAAEb;AAAM,CAAC,KAAKA,KAAK,CAACe,OAAO,CAACC,EAAE;AAC/C,CAAC;AAACC,GAAA,GAXIN,QAAQ;AAad,MAAMO,QAAQ,GAAGhC,MAAM,CAACa,GAAG;AAC3B,eAAe,CAAC;EAAEC;AAAM,CAAC,KAAKA,KAAK,CAACc,SAAS,CAACD,EAAE;AAChD,iBAAiB,CAAC;EAAEb;AAAM,CAAC,KAAKA,KAAK,CAACmB,WAAW,CAACC,IAAI;AACtD,WAAW,CAAC;EAAEpB;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACE,KAAK;AAC5C,CAAC;AAACkB,GAAA,GAJIH,QAAQ;AAMd,MAAMI,UAAU,GAAGpC,MAAM,CAACa,GAAG;AAC7B,eAAe,CAAC;EAAEC;AAAM,CAAC,KAAKA,KAAK,CAACc,SAAS,CAACS,EAAE;AAChD,WAAW,CAAC;EAAEvB;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACC,IAAI,CAAC,GAAG,CAAC;AAChD,gBAAgB,CAAC;EAAEF;AAAM,CAAC,KAAKA,KAAK,CAACI,OAAO,CAAC,CAAC,CAAC;AAC/C,CAAC;AAACoB,GAAA,GAJIF,UAAU;AAMhB,MAAMG,UAAU,GAAGvC,MAAM,CAACwC,GAAG;AAC7B;AACA;AACA,CAAC;AAACC,GAAA,GAHIF,UAAU;AAKhB,MAAMG,UAAU,GAAG1C,MAAM,CAACa,GAAG;AAC7B,mBAAmB,CAAC;EAAEC;AAAM,CAAC,KAAKA,KAAK,CAACI,OAAO,CAAC,CAAC,CAAC;AAClD,CAAC;AAACyB,GAAA,GAFID,UAAU;AAIhB,MAAME,YAAY,GAAG5C,MAAM,CAACa,GAAG;AAC/B,eAAe,CAAC;EAAEC;AAAM,CAAC,KAAKA,KAAK,CAACI,OAAO,CAAC,CAAC,CAAC;AAC9C,mBAAmB,CAAC;EAAEJ;AAAM,CAAC,KAAKA,KAAK,CAACI,OAAO,CAAC,CAAC,CAAC;AAClD,eAAe,CAAC;EAAEJ;AAAM,CAAC,KAAKA,KAAK,CAACc,SAAS,CAACiB,EAAE;AAChD,iBAAiB,CAAC;EAAE/B;AAAM,CAAC,KAAKA,KAAK,CAACmB,WAAW,CAACa,QAAQ;AAC1D,WAAW,CAAC;EAAEhC;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACC,IAAI,CAAC,GAAG,CAAC;AAChD;AACA;AACA,CAAC;AAAC+B,GAAA,GARIH,YAAY;AAUlB,MAAMI,OAAO,GAAGhD,MAAM,CAACC,MAAM,CAACgD,MAAM,CAAC;AACrC;AACA;AACA;AACA,SAAS,CAAC;EAAEnC;AAAM,CAAC,KAAKA,KAAK,CAACI,OAAO,CAAC,CAAC,CAAC;AACxC,aAAa,CAAC;EAAEJ;AAAM,CAAC,KAAKA,KAAK,CAACI,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;EAAEJ;AAAM,CAAC,KAAKA,KAAK,CAACI,OAAO,CAAC,CAAC,CAAC;AAC/E;AACA;AACA,WAAW,CAAC;EAAEJ;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACC,IAAI,CAAC,GAAG,CAAC;AAChD,eAAe,CAAC;EAAEF;AAAM,CAAC,KAAKA,KAAK,CAACc,SAAS,CAACsB,IAAI;AAClD,iBAAiB,CAAC;EAAEpC;AAAM,CAAC,KAAKA,KAAK,CAACmB,WAAW,CAACkB,MAAM;AACxD;AACA;AACA,oBAAoB,CAAC;EAAErC;AAAM,CAAC,KAAKA,KAAK,CAACsC,WAAW,CAACC,IAAI;AACzD;AACA;AACA;AACA,kBAAkB,CAAC;EAAEvC;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACC,IAAI,CAAC,GAAG,CAAC;AACvD,aAAa,CAAC;EAAEF;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACE,KAAK;AAC9C;AACA;AACA,IAAI,CAAC;EAAEqC,MAAM;EAAExC;AAAM,CAAC,KAClBwC,MAAM,IACN;AACJ;AACA,QAAQxC,KAAK,CAACC,MAAM,CAACI,OAAO,CAAC,GAAG,CAAC;AACjC,QAAQL,KAAK,CAACC,MAAM,CAACI,OAAO,CAAC,GAAG,CAAC;AACjC,aAAaL,KAAK,CAACC,MAAM,CAACE,KAAK;AAC/B,8BAA8BH,KAAK,CAACC,MAAM,CAACI,OAAO,CAAC,GAAG,CAAC;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoBL,KAAK,CAACC,MAAM,CAACI,OAAO,CAAC,GAAG,CAAC;AAC7C;AACA,GAAG;AACH,CAAC;AAACoC,GAAA,GAxCIP,OAAO;AA0Cb,MAAMQ,OAAO,GAAGxD,MAAM,CAACa,GAAG;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC4C,GAAA,GAPID,OAAO;AASb,MAAME,OAAO,GAAG1D,MAAM,CAAC2D,IAAI;AAC3B;AACA,CAAC;AAACC,IAAA,GAFIF,OAAO;AAIb,MAAMG,KAAK,GAAG7D,MAAM,CAACa,GAAG;AACxB,gBAAgB,CAAC;EAAEC;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACI,OAAO,CAAC,GAAG,CAAC;AACxD,WAAW,CAAC;EAAEL;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACE,KAAK;AAC5C,eAAe,CAAC;EAAEH;AAAM,CAAC,KAAKA,KAAK,CAACc,SAAS,CAACiB,EAAE;AAChD,iBAAiB,CAAC;EAAE/B;AAAM,CAAC,KAAKA,KAAK,CAACmB,WAAW,CAACa,QAAQ;AAC1D;AACA,mBAAmB,CAAC;EAAEhC;AAAM,CAAC,KAAKA,KAAK,CAACY,YAAY,CAACoC,IAAI;AACzD;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAZIF,KAAK;AAcX,MAAMG,aAAa,GAAGhE,MAAM,CAACa,GAAG;AAChC;AACA,YAAY,CAAC;EAAEC;AAAM,CAAC,KAAKA,KAAK,CAACI,OAAO,CAAC,CAAC,CAAC;AAC3C,UAAU,CAAC;EAAEJ;AAAM,CAAC,KAAKA,KAAK,CAACI,OAAO,CAAC,CAAC,CAAC;AACzC,WAAW,CAAC;EAAEJ;AAAM,CAAC,KAAKA,KAAK,CAACI,OAAO,CAAC,CAAC,CAAC;AAC1C,iBAAiB,CAAC;EAAEJ;AAAM,CAAC,KAAKA,KAAK,CAACI,OAAO,CAAC,CAAC,CAAC;AAChD,0BAA0B,CAAC;EAAEJ;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACC,IAAI,CAAC,GAAG,CAAC;AAC/D;AACA,CAAC;AAACiD,IAAA,GARID,aAAa;AAUnB,MAAME,UAAU,GAAGlE,MAAM,CAACa,GAAG;AAC7B,eAAe,CAAC;EAAEC;AAAM,CAAC,KAAKA,KAAK,CAACc,SAAS,CAACiB,EAAE;AAChD,WAAW,CAAC;EAAE/B;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACC,IAAI,CAAC,GAAG,CAAC;AAChD;AACA,CAAC;AAACmD,IAAA,GAJID,UAAU;AAMhB,MAAME,OAAO,GAAGA,CAAC;EAAEC,SAAS;EAAEC;AAAY,CAAC,KAAK;EAC9C,MAAMC,SAAS,GAAG,CAChB;IACEC,OAAO,EAAE,UAAU;IACnBC,KAAK,EAAE,CACL;MACEC,EAAE,EAAE,WAAW;MACfC,KAAK,EAAE,aAAa;MACpBC,IAAI,eAAEjE,OAAA,CAACT,MAAM;QAAA2E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IACjB,CAAC,EACD;MACEN,EAAE,EAAE,WAAW;MACfC,KAAK,EAAE,WAAW;MAClBC,IAAI,eAAEjE,OAAA,CAACsE,WAAW;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACrBE,KAAK,EAAE;IACT,CAAC;EAEL,CAAC,EACD;IACEV,OAAO,EAAE,eAAe;IACxBC,KAAK,EAAE,CACL;MACEC,EAAE,EAAE,OAAO;MACXC,KAAK,EAAE,eAAe;MACtBC,IAAI,eAAEjE,OAAA,CAACR,MAAM;QAAA0E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IACjB,CAAC,EACD;MACEN,EAAE,EAAE,QAAQ;MACZC,KAAK,EAAE,cAAc;MACrBC,IAAI,eAAEjE,OAAA,CAACP,QAAQ;QAAAyE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IACnB,CAAC;EAEL,CAAC,EACD;IACER,OAAO,EAAE,cAAc;IACvBC,KAAK,EAAE,CACL;MACEC,EAAE,EAAE,OAAO;MACXC,KAAK,EAAE,kBAAkB;MACzBC,IAAI,eAAEjE,OAAA,CAACN,OAAO;QAAAwE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAClB,CAAC,EACD;MACEN,EAAE,EAAE,UAAU;MACdC,KAAK,EAAE,WAAW;MAClBC,IAAI,eAAEjE,OAAA,CAACL,UAAU;QAAAuE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IACrB,CAAC;EAEL,CAAC,CACF;EAED,oBACErE,OAAA,CAACC,gBAAgB;IACfuE,OAAO,EAAE;MAAEC,CAAC,EAAE,CAAC;IAAI,CAAE;IACrBC,OAAO,EAAE;MAAED,CAAC,EAAE;IAAE,CAAE;IAClBE,UAAU,EAAE;MAAEC,QAAQ,EAAE,GAAG;MAAEC,IAAI,EAAE;IAAU,CAAE;IAAAC,QAAA,gBAE/C9E,OAAA,CAACU,aAAa;MAAAoE,QAAA,eACZ9E,OAAA,CAACY,IAAI;QAAAkE,QAAA,gBACH9E,OAAA,CAACc,QAAQ;UAAAgE,QAAA,eACP9E,OAAA,CAACH,MAAM;YAAAqE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACXrE,OAAA;UAAA8E,QAAA,gBACE9E,OAAA,CAACqB,QAAQ;YAAAyD,QAAA,EAAC;UAAiB;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eACtCrE,OAAA,CAACyB,UAAU;YAAAqD,QAAA,EAAC;UAAY;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,eAEhBrE,OAAA,CAAC4B,UAAU;MAAAkD,QAAA,EACRlB,SAAS,CAACmB,GAAG,CAAC,CAAClB,OAAO,EAAEmB,YAAY,kBACnChF,OAAA,CAAC+B,UAAU;QAAA+C,QAAA,gBACT9E,OAAA,CAACiC,YAAY;UAAA6C,QAAA,EAAEjB,OAAO,CAACA;QAAO;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAAC,EAC7CR,OAAO,CAACC,KAAK,CAACiB,GAAG,CAAC,CAACE,IAAI,EAAEC,SAAS,kBACjClF,OAAA,CAACqC,OAAO;UAENM,MAAM,EAAEe,SAAS,KAAKuB,IAAI,CAAClB,EAAG;UAC9BoB,OAAO,EAAEA,CAAA,KAAMxB,WAAW,CAACsB,IAAI,CAAClB,EAAE,CAAE;UACpCqB,UAAU,EAAE;YAAEX,CAAC,EAAE;UAAE,CAAE;UACrBY,QAAQ,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC1Bd,OAAO,EAAE;YAAEe,OAAO,EAAE,CAAC;YAAEd,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCC,OAAO,EAAE;YAAEa,OAAO,EAAE,CAAC;YAAEd,CAAC,EAAE;UAAE,CAAE;UAC9BE,UAAU,EAAE;YACVa,KAAK,EAAGR,YAAY,GAAG,GAAG,GAAKE,SAAS,GAAG,IAAK;YAChDN,QAAQ,EAAE;UACZ,CAAE;UAAAE,QAAA,gBAEF9E,OAAA,CAAC6C,OAAO;YAAAiC,QAAA,EAAEG,IAAI,CAAChB;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eAC9BrE,OAAA,CAAC+C,OAAO;YAAA+B,QAAA,EAAEG,IAAI,CAACjB;UAAK;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,EAC9BY,IAAI,CAACV,KAAK,iBAAIvE,OAAA,CAACkD,KAAK;YAAA4B,QAAA,EAAEG,IAAI,CAACV;UAAK;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,GAdrCY,IAAI,CAAClB,EAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAeL,CACV,CAAC;MAAA,GApBaR,OAAO,CAACA,OAAO;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAqBpB,CACb;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC,eAEbrE,OAAA,CAACqD,aAAa;MAAAyB,QAAA,eACZ9E,OAAA,CAACuD,UAAU;QAAAuB,QAAA,GAAC,2JAEV,eAAA9E,OAAA;UAAAkE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,oDAER;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEvB,CAAC;AAACoB,IAAA,GAxGIhC,OAAO;AA0Gb,eAAeA,OAAO;AAAC,IAAAhD,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAO,GAAA,EAAAI,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAQ,GAAA,EAAAE,GAAA,EAAAG,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAiC,IAAA;AAAAC,YAAA,CAAAjF,EAAA;AAAAiF,YAAA,CAAA/E,GAAA;AAAA+E,YAAA,CAAA7E,GAAA;AAAA6E,YAAA,CAAAtE,GAAA;AAAAsE,YAAA,CAAAlE,GAAA;AAAAkE,YAAA,CAAA/D,GAAA;AAAA+D,YAAA,CAAA5D,GAAA;AAAA4D,YAAA,CAAA1D,GAAA;AAAA0D,YAAA,CAAAtD,GAAA;AAAAsD,YAAA,CAAA9C,GAAA;AAAA8C,YAAA,CAAA5C,GAAA;AAAA4C,YAAA,CAAAzC,IAAA;AAAAyC,YAAA,CAAAtC,IAAA;AAAAsC,YAAA,CAAApC,IAAA;AAAAoC,YAAA,CAAAlC,IAAA;AAAAkC,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}