{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, {\n  value: 'Module'\n});\nfunction isPlainObject(value) {\n  if (!value || typeof value !== 'object') {\n    return false;\n  }\n  const proto = Object.getPrototypeOf(value);\n  const hasObjectPrototype = proto === null || proto === Object.prototype || Object.getPrototypeOf(proto) === null;\n  if (!hasObjectPrototype) {\n    return false;\n  }\n  return Object.prototype.toString.call(value) === '[object Object]';\n}\nexports.isPlainObject = isPlainObject;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "Symbol", "toStringTag", "value", "isPlainObject", "proto", "getPrototypeOf", "hasObjectPrototype", "prototype", "toString", "call"], "sources": ["D:/menasa/frontend/node_modules/es-toolkit/dist/predicate/isPlainObject.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\nfunction isPlainObject(value) {\n    if (!value || typeof value !== 'object') {\n        return false;\n    }\n    const proto = Object.getPrototypeOf(value);\n    const hasObjectPrototype = proto === null ||\n        proto === Object.prototype ||\n        Object.getPrototypeOf(proto) === null;\n    if (!hasObjectPrototype) {\n        return false;\n    }\n    return Object.prototype.toString.call(value) === '[object Object]';\n}\n\nexports.isPlainObject = isPlainObject;\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAEC,MAAM,CAACC,WAAW,EAAE;EAAEC,KAAK,EAAE;AAAS,CAAC,CAAC;AAEvE,SAASC,aAAaA,CAACD,KAAK,EAAE;EAC1B,IAAI,CAACA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IACrC,OAAO,KAAK;EAChB;EACA,MAAME,KAAK,GAAGP,MAAM,CAACQ,cAAc,CAACH,KAAK,CAAC;EAC1C,MAAMI,kBAAkB,GAAGF,KAAK,KAAK,IAAI,IACrCA,KAAK,KAAKP,MAAM,CAACU,SAAS,IAC1BV,MAAM,CAACQ,cAAc,CAACD,KAAK,CAAC,KAAK,IAAI;EACzC,IAAI,CAACE,kBAAkB,EAAE;IACrB,OAAO,KAAK;EAChB;EACA,OAAOT,MAAM,CAACU,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACP,KAAK,CAAC,KAAK,iBAAiB;AACtE;AAEAH,OAAO,CAACI,aAAa,GAAGA,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}