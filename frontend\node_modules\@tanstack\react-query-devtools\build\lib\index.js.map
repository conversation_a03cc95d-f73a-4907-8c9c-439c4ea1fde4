{"version": 3, "file": "index.js", "sources": ["../../src/index.ts"], "sourcesContent": ["'use client'\n\nimport * as devtools from './devtools'\n\nexport const ReactQueryDevtools: typeof devtools['ReactQueryDevtools'] =\n  process.env.NODE_ENV !== 'development'\n    ? function () {\n        return null\n      }\n    : devtools.ReactQueryDevtools\n\nexport const ReactQueryDevtoolsPanel: typeof devtools['ReactQueryDevtoolsPanel'] =\n  process.env.NODE_ENV !== 'development'\n    ? (function () {\n        return null\n      } as any)\n    : devtools.ReactQueryDevtoolsPanel\n"], "names": [], "mappings": ";;;;;;;AAIO;AAGC;AACD;AAGA;AAGC;AACD;;;"}