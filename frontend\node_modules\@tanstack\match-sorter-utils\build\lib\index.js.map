{"version": 3, "file": "index.js", "sources": ["../../src/index.ts"], "sourcesContent": ["/**\n * @name match-sorter\n * @license MIT license.\n * @copyright (c) 2099 Kent <PERSON><PERSON>\n * <AUTHOR> <<EMAIL>> (https://kentcdodds.com)\n */\n\n// This is a fork of match-sorter. Instead of offering\n// a unified API for filtering and sorting in a single pass,\n// match-sorter-utils provides the lower-level utilities of\n// ranking items and comparing ranks in a way that can\n// be incrementally applied to a system rather than\n// all-at-once.\n\n// 1. Use the rankItem function to rank an item\n// 2. Use the resulting rankingInfo.passed to filter\n// 3. Use the resulting rankingInfo.rank to sort\n\n// For bundling purposes (mainly remove-accents not being esm safe/ready),\n// we've also hard-coded remove-accents into this source.\n// The remove-accents package is still included as a dependency\n// for attribution purposes, but it will not be imported and bundled.\n\nimport { removeAccents } from './remove-accents'\n\nexport type AccessorAttributes = {\n  threshold?: Ranking\n  maxRanking: Ranking\n  minRanking: Ranking\n}\n\nexport interface RankingInfo {\n  rankedValue: any\n  rank: Ranking\n  accessorIndex: number\n  accessorThreshold: Ranking | undefined\n  passed: boolean\n}\n\nexport interface AccessorOptions<TItem> {\n  accessor: AccessorFn<TItem>\n  threshold?: Ranking\n  maxRanking?: Ranking\n  minRanking?: Ranking\n}\n\nexport type AccessorFn<TItem> = (item: TItem) => string | Array<string>\n\nexport type Accessor<TItem> = AccessorFn<TItem> | AccessorOptions<TItem>\n\nexport interface RankItemOptions<TItem = unknown> {\n  accessors?: ReadonlyArray<Accessor<TItem>>\n  threshold?: Ranking\n  keepDiacritics?: boolean\n}\n\nexport const rankings = {\n  CASE_SENSITIVE_EQUAL: 7,\n  EQUAL: 6,\n  STARTS_WITH: 5,\n  WORD_STARTS_WITH: 4,\n  CONTAINS: 3,\n  ACRONYM: 2,\n  MATCHES: 1,\n  NO_MATCH: 0,\n} as const\n\nexport type Ranking = (typeof rankings)[keyof typeof rankings]\n\n/**\n * Gets the highest ranking for value for the given item based on its values for the given keys\n * @param {*} item - the item to rank\n * @param {String} value - the value to rank against\n * @param {Object} options - options to control the ranking\n * @return {{rank: Number, accessorIndex: Number, accessorThreshold: Number}} - the highest ranking\n */\nexport function rankItem<TItem>(\n  item: TItem,\n  value: string,\n  options?: RankItemOptions<TItem>\n): RankingInfo {\n  options = options || {}\n\n  options.threshold = options.threshold ?? rankings.MATCHES\n\n  if (!options.accessors) {\n    // if keys is not specified, then we assume the item given is ready to be matched\n    const rank = getMatchRanking(item as unknown as string, value, options)\n    return {\n      // ends up being duplicate of 'item' in matches but consistent\n      rankedValue: item,\n      rank,\n      accessorIndex: -1,\n      accessorThreshold: options.threshold,\n      passed: rank >= options.threshold,\n    }\n  }\n\n  const valuesToRank = getAllValuesToRank(item, options.accessors)\n\n  const rankingInfo: RankingInfo = {\n    rankedValue: item,\n    rank: rankings.NO_MATCH as Ranking,\n    accessorIndex: -1,\n    accessorThreshold: options.threshold,\n    passed: false,\n  }\n\n  for (let i = 0; i < valuesToRank.length; i++) {\n    const rankValue = valuesToRank[i]!\n\n    let newRank = getMatchRanking(rankValue.itemValue, value, options)\n\n    const {\n      minRanking,\n      maxRanking,\n      threshold = options.threshold,\n    } = rankValue.attributes\n\n    if (newRank < minRanking && newRank >= rankings.MATCHES) {\n      newRank = minRanking\n    } else if (newRank > maxRanking) {\n      newRank = maxRanking\n    }\n\n    newRank = Math.min(newRank, maxRanking) as Ranking\n\n    if (newRank >= threshold && newRank > rankingInfo.rank) {\n      rankingInfo.rank = newRank\n      rankingInfo.passed = true\n      rankingInfo.accessorIndex = i\n      rankingInfo.accessorThreshold = threshold\n      rankingInfo.rankedValue = rankValue.itemValue\n    }\n  }\n\n  return rankingInfo\n}\n\n/**\n * Gives a rankings score based on how well the two strings match.\n * @param {String} testString - the string to test against\n * @param {String} stringToRank - the string to rank\n * @param {Object} options - options for the match (like keepDiacritics for comparison)\n * @returns {Number} the ranking for how well stringToRank matches testString\n */\nfunction getMatchRanking<TItem>(\n  testString: string,\n  stringToRank: string,\n  options: RankItemOptions<TItem>\n): Ranking {\n  testString = prepareValueForComparison(testString, options)\n  stringToRank = prepareValueForComparison(stringToRank, options)\n\n  // too long\n  if (stringToRank.length > testString.length) {\n    return rankings.NO_MATCH\n  }\n\n  // case sensitive equals\n  if (testString === stringToRank) {\n    return rankings.CASE_SENSITIVE_EQUAL\n  }\n\n  // Lower casing before further comparison\n  testString = testString.toLowerCase()\n  stringToRank = stringToRank.toLowerCase()\n\n  // case insensitive equals\n  if (testString === stringToRank) {\n    return rankings.EQUAL\n  }\n\n  // starts with\n  if (testString.startsWith(stringToRank)) {\n    return rankings.STARTS_WITH\n  }\n\n  // word starts with\n  if (testString.includes(` ${stringToRank}`)) {\n    return rankings.WORD_STARTS_WITH\n  }\n\n  // contains\n  if (testString.includes(stringToRank)) {\n    return rankings.CONTAINS\n  } else if (stringToRank.length === 1) {\n    // If the only character in the given stringToRank\n    //   isn't even contained in the testString, then\n    //   it's definitely not a match.\n    return rankings.NO_MATCH\n  }\n\n  // acronym\n  if (getAcronym(testString).includes(stringToRank)) {\n    return rankings.ACRONYM\n  }\n\n  // will return a number between rankings.MATCHES and\n  // rankings.MATCHES + 1 depending  on how close of a match it is.\n  return getClosenessRanking(testString, stringToRank)\n}\n\n/**\n * Generates an acronym for a string.\n *\n * @param {String} string the string for which to produce the acronym\n * @returns {String} the acronym\n */\nfunction getAcronym(string: string): string {\n  let acronym = ''\n  const wordsInString = string.split(' ')\n  wordsInString.forEach(wordInString => {\n    const splitByHyphenWords = wordInString.split('-')\n    splitByHyphenWords.forEach(splitByHyphenWord => {\n      acronym += splitByHyphenWord.substr(0, 1)\n    })\n  })\n  return acronym\n}\n\n/**\n * Returns a score based on how spread apart the\n * characters from the stringToRank are within the testString.\n * A number close to rankings.MATCHES represents a loose match. A number close\n * to rankings.MATCHES + 1 represents a tighter match.\n * @param {String} testString - the string to test against\n * @param {String} stringToRank - the string to rank\n * @returns {Number} the number between rankings.MATCHES and\n * rankings.MATCHES + 1 for how well stringToRank matches testString\n */\nfunction getClosenessRanking(\n  testString: string,\n  stringToRank: string\n): Ranking {\n  let matchingInOrderCharCount = 0\n  let charNumber = 0\n  function findMatchingCharacter(\n    matchChar: undefined | string,\n    string: string,\n    index: number\n  ) {\n    for (let j = index, J = string.length; j < J; j++) {\n      const stringChar = string[j]\n      if (stringChar === matchChar) {\n        matchingInOrderCharCount += 1\n        return j + 1\n      }\n    }\n    return -1\n  }\n  function getRanking(spread: number) {\n    const spreadPercentage = 1 / spread\n    const inOrderPercentage = matchingInOrderCharCount / stringToRank.length\n    const ranking = rankings.MATCHES + inOrderPercentage * spreadPercentage\n    return ranking as Ranking\n  }\n  const firstIndex = findMatchingCharacter(stringToRank[0], testString, 0)\n  if (firstIndex < 0) {\n    return rankings.NO_MATCH\n  }\n  charNumber = firstIndex\n  for (let i = 1, I = stringToRank.length; i < I; i++) {\n    const matchChar = stringToRank[i]\n    charNumber = findMatchingCharacter(matchChar, testString, charNumber)\n    const found = charNumber > -1\n    if (!found) {\n      return rankings.NO_MATCH\n    }\n  }\n\n  const spread = charNumber - firstIndex\n  return getRanking(spread)\n}\n\n/**\n * Sorts items that have a rank, index, and accessorIndex\n * @param {Object} a - the first item to sort\n * @param {Object} b - the second item to sort\n * @return {Number} -1 if a should come first, 1 if b should come first, 0 if equal\n */\nexport function compareItems<TItem>(a: RankingInfo, b: RankingInfo): number {\n  return a.rank === b.rank ? 0 : a.rank > b.rank ? -1 : 1\n}\n\n/**\n * Prepares value for comparison by stringifying it, removing diacritics (if specified)\n * @param {String} value - the value to clean\n * @param {Object} options - {keepDiacritics: whether to remove diacritics}\n * @return {String} the prepared value\n */\nfunction prepareValueForComparison<TItem>(\n  value: string,\n  { keepDiacritics }: RankItemOptions<TItem>\n): string {\n  // value might not actually be a string at this point (we don't get to choose)\n  // so part of preparing the value for comparison is ensure that it is a string\n  value = `${value}` // toString\n  if (!keepDiacritics) {\n    value = removeAccents(value)\n  }\n  return value\n}\n\n/**\n * Gets value for key in item at arbitrarily nested keypath\n * @param {Object} item - the item\n * @param {Object|Function} key - the potentially nested keypath or property callback\n * @return {Array} - an array containing the value(s) at the nested keypath\n */\nfunction getItemValues<TItem>(\n  item: TItem,\n  accessor: Accessor<TItem>\n): Array<string> {\n  let accessorFn = accessor as AccessorFn<TItem>\n\n  if (typeof accessor === 'object') {\n    accessorFn = accessor.accessor\n  }\n\n  const value = accessorFn(item)\n\n  // because `value` can also be undefined\n  if (value == null) {\n    return []\n  }\n\n  if (Array.isArray(value)) {\n    return value\n  }\n\n  return [String(value)]\n}\n\n/**\n * Gets all the values for the given keys in the given item and returns an array of those values\n * @param item - the item from which the values will be retrieved\n * @param keys - the keys to use to retrieve the values\n * @return objects with {itemValue, attributes}\n */\nfunction getAllValuesToRank<TItem>(\n  item: TItem,\n  accessors: ReadonlyArray<Accessor<TItem>>\n) {\n  const allValues: Array<{\n    itemValue: string\n    attributes: AccessorAttributes\n  }> = []\n  for (let j = 0, J = accessors.length; j < J; j++) {\n    const accessor = accessors[j]!\n    const attributes = getAccessorAttributes(accessor)\n    const itemValues = getItemValues(item, accessor)\n    for (let i = 0, I = itemValues.length; i < I; i++) {\n      allValues.push({\n        itemValue: itemValues[i]!,\n        attributes,\n      })\n    }\n  }\n  return allValues\n}\n\nconst defaultKeyAttributes = {\n  maxRanking: Infinity as Ranking,\n  minRanking: -Infinity as Ranking,\n}\n/**\n * Gets all the attributes for the given accessor\n * @param accessor - the accessor from which the attributes will be retrieved\n * @return object containing the accessor's attributes\n */\nfunction getAccessorAttributes<TItem>(\n  accessor: Accessor<TItem>\n): AccessorAttributes {\n  if (typeof accessor === 'function') {\n    return defaultKeyAttributes\n  }\n  return { ...defaultKeyAttributes, ...accessor }\n}\n"], "names": ["rankings", "CASE_SENSITIVE_EQUAL", "EQUAL", "STARTS_WITH", "WORD_STARTS_WITH", "CONTAINS", "ACRONYM", "MATCHES", "NO_MATCH", "rankItem", "item", "value", "options", "_options$threshold", "threshold", "accessors", "rank", "getMatchRanking", "rankedValue", "accessorIndex", "accessorThreshold", "passed", "valuesToRank", "getAllValuesToRank", "rankingInfo", "i", "length", "rankValue", "newRank", "itemValue", "minRanking", "maxRanking", "attributes", "Math", "min", "testString", "stringToRank", "prepareValueForComparison", "toLowerCase", "startsWith", "includes", "getAcronym", "getClosenessRanking", "string", "acronym", "wordsInString", "split", "for<PERSON>ach", "wordInString", "splitByHyphenWords", "splitByHyphenWord", "substr", "matchingInOrderCharCount", "char<PERSON><PERSON>ber", "findMatchingCharacter", "matchChar", "index", "j", "J", "stringChar", "getRanking", "spread", "spreadPercentage", "inOrderPercentage", "ranking", "firstIndex", "I", "found", "compareItems", "a", "b", "_ref", "keepDiacritics", "removeAccents", "getItemValues", "accessor", "accessorFn", "Array", "isArray", "String", "allValues", "getAccessorAttributes", "itemValues", "push", "defaultKeyAttributes", "Infinity"], "mappings": ";;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;;AAmDO,MAAMA,QAAQ,GAAG;AACtBC,EAAAA,oBAAoB,EAAE,CAAC;AACvBC,EAAAA,KAAK,EAAE,CAAC;AACRC,EAAAA,WAAW,EAAE,CAAC;AACdC,EAAAA,gBAAgB,EAAE,CAAC;AACnBC,EAAAA,QAAQ,EAAE,CAAC;AACXC,EAAAA,OAAO,EAAE,CAAC;AACVC,EAAAA,OAAO,EAAE,CAAC;AACVC,EAAAA,QAAQ,EAAE,CAAA;AACZ,EAAU;AAIV;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,QAAQA,CACtBC,IAAW,EACXC,KAAa,EACbC,OAAgC,EACnB;AAAA,EAAA,IAAAC,kBAAA,CAAA;AACbD,EAAAA,OAAO,GAAGA,OAAO,IAAI,EAAE,CAAA;AAEvBA,EAAAA,OAAO,CAACE,SAAS,GAAAD,CAAAA,kBAAA,GAAGD,OAAO,CAACE,SAAS,KAAAD,IAAAA,GAAAA,kBAAA,GAAIb,QAAQ,CAACO,OAAO,CAAA;AAEzD,EAAA,IAAI,CAACK,OAAO,CAACG,SAAS,EAAE;AACtB;IACA,MAAMC,IAAI,GAAGC,eAAe,CAACP,IAAI,EAAuBC,KAAK,EAAEC,OAAO,CAAC,CAAA;IACvE,OAAO;AACL;AACAM,MAAAA,WAAW,EAAER,IAAI;MACjBM,IAAI;MACJG,aAAa,EAAE,CAAC,CAAC;MACjBC,iBAAiB,EAAER,OAAO,CAACE,SAAS;AACpCO,MAAAA,MAAM,EAAEL,IAAI,IAAIJ,OAAO,CAACE,SAAAA;KACzB,CAAA;AACH,GAAA;EAEA,MAAMQ,YAAY,GAAGC,kBAAkB,CAACb,IAAI,EAAEE,OAAO,CAACG,SAAS,CAAC,CAAA;AAEhE,EAAA,MAAMS,WAAwB,GAAG;AAC/BN,IAAAA,WAAW,EAAER,IAAI;IACjBM,IAAI,EAAEhB,QAAQ,CAACQ,QAAmB;IAClCW,aAAa,EAAE,CAAC,CAAC;IACjBC,iBAAiB,EAAER,OAAO,CAACE,SAAS;AACpCO,IAAAA,MAAM,EAAE,KAAA;GACT,CAAA;AAED,EAAA,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,YAAY,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;AAC5C,IAAA,MAAME,SAAS,GAAGL,YAAY,CAACG,CAAC,CAAE,CAAA;IAElC,IAAIG,OAAO,GAAGX,eAAe,CAACU,SAAS,CAACE,SAAS,EAAElB,KAAK,EAAEC,OAAO,CAAC,CAAA;IAElE,MAAM;MACJkB,UAAU;MACVC,UAAU;MACVjB,SAAS,GAAGF,OAAO,CAACE,SAAAA;KACrB,GAAGa,SAAS,CAACK,UAAU,CAAA;IAExB,IAAIJ,OAAO,GAAGE,UAAU,IAAIF,OAAO,IAAI5B,QAAQ,CAACO,OAAO,EAAE;AACvDqB,MAAAA,OAAO,GAAGE,UAAU,CAAA;AACtB,KAAC,MAAM,IAAIF,OAAO,GAAGG,UAAU,EAAE;AAC/BH,MAAAA,OAAO,GAAGG,UAAU,CAAA;AACtB,KAAA;IAEAH,OAAO,GAAGK,IAAI,CAACC,GAAG,CAACN,OAAO,EAAEG,UAAU,CAAY,CAAA;IAElD,IAAIH,OAAO,IAAId,SAAS,IAAIc,OAAO,GAAGJ,WAAW,CAACR,IAAI,EAAE;MACtDQ,WAAW,CAACR,IAAI,GAAGY,OAAO,CAAA;MAC1BJ,WAAW,CAACH,MAAM,GAAG,IAAI,CAAA;MACzBG,WAAW,CAACL,aAAa,GAAGM,CAAC,CAAA;MAC7BD,WAAW,CAACJ,iBAAiB,GAAGN,SAAS,CAAA;AACzCU,MAAAA,WAAW,CAACN,WAAW,GAAGS,SAAS,CAACE,SAAS,CAAA;AAC/C,KAAA;AACF,GAAA;AAEA,EAAA,OAAOL,WAAW,CAAA;AACpB,CAAA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASP,eAAeA,CACtBkB,UAAkB,EAClBC,YAAoB,EACpBxB,OAA+B,EACtB;AACTuB,EAAAA,UAAU,GAAGE,yBAAyB,CAACF,UAAU,EAAEvB,OAAO,CAAC,CAAA;AAC3DwB,EAAAA,YAAY,GAAGC,yBAAyB,CAACD,YAAY,EAAExB,OAAO,CAAC,CAAA;;AAE/D;AACA,EAAA,IAAIwB,YAAY,CAACV,MAAM,GAAGS,UAAU,CAACT,MAAM,EAAE;IAC3C,OAAO1B,QAAQ,CAACQ,QAAQ,CAAA;AAC1B,GAAA;;AAEA;EACA,IAAI2B,UAAU,KAAKC,YAAY,EAAE;IAC/B,OAAOpC,QAAQ,CAACC,oBAAoB,CAAA;AACtC,GAAA;;AAEA;AACAkC,EAAAA,UAAU,GAAGA,UAAU,CAACG,WAAW,EAAE,CAAA;AACrCF,EAAAA,YAAY,GAAGA,YAAY,CAACE,WAAW,EAAE,CAAA;;AAEzC;EACA,IAAIH,UAAU,KAAKC,YAAY,EAAE;IAC/B,OAAOpC,QAAQ,CAACE,KAAK,CAAA;AACvB,GAAA;;AAEA;AACA,EAAA,IAAIiC,UAAU,CAACI,UAAU,CAACH,YAAY,CAAC,EAAE;IACvC,OAAOpC,QAAQ,CAACG,WAAW,CAAA;AAC7B,GAAA;;AAEA;EACA,IAAIgC,UAAU,CAACK,QAAQ,CAAC,IAAIJ,YAAY,CAAA,CAAE,CAAC,EAAE;IAC3C,OAAOpC,QAAQ,CAACI,gBAAgB,CAAA;AAClC,GAAA;;AAEA;AACA,EAAA,IAAI+B,UAAU,CAACK,QAAQ,CAACJ,YAAY,CAAC,EAAE;IACrC,OAAOpC,QAAQ,CAACK,QAAQ,CAAA;AAC1B,GAAC,MAAM,IAAI+B,YAAY,CAACV,MAAM,KAAK,CAAC,EAAE;AACpC;AACA;AACA;IACA,OAAO1B,QAAQ,CAACQ,QAAQ,CAAA;AAC1B,GAAA;;AAEA;EACA,IAAIiC,UAAU,CAACN,UAAU,CAAC,CAACK,QAAQ,CAACJ,YAAY,CAAC,EAAE;IACjD,OAAOpC,QAAQ,CAACM,OAAO,CAAA;AACzB,GAAA;;AAEA;AACA;AACA,EAAA,OAAOoC,mBAAmB,CAACP,UAAU,EAAEC,YAAY,CAAC,CAAA;AACtD,CAAA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASK,UAAUA,CAACE,MAAc,EAAU;EAC1C,IAAIC,OAAO,GAAG,EAAE,CAAA;AAChB,EAAA,MAAMC,aAAa,GAAGF,MAAM,CAACG,KAAK,CAAC,GAAG,CAAC,CAAA;AACvCD,EAAAA,aAAa,CAACE,OAAO,CAACC,YAAY,IAAI;AACpC,IAAA,MAAMC,kBAAkB,GAAGD,YAAY,CAACF,KAAK,CAAC,GAAG,CAAC,CAAA;AAClDG,IAAAA,kBAAkB,CAACF,OAAO,CAACG,iBAAiB,IAAI;MAC9CN,OAAO,IAAIM,iBAAiB,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;AAC3C,KAAC,CAAC,CAAA;AACJ,GAAC,CAAC,CAAA;AACF,EAAA,OAAOP,OAAO,CAAA;AAChB,CAAA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASF,mBAAmBA,CAC1BP,UAAkB,EAClBC,YAAoB,EACX;EACT,IAAIgB,wBAAwB,GAAG,CAAC,CAAA;EAChC,IAAIC,UAAU,GAAG,CAAC,CAAA;AAClB,EAAA,SAASC,qBAAqBA,CAC5BC,SAA6B,EAC7BZ,MAAc,EACda,KAAa,EACb;AACA,IAAA,KAAK,IAAIC,CAAC,GAAGD,KAAK,EAAEE,CAAC,GAAGf,MAAM,CAACjB,MAAM,EAAE+B,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;AACjD,MAAA,MAAME,UAAU,GAAGhB,MAAM,CAACc,CAAC,CAAC,CAAA;MAC5B,IAAIE,UAAU,KAAKJ,SAAS,EAAE;AAC5BH,QAAAA,wBAAwB,IAAI,CAAC,CAAA;QAC7B,OAAOK,CAAC,GAAG,CAAC,CAAA;AACd,OAAA;AACF,KAAA;AACA,IAAA,OAAO,CAAC,CAAC,CAAA;AACX,GAAA;EACA,SAASG,UAAUA,CAACC,MAAc,EAAE;AAClC,IAAA,MAAMC,gBAAgB,GAAG,CAAC,GAAGD,MAAM,CAAA;AACnC,IAAA,MAAME,iBAAiB,GAAGX,wBAAwB,GAAGhB,YAAY,CAACV,MAAM,CAAA;IACxE,MAAMsC,OAAO,GAAGhE,QAAQ,CAACO,OAAO,GAAGwD,iBAAiB,GAAGD,gBAAgB,CAAA;AACvE,IAAA,OAAOE,OAAO,CAAA;AAChB,GAAA;AACA,EAAA,MAAMC,UAAU,GAAGX,qBAAqB,CAAClB,YAAY,CAAC,CAAC,CAAC,EAAED,UAAU,EAAE,CAAC,CAAC,CAAA;EACxE,IAAI8B,UAAU,GAAG,CAAC,EAAE;IAClB,OAAOjE,QAAQ,CAACQ,QAAQ,CAAA;AAC1B,GAAA;AACA6C,EAAAA,UAAU,GAAGY,UAAU,CAAA;AACvB,EAAA,KAAK,IAAIxC,CAAC,GAAG,CAAC,EAAEyC,CAAC,GAAG9B,YAAY,CAACV,MAAM,EAAED,CAAC,GAAGyC,CAAC,EAAEzC,CAAC,EAAE,EAAE;AACnD,IAAA,MAAM8B,SAAS,GAAGnB,YAAY,CAACX,CAAC,CAAC,CAAA;IACjC4B,UAAU,GAAGC,qBAAqB,CAACC,SAAS,EAAEpB,UAAU,EAAEkB,UAAU,CAAC,CAAA;AACrE,IAAA,MAAMc,KAAK,GAAGd,UAAU,GAAG,CAAC,CAAC,CAAA;IAC7B,IAAI,CAACc,KAAK,EAAE;MACV,OAAOnE,QAAQ,CAACQ,QAAQ,CAAA;AAC1B,KAAA;AACF,GAAA;AAEA,EAAA,MAAMqD,MAAM,GAAGR,UAAU,GAAGY,UAAU,CAAA;EACtC,OAAOL,UAAU,CAACC,MAAM,CAAC,CAAA;AAC3B,CAAA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAASO,YAAYA,CAAQC,CAAc,EAAEC,CAAc,EAAU;EAC1E,OAAOD,CAAC,CAACrD,IAAI,KAAKsD,CAAC,CAACtD,IAAI,GAAG,CAAC,GAAGqD,CAAC,CAACrD,IAAI,GAAGsD,CAAC,CAACtD,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,CAAA;AACzD,CAAA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASqB,yBAAyBA,CAChC1B,KAAa,EAAA4D,IAAA,EAEL;EAAA,IADR;AAAEC,IAAAA,cAAAA;AAAuC,GAAC,GAAAD,IAAA,CAAA;AAE1C;AACA;AACA5D,EAAAA,KAAK,GAAG,CAAA,EAAGA,KAAK,CAAA,CAAE,CAAC;EACnB,IAAI,CAAC6D,cAAc,EAAE;AACnB7D,IAAAA,KAAK,GAAG8D,2BAAa,CAAC9D,KAAK,CAAC,CAAA;AAC9B,GAAA;AACA,EAAA,OAAOA,KAAK,CAAA;AACd,CAAA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS+D,aAAaA,CACpBhE,IAAW,EACXiE,QAAyB,EACV;EACf,IAAIC,UAAU,GAAGD,QAA6B,CAAA;AAE9C,EAAA,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;IAChCC,UAAU,GAAGD,QAAQ,CAACA,QAAQ,CAAA;AAChC,GAAA;AAEA,EAAA,MAAMhE,KAAK,GAAGiE,UAAU,CAAClE,IAAI,CAAC,CAAA;;AAE9B;EACA,IAAIC,KAAK,IAAI,IAAI,EAAE;AACjB,IAAA,OAAO,EAAE,CAAA;AACX,GAAA;AAEA,EAAA,IAAIkE,KAAK,CAACC,OAAO,CAACnE,KAAK,CAAC,EAAE;AACxB,IAAA,OAAOA,KAAK,CAAA;AACd,GAAA;AAEA,EAAA,OAAO,CAACoE,MAAM,CAACpE,KAAK,CAAC,CAAC,CAAA;AACxB,CAAA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASY,kBAAkBA,CACzBb,IAAW,EACXK,SAAyC,EACzC;EACA,MAAMiE,SAGJ,GAAG,EAAE,CAAA;AACP,EAAA,KAAK,IAAIvB,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG3C,SAAS,CAACW,MAAM,EAAE+B,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;AAChD,IAAA,MAAMkB,QAAQ,GAAG5D,SAAS,CAAC0C,CAAC,CAAE,CAAA;AAC9B,IAAA,MAAMzB,UAAU,GAAGiD,qBAAqB,CAACN,QAAQ,CAAC,CAAA;AAClD,IAAA,MAAMO,UAAU,GAAGR,aAAa,CAAChE,IAAI,EAAEiE,QAAQ,CAAC,CAAA;AAChD,IAAA,KAAK,IAAIlD,CAAC,GAAG,CAAC,EAAEyC,CAAC,GAAGgB,UAAU,CAACxD,MAAM,EAAED,CAAC,GAAGyC,CAAC,EAAEzC,CAAC,EAAE,EAAE;MACjDuD,SAAS,CAACG,IAAI,CAAC;AACbtD,QAAAA,SAAS,EAAEqD,UAAU,CAACzD,CAAC,CAAE;AACzBO,QAAAA,UAAAA;AACF,OAAC,CAAC,CAAA;AACJ,KAAA;AACF,GAAA;AACA,EAAA,OAAOgD,SAAS,CAAA;AAClB,CAAA;AAEA,MAAMI,oBAAoB,GAAG;AAC3BrD,EAAAA,UAAU,EAAEsD,QAAmB;AAC/BvD,EAAAA,UAAU,EAAE,CAACuD,QAAAA;AACf,CAAC,CAAA;AACD;AACA;AACA;AACA;AACA;AACA,SAASJ,qBAAqBA,CAC5BN,QAAyB,EACL;AACpB,EAAA,IAAI,OAAOA,QAAQ,KAAK,UAAU,EAAE;AAClC,IAAA,OAAOS,oBAAoB,CAAA;AAC7B,GAAA;EACA,OAAO;AAAE,IAAA,GAAGA,oBAAoB;IAAE,GAAGT,QAAAA;GAAU,CAAA;AACjD;;;;;;"}