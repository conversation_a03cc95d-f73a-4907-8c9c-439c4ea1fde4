{"ast": null, "code": "import { useMemo } from 'react';\nimport { isForcedMotionValue } from '../../motion/utils/is-forced-motion-value.mjs';\nimport { isMotionValue } from '../../value/utils/is-motion-value.mjs';\nimport { buildHTMLStyles } from './utils/build-styles.mjs';\nimport { createHtmlRenderState } from './utils/create-render-state.mjs';\nfunction copyRawValuesOnly(target, source, props) {\n  for (const key in source) {\n    if (!isMotionValue(source[key]) && !isForcedMotionValue(key, props)) {\n      target[key] = source[key];\n    }\n  }\n}\nfunction useInitialMotionValues({\n  transformTemplate\n}, visualState, isStatic) {\n  return useMemo(() => {\n    const state = createHtmlRenderState();\n    buildHTMLStyles(state, visualState, {\n      enableHardwareAcceleration: !isStatic\n    }, transformTemplate);\n    return Object.assign({}, state.vars, state.style);\n  }, [visualState]);\n}\nfunction useStyle(props, visualState, isStatic) {\n  const styleProp = props.style || {};\n  const style = {};\n  /**\n   * Copy non-Motion Values straight into style\n   */\n  copyRawValuesOnly(style, styleProp, props);\n  Object.assign(style, useInitialMotionValues(props, visualState, isStatic));\n  return props.transformValues ? props.transformValues(style) : style;\n}\nfunction useHTMLProps(props, visualState, isStatic) {\n  // The `any` isn't ideal but it is the type of createElement props argument\n  const htmlProps = {};\n  const style = useStyle(props, visualState, isStatic);\n  if (props.drag && props.dragListener !== false) {\n    // Disable the ghost element when a user drags\n    htmlProps.draggable = false;\n    // Disable text selection\n    style.userSelect = style.WebkitUserSelect = style.WebkitTouchCallout = \"none\";\n    // Disable scrolling on the draggable direction\n    style.touchAction = props.drag === true ? \"none\" : `pan-${props.drag === \"x\" ? \"y\" : \"x\"}`;\n  }\n  if (props.tabIndex === undefined && (props.onTap || props.onTapStart || props.whileTap)) {\n    htmlProps.tabIndex = 0;\n  }\n  htmlProps.style = style;\n  return htmlProps;\n}\nexport { copyRawValuesOnly, useHTMLProps };", "map": {"version": 3, "names": ["useMemo", "isForcedMotionValue", "isMotionValue", "buildHTMLStyles", "createHtmlRenderState", "copyRawValuesOnly", "target", "source", "props", "key", "useInitialMotionValues", "transformTemplate", "visualState", "isStatic", "state", "enableHardwareAcceleration", "Object", "assign", "vars", "style", "useStyle", "styleProp", "transformValues", "useHTMLProps", "htmlProps", "drag", "dragListener", "draggable", "userSelect", "WebkitUserSelect", "WebkitTouchCallout", "touchAction", "tabIndex", "undefined", "onTap", "onTapStart", "whileTap"], "sources": ["D:/menasa/frontend/node_modules/framer-motion/dist/es/render/html/use-props.mjs"], "sourcesContent": ["import { useMemo } from 'react';\nimport { isForcedMotionValue } from '../../motion/utils/is-forced-motion-value.mjs';\nimport { isMotionValue } from '../../value/utils/is-motion-value.mjs';\nimport { buildHTMLStyles } from './utils/build-styles.mjs';\nimport { createHtmlRenderState } from './utils/create-render-state.mjs';\n\nfunction copyRawValuesOnly(target, source, props) {\n    for (const key in source) {\n        if (!isMotionValue(source[key]) && !isForcedMotionValue(key, props)) {\n            target[key] = source[key];\n        }\n    }\n}\nfunction useInitialMotionValues({ transformTemplate }, visualState, isStatic) {\n    return useMemo(() => {\n        const state = createHtmlRenderState();\n        buildHTMLStyles(state, visualState, { enableHardwareAcceleration: !isStatic }, transformTemplate);\n        return Object.assign({}, state.vars, state.style);\n    }, [visualState]);\n}\nfunction useStyle(props, visualState, isStatic) {\n    const styleProp = props.style || {};\n    const style = {};\n    /**\n     * Copy non-Motion Values straight into style\n     */\n    copyRawValuesOnly(style, styleProp, props);\n    Object.assign(style, useInitialMotionValues(props, visualState, isStatic));\n    return props.transformValues ? props.transformValues(style) : style;\n}\nfunction useHTMLProps(props, visualState, isStatic) {\n    // The `any` isn't ideal but it is the type of createElement props argument\n    const htmlProps = {};\n    const style = useStyle(props, visualState, isStatic);\n    if (props.drag && props.dragListener !== false) {\n        // Disable the ghost element when a user drags\n        htmlProps.draggable = false;\n        // Disable text selection\n        style.userSelect =\n            style.WebkitUserSelect =\n                style.WebkitTouchCallout =\n                    \"none\";\n        // Disable scrolling on the draggable direction\n        style.touchAction =\n            props.drag === true\n                ? \"none\"\n                : `pan-${props.drag === \"x\" ? \"y\" : \"x\"}`;\n    }\n    if (props.tabIndex === undefined &&\n        (props.onTap || props.onTapStart || props.whileTap)) {\n        htmlProps.tabIndex = 0;\n    }\n    htmlProps.style = style;\n    return htmlProps;\n}\n\nexport { copyRawValuesOnly, useHTMLProps };\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,OAAO;AAC/B,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,aAAa,QAAQ,uCAAuC;AACrE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,qBAAqB,QAAQ,iCAAiC;AAEvE,SAASC,iBAAiBA,CAACC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAE;EAC9C,KAAK,MAAMC,GAAG,IAAIF,MAAM,EAAE;IACtB,IAAI,CAACL,aAAa,CAACK,MAAM,CAACE,GAAG,CAAC,CAAC,IAAI,CAACR,mBAAmB,CAACQ,GAAG,EAAED,KAAK,CAAC,EAAE;MACjEF,MAAM,CAACG,GAAG,CAAC,GAAGF,MAAM,CAACE,GAAG,CAAC;IAC7B;EACJ;AACJ;AACA,SAASC,sBAAsBA,CAAC;EAAEC;AAAkB,CAAC,EAAEC,WAAW,EAAEC,QAAQ,EAAE;EAC1E,OAAOb,OAAO,CAAC,MAAM;IACjB,MAAMc,KAAK,GAAGV,qBAAqB,CAAC,CAAC;IACrCD,eAAe,CAACW,KAAK,EAAEF,WAAW,EAAE;MAAEG,0BAA0B,EAAE,CAACF;IAAS,CAAC,EAAEF,iBAAiB,CAAC;IACjG,OAAOK,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEH,KAAK,CAACI,IAAI,EAAEJ,KAAK,CAACK,KAAK,CAAC;EACrD,CAAC,EAAE,CAACP,WAAW,CAAC,CAAC;AACrB;AACA,SAASQ,QAAQA,CAACZ,KAAK,EAAEI,WAAW,EAAEC,QAAQ,EAAE;EAC5C,MAAMQ,SAAS,GAAGb,KAAK,CAACW,KAAK,IAAI,CAAC,CAAC;EACnC,MAAMA,KAAK,GAAG,CAAC,CAAC;EAChB;AACJ;AACA;EACId,iBAAiB,CAACc,KAAK,EAAEE,SAAS,EAAEb,KAAK,CAAC;EAC1CQ,MAAM,CAACC,MAAM,CAACE,KAAK,EAAET,sBAAsB,CAACF,KAAK,EAAEI,WAAW,EAAEC,QAAQ,CAAC,CAAC;EAC1E,OAAOL,KAAK,CAACc,eAAe,GAAGd,KAAK,CAACc,eAAe,CAACH,KAAK,CAAC,GAAGA,KAAK;AACvE;AACA,SAASI,YAAYA,CAACf,KAAK,EAAEI,WAAW,EAAEC,QAAQ,EAAE;EAChD;EACA,MAAMW,SAAS,GAAG,CAAC,CAAC;EACpB,MAAML,KAAK,GAAGC,QAAQ,CAACZ,KAAK,EAAEI,WAAW,EAAEC,QAAQ,CAAC;EACpD,IAAIL,KAAK,CAACiB,IAAI,IAAIjB,KAAK,CAACkB,YAAY,KAAK,KAAK,EAAE;IAC5C;IACAF,SAAS,CAACG,SAAS,GAAG,KAAK;IAC3B;IACAR,KAAK,CAACS,UAAU,GACZT,KAAK,CAACU,gBAAgB,GAClBV,KAAK,CAACW,kBAAkB,GACpB,MAAM;IAClB;IACAX,KAAK,CAACY,WAAW,GACbvB,KAAK,CAACiB,IAAI,KAAK,IAAI,GACb,MAAM,GACN,OAAOjB,KAAK,CAACiB,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE;EACrD;EACA,IAAIjB,KAAK,CAACwB,QAAQ,KAAKC,SAAS,KAC3BzB,KAAK,CAAC0B,KAAK,IAAI1B,KAAK,CAAC2B,UAAU,IAAI3B,KAAK,CAAC4B,QAAQ,CAAC,EAAE;IACrDZ,SAAS,CAACQ,QAAQ,GAAG,CAAC;EAC1B;EACAR,SAAS,CAACL,KAAK,GAAGA,KAAK;EACvB,OAAOK,SAAS;AACpB;AAEA,SAASnB,iBAAiB,EAAEkB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}