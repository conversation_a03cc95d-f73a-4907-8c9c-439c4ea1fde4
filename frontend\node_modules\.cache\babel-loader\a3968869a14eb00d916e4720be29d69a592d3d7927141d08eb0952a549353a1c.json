{"ast": null, "code": "import { createSlice } from '@reduxjs/toolkit';\nvar initialState = {\n  layoutType: 'horizontal',\n  width: 0,\n  height: 0,\n  margin: {\n    top: 5,\n    right: 5,\n    bottom: 5,\n    left: 5\n  },\n  scale: 1\n};\nvar chartLayoutSlice = createSlice({\n  name: 'chartLayout',\n  initialState,\n  reducers: {\n    setLayout(state, action) {\n      state.layoutType = action.payload;\n    },\n    setChartSize(state, action) {\n      state.width = action.payload.width;\n      state.height = action.payload.height;\n    },\n    setMargin(state, action) {\n      state.margin.top = action.payload.top;\n      state.margin.right = action.payload.right;\n      state.margin.bottom = action.payload.bottom;\n      state.margin.left = action.payload.left;\n    },\n    setScale(state, action) {\n      state.scale = action.payload;\n    }\n  }\n});\nexport var {\n  setMargin,\n  setLayout,\n  setChartSize,\n  setScale\n} = chartLayoutSlice.actions;\nexport var chartLayoutReducer = chartLayoutSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "initialState", "layoutType", "width", "height", "margin", "top", "right", "bottom", "left", "scale", "chartLayoutSlice", "name", "reducers", "setLayout", "state", "action", "payload", "setChartSize", "<PERSON><PERSON><PERSON><PERSON>", "setScale", "actions", "chartLayoutReducer", "reducer"], "sources": ["D:/menasa/frontend/node_modules/recharts/es6/state/layoutSlice.js"], "sourcesContent": ["import { createSlice } from '@reduxjs/toolkit';\nvar initialState = {\n  layoutType: 'horizontal',\n  width: 0,\n  height: 0,\n  margin: {\n    top: 5,\n    right: 5,\n    bottom: 5,\n    left: 5\n  },\n  scale: 1\n};\nvar chartLayoutSlice = createSlice({\n  name: 'chartLayout',\n  initialState,\n  reducers: {\n    setLayout(state, action) {\n      state.layoutType = action.payload;\n    },\n    setChartSize(state, action) {\n      state.width = action.payload.width;\n      state.height = action.payload.height;\n    },\n    setMargin(state, action) {\n      state.margin.top = action.payload.top;\n      state.margin.right = action.payload.right;\n      state.margin.bottom = action.payload.bottom;\n      state.margin.left = action.payload.left;\n    },\n    setScale(state, action) {\n      state.scale = action.payload;\n    }\n  }\n});\nexport var {\n  setMargin,\n  setLayout,\n  setChartSize,\n  setScale\n} = chartLayoutSlice.actions;\nexport var chartLayoutReducer = chartLayoutSlice.reducer;"], "mappings": "AAAA,SAASA,WAAW,QAAQ,kBAAkB;AAC9C,IAAIC,YAAY,GAAG;EACjBC,UAAU,EAAE,YAAY;EACxBC,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE,CAAC;EACTC,MAAM,EAAE;IACNC,GAAG,EAAE,CAAC;IACNC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTC,IAAI,EAAE;EACR,CAAC;EACDC,KAAK,EAAE;AACT,CAAC;AACD,IAAIC,gBAAgB,GAAGX,WAAW,CAAC;EACjCY,IAAI,EAAE,aAAa;EACnBX,YAAY;EACZY,QAAQ,EAAE;IACRC,SAASA,CAACC,KAAK,EAAEC,MAAM,EAAE;MACvBD,KAAK,CAACb,UAAU,GAAGc,MAAM,CAACC,OAAO;IACnC,CAAC;IACDC,YAAYA,CAACH,KAAK,EAAEC,MAAM,EAAE;MAC1BD,KAAK,CAACZ,KAAK,GAAGa,MAAM,CAACC,OAAO,CAACd,KAAK;MAClCY,KAAK,CAACX,MAAM,GAAGY,MAAM,CAACC,OAAO,CAACb,MAAM;IACtC,CAAC;IACDe,SAASA,CAACJ,KAAK,EAAEC,MAAM,EAAE;MACvBD,KAAK,CAACV,MAAM,CAACC,GAAG,GAAGU,MAAM,CAACC,OAAO,CAACX,GAAG;MACrCS,KAAK,CAACV,MAAM,CAACE,KAAK,GAAGS,MAAM,CAACC,OAAO,CAACV,KAAK;MACzCQ,KAAK,CAACV,MAAM,CAACG,MAAM,GAAGQ,MAAM,CAACC,OAAO,CAACT,MAAM;MAC3CO,KAAK,CAACV,MAAM,CAACI,IAAI,GAAGO,MAAM,CAACC,OAAO,CAACR,IAAI;IACzC,CAAC;IACDW,QAAQA,CAACL,KAAK,EAAEC,MAAM,EAAE;MACtBD,KAAK,CAACL,KAAK,GAAGM,MAAM,CAACC,OAAO;IAC9B;EACF;AACF,CAAC,CAAC;AACF,OAAO,IAAI;EACTE,SAAS;EACTL,SAAS;EACTI,YAAY;EACZE;AACF,CAAC,GAAGT,gBAAgB,CAACU,OAAO;AAC5B,OAAO,IAAIC,kBAAkB,GAAGX,gBAAgB,CAACY,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}