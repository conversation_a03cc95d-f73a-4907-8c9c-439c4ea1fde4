{"version": 3, "file": "screenreader.esm.js", "sources": ["../../src/screenreader.tsx"], "sourcesContent": ["import * as React from 'react'\n\nexport default function ScreenReader({ text }: { text: string }) {\n  return (\n    <span\n      style={{\n        position: 'absolute',\n        width: '0.1px',\n        height: '0.1px',\n        overflow: 'hidden',\n      }}\n    >\n      {text}\n    </span>\n  )\n}\n"], "names": ["ScreenReader", "text", "position", "width", "height", "overflow"], "mappings": ";;AAEe,SAASA,YAAT,CAAsB;AAAEC,EAAAA,IAAAA;AAAF,CAAtB,EAAkD;EAC/D,oBACE,KAAA,CAAA,aAAA,CAAA,MAAA,EAAA;AACE,IAAA,KAAK,EAAE;AACLC,MAAAA,QAAQ,EAAE,UADL;AAELC,MAAAA,KAAK,EAAE,OAFF;AAGLC,MAAAA,MAAM,EAAE,OAHH;AAILC,MAAAA,QAAQ,EAAE,QAAA;AAJL,KAAA;AADT,GAAA,EAQGJ,IARH,CADF,CAAA;AAYD;;;;"}