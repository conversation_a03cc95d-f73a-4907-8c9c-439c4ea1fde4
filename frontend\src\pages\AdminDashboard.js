import React, { useState } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import Sidebar from '../components/admin/Sidebar';
import FileUpload from '../components/admin/FileUpload';
import UserManagement from '../components/admin/UserManagement';
import Statistics from '../components/admin/Statistics';
import FileManagement from '../components/admin/FileManagement';
import ReportsCenter from '../components/reports/ReportsCenter';

const AdminLayout = styled.div`
  display: flex;
  min-height: 100vh;
  background: ${({ theme }) => theme.colors.gray[50]};
`;

const Content = styled(motion.div)`
  flex: 1;
  background: ${({ theme }) => theme.colors.gray[50]};
  overflow-x: hidden;
`;

function AdminDashboard() {
  const [activeTab, setActiveTab] = useState('dashboard');

  const renderContent = () => {
    switch(activeTab) {
      case 'dashboard':
        return <Statistics />;
      case 'analytics':
        return <ReportsCenter />;
      case 'files':
        return <FileManagement />;
      case 'upload':
        return <FileUpload />;
      case 'users':
        return <UserManagement />;
      case 'settings':
        return <div style={{ padding: '48px', textAlign: 'center', color: '#6b7280' }}>
          <h2>الإعدادات</h2>
          <p>صفحة الإعدادات قيد التطوير</p>
        </div>;
      default:
        return <Statistics />;
    }
  };

  return (
    <AdminLayout>
      <Sidebar activeTab={activeTab} onTabChange={setActiveTab} />
      <Content
        key={activeTab}
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.3 }}
      >
        {renderContent()}
      </Content>
    </AdminLayout>
  );
}

export default AdminDashboard;