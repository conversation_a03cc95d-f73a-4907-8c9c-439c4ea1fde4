{"ast": null, "code": "function sign(x) {\n  return x < 0 ? -1 : 1;\n}\n\n// Calculate the slopes of the tangents (Hermite-type interpolation) based on\n// the following paper: <PERSON>effen, M. 1990. A Simple Method for Monotonic\n// Interpolation in One Dimension. Astronomy and Astrophysics, Vol. 239, NO.\n// NOV(II), P. 443, 1990.\nfunction slope3(that, x2, y2) {\n  var h0 = that._x1 - that._x0,\n    h1 = x2 - that._x1,\n    s0 = (that._y1 - that._y0) / (h0 || h1 < 0 && -0),\n    s1 = (y2 - that._y1) / (h1 || h0 < 0 && -0),\n    p = (s0 * h1 + s1 * h0) / (h0 + h1);\n  return (sign(s0) + sign(s1)) * Math.min(Math.abs(s0), Math.abs(s1), 0.5 * Math.abs(p)) || 0;\n}\n\n// Calculate a one-sided slope.\nfunction slope2(that, t) {\n  var h = that._x1 - that._x0;\n  return h ? (3 * (that._y1 - that._y0) / h - t) / 2 : t;\n}\n\n// According to https://en.wikipedia.org/wiki/Cubic_Hermite_spline#Representations\n// \"you can express cubic Hermite interpolation in terms of cubic Bézier curves\n// with respect to the four values p0, p0 + m0 / 3, p1 - m1 / 3, p1\".\nfunction point(that, t0, t1) {\n  var x0 = that._x0,\n    y0 = that._y0,\n    x1 = that._x1,\n    y1 = that._y1,\n    dx = (x1 - x0) / 3;\n  that._context.bezierCurveTo(x0 + dx, y0 + dx * t0, x1 - dx, y1 - dx * t1, x1, y1);\n}\nfunction MonotoneX(context) {\n  this._context = context;\n}\nMonotoneX.prototype = {\n  areaStart: function () {\n    this._line = 0;\n  },\n  areaEnd: function () {\n    this._line = NaN;\n  },\n  lineStart: function () {\n    this._x0 = this._x1 = this._y0 = this._y1 = this._t0 = NaN;\n    this._point = 0;\n  },\n  lineEnd: function () {\n    switch (this._point) {\n      case 2:\n        this._context.lineTo(this._x1, this._y1);\n        break;\n      case 3:\n        point(this, this._t0, slope2(this, this._t0));\n        break;\n    }\n    if (this._line || this._line !== 0 && this._point === 1) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function (x, y) {\n    var t1 = NaN;\n    x = +x, y = +y;\n    if (x === this._x1 && y === this._y1) return; // Ignore coincident points.\n    switch (this._point) {\n      case 0:\n        this._point = 1;\n        this._line ? this._context.lineTo(x, y) : this._context.moveTo(x, y);\n        break;\n      case 1:\n        this._point = 2;\n        break;\n      case 2:\n        this._point = 3;\n        point(this, slope2(this, t1 = slope3(this, x, y)), t1);\n        break;\n      default:\n        point(this, this._t0, t1 = slope3(this, x, y));\n        break;\n    }\n    this._x0 = this._x1, this._x1 = x;\n    this._y0 = this._y1, this._y1 = y;\n    this._t0 = t1;\n  }\n};\nfunction MonotoneY(context) {\n  this._context = new ReflectContext(context);\n}\n(MonotoneY.prototype = Object.create(MonotoneX.prototype)).point = function (x, y) {\n  MonotoneX.prototype.point.call(this, y, x);\n};\nfunction ReflectContext(context) {\n  this._context = context;\n}\nReflectContext.prototype = {\n  moveTo: function (x, y) {\n    this._context.moveTo(y, x);\n  },\n  closePath: function () {\n    this._context.closePath();\n  },\n  lineTo: function (x, y) {\n    this._context.lineTo(y, x);\n  },\n  bezierCurveTo: function (x1, y1, x2, y2, x, y) {\n    this._context.bezierCurveTo(y1, x1, y2, x2, y, x);\n  }\n};\nexport function monotoneX(context) {\n  return new MonotoneX(context);\n}\nexport function monotoneY(context) {\n  return new MonotoneY(context);\n}", "map": {"version": 3, "names": ["sign", "x", "slope3", "that", "x2", "y2", "h0", "_x1", "_x0", "h1", "s0", "_y1", "_y0", "s1", "p", "Math", "min", "abs", "slope2", "t", "h", "point", "t0", "t1", "x0", "y0", "x1", "y1", "dx", "_context", "bezierCurveTo", "MonotoneX", "context", "prototype", "areaStart", "_line", "areaEnd", "NaN", "lineStart", "_t0", "_point", "lineEnd", "lineTo", "closePath", "y", "moveTo", "MonotoneY", "ReflectContext", "Object", "create", "call", "monotoneX", "monotoneY"], "sources": ["D:/menasa/frontend/node_modules/d3-shape/src/curve/monotone.js"], "sourcesContent": ["function sign(x) {\n  return x < 0 ? -1 : 1;\n}\n\n// Calculate the slopes of the tangents (Hermite-type interpolation) based on\n// the following paper: <PERSON>effen, M. 1990. A Simple Method for Monotonic\n// Interpolation in One Dimension. Astronomy and Astrophysics, Vol. 239, NO.\n// NOV(II), P. 443, 1990.\nfunction slope3(that, x2, y2) {\n  var h0 = that._x1 - that._x0,\n      h1 = x2 - that._x1,\n      s0 = (that._y1 - that._y0) / (h0 || h1 < 0 && -0),\n      s1 = (y2 - that._y1) / (h1 || h0 < 0 && -0),\n      p = (s0 * h1 + s1 * h0) / (h0 + h1);\n  return (sign(s0) + sign(s1)) * Math.min(Math.abs(s0), Math.abs(s1), 0.5 * Math.abs(p)) || 0;\n}\n\n// Calculate a one-sided slope.\nfunction slope2(that, t) {\n  var h = that._x1 - that._x0;\n  return h ? (3 * (that._y1 - that._y0) / h - t) / 2 : t;\n}\n\n// According to https://en.wikipedia.org/wiki/Cubic_Hermite_spline#Representations\n// \"you can express cubic Hermite interpolation in terms of cubic Bézier curves\n// with respect to the four values p0, p0 + m0 / 3, p1 - m1 / 3, p1\".\nfunction point(that, t0, t1) {\n  var x0 = that._x0,\n      y0 = that._y0,\n      x1 = that._x1,\n      y1 = that._y1,\n      dx = (x1 - x0) / 3;\n  that._context.bezierCurveTo(x0 + dx, y0 + dx * t0, x1 - dx, y1 - dx * t1, x1, y1);\n}\n\nfunction MonotoneX(context) {\n  this._context = context;\n}\n\nMonotoneX.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._x0 = this._x1 =\n    this._y0 = this._y1 =\n    this._t0 = NaN;\n    this._point = 0;\n  },\n  lineEnd: function() {\n    switch (this._point) {\n      case 2: this._context.lineTo(this._x1, this._y1); break;\n      case 3: point(this, this._t0, slope2(this, this._t0)); break;\n    }\n    if (this._line || (this._line !== 0 && this._point === 1)) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function(x, y) {\n    var t1 = NaN;\n\n    x = +x, y = +y;\n    if (x === this._x1 && y === this._y1) return; // Ignore coincident points.\n    switch (this._point) {\n      case 0: this._point = 1; this._line ? this._context.lineTo(x, y) : this._context.moveTo(x, y); break;\n      case 1: this._point = 2; break;\n      case 2: this._point = 3; point(this, slope2(this, t1 = slope3(this, x, y)), t1); break;\n      default: point(this, this._t0, t1 = slope3(this, x, y)); break;\n    }\n\n    this._x0 = this._x1, this._x1 = x;\n    this._y0 = this._y1, this._y1 = y;\n    this._t0 = t1;\n  }\n}\n\nfunction MonotoneY(context) {\n  this._context = new ReflectContext(context);\n}\n\n(MonotoneY.prototype = Object.create(MonotoneX.prototype)).point = function(x, y) {\n  MonotoneX.prototype.point.call(this, y, x);\n};\n\nfunction ReflectContext(context) {\n  this._context = context;\n}\n\nReflectContext.prototype = {\n  moveTo: function(x, y) { this._context.moveTo(y, x); },\n  closePath: function() { this._context.closePath(); },\n  lineTo: function(x, y) { this._context.lineTo(y, x); },\n  bezierCurveTo: function(x1, y1, x2, y2, x, y) { this._context.bezierCurveTo(y1, x1, y2, x2, y, x); }\n};\n\nexport function monotoneX(context) {\n  return new MonotoneX(context);\n}\n\nexport function monotoneY(context) {\n  return new MonotoneY(context);\n}\n"], "mappings": "AAAA,SAASA,IAAIA,CAACC,CAAC,EAAE;EACf,OAAOA,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;AACvB;;AAEA;AACA;AACA;AACA;AACA,SAASC,MAAMA,CAACC,IAAI,EAAEC,EAAE,EAAEC,EAAE,EAAE;EAC5B,IAAIC,EAAE,GAAGH,IAAI,CAACI,GAAG,GAAGJ,IAAI,CAACK,GAAG;IACxBC,EAAE,GAAGL,EAAE,GAAGD,IAAI,CAACI,GAAG;IAClBG,EAAE,GAAG,CAACP,IAAI,CAACQ,GAAG,GAAGR,IAAI,CAACS,GAAG,KAAKN,EAAE,IAAIG,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;IACjDI,EAAE,GAAG,CAACR,EAAE,GAAGF,IAAI,CAACQ,GAAG,KAAKF,EAAE,IAAIH,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;IAC3CQ,CAAC,GAAG,CAACJ,EAAE,GAAGD,EAAE,GAAGI,EAAE,GAAGP,EAAE,KAAKA,EAAE,GAAGG,EAAE,CAAC;EACvC,OAAO,CAACT,IAAI,CAACU,EAAE,CAAC,GAAGV,IAAI,CAACa,EAAE,CAAC,IAAIE,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACP,EAAE,CAAC,EAAEK,IAAI,CAACE,GAAG,CAACJ,EAAE,CAAC,EAAE,GAAG,GAAGE,IAAI,CAACE,GAAG,CAACH,CAAC,CAAC,CAAC,IAAI,CAAC;AAC7F;;AAEA;AACA,SAASI,MAAMA,CAACf,IAAI,EAAEgB,CAAC,EAAE;EACvB,IAAIC,CAAC,GAAGjB,IAAI,CAACI,GAAG,GAAGJ,IAAI,CAACK,GAAG;EAC3B,OAAOY,CAAC,GAAG,CAAC,CAAC,IAAIjB,IAAI,CAACQ,GAAG,GAAGR,IAAI,CAACS,GAAG,CAAC,GAAGQ,CAAC,GAAGD,CAAC,IAAI,CAAC,GAAGA,CAAC;AACxD;;AAEA;AACA;AACA;AACA,SAASE,KAAKA,CAAClB,IAAI,EAAEmB,EAAE,EAAEC,EAAE,EAAE;EAC3B,IAAIC,EAAE,GAAGrB,IAAI,CAACK,GAAG;IACbiB,EAAE,GAAGtB,IAAI,CAACS,GAAG;IACbc,EAAE,GAAGvB,IAAI,CAACI,GAAG;IACboB,EAAE,GAAGxB,IAAI,CAACQ,GAAG;IACbiB,EAAE,GAAG,CAACF,EAAE,GAAGF,EAAE,IAAI,CAAC;EACtBrB,IAAI,CAAC0B,QAAQ,CAACC,aAAa,CAACN,EAAE,GAAGI,EAAE,EAAEH,EAAE,GAAGG,EAAE,GAAGN,EAAE,EAAEI,EAAE,GAAGE,EAAE,EAAED,EAAE,GAAGC,EAAE,GAAGL,EAAE,EAAEG,EAAE,EAAEC,EAAE,CAAC;AACnF;AAEA,SAASI,SAASA,CAACC,OAAO,EAAE;EAC1B,IAAI,CAACH,QAAQ,GAAGG,OAAO;AACzB;AAEAD,SAAS,CAACE,SAAS,GAAG;EACpBC,SAAS,EAAE,SAAAA,CAAA,EAAW;IACpB,IAAI,CAACC,KAAK,GAAG,CAAC;EAChB,CAAC;EACDC,OAAO,EAAE,SAAAA,CAAA,EAAW;IAClB,IAAI,CAACD,KAAK,GAAGE,GAAG;EAClB,CAAC;EACDC,SAAS,EAAE,SAAAA,CAAA,EAAW;IACpB,IAAI,CAAC9B,GAAG,GAAG,IAAI,CAACD,GAAG,GACnB,IAAI,CAACK,GAAG,GAAG,IAAI,CAACD,GAAG,GACnB,IAAI,CAAC4B,GAAG,GAAGF,GAAG;IACd,IAAI,CAACG,MAAM,GAAG,CAAC;EACjB,CAAC;EACDC,OAAO,EAAE,SAAAA,CAAA,EAAW;IAClB,QAAQ,IAAI,CAACD,MAAM;MACjB,KAAK,CAAC;QAAE,IAAI,CAACX,QAAQ,CAACa,MAAM,CAAC,IAAI,CAACnC,GAAG,EAAE,IAAI,CAACI,GAAG,CAAC;QAAE;MAClD,KAAK,CAAC;QAAEU,KAAK,CAAC,IAAI,EAAE,IAAI,CAACkB,GAAG,EAAErB,MAAM,CAAC,IAAI,EAAE,IAAI,CAACqB,GAAG,CAAC,CAAC;QAAE;IACzD;IACA,IAAI,IAAI,CAACJ,KAAK,IAAK,IAAI,CAACA,KAAK,KAAK,CAAC,IAAI,IAAI,CAACK,MAAM,KAAK,CAAE,EAAE,IAAI,CAACX,QAAQ,CAACc,SAAS,CAAC,CAAC;IACpF,IAAI,CAACR,KAAK,GAAG,CAAC,GAAG,IAAI,CAACA,KAAK;EAC7B,CAAC;EACDd,KAAK,EAAE,SAAAA,CAASpB,CAAC,EAAE2C,CAAC,EAAE;IACpB,IAAIrB,EAAE,GAAGc,GAAG;IAEZpC,CAAC,GAAG,CAACA,CAAC,EAAE2C,CAAC,GAAG,CAACA,CAAC;IACd,IAAI3C,CAAC,KAAK,IAAI,CAACM,GAAG,IAAIqC,CAAC,KAAK,IAAI,CAACjC,GAAG,EAAE,OAAO,CAAC;IAC9C,QAAQ,IAAI,CAAC6B,MAAM;MACjB,KAAK,CAAC;QAAE,IAAI,CAACA,MAAM,GAAG,CAAC;QAAE,IAAI,CAACL,KAAK,GAAG,IAAI,CAACN,QAAQ,CAACa,MAAM,CAACzC,CAAC,EAAE2C,CAAC,CAAC,GAAG,IAAI,CAACf,QAAQ,CAACgB,MAAM,CAAC5C,CAAC,EAAE2C,CAAC,CAAC;QAAE;MAC/F,KAAK,CAAC;QAAE,IAAI,CAACJ,MAAM,GAAG,CAAC;QAAE;MACzB,KAAK,CAAC;QAAE,IAAI,CAACA,MAAM,GAAG,CAAC;QAAEnB,KAAK,CAAC,IAAI,EAAEH,MAAM,CAAC,IAAI,EAAEK,EAAE,GAAGrB,MAAM,CAAC,IAAI,EAAED,CAAC,EAAE2C,CAAC,CAAC,CAAC,EAAErB,EAAE,CAAC;QAAE;MACjF;QAASF,KAAK,CAAC,IAAI,EAAE,IAAI,CAACkB,GAAG,EAAEhB,EAAE,GAAGrB,MAAM,CAAC,IAAI,EAAED,CAAC,EAAE2C,CAAC,CAAC,CAAC;QAAE;IAC3D;IAEA,IAAI,CAACpC,GAAG,GAAG,IAAI,CAACD,GAAG,EAAE,IAAI,CAACA,GAAG,GAAGN,CAAC;IACjC,IAAI,CAACW,GAAG,GAAG,IAAI,CAACD,GAAG,EAAE,IAAI,CAACA,GAAG,GAAGiC,CAAC;IACjC,IAAI,CAACL,GAAG,GAAGhB,EAAE;EACf;AACF,CAAC;AAED,SAASuB,SAASA,CAACd,OAAO,EAAE;EAC1B,IAAI,CAACH,QAAQ,GAAG,IAAIkB,cAAc,CAACf,OAAO,CAAC;AAC7C;AAEA,CAACc,SAAS,CAACb,SAAS,GAAGe,MAAM,CAACC,MAAM,CAAClB,SAAS,CAACE,SAAS,CAAC,EAAEZ,KAAK,GAAG,UAASpB,CAAC,EAAE2C,CAAC,EAAE;EAChFb,SAAS,CAACE,SAAS,CAACZ,KAAK,CAAC6B,IAAI,CAAC,IAAI,EAAEN,CAAC,EAAE3C,CAAC,CAAC;AAC5C,CAAC;AAED,SAAS8C,cAAcA,CAACf,OAAO,EAAE;EAC/B,IAAI,CAACH,QAAQ,GAAGG,OAAO;AACzB;AAEAe,cAAc,CAACd,SAAS,GAAG;EACzBY,MAAM,EAAE,SAAAA,CAAS5C,CAAC,EAAE2C,CAAC,EAAE;IAAE,IAAI,CAACf,QAAQ,CAACgB,MAAM,CAACD,CAAC,EAAE3C,CAAC,CAAC;EAAE,CAAC;EACtD0C,SAAS,EAAE,SAAAA,CAAA,EAAW;IAAE,IAAI,CAACd,QAAQ,CAACc,SAAS,CAAC,CAAC;EAAE,CAAC;EACpDD,MAAM,EAAE,SAAAA,CAASzC,CAAC,EAAE2C,CAAC,EAAE;IAAE,IAAI,CAACf,QAAQ,CAACa,MAAM,CAACE,CAAC,EAAE3C,CAAC,CAAC;EAAE,CAAC;EACtD6B,aAAa,EAAE,SAAAA,CAASJ,EAAE,EAAEC,EAAE,EAAEvB,EAAE,EAAEC,EAAE,EAAEJ,CAAC,EAAE2C,CAAC,EAAE;IAAE,IAAI,CAACf,QAAQ,CAACC,aAAa,CAACH,EAAE,EAAED,EAAE,EAAErB,EAAE,EAAED,EAAE,EAAEwC,CAAC,EAAE3C,CAAC,CAAC;EAAE;AACrG,CAAC;AAED,OAAO,SAASkD,SAASA,CAACnB,OAAO,EAAE;EACjC,OAAO,IAAID,SAAS,CAACC,OAAO,CAAC;AAC/B;AAEA,OAAO,SAASoB,SAASA,CAACpB,OAAO,EAAE;EACjC,OAAO,IAAIc,SAAS,CAACd,OAAO,CAAC;AAC/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}