import React from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { <PERSON><PERSON>lertTriangle, FiRefreshCw, FiHome } from 'react-icons/fi';
import { Button } from './ui';

const ErrorContainer = styled.div`
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: ${({ theme }) => theme.colors.gray[50]};
  padding: ${({ theme }) => theme.spacing[4]};
`;

const ErrorCard = styled(motion.div)`
  background: ${({ theme }) => theme.colors.white};
  border-radius: ${({ theme }) => theme.borderRadius.xl};
  padding: ${({ theme }) => theme.spacing[8]};
  box-shadow: ${({ theme }) => theme.shadows.xl};
  text-align: center;
  max-width: 500px;
  width: 100%;
`;

const ErrorIcon = styled.div`
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, ${({ theme }) => theme.colors.error[500]}, ${({ theme }) => theme.colors.error[600]});
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto ${({ theme }) => theme.spacing[6]};
  color: white;
  font-size: ${({ theme }) => theme.fontSizes['2xl']};
`;

const ErrorTitle = styled.h1`
  font-size: ${({ theme }) => theme.fontSizes['2xl']};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  color: ${({ theme }) => theme.colors.gray[900]};
  margin-bottom: ${({ theme }) => theme.spacing[4]};
`;

const ErrorMessage = styled.p`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  color: ${({ theme }) => theme.colors.gray[600]};
  margin-bottom: ${({ theme }) => theme.spacing[6]};
  line-height: ${({ theme }) => theme.lineHeights.relaxed};
`;

const ErrorDetails = styled.details`
  margin-bottom: ${({ theme }) => theme.spacing[6]};
  text-align: right;
  
  summary {
    cursor: pointer;
    font-weight: ${({ theme }) => theme.fontWeights.medium};
    color: ${({ theme }) => theme.colors.gray[700]};
    margin-bottom: ${({ theme }) => theme.spacing[2]};
  }
  
  pre {
    background: ${({ theme }) => theme.colors.gray[100]};
    padding: ${({ theme }) => theme.spacing[4]};
    border-radius: ${({ theme }) => theme.borderRadius.lg};
    font-size: ${({ theme }) => theme.fontSizes.sm};
    color: ${({ theme }) => theme.colors.gray[800]};
    overflow-x: auto;
    white-space: pre-wrap;
    word-break: break-word;
  }
`;

const ErrorActions = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing[3]};
  justify-content: center;
  
  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    flex-direction: column;
  }
`;

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    this.setState({
      error: error,
      errorInfo: errorInfo
    });

    // يمكن إضافة تسجيل الأخطاء هنا
    console.error('Error caught by boundary:', error, errorInfo);
  }

  handleReload = () => {
    window.location.reload();
  };

  handleGoHome = () => {
    window.location.href = '/';
  };

  render() {
    if (this.state.hasError) {
      return (
        <ErrorContainer>
          <ErrorCard
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3 }}
          >
            <ErrorIcon>
              <FiAlertTriangle />
            </ErrorIcon>
            
            <ErrorTitle>حدث خطأ غير متوقع</ErrorTitle>
            
            <ErrorMessage>
              نعتذر، حدث خطأ في التطبيق. يرجى المحاولة مرة أخرى أو العودة للصفحة الرئيسية.
            </ErrorMessage>

            {process.env.NODE_ENV === 'development' && this.state.error && (
              <ErrorDetails>
                <summary>تفاصيل الخطأ (للمطورين)</summary>
                <pre>
                  {this.state.error.toString()}
                  {this.state.errorInfo.componentStack}
                </pre>
              </ErrorDetails>
            )}

            <ErrorActions>
              <Button
                variant="primary"
                onClick={this.handleReload}
                leftIcon={<FiRefreshCw size={18} />}
              >
                إعادة تحميل الصفحة
              </Button>
              
              <Button
                variant="outline"
                onClick={this.handleGoHome}
                leftIcon={<FiHome size={18} />}
              >
                العودة للرئيسية
              </Button>
            </ErrorActions>
          </ErrorCard>
        </ErrorContainer>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
