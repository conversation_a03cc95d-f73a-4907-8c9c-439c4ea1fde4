{"ast": null, "code": "var _excluded = [\"children\"];\nfunction _objectWithoutProperties(e, t) {\n  if (null == e) return {};\n  var o,\n    r,\n    i = _objectWithoutPropertiesLoose(e, t);\n  if (Object.getOwnPropertySymbols) {\n    var n = Object.getOwnPropertySymbols(e);\n    for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]);\n  }\n  return i;\n}\nfunction _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (-1 !== e.indexOf(n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nimport * as React from 'react';\nimport { forwardRef } from 'react';\nimport { useChartHeight, useChartWidth } from '../context/chartLayoutContext';\nimport { useAccessibilityLayer } from '../context/accessibilityContext';\nimport { useIsPanorama } from '../context/PanoramaContext';\nimport { Surface } from './Surface';\nimport { useAppSelector } from '../state/hooks';\nimport { selectBrushDimensions } from '../state/selectors/brushSelectors';\nimport { isPositiveNumber } from '../util/isWellBehavedNumber';\nvar FULL_WIDTH_AND_HEIGHT = {\n  width: '100%',\n  height: '100%'\n};\nvar MainChartSurface = /*#__PURE__*/forwardRef((props, ref) => {\n  var width = useChartWidth();\n  var height = useChartHeight();\n  var hasAccessibilityLayer = useAccessibilityLayer();\n  if (!isPositiveNumber(width) || !isPositiveNumber(height)) {\n    return null;\n  }\n  var {\n    children,\n    otherAttributes,\n    title,\n    desc\n  } = props;\n  var tabIndex, role;\n  if (typeof otherAttributes.tabIndex === 'number') {\n    tabIndex = otherAttributes.tabIndex;\n  } else {\n    tabIndex = hasAccessibilityLayer ? 0 : undefined;\n  }\n  if (typeof otherAttributes.role === 'string') {\n    role = otherAttributes.role;\n  } else {\n    role = hasAccessibilityLayer ? 'application' : undefined;\n  }\n  return /*#__PURE__*/React.createElement(Surface, _extends({}, otherAttributes, {\n    title: title,\n    desc: desc,\n    role: role,\n    tabIndex: tabIndex,\n    width: width,\n    height: height,\n    style: FULL_WIDTH_AND_HEIGHT,\n    ref: ref\n  }), children);\n});\nvar BrushPanoramaSurface = _ref => {\n  var {\n    children\n  } = _ref;\n  var brushDimensions = useAppSelector(selectBrushDimensions);\n  if (!brushDimensions) {\n    return null;\n  }\n  var {\n    width,\n    height,\n    y,\n    x\n  } = brushDimensions;\n  return /*#__PURE__*/React.createElement(Surface, {\n    width: width,\n    height: height,\n    x: x,\n    y: y\n  }, children);\n};\nexport var RootSurface = /*#__PURE__*/forwardRef((_ref2, ref) => {\n  var {\n      children\n    } = _ref2,\n    rest = _objectWithoutProperties(_ref2, _excluded);\n  var isPanorama = useIsPanorama();\n  if (isPanorama) {\n    return /*#__PURE__*/React.createElement(BrushPanoramaSurface, null, children);\n  }\n  return /*#__PURE__*/React.createElement(MainChartSurface, _extends({\n    ref: ref\n  }, rest), children);\n});", "map": {"version": 3, "names": ["_excluded", "_objectWithoutProperties", "e", "t", "o", "r", "i", "_objectWithoutPropertiesLoose", "Object", "getOwnPropertySymbols", "n", "length", "indexOf", "propertyIsEnumerable", "call", "hasOwnProperty", "_extends", "assign", "bind", "arguments", "apply", "React", "forwardRef", "useChartHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>th", "useAccessibilityLayer", "useIsPanorama", "Surface", "useAppSelector", "selectBrushDimensions", "isPositiveNumber", "FULL_WIDTH_AND_HEIGHT", "width", "height", "MainChartSurface", "props", "ref", "hasAccessibilityLayer", "children", "otherAttributes", "title", "desc", "tabIndex", "role", "undefined", "createElement", "style", "BrushPanoramaSurface", "_ref", "brushDimensions", "y", "x", "RootSurface", "_ref2", "rest", "isPanorama"], "sources": ["D:/menasa/frontend/node_modules/recharts/es6/container/RootSurface.js"], "sourcesContent": ["var _excluded = [\"children\"];\nfunction _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var n = Object.getOwnPropertySymbols(e); for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (-1 !== e.indexOf(n)) continue; t[n] = r[n]; } return t; }\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nimport * as React from 'react';\nimport { forwardRef } from 'react';\nimport { useChartHeight, useChartWidth } from '../context/chartLayoutContext';\nimport { useAccessibilityLayer } from '../context/accessibilityContext';\nimport { useIsPanorama } from '../context/PanoramaContext';\nimport { Surface } from './Surface';\nimport { useAppSelector } from '../state/hooks';\nimport { selectBrushDimensions } from '../state/selectors/brushSelectors';\nimport { isPositiveNumber } from '../util/isWellBehavedNumber';\nvar FULL_WIDTH_AND_HEIGHT = {\n  width: '100%',\n  height: '100%'\n};\nvar MainChartSurface = /*#__PURE__*/forwardRef((props, ref) => {\n  var width = useChartWidth();\n  var height = useChartHeight();\n  var hasAccessibilityLayer = useAccessibilityLayer();\n  if (!isPositiveNumber(width) || !isPositiveNumber(height)) {\n    return null;\n  }\n  var {\n    children,\n    otherAttributes,\n    title,\n    desc\n  } = props;\n  var tabIndex, role;\n  if (typeof otherAttributes.tabIndex === 'number') {\n    tabIndex = otherAttributes.tabIndex;\n  } else {\n    tabIndex = hasAccessibilityLayer ? 0 : undefined;\n  }\n  if (typeof otherAttributes.role === 'string') {\n    role = otherAttributes.role;\n  } else {\n    role = hasAccessibilityLayer ? 'application' : undefined;\n  }\n  return /*#__PURE__*/React.createElement(Surface, _extends({}, otherAttributes, {\n    title: title,\n    desc: desc,\n    role: role,\n    tabIndex: tabIndex,\n    width: width,\n    height: height,\n    style: FULL_WIDTH_AND_HEIGHT,\n    ref: ref\n  }), children);\n});\nvar BrushPanoramaSurface = _ref => {\n  var {\n    children\n  } = _ref;\n  var brushDimensions = useAppSelector(selectBrushDimensions);\n  if (!brushDimensions) {\n    return null;\n  }\n  var {\n    width,\n    height,\n    y,\n    x\n  } = brushDimensions;\n  return /*#__PURE__*/React.createElement(Surface, {\n    width: width,\n    height: height,\n    x: x,\n    y: y\n  }, children);\n};\nexport var RootSurface = /*#__PURE__*/forwardRef((_ref2, ref) => {\n  var {\n      children\n    } = _ref2,\n    rest = _objectWithoutProperties(_ref2, _excluded);\n  var isPanorama = useIsPanorama();\n  if (isPanorama) {\n    return /*#__PURE__*/React.createElement(BrushPanoramaSurface, null, children);\n  }\n  return /*#__PURE__*/React.createElement(MainChartSurface, _extends({\n    ref: ref\n  }, rest), children);\n});"], "mappings": "AAAA,IAAIA,SAAS,GAAG,CAAC,UAAU,CAAC;AAC5B,SAASC,wBAAwBA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAI,IAAI,IAAID,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,CAAC;IAAEC,CAAC;IAAEC,CAAC,GAAGC,6BAA6B,CAACL,CAAC,EAAEC,CAAC,CAAC;EAAE,IAAIK,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIC,CAAC,GAAGF,MAAM,CAACC,qBAAqB,CAACP,CAAC,CAAC;IAAE,KAAKG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGK,CAAC,CAACC,MAAM,EAAEN,CAAC,EAAE,EAAED,CAAC,GAAGM,CAAC,CAACL,CAAC,CAAC,EAAE,CAAC,CAAC,KAAKF,CAAC,CAACS,OAAO,CAACR,CAAC,CAAC,IAAI,CAAC,CAAC,CAACS,oBAAoB,CAACC,IAAI,CAACZ,CAAC,EAAEE,CAAC,CAAC,KAAKE,CAAC,CAACF,CAAC,CAAC,GAAGF,CAAC,CAACE,CAAC,CAAC,CAAC;EAAE;EAAE,OAAOE,CAAC;AAAE;AACrU,SAASC,6BAA6BA,CAACF,CAAC,EAAEH,CAAC,EAAE;EAAE,IAAI,IAAI,IAAIG,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIF,CAAC,GAAG,CAAC,CAAC;EAAE,KAAK,IAAIO,CAAC,IAAIL,CAAC,EAAE,IAAI,CAAC,CAAC,CAACU,cAAc,CAACD,IAAI,CAACT,CAAC,EAAEK,CAAC,CAAC,EAAE;IAAE,IAAI,CAAC,CAAC,KAAKR,CAAC,CAACU,OAAO,CAACF,CAAC,CAAC,EAAE;IAAUP,CAAC,CAACO,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;EAAE;EAAE,OAAOP,CAAC;AAAE;AACtM,SAASa,QAAQA,CAAA,EAAG;EAAE,OAAOA,QAAQ,GAAGR,MAAM,CAACS,MAAM,GAAGT,MAAM,CAACS,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUR,CAAC,EAAE;IAAE,KAAK,IAAIR,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiB,SAAS,CAACR,MAAM,EAAET,CAAC,EAAE,EAAE;MAAE,IAAIC,CAAC,GAAGgB,SAAS,CAACjB,CAAC,CAAC;MAAE,KAAK,IAAIG,CAAC,IAAIF,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEY,cAAc,CAACD,IAAI,CAACX,CAAC,EAAEE,CAAC,CAAC,KAAKK,CAAC,CAACL,CAAC,CAAC,GAAGF,CAAC,CAACE,CAAC,CAAC,CAAC;IAAE;IAAE,OAAOK,CAAC;EAAE,CAAC,EAAEM,QAAQ,CAACI,KAAK,CAAC,IAAI,EAAED,SAAS,CAAC;AAAE;AACnR,OAAO,KAAKE,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,OAAO;AAClC,SAASC,cAAc,EAAEC,aAAa,QAAQ,+BAA+B;AAC7E,SAASC,qBAAqB,QAAQ,iCAAiC;AACvE,SAASC,aAAa,QAAQ,4BAA4B;AAC1D,SAASC,OAAO,QAAQ,WAAW;AACnC,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,SAASC,qBAAqB,QAAQ,mCAAmC;AACzE,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,IAAIC,qBAAqB,GAAG;EAC1BC,KAAK,EAAE,MAAM;EACbC,MAAM,EAAE;AACV,CAAC;AACD,IAAIC,gBAAgB,GAAG,aAAaZ,UAAU,CAAC,CAACa,KAAK,EAAEC,GAAG,KAAK;EAC7D,IAAIJ,KAAK,GAAGR,aAAa,CAAC,CAAC;EAC3B,IAAIS,MAAM,GAAGV,cAAc,CAAC,CAAC;EAC7B,IAAIc,qBAAqB,GAAGZ,qBAAqB,CAAC,CAAC;EACnD,IAAI,CAACK,gBAAgB,CAACE,KAAK,CAAC,IAAI,CAACF,gBAAgB,CAACG,MAAM,CAAC,EAAE;IACzD,OAAO,IAAI;EACb;EACA,IAAI;IACFK,QAAQ;IACRC,eAAe;IACfC,KAAK;IACLC;EACF,CAAC,GAAGN,KAAK;EACT,IAAIO,QAAQ,EAAEC,IAAI;EAClB,IAAI,OAAOJ,eAAe,CAACG,QAAQ,KAAK,QAAQ,EAAE;IAChDA,QAAQ,GAAGH,eAAe,CAACG,QAAQ;EACrC,CAAC,MAAM;IACLA,QAAQ,GAAGL,qBAAqB,GAAG,CAAC,GAAGO,SAAS;EAClD;EACA,IAAI,OAAOL,eAAe,CAACI,IAAI,KAAK,QAAQ,EAAE;IAC5CA,IAAI,GAAGJ,eAAe,CAACI,IAAI;EAC7B,CAAC,MAAM;IACLA,IAAI,GAAGN,qBAAqB,GAAG,aAAa,GAAGO,SAAS;EAC1D;EACA,OAAO,aAAavB,KAAK,CAACwB,aAAa,CAAClB,OAAO,EAAEX,QAAQ,CAAC,CAAC,CAAC,EAAEuB,eAAe,EAAE;IAC7EC,KAAK,EAAEA,KAAK;IACZC,IAAI,EAAEA,IAAI;IACVE,IAAI,EAAEA,IAAI;IACVD,QAAQ,EAAEA,QAAQ;IAClBV,KAAK,EAAEA,KAAK;IACZC,MAAM,EAAEA,MAAM;IACda,KAAK,EAAEf,qBAAqB;IAC5BK,GAAG,EAAEA;EACP,CAAC,CAAC,EAAEE,QAAQ,CAAC;AACf,CAAC,CAAC;AACF,IAAIS,oBAAoB,GAAGC,IAAI,IAAI;EACjC,IAAI;IACFV;EACF,CAAC,GAAGU,IAAI;EACR,IAAIC,eAAe,GAAGrB,cAAc,CAACC,qBAAqB,CAAC;EAC3D,IAAI,CAACoB,eAAe,EAAE;IACpB,OAAO,IAAI;EACb;EACA,IAAI;IACFjB,KAAK;IACLC,MAAM;IACNiB,CAAC;IACDC;EACF,CAAC,GAAGF,eAAe;EACnB,OAAO,aAAa5B,KAAK,CAACwB,aAAa,CAAClB,OAAO,EAAE;IAC/CK,KAAK,EAAEA,KAAK;IACZC,MAAM,EAAEA,MAAM;IACdkB,CAAC,EAAEA,CAAC;IACJD,CAAC,EAAEA;EACL,CAAC,EAAEZ,QAAQ,CAAC;AACd,CAAC;AACD,OAAO,IAAIc,WAAW,GAAG,aAAa9B,UAAU,CAAC,CAAC+B,KAAK,EAAEjB,GAAG,KAAK;EAC/D,IAAI;MACAE;IACF,CAAC,GAAGe,KAAK;IACTC,IAAI,GAAGrD,wBAAwB,CAACoD,KAAK,EAAErD,SAAS,CAAC;EACnD,IAAIuD,UAAU,GAAG7B,aAAa,CAAC,CAAC;EAChC,IAAI6B,UAAU,EAAE;IACd,OAAO,aAAalC,KAAK,CAACwB,aAAa,CAACE,oBAAoB,EAAE,IAAI,EAAET,QAAQ,CAAC;EAC/E;EACA,OAAO,aAAajB,KAAK,CAACwB,aAAa,CAACX,gBAAgB,EAAElB,QAAQ,CAAC;IACjEoB,GAAG,EAAEA;EACP,CAAC,EAAEkB,IAAI,CAAC,EAAEhB,QAAQ,CAAC;AACrB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}