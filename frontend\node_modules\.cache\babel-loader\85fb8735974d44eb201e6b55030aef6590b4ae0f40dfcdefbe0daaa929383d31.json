{"ast": null, "code": "import { find } from './util';\nvar CustomTransformerRegistry = /** @class */function () {\n  function CustomTransformerRegistry() {\n    this.transfomers = {};\n  }\n  CustomTransformerRegistry.prototype.register = function (transformer) {\n    this.transfomers[transformer.name] = transformer;\n  };\n  CustomTransformerRegistry.prototype.findApplicable = function (v) {\n    return find(this.transfomers, function (transformer) {\n      return transformer.isApplicable(v);\n    });\n  };\n  CustomTransformerRegistry.prototype.findByName = function (name) {\n    return this.transfomers[name];\n  };\n  return CustomTransformerRegistry;\n}();\nexport { CustomTransformerRegistry };", "map": {"version": 3, "names": ["find", "CustomTransformerRegistry", "transfomers", "prototype", "register", "transformer", "name", "findApplicable", "v", "isApplicable", "findByName"], "sources": ["D:\\menasa\\frontend\\node_modules\\superjson\\src\\custom-transformer-registry.ts"], "sourcesContent": ["import { JSONValue } from './types';\nimport { find } from './util';\n\nexport interface CustomTransfomer<I, O extends JSONValue> {\n  name: string;\n  isApplicable: (v: any) => v is I;\n  serialize: (v: I) => O;\n  deserialize: (v: O) => I;\n}\n\nexport class CustomTransformerRegistry {\n  private transfomers: Record<string, CustomTransfomer<any, any>> = {};\n\n  register<I, O extends JSONValue>(transformer: CustomTransfomer<I, O>) {\n    this.transfomers[transformer.name] = transformer;\n  }\n\n  findApplicable<T>(v: T) {\n    return find(this.transfomers, transformer =>\n      transformer.isApplicable(v)\n    ) as CustomTransfomer<T, JSONValue> | undefined;\n  }\n\n  findByName(name: string) {\n    return this.transfomers[name];\n  }\n}\n"], "mappings": "AACA,SAASA,IAAI,QAAQ,QAAQ;AAS7B,IAAAC,yBAAA;EAAA,SAAAA,0BAAA;IACU,KAAAC,WAAW,GAA+C,EAAE;EAetE;EAbED,yBAAA,CAAAE,SAAA,CAAAC,QAAQ,GAAR,UAAiCC,WAAmC;IAClE,IAAI,CAACH,WAAW,CAACG,WAAW,CAACC,IAAI,CAAC,GAAGD,WAAW;EAClD,CAAC;EAEDJ,yBAAA,CAAAE,SAAA,CAAAI,cAAc,GAAd,UAAkBC,CAAI;IACpB,OAAOR,IAAI,CAAC,IAAI,CAACE,WAAW,EAAE,UAAAG,WAAW;MACvC,OAAAA,WAAW,CAACI,YAAY,CAACD,CAAC,CAAC;IAA3B,CAA2B,CACkB;EACjD,CAAC;EAEDP,yBAAA,CAAAE,SAAA,CAAAO,UAAU,GAAV,UAAWJ,IAAY;IACrB,OAAO,IAAI,CAACJ,WAAW,CAACI,IAAI,CAAC;EAC/B,CAAC;EACH,OAAAL,yBAAC;AAAD,CAAC,CAhBD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}