{"ast": null, "code": "export var escapeKey = function (key) {\n  return key.replace(/\\./g, '\\\\.');\n};\nexport var stringifyPath = function (path) {\n  return path.map(String).map(escapeKey).join('.');\n};\nexport var parsePath = function (string) {\n  var result = [];\n  var segment = '';\n  for (var i = 0; i < string.length; i++) {\n    var char = string.charAt(i);\n    var isEscapedDot = char === '\\\\' && string.charAt(i + 1) === '.';\n    if (isEscapedDot) {\n      segment += '.';\n      i++;\n      continue;\n    }\n    var isEndOfSegment = char === '.';\n    if (isEndOfSegment) {\n      result.push(segment);\n      segment = '';\n      continue;\n    }\n    segment += char;\n  }\n  var lastSegment = segment;\n  result.push(lastSegment);\n  return result;\n};", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "key", "replace", "stringifyPath", "path", "map", "String", "join", "parsePath", "string", "result", "segment", "i", "length", "char", "char<PERSON>t", "isEscapedDot", "isEndOfSegment", "push", "lastSegment"], "sources": ["D:\\menasa\\frontend\\node_modules\\superjson\\src\\pathstringifier.ts"], "sourcesContent": ["export type StringifiedPath = string;\ntype Path = string[];\n\nexport const escapeKey = (key: string) => key.replace(/\\./g, '\\\\.');\n\nexport const stringifyPath = (path: Path): StringifiedPath =>\n  path\n    .map(String)\n    .map(escapeKey)\n    .join('.');\n\nexport const parsePath = (string: StringifiedPath) => {\n  const result: string[] = [];\n\n  let segment = '';\n  for (let i = 0; i < string.length; i++) {\n    let char = string.charAt(i);\n\n    const isEscapedDot = char === '\\\\' && string.charAt(i + 1) === '.';\n    if (isEscapedDot) {\n      segment += '.';\n      i++;\n      continue;\n    }\n\n    const isEndOfSegment = char === '.';\n    if (isEndOfSegment) {\n      result.push(segment);\n      segment = '';\n      continue;\n    }\n\n    segment += char;\n  }\n\n  const lastSegment = segment;\n  result.push(lastSegment);\n\n  return result;\n};\n"], "mappings": "AAGA,OAAO,IAAMA,SAAS,GAAG,SAAAA,CAACC,GAAW;EAAK,OAAAA,GAAG,CAACC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC;AAAzB,CAAyB;AAEnE,OAAO,IAAMC,aAAa,GAAG,SAAAA,CAACC,IAAU;EACtC,OAAAA,IAAI,CACDC,GAAG,CAACC,MAAM,CAAC,CACXD,GAAG,CAACL,SAAS,CAAC,CACdO,IAAI,CAAC,GAAG,CAAC;AAHZ,CAGY;AAEd,OAAO,IAAMC,SAAS,GAAG,SAAAA,CAACC,MAAuB;EAC/C,IAAMC,MAAM,GAAa,EAAE;EAE3B,IAAIC,OAAO,GAAG,EAAE;EAChB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,MAAM,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;IACtC,IAAIE,IAAI,GAAGL,MAAM,CAACM,MAAM,CAACH,CAAC,CAAC;IAE3B,IAAMI,YAAY,GAAGF,IAAI,KAAK,IAAI,IAAIL,MAAM,CAACM,MAAM,CAACH,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG;IAClE,IAAII,YAAY,EAAE;MAChBL,OAAO,IAAI,GAAG;MACdC,CAAC,EAAE;MACH;;IAGF,IAAMK,cAAc,GAAGH,IAAI,KAAK,GAAG;IACnC,IAAIG,cAAc,EAAE;MAClBP,MAAM,CAACQ,IAAI,CAACP,OAAO,CAAC;MACpBA,OAAO,GAAG,EAAE;MACZ;;IAGFA,OAAO,IAAIG,IAAI;;EAGjB,IAAMK,WAAW,GAAGR,OAAO;EAC3BD,MAAM,CAACQ,IAAI,CAACC,WAAW,CAAC;EAExB,OAAOT,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}