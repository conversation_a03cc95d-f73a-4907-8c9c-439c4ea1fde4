{"ast": null, "code": "var degrees = 180 / Math.PI;\nexport var identity = {\n  translateX: 0,\n  translateY: 0,\n  rotate: 0,\n  skewX: 0,\n  scaleX: 1,\n  scaleY: 1\n};\nexport default function (a, b, c, d, e, f) {\n  var scaleX, scaleY, skewX;\n  if (scaleX = Math.sqrt(a * a + b * b)) a /= scaleX, b /= scaleX;\n  if (skewX = a * c + b * d) c -= a * skewX, d -= b * skewX;\n  if (scaleY = Math.sqrt(c * c + d * d)) c /= scaleY, d /= scaleY, skewX /= scaleY;\n  if (a * d < b * c) a = -a, b = -b, skewX = -skewX, scaleX = -scaleX;\n  return {\n    translateX: e,\n    translateY: f,\n    rotate: Math.atan2(b, a) * degrees,\n    skewX: Math.atan(skewX) * degrees,\n    scaleX: scaleX,\n    scaleY: scaleY\n  };\n}", "map": {"version": 3, "names": ["degrees", "Math", "PI", "identity", "translateX", "translateY", "rotate", "skewX", "scaleX", "scaleY", "a", "b", "c", "d", "e", "f", "sqrt", "atan2", "atan"], "sources": ["D:/menasa/frontend/node_modules/d3-interpolate/src/transform/decompose.js"], "sourcesContent": ["var degrees = 180 / Math.PI;\n\nexport var identity = {\n  translateX: 0,\n  translateY: 0,\n  rotate: 0,\n  skewX: 0,\n  scaleX: 1,\n  scaleY: 1\n};\n\nexport default function(a, b, c, d, e, f) {\n  var scaleX, scaleY, skewX;\n  if (scaleX = Math.sqrt(a * a + b * b)) a /= scaleX, b /= scaleX;\n  if (skewX = a * c + b * d) c -= a * skewX, d -= b * skewX;\n  if (scaleY = Math.sqrt(c * c + d * d)) c /= scaleY, d /= scaleY, skewX /= scaleY;\n  if (a * d < b * c) a = -a, b = -b, skewX = -skewX, scaleX = -scaleX;\n  return {\n    translateX: e,\n    translateY: f,\n    rotate: Math.atan2(b, a) * degrees,\n    skewX: Math.atan(skewX) * degrees,\n    scaleX: scaleX,\n    scaleY: scaleY\n  };\n}\n"], "mappings": "AAAA,IAAIA,OAAO,GAAG,GAAG,GAAGC,IAAI,CAACC,EAAE;AAE3B,OAAO,IAAIC,QAAQ,GAAG;EACpBC,UAAU,EAAE,CAAC;EACbC,UAAU,EAAE,CAAC;EACbC,MAAM,EAAE,CAAC;EACTC,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE,CAAC;EACTC,MAAM,EAAE;AACV,CAAC;AAED,eAAe,UAASC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EACxC,IAAIP,MAAM,EAAEC,MAAM,EAAEF,KAAK;EACzB,IAAIC,MAAM,GAAGP,IAAI,CAACe,IAAI,CAACN,CAAC,GAAGA,CAAC,GAAGC,CAAC,GAAGA,CAAC,CAAC,EAAED,CAAC,IAAIF,MAAM,EAAEG,CAAC,IAAIH,MAAM;EAC/D,IAAID,KAAK,GAAGG,CAAC,GAAGE,CAAC,GAAGD,CAAC,GAAGE,CAAC,EAAED,CAAC,IAAIF,CAAC,GAAGH,KAAK,EAAEM,CAAC,IAAIF,CAAC,GAAGJ,KAAK;EACzD,IAAIE,MAAM,GAAGR,IAAI,CAACe,IAAI,CAACJ,CAAC,GAAGA,CAAC,GAAGC,CAAC,GAAGA,CAAC,CAAC,EAAED,CAAC,IAAIH,MAAM,EAAEI,CAAC,IAAIJ,MAAM,EAAEF,KAAK,IAAIE,MAAM;EAChF,IAAIC,CAAC,GAAGG,CAAC,GAAGF,CAAC,GAAGC,CAAC,EAAEF,CAAC,GAAG,CAACA,CAAC,EAAEC,CAAC,GAAG,CAACA,CAAC,EAAEJ,KAAK,GAAG,CAACA,KAAK,EAAEC,MAAM,GAAG,CAACA,MAAM;EACnE,OAAO;IACLJ,UAAU,EAAEU,CAAC;IACbT,UAAU,EAAEU,CAAC;IACbT,MAAM,EAAEL,IAAI,CAACgB,KAAK,CAACN,CAAC,EAAED,CAAC,CAAC,GAAGV,OAAO;IAClCO,KAAK,EAAEN,IAAI,CAACiB,IAAI,CAACX,KAAK,CAAC,GAAGP,OAAO;IACjCQ,MAAM,EAAEA,MAAM;IACdC,MAAM,EAAEA;EACV,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}