{"ast": null, "code": "import { styled } from './utils.mjs';\nconst Panel = styled('div', (_props, theme) => ({\n  fontSize: 'clamp(12px, 1.5vw, 14px)',\n  fontFamily: \"sans-serif\",\n  display: 'flex',\n  backgroundColor: theme.background,\n  color: theme.foreground\n}), {\n  '(max-width: 700px)': {\n    flexDirection: 'column'\n  },\n  '(max-width: 600px)': {\n    fontSize: '.9em' // flexDirection: 'column',\n  }\n});\nconst ActiveQueryPanel = styled('div', () => ({\n  flex: '1 1 500px',\n  display: 'flex',\n  flexDirection: 'column',\n  overflow: 'auto',\n  height: '100%'\n}), {\n  '(max-width: 700px)': (_props, theme) => ({\n    borderTop: \"2px solid \" + theme.gray\n  })\n});\nconst Button = styled('button', (props, theme) => ({\n  appearance: 'none',\n  fontSize: '.9em',\n  fontWeight: 'bold',\n  background: theme.gray,\n  border: '0',\n  borderRadius: '.3em',\n  color: 'white',\n  padding: '.5em',\n  opacity: props.disabled ? '.5' : undefined,\n  cursor: 'pointer'\n}));\nconst QueryKeys = styled('span', {\n  display: 'flex',\n  flexWrap: 'wrap',\n  gap: '0.5em',\n  fontSize: '0.9em'\n});\nconst QueryKey = styled('span', {\n  display: 'inline-flex',\n  alignItems: 'center',\n  padding: '.2em .4em',\n  fontWeight: 'bold',\n  textShadow: '0 0 10px black',\n  borderRadius: '.2em'\n});\nconst Code = styled('code', {\n  fontSize: '.9em',\n  color: 'inherit',\n  background: 'inherit'\n});\nconst Input = styled('input', (_props, theme) => ({\n  backgroundColor: theme.inputBackgroundColor,\n  border: 0,\n  borderRadius: '.2em',\n  color: theme.inputTextColor,\n  fontSize: '.9em',\n  lineHeight: \"1.3\",\n  padding: '.3em .4em'\n}));\nconst Select = styled('select', (_props, theme) => ({\n  display: \"inline-block\",\n  fontSize: \".9em\",\n  fontFamily: \"sans-serif\",\n  fontWeight: 'normal',\n  lineHeight: \"1.3\",\n  padding: \".3em 1.5em .3em .5em\",\n  height: 'auto',\n  border: 0,\n  borderRadius: \".2em\",\n  appearance: \"none\",\n  WebkitAppearance: 'none',\n  backgroundColor: theme.inputBackgroundColor,\n  backgroundImage: \"url(\\\"data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='100' height='100' fill='%23444444'><polygon points='0,25 100,25 50,75'/></svg>\\\")\",\n  backgroundRepeat: \"no-repeat\",\n  backgroundPosition: \"right .55em center\",\n  backgroundSize: \".65em auto, 100%\",\n  color: theme.inputTextColor\n}), {\n  '(max-width: 500px)': {\n    display: 'none'\n  }\n});\nexport { ActiveQueryPanel, Button, Code, Input, Panel, QueryKey, QueryKeys, Select };", "map": {"version": 3, "names": ["Panel", "styled", "_props", "theme", "fontSize", "fontFamily", "display", "backgroundColor", "background", "color", "foreground", "flexDirection", "ActiveQueryPanel", "flex", "overflow", "height", "(max-width: 700px)", "borderTop", "gray", "<PERSON><PERSON>", "props", "appearance", "fontWeight", "border", "borderRadius", "padding", "opacity", "disabled", "undefined", "cursor", "Query<PERSON><PERSON>s", "flexWrap", "gap", "Query<PERSON>ey", "alignItems", "textShadow", "Code", "Input", "inputBackgroundColor", "inputTextColor", "lineHeight", "Select", "WebkitAppearance", "backgroundImage", "backgroundRepeat", "backgroundPosition", "backgroundSize"], "sources": ["D:\\menasa\\frontend\\node_modules\\@tanstack\\react-query-devtools\\src\\styledComponents.ts"], "sourcesContent": ["import { styled } from './utils'\n\nexport const Panel = styled(\n  'div',\n  (_props, theme) => ({\n    fontSize: 'clamp(12px, 1.5vw, 14px)',\n    fontFamily: `sans-serif`,\n    display: 'flex',\n    backgroundColor: theme.background,\n    color: theme.foreground,\n  }),\n  {\n    '(max-width: 700px)': {\n      flexDirection: 'column',\n    },\n    '(max-width: 600px)': {\n      fontSize: '.9em',\n      // flexDirection: 'column',\n    },\n  },\n)\n\nexport const ActiveQueryPanel = styled(\n  'div',\n  () => ({\n    flex: '1 1 500px',\n    display: 'flex',\n    flexDirection: 'column',\n    overflow: 'auto',\n    height: '100%',\n  }),\n  {\n    '(max-width: 700px)': (_props, theme) => ({\n      borderTop: `2px solid ${theme.gray}`,\n    }),\n  },\n)\n\nexport const Button = styled('button', (props, theme) => ({\n  appearance: 'none',\n  fontSize: '.9em',\n  fontWeight: 'bold',\n  background: theme.gray,\n  border: '0',\n  borderRadius: '.3em',\n  color: 'white',\n  padding: '.5em',\n  opacity: props.disabled ? '.5' : undefined,\n  cursor: 'pointer',\n}))\n\nexport const QueryKeys = styled('span', {\n  display: 'flex',\n  flexWrap: 'wrap',\n  gap: '0.5em',\n  fontSize: '0.9em',\n})\n\nexport const QueryKey = styled('span', {\n  display: 'inline-flex',\n  alignItems: 'center',\n  padding: '.2em .4em',\n  fontWeight: 'bold',\n  textShadow: '0 0 10px black',\n  borderRadius: '.2em',\n})\n\nexport const Code = styled('code', {\n  fontSize: '.9em',\n  color: 'inherit',\n  background: 'inherit',\n})\n\nexport const Input = styled('input', (_props, theme) => ({\n  backgroundColor: theme.inputBackgroundColor,\n  border: 0,\n  borderRadius: '.2em',\n  color: theme.inputTextColor,\n  fontSize: '.9em',\n  lineHeight: `1.3`,\n  padding: '.3em .4em',\n}))\n\nexport const Select = styled(\n  'select',\n  (_props, theme) => ({\n    display: `inline-block`,\n    fontSize: `.9em`,\n    fontFamily: `sans-serif`,\n    fontWeight: 'normal',\n    lineHeight: `1.3`,\n    padding: `.3em 1.5em .3em .5em`,\n    height: 'auto',\n    border: 0,\n    borderRadius: `.2em`,\n    appearance: `none`,\n    WebkitAppearance: 'none',\n    backgroundColor: theme.inputBackgroundColor,\n    backgroundImage: `url(\"data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='100' height='100' fill='%23444444'><polygon points='0,25 100,25 50,75'/></svg>\")`,\n    backgroundRepeat: `no-repeat`,\n    backgroundPosition: `right .55em center`,\n    backgroundSize: `.65em auto, 100%`,\n    color: theme.inputTextColor,\n  }),\n  {\n    '(max-width: 500px)': {\n      display: 'none',\n    },\n  },\n)\n"], "mappings": ";AAEO,MAAMA,KAAK,GAAGC,MAAM,CACzB,KADyB,EAEzB,CAACC,MAAD,EAASC,KAAT,MAAoB;EAClBC,QAAQ,EAAE,0BADQ;EAElBC,UAAU,EAFQ;EAGlBC,OAAO,EAAE,MAHS;EAIlBC,eAAe,EAAEJ,KAAK,CAACK,UAJL;EAKlBC,KAAK,EAAEN,KAAK,CAACO;AALK,CAApB,CAFyB,EASzB;EACE,oBAAsB;IACpBC,aAAa,EAAE;GAFnB;EAIE,oBAAsB;IACpBP,QAAQ,EAAE,MADU;EAAA;AAJxB,CATyB;MAoBdQ,gBAAgB,GAAGX,MAAM,CACpC,KADoC,EAEpC,OAAO;EACLY,IAAI,EAAE,WADD;EAELP,OAAO,EAAE,MAFJ;EAGLK,aAAa,EAAE,QAHV;EAILG,QAAQ,EAAE,MAJL;EAKLC,MAAM,EAAE;AALH,CAAP,CAFoC,EASpC;EACE,sBAAsBC,CAACd,MAAD,EAASC,KAAT,MAAoB;IACxCc,SAAS,iBAAed,KAAK,CAACe;GADV;AADxB,CAToC;AAgB/B,MAAMC,MAAM,GAAGlB,MAAM,CAAC,QAAD,EAAW,CAACmB,KAAD,EAAQjB,KAAR,MAAmB;EACxDkB,UAAU,EAAE,MAD4C;EAExDjB,QAAQ,EAAE,MAF8C;EAGxDkB,UAAU,EAAE,MAH4C;EAIxDd,UAAU,EAAEL,KAAK,CAACe,IAJsC;EAKxDK,MAAM,EAAE,GALgD;EAMxDC,YAAY,EAAE,MAN0C;EAOxDf,KAAK,EAAE,OAPiD;EAQxDgB,OAAO,EAAE,MAR+C;EASxDC,OAAO,EAAEN,KAAK,CAACO,QAAN,GAAiB,IAAjB,GAAwBC,SATuB;EAUxDC,MAAM,EAAE;AAVgD,CAAnB,CAAX;MAafC,SAAS,GAAG7B,MAAM,CAAC,MAAD,EAAS;EACtCK,OAAO,EAAE,MAD6B;EAEtCyB,QAAQ,EAAE,MAF4B;EAGtCC,GAAG,EAAE,OAHiC;EAItC5B,QAAQ,EAAE;AAJ4B,CAAT;MAOlB6B,QAAQ,GAAGhC,MAAM,CAAC,MAAD,EAAS;EACrCK,OAAO,EAAE,aAD4B;EAErC4B,UAAU,EAAE,QAFyB;EAGrCT,OAAO,EAAE,WAH4B;EAIrCH,UAAU,EAAE,MAJyB;EAKrCa,UAAU,EAAE,gBALyB;EAMrCX,YAAY,EAAE;AANuB,CAAT;MASjBY,IAAI,GAAGnC,MAAM,CAAC,MAAD,EAAS;EACjCG,QAAQ,EAAE,MADuB;EAEjCK,KAAK,EAAE,SAF0B;EAGjCD,UAAU,EAAE;AAHqB,CAAT;AAMnB,MAAM6B,KAAK,GAAGpC,MAAM,CAAC,OAAD,EAAU,CAACC,MAAD,EAASC,KAAT,MAAoB;EACvDI,eAAe,EAAEJ,KAAK,CAACmC,oBADgC;EAEvDf,MAAM,EAAE,CAF+C;EAGvDC,YAAY,EAAE,MAHyC;EAIvDf,KAAK,EAAEN,KAAK,CAACoC,cAJ0C;EAKvDnC,QAAQ,EAAE,MAL6C;EAMvDoC,UAAU,EAN6C;EAOvDf,OAAO,EAAE;AAP8C,CAApB,CAAV;AAUpB,MAAMgB,MAAM,GAAGxC,MAAM,CAC1B,QAD0B,EAE1B,CAACC,MAAD,EAASC,KAAT,MAAoB;EAClBG,OAAO,EADW;EAElBF,QAAQ,EAFU;EAGlBC,UAAU,EAHQ;EAIlBiB,UAAU,EAAE,QAJM;EAKlBkB,UAAU,EALQ;EAMlBf,OAAO,EANW;EAOlBV,MAAM,EAAE,MAPU;EAQlBQ,MAAM,EAAE,CARU;EASlBC,YAAY,EATM;EAUlBH,UAAU,EAVQ;EAWlBqB,gBAAgB,EAAE,MAXA;EAYlBnC,eAAe,EAAEJ,KAAK,CAACmC,oBAZL;EAalBK,eAAe,EAbG;EAclBC,gBAAgB,EAdE;EAelBC,kBAAkB,EAfA;EAgBlBC,cAAc,EAhBI;EAiBlBrC,KAAK,EAAEN,KAAK,CAACoC;AAjBK,CAApB,CAF0B,EAqB1B;EACE,oBAAsB;IACpBjC,OAAO,EAAE;EADW;AADxB,CArB0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}