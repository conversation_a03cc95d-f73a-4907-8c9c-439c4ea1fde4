# دليل النشر - نظام إدارة الملفات التعليمية

## 🚀 نشر التطبيق على Vercel

### المتطلبات
- حساب [Vercel](https://vercel.com)
- حساب [Supabase](https://supabase.com)
- مشروع GitHub

### خطوات النشر

1. **رفع المشروع إلى GitHub**
```bash
git add .
git commit -m "Ready for deployment"
git push origin main
```

2. **ربط المشروع بـ Vercel**
   - انتقل إلى Vercel Dashboard
   - اختر "New Project"
   - اربط حساب GitHub واختر المشروع
   - اضبط إعدادات البناء:
     - Framework Preset: Create React App
     - Root Directory: frontend
     - Build Command: npm run build
     - Output Directory: build

3. **إعداد متغيرات البيئة في Vercel**
   - انتقل إلى Project Settings > Environment Variables
   - أضف المتغيرات التالية:
     ```
     REACT_APP_SUPABASE_URL=your_supabase_url
     REACT_APP_SUPABASE_ANON_KEY=your_supabase_anon_key
     ```

4. **نشر التطبيق**
   - اضغط "Deploy"
   - انتظر اكتمال عملية البناء

## 🗄️ إعداد Supabase للإنتاج

### إعدادات الأمان

1. **تحديث URL المسموح**
   - انتقل إلى Authentication > URL Configuration
   - أضف رابط Vercel إلى Site URL
   - أضف الرابط إلى Redirect URLs

2. **إعداد CORS**
   - انتقل إلى Settings > API
   - أضف رابط Vercel إلى CORS origins

3. **مراجعة RLS Policies**
   - تأكد من أن جميع الجداول محمية بـ RLS
   - اختبر الوصول للبيانات

### إعدادات Storage

1. **إعداد Bucket**
   - انتقل إلى Storage
   - تأكد من إعدادات bucket "educational-files"
   - اضبط حد حجم الملف (100MB)

2. **إعداد CDN**
   - فعل CDN للملفات
   - اضبط cache headers

## 🔧 إعدادات الأداء

### تحسين React

1. **Code Splitting**
```javascript
// في App.js
const StudentDashboard = lazy(() => import('./pages/StudentDashboard'));
const AdminDashboard = lazy(() => import('./pages/AdminDashboard'));
```

2. **تحسين Bundle**
```bash
# تحليل حجم Bundle
npm install --save-dev webpack-bundle-analyzer
npm run build
npx webpack-bundle-analyzer build/static/js/*.js
```

### تحسين Supabase

1. **فهرسة قاعدة البيانات**
   - تأكد من وجود فهارس على الحقول المستخدمة في البحث
   - راقب أداء الاستعلامات

2. **تحسين الاستعلامات**
   - استخدم select() لتحديد الحقول المطلوبة فقط
   - استخدم limit() للتحكم في عدد النتائج

## 📊 المراقبة والتحليل

### إعداد Vercel Analytics
```bash
npm install @vercel/analytics
```

```javascript
// في index.js
import { Analytics } from '@vercel/analytics/react';

function App() {
  return (
    <>
      <YourApp />
      <Analytics />
    </>
  );
}
```

### مراقبة Supabase
- استخدم Supabase Dashboard لمراقبة:
  - عدد الطلبات
  - استخدام قاعدة البيانات
  - استخدام Storage

## 🔒 الأمان

### متغيرات البيئة
- لا تضع أبداً service_role key في Frontend
- استخدم anon key فقط
- احم متغيرات البيئة الحساسة

### RLS Policies
```sql
-- مثال على سياسة أمان محكمة
CREATE POLICY "Users can only see own data" ON profiles
  FOR SELECT USING (auth.uid() = id);
```

### HTTPS
- تأكد من استخدام HTTPS في الإنتاج
- Vercel يوفر HTTPS تلقائياً

## 🚨 استكشاف الأخطاء

### مشاكل شائعة

1. **خطأ CORS**
   - تحقق من إعدادات CORS في Supabase
   - تأكد من إضافة domain الصحيح

2. **خطأ RLS**
   - تحقق من سياسات RLS
   - تأكد من صحة المصادقة

3. **خطأ Build**
   - تحقق من متغيرات البيئة
   - راجع logs في Vercel

### أدوات التشخيص

1. **Vercel Logs**
```bash
vercel logs your-project-url
```

2. **Supabase Logs**
   - انتقل إلى Logs في Supabase Dashboard
   - راقب API requests و Database queries

## 📈 التحسين المستمر

### مراقبة الأداء
- استخدم Lighthouse لتحليل الأداء
- راقب Core Web Vitals
- اختبر على أجهزة مختلفة

### تحديثات منتظمة
- حدث التبعيات بانتظام
- راقب تحديثات Supabase
- اختبر الميزات الجديدة

### النسخ الاحتياطي
- اعمل backup منتظم لقاعدة البيانات
- احفظ نسخة من إعدادات Supabase
- وثق التغييرات المهمة

## 🎯 نصائح للإنتاج

1. **استخدم Environment Variables**
   - فصل إعدادات التطوير عن الإنتاج
   - احم المفاتيح الحساسة

2. **اختبر قبل النشر**
   - اختبر جميع الوظائف
   - تأكد من عمل RLS بشكل صحيح

3. **راقب الاستخدام**
   - راقب حدود Supabase
   - خطط للترقية عند الحاجة

4. **وثق التغييرات**
   - احتفظ بسجل التحديثات
   - وثق إعدادات الإنتاج
