{"version": 3, "file": "is.js", "sourceRoot": "", "sources": ["../../src/is.ts"], "names": [], "mappings": "AAAA,IAAM,OAAO,GAAG,UAAC,OAAY;IAC3B,OAAA,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAApD,CAAoD,CAAC;AAEvD,MAAM,CAAC,IAAM,WAAW,GAAG,UAAC,OAAY;IACtC,OAAA,OAAO,OAAO,KAAK,WAAW;AAA9B,CAA8B,CAAC;AAEjC,MAAM,CAAC,IAAM,MAAM,GAAG,UAAC,OAAY,IAAsB,OAAA,OAAO,KAAK,IAAI,EAAhB,CAAgB,CAAC;AAE1E,MAAM,CAAC,IAAM,aAAa,GAAG,UAC3B,OAAY;IAEZ,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,KAAK,IAAI;QAAE,OAAO,KAAK,CAAC;IAClE,IAAI,OAAO,KAAK,MAAM,CAAC,SAAS;QAAE,OAAO,KAAK,CAAC;IAC/C,IAAI,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK,IAAI;QAAE,OAAO,IAAI,CAAC;IAEzD,OAAO,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK,MAAM,CAAC,SAAS,CAAC;AAC7D,CAAC,CAAC;AAEF,MAAM,CAAC,IAAM,aAAa,GAAG,UAAC,OAAY;IACxC,OAAA,aAAa,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK,CAAC;AAA3D,CAA2D,CAAC;AAE9D,MAAM,CAAC,IAAM,OAAO,GAAG,UAAC,OAAY;IAClC,OAAA,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC;AAAtB,CAAsB,CAAC;AAEzB,MAAM,CAAC,IAAM,QAAQ,GAAG,UAAC,OAAY;IACnC,OAAA,OAAO,OAAO,KAAK,QAAQ;AAA3B,CAA2B,CAAC;AAE9B,MAAM,CAAC,IAAM,QAAQ,GAAG,UAAC,OAAY;IACnC,OAAA,OAAO,OAAO,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;AAA9C,CAA8C,CAAC;AAEjD,MAAM,CAAC,IAAM,SAAS,GAAG,UAAC,OAAY;IACpC,OAAA,OAAO,OAAO,KAAK,SAAS;AAA5B,CAA4B,CAAC;AAE/B,MAAM,CAAC,IAAM,QAAQ,GAAG,UAAC,OAAY;IACnC,OAAA,OAAO,YAAY,MAAM;AAAzB,CAAyB,CAAC;AAE5B,MAAM,CAAC,IAAM,KAAK,GAAG,UAAC,OAAY;IAChC,OAAA,OAAO,YAAY,GAAG;AAAtB,CAAsB,CAAC;AAEzB,MAAM,CAAC,IAAM,KAAK,GAAG,UAAC,OAAY;IAChC,OAAA,OAAO,YAAY,GAAG;AAAtB,CAAsB,CAAC;AAEzB,MAAM,CAAC,IAAM,QAAQ,GAAG,UAAC,OAAY;IACnC,OAAA,OAAO,CAAC,OAAO,CAAC,KAAK,QAAQ;AAA7B,CAA6B,CAAC;AAEhC,MAAM,CAAC,IAAM,MAAM,GAAG,UAAC,OAAY;IACjC,OAAA,OAAO,YAAY,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;AAApD,CAAoD,CAAC;AAEvD,MAAM,CAAC,IAAM,OAAO,GAAG,UAAC,OAAY;IAClC,OAAA,OAAO,YAAY,KAAK;AAAxB,CAAwB,CAAC;AAE3B,MAAM,CAAC,IAAM,UAAU,GAAG,UAAC,OAAY;IACrC,OAAA,OAAO,OAAO,KAAK,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC;AAA7C,CAA6C,CAAC;AAEhD,MAAM,CAAC,IAAM,WAAW,GAAG,UACzB,OAAY;IAEZ,OAAA,SAAS,CAAC,OAAO,CAAC;QAClB,MAAM,CAAC,OAAO,CAAC;QACf,WAAW,CAAC,OAAO,CAAC;QACpB,QAAQ,CAAC,OAAO,CAAC;QACjB,QAAQ,CAAC,OAAO,CAAC;QACjB,QAAQ,CAAC,OAAO,CAAC;AALjB,CAKiB,CAAC;AAEpB,MAAM,CAAC,IAAM,QAAQ,GAAG,UAAC,OAAY;IACnC,OAAA,OAAO,OAAO,KAAK,QAAQ;AAA3B,CAA2B,CAAC;AAE9B,MAAM,CAAC,IAAM,UAAU,GAAG,UAAC,OAAY;IACrC,OAAA,OAAO,KAAK,QAAQ,IAAI,OAAO,KAAK,CAAC,QAAQ;AAA7C,CAA6C,CAAC;AAehD,MAAM,CAAC,IAAM,YAAY,GAAG,UAAC,OAAY;IACvC,OAAA,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,OAAO,YAAY,QAAQ,CAAC;AAA7D,CAA6D,CAAC;AAEhE,MAAM,CAAC,IAAM,KAAK,GAAG,UAAC,OAAY,IAAqB,OAAA,OAAO,YAAY,GAAG,EAAtB,CAAsB,CAAC"}