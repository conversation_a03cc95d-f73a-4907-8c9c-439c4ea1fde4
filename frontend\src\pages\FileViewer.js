import React, { useState } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { 
  FiArrowLeft, 
  FiDownload, 
  FiStar, 
  FiHeart,
  FiShare2,
  FiEye,
  FiCalendar,
  FiUser,
  FiFile
} from 'react-icons/fi';
import { Card, Button } from '../components/ui';
import { PageTransition } from '../components/ui/AnimatedComponents';
import CommentsSection from '../components/comments/CommentsSection';

const ViewerContainer = styled.div`
  min-height: 100vh;
  background: ${({ theme }) => theme.colors.gray[50]};
  padding: ${({ theme }) => theme.spacing[6]} 0;
`;

const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 ${({ theme }) => theme.spacing[4]};
`;

const Header = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[4]};
  margin-bottom: ${({ theme }) => theme.spacing[8]};
  
  @media (max-width: ${({ theme }) => theme.breakpoints.md}) {
    flex-direction: column;
    align-items: stretch;
    gap: ${({ theme }) => theme.spacing[3]};
  }
`;

const BackButton = styled(Button)`
  @media (max-width: ${({ theme }) => theme.breakpoints.md}) {
    align-self: flex-start;
  }
`;

const FileInfo = styled.div`
  flex: 1;
`;

const FileName = styled.h1`
  font-size: ${({ theme }) => theme.fontSizes['3xl']};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  color: ${({ theme }) => theme.colors.gray[900]};
  margin-bottom: ${({ theme }) => theme.spacing[2]};
  
  @media (max-width: ${({ theme }) => theme.breakpoints.md}) {
    font-size: ${({ theme }) => theme.fontSizes['2xl']};
  }
`;

const FileDescription = styled.p`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  color: ${({ theme }) => theme.colors.gray[600]};
  line-height: ${({ theme }) => theme.lineHeights.relaxed};
`;

const ActionButtons = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing[3]};
  
  @media (max-width: ${({ theme }) => theme.breakpoints.md}) {
    flex-wrap: wrap;
  }
`;

const ViewerContent = styled.div`
  display: grid;
  grid-template-columns: 1fr 350px;
  gap: ${({ theme }) => theme.spacing[8]};
  
  @media (max-width: ${({ theme }) => theme.breakpoints.lg}) {
    grid-template-columns: 1fr;
    gap: ${({ theme }) => theme.spacing[6]};
  }
`;

const MainViewer = styled(Card)`
  min-height: 600px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: ${({ theme }) => theme.colors.white};
`;

const ViewerPlaceholder = styled.div`
  text-align: center;
  color: ${({ theme }) => theme.colors.gray[500]};
`;

const Sidebar = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing[6]};
`;

const MetadataCard = styled(Card)``;

const MetadataItem = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[3]};
  padding: ${({ theme }) => theme.spacing[3]} 0;
  border-bottom: 1px solid ${({ theme }) => theme.colors.gray[100]};
  
  &:last-child {
    border-bottom: none;
  }
`;

const MetadataIcon = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: ${({ theme }) => theme.colors.gray[100]};
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${({ theme }) => theme.colors.gray[600]};
`;

const MetadataContent = styled.div`
  flex: 1;
`;

const MetadataLabel = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.gray[500]};
  margin-bottom: 2px;
`;

const MetadataValue = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.base};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  color: ${({ theme }) => theme.colors.gray[900]};
`;

const RatingSection = styled(Card)``;

const RatingHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: ${({ theme }) => theme.spacing[4]};
`;

const RatingTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  color: ${({ theme }) => theme.colors.gray[900]};
`;

const RatingStars = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing[1]};
`;

const Star = styled(motion.button)`
  background: none;
  border: none;
  color: ${({ filled, theme }) => 
    filled ? theme.colors.warning[400] : theme.colors.gray[300]};
  cursor: pointer;
  padding: ${({ theme }) => theme.spacing[1]};
  border-radius: ${({ theme }) => theme.borderRadius.base};
  
  &:hover {
    background: ${({ theme }) => theme.colors.gray[100]};
  }
`;

const RatingText = styled.div`
  text-align: center;
  margin-top: ${({ theme }) => theme.spacing[2]};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.gray[600]};
`;

const FileViewer = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [userRating, setUserRating] = useState(0);
  const [hoverRating, setHoverRating] = useState(0);

  // Mock data - في التطبيق الحقيقي سيتم جلبها من API
  const fileData = {
    id: id,
    title: 'مقدمة في البرمجة',
    description: 'ملف تعليمي شامل يغطي أساسيات البرمجة والمفاهيم الأساسية للمبتدئين',
    type: 'pdf',
    size: 2048576,
    uploadedBy: 'أحمد محمد',
    uploadDate: '2024-01-15',
    downloadCount: 234,
    averageRating: 4.5,
    subject: 'علوم الحاسوب',
    semester: 'الفصل الأول'
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA');
  };

  const handleRating = (rating) => {
    setUserRating(rating);
    // هنا يمكن إرسال التقييم إلى الخادم
    console.log('Rating submitted:', rating);
  };

  const handleDownload = () => {
    // منطق تحميل الملف
    console.log('Downloading file:', fileData.id);
  };

  const handleShare = () => {
    // منطق مشاركة الملف
    if (navigator.share) {
      navigator.share({
        title: fileData.title,
        text: fileData.description,
        url: window.location.href
      });
    } else {
      // Fallback للمتصفحات التي لا تدعم Web Share API
      navigator.clipboard.writeText(window.location.href);
      alert('تم نسخ الرابط إلى الحافظة');
    }
  };

  return (
    <PageTransition>
      <ViewerContainer>
        <Container>
          <Header>
            <BackButton
              variant="outline"
              leftIcon={<FiArrowLeft size={18} />}
              onClick={() => navigate(-1)}
            >
              العودة
            </BackButton>
            
            <FileInfo>
              <FileName>{fileData.title}</FileName>
              <FileDescription>{fileData.description}</FileDescription>
            </FileInfo>
            
            <ActionButtons>
              <Button
                variant="primary"
                leftIcon={<FiDownload size={18} />}
                onClick={handleDownload}
              >
                تحميل
              </Button>
              <Button
                variant="outline"
                leftIcon={<FiShare2 size={18} />}
                onClick={handleShare}
              >
                مشاركة
              </Button>
              <Button
                variant="ghost"
                leftIcon={<FiHeart size={18} />}
              >
                إضافة للمفضلة
              </Button>
            </ActionButtons>
          </Header>

          <ViewerContent>
            <MainViewer padding="xl">
              <ViewerPlaceholder>
                <FiFile size={64} style={{ marginBottom: '16px', opacity: 0.5 }} />
                <div style={{ fontSize: '18px', marginBottom: '8px' }}>
                  معاينة الملف
                </div>
                <div style={{ fontSize: '14px' }}>
                  معاينة الملفات ستكون متاحة قريباً
                </div>
              </ViewerPlaceholder>
            </MainViewer>

            <Sidebar>
              <MetadataCard padding="lg">
                <h3 style={{ marginBottom: '24px', fontSize: '18px', fontWeight: 600 }}>
                  معلومات الملف
                </h3>
                
                <MetadataItem>
                  <MetadataIcon>
                    <FiUser size={18} />
                  </MetadataIcon>
                  <MetadataContent>
                    <MetadataLabel>رفع بواسطة</MetadataLabel>
                    <MetadataValue>{fileData.uploadedBy}</MetadataValue>
                  </MetadataContent>
                </MetadataItem>

                <MetadataItem>
                  <MetadataIcon>
                    <FiCalendar size={18} />
                  </MetadataIcon>
                  <MetadataContent>
                    <MetadataLabel>تاريخ الرفع</MetadataLabel>
                    <MetadataValue>{formatDate(fileData.uploadDate)}</MetadataValue>
                  </MetadataContent>
                </MetadataItem>

                <MetadataItem>
                  <MetadataIcon>
                    <FiFile size={18} />
                  </MetadataIcon>
                  <MetadataContent>
                    <MetadataLabel>حجم الملف</MetadataLabel>
                    <MetadataValue>{formatFileSize(fileData.size)}</MetadataValue>
                  </MetadataContent>
                </MetadataItem>

                <MetadataItem>
                  <MetadataIcon>
                    <FiEye size={18} />
                  </MetadataIcon>
                  <MetadataContent>
                    <MetadataLabel>عدد التحميلات</MetadataLabel>
                    <MetadataValue>{fileData.downloadCount}</MetadataValue>
                  </MetadataContent>
                </MetadataItem>
              </MetadataCard>

              <RatingSection padding="lg">
                <RatingHeader>
                  <RatingTitle>تقييم الملف</RatingTitle>
                  <div style={{ fontSize: '14px', color: '#6b7280' }}>
                    {fileData.averageRating.toFixed(1)} / 5.0
                  </div>
                </RatingHeader>
                
                <RatingStars>
                  {[1, 2, 3, 4, 5].map((star) => (
                    <Star
                      key={star}
                      filled={star <= (hoverRating || userRating)}
                      onClick={() => handleRating(star)}
                      onMouseEnter={() => setHoverRating(star)}
                      onMouseLeave={() => setHoverRating(0)}
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                    >
                      <FiStar size={20} fill={star <= (hoverRating || userRating) ? 'currentColor' : 'none'} />
                    </Star>
                  ))}
                </RatingStars>
                
                <RatingText>
                  {userRating > 0 ? `لقد قيمت هذا الملف بـ ${userRating} نجوم` : 'انقر على النجوم لتقييم الملف'}
                </RatingText>
              </RatingSection>
            </Sidebar>
          </ViewerContent>

          {/* قسم التعليقات */}
          <div style={{ marginTop: '32px' }}>
            <CommentsSection fileId={id} />
          </div>
        </Container>
      </ViewerContainer>
    </PageTransition>
  );
};

export default FileViewer;
