{"ast": null, "code": "import { SubscriptionManager } from '../../utils/subscription-manager.mjs';\nimport { mixValues } from '../animation/mix-values.mjs';\nimport { copyBoxInto } from '../geometry/copy.mjs';\nimport { translateAxis, transformBox, applyBoxDelta, applyTreeDeltas } from '../geometry/delta-apply.mjs';\nimport { calcRelativePosition, calcRelativeBox, calcBoxDelta, calcLength, isNear } from '../geometry/delta-calc.mjs';\nimport { removeBoxTransforms } from '../geometry/delta-remove.mjs';\nimport { createBox, createDelta } from '../geometry/models.mjs';\nimport { getValueTransition } from '../../animation/utils/transitions.mjs';\nimport { boxEqualsRounded, isDeltaZero, aspectRatio, boxEquals } from '../geometry/utils.mjs';\nimport { NodeStack } from '../shared/stack.mjs';\nimport { scaleCorrectors } from '../styles/scale-correction.mjs';\nimport { buildProjectionTransform } from '../styles/transform.mjs';\nimport { eachAxis } from '../utils/each-axis.mjs';\nimport { hasTransform, hasScale, has2DTranslate } from '../utils/has-transform.mjs';\nimport { FlatTree } from '../../render/utils/flat-tree.mjs';\nimport { resolveMotionValue } from '../../value/utils/resolve-motion-value.mjs';\nimport { globalProjectionState } from './state.mjs';\nimport { delay } from '../../utils/delay.mjs';\nimport { mix } from '../../utils/mix.mjs';\nimport { record } from '../../debug/record.mjs';\nimport { isSVGElement } from '../../render/dom/utils/is-svg-element.mjs';\nimport { animateSingleValue } from '../../animation/interfaces/single-value.mjs';\nimport { clamp } from '../../utils/clamp.mjs';\nimport { cancelFrame, frameData, steps, frame } from '../../frameloop/frame.mjs';\nimport { noop } from '../../utils/noop.mjs';\nconst transformAxes = [\"\", \"X\", \"Y\", \"Z\"];\nconst hiddenVisibility = {\n  visibility: \"hidden\"\n};\n/**\n * We use 1000 as the animation target as 0-1000 maps better to pixels than 0-1\n * which has a noticeable difference in spring animations\n */\nconst animationTarget = 1000;\nlet id = 0;\n/**\n * Use a mutable data object for debug data so as to not create a new\n * object every frame.\n */\nconst projectionFrameData = {\n  type: \"projectionFrame\",\n  totalNodes: 0,\n  resolvedTargetDeltas: 0,\n  recalculatedProjection: 0\n};\nfunction createProjectionNode({\n  attachResizeListener,\n  defaultParent,\n  measureScroll,\n  checkIsScrollRoot,\n  resetTransform\n}) {\n  return class ProjectionNode {\n    constructor(latestValues = {}, parent = defaultParent === null || defaultParent === void 0 ? void 0 : defaultParent()) {\n      /**\n       * A unique ID generated for every projection node.\n       */\n      this.id = id++;\n      /**\n       * An id that represents a unique session instigated by startUpdate.\n       */\n      this.animationId = 0;\n      /**\n       * A Set containing all this component's children. This is used to iterate\n       * through the children.\n       *\n       * TODO: This could be faster to iterate as a flat array stored on the root node.\n       */\n      this.children = new Set();\n      /**\n       * Options for the node. We use this to configure what kind of layout animations\n       * we should perform (if any).\n       */\n      this.options = {};\n      /**\n       * We use this to detect when its safe to shut down part of a projection tree.\n       * We have to keep projecting children for scale correction and relative projection\n       * until all their parents stop performing layout animations.\n       */\n      this.isTreeAnimating = false;\n      this.isAnimationBlocked = false;\n      /**\n       * Flag to true if we think this layout has been changed. We can't always know this,\n       * currently we set it to true every time a component renders, or if it has a layoutDependency\n       * if that has changed between renders. Additionally, components can be grouped by LayoutGroup\n       * and if one node is dirtied, they all are.\n       */\n      this.isLayoutDirty = false;\n      /**\n       * Flag to true if we think the projection calculations for this node needs\n       * recalculating as a result of an updated transform or layout animation.\n       */\n      this.isProjectionDirty = false;\n      /**\n       * Flag to true if the layout *or* transform has changed. This then gets propagated\n       * throughout the projection tree, forcing any element below to recalculate on the next frame.\n       */\n      this.isSharedProjectionDirty = false;\n      /**\n       * Flag transform dirty. This gets propagated throughout the whole tree but is only\n       * respected by shared nodes.\n       */\n      this.isTransformDirty = false;\n      /**\n       * Block layout updates for instant layout transitions throughout the tree.\n       */\n      this.updateManuallyBlocked = false;\n      this.updateBlockedByResize = false;\n      /**\n       * Set to true between the start of the first `willUpdate` call and the end of the `didUpdate`\n       * call.\n       */\n      this.isUpdating = false;\n      /**\n       * If this is an SVG element we currently disable projection transforms\n       */\n      this.isSVG = false;\n      /**\n       * Flag to true (during promotion) if a node doing an instant layout transition needs to reset\n       * its projection styles.\n       */\n      this.needsReset = false;\n      /**\n       * Flags whether this node should have its transform reset prior to measuring.\n       */\n      this.shouldResetTransform = false;\n      /**\n       * An object representing the calculated contextual/accumulated/tree scale.\n       * This will be used to scale calculcated projection transforms, as these are\n       * calculated in screen-space but need to be scaled for elements to layoutly\n       * make it to their calculated destinations.\n       *\n       * TODO: Lazy-init\n       */\n      this.treeScale = {\n        x: 1,\n        y: 1\n      };\n      /**\n       *\n       */\n      this.eventHandlers = new Map();\n      this.hasTreeAnimated = false;\n      // Note: Currently only running on root node\n      this.updateScheduled = false;\n      this.projectionUpdateScheduled = false;\n      this.checkUpdateFailed = () => {\n        if (this.isUpdating) {\n          this.isUpdating = false;\n          this.clearAllSnapshots();\n        }\n      };\n      /**\n       * This is a multi-step process as shared nodes might be of different depths. Nodes\n       * are sorted by depth order, so we need to resolve the entire tree before moving to\n       * the next step.\n       */\n      this.updateProjection = () => {\n        this.projectionUpdateScheduled = false;\n        /**\n         * Reset debug counts. Manually resetting rather than creating a new\n         * object each frame.\n         */\n        projectionFrameData.totalNodes = projectionFrameData.resolvedTargetDeltas = projectionFrameData.recalculatedProjection = 0;\n        this.nodes.forEach(propagateDirtyNodes);\n        this.nodes.forEach(resolveTargetDelta);\n        this.nodes.forEach(calcProjection);\n        this.nodes.forEach(cleanDirtyNodes);\n        record(projectionFrameData);\n      };\n      this.hasProjected = false;\n      this.isVisible = true;\n      this.animationProgress = 0;\n      /**\n       * Shared layout\n       */\n      // TODO Only running on root node\n      this.sharedNodes = new Map();\n      this.latestValues = latestValues;\n      this.root = parent ? parent.root || parent : this;\n      this.path = parent ? [...parent.path, parent] : [];\n      this.parent = parent;\n      this.depth = parent ? parent.depth + 1 : 0;\n      for (let i = 0; i < this.path.length; i++) {\n        this.path[i].shouldResetTransform = true;\n      }\n      if (this.root === this) this.nodes = new FlatTree();\n    }\n    addEventListener(name, handler) {\n      if (!this.eventHandlers.has(name)) {\n        this.eventHandlers.set(name, new SubscriptionManager());\n      }\n      return this.eventHandlers.get(name).add(handler);\n    }\n    notifyListeners(name, ...args) {\n      const subscriptionManager = this.eventHandlers.get(name);\n      subscriptionManager && subscriptionManager.notify(...args);\n    }\n    hasListeners(name) {\n      return this.eventHandlers.has(name);\n    }\n    /**\n     * Lifecycles\n     */\n    mount(instance, isLayoutDirty = this.root.hasTreeAnimated) {\n      if (this.instance) return;\n      this.isSVG = isSVGElement(instance);\n      this.instance = instance;\n      const {\n        layoutId,\n        layout,\n        visualElement\n      } = this.options;\n      if (visualElement && !visualElement.current) {\n        visualElement.mount(instance);\n      }\n      this.root.nodes.add(this);\n      this.parent && this.parent.children.add(this);\n      if (isLayoutDirty && (layout || layoutId)) {\n        this.isLayoutDirty = true;\n      }\n      if (attachResizeListener) {\n        let cancelDelay;\n        const resizeUnblockUpdate = () => this.root.updateBlockedByResize = false;\n        attachResizeListener(instance, () => {\n          this.root.updateBlockedByResize = true;\n          cancelDelay && cancelDelay();\n          cancelDelay = delay(resizeUnblockUpdate, 250);\n          if (globalProjectionState.hasAnimatedSinceResize) {\n            globalProjectionState.hasAnimatedSinceResize = false;\n            this.nodes.forEach(finishAnimation);\n          }\n        });\n      }\n      if (layoutId) {\n        this.root.registerSharedNode(layoutId, this);\n      }\n      // Only register the handler if it requires layout animation\n      if (this.options.animate !== false && visualElement && (layoutId || layout)) {\n        this.addEventListener(\"didUpdate\", ({\n          delta,\n          hasLayoutChanged,\n          hasRelativeTargetChanged,\n          layout: newLayout\n        }) => {\n          if (this.isTreeAnimationBlocked()) {\n            this.target = undefined;\n            this.relativeTarget = undefined;\n            return;\n          }\n          // TODO: Check here if an animation exists\n          const layoutTransition = this.options.transition || visualElement.getDefaultTransition() || defaultLayoutTransition;\n          const {\n            onLayoutAnimationStart,\n            onLayoutAnimationComplete\n          } = visualElement.getProps();\n          /**\n           * The target layout of the element might stay the same,\n           * but its position relative to its parent has changed.\n           */\n          const targetChanged = !this.targetLayout || !boxEqualsRounded(this.targetLayout, newLayout) || hasRelativeTargetChanged;\n          /**\n           * If the layout hasn't seemed to have changed, it might be that the\n           * element is visually in the same place in the document but its position\n           * relative to its parent has indeed changed. So here we check for that.\n           */\n          const hasOnlyRelativeTargetChanged = !hasLayoutChanged && hasRelativeTargetChanged;\n          if (this.options.layoutRoot || this.resumeFrom && this.resumeFrom.instance || hasOnlyRelativeTargetChanged || hasLayoutChanged && (targetChanged || !this.currentAnimation)) {\n            if (this.resumeFrom) {\n              this.resumingFrom = this.resumeFrom;\n              this.resumingFrom.resumingFrom = undefined;\n            }\n            this.setAnimationOrigin(delta, hasOnlyRelativeTargetChanged);\n            const animationOptions = {\n              ...getValueTransition(layoutTransition, \"layout\"),\n              onPlay: onLayoutAnimationStart,\n              onComplete: onLayoutAnimationComplete\n            };\n            if (visualElement.shouldReduceMotion || this.options.layoutRoot) {\n              animationOptions.delay = 0;\n              animationOptions.type = false;\n            }\n            this.startAnimation(animationOptions);\n          } else {\n            /**\n             * If the layout hasn't changed and we have an animation that hasn't started yet,\n             * finish it immediately. Otherwise it will be animating from a location\n             * that was probably never commited to screen and look like a jumpy box.\n             */\n            if (!hasLayoutChanged) {\n              finishAnimation(this);\n            }\n            if (this.isLead() && this.options.onExitComplete) {\n              this.options.onExitComplete();\n            }\n          }\n          this.targetLayout = newLayout;\n        });\n      }\n    }\n    unmount() {\n      this.options.layoutId && this.willUpdate();\n      this.root.nodes.remove(this);\n      const stack = this.getStack();\n      stack && stack.remove(this);\n      this.parent && this.parent.children.delete(this);\n      this.instance = undefined;\n      cancelFrame(this.updateProjection);\n    }\n    // only on the root\n    blockUpdate() {\n      this.updateManuallyBlocked = true;\n    }\n    unblockUpdate() {\n      this.updateManuallyBlocked = false;\n    }\n    isUpdateBlocked() {\n      return this.updateManuallyBlocked || this.updateBlockedByResize;\n    }\n    isTreeAnimationBlocked() {\n      return this.isAnimationBlocked || this.parent && this.parent.isTreeAnimationBlocked() || false;\n    }\n    // Note: currently only running on root node\n    startUpdate() {\n      if (this.isUpdateBlocked()) return;\n      this.isUpdating = true;\n      this.nodes && this.nodes.forEach(resetRotation);\n      this.animationId++;\n    }\n    getTransformTemplate() {\n      const {\n        visualElement\n      } = this.options;\n      return visualElement && visualElement.getProps().transformTemplate;\n    }\n    willUpdate(shouldNotifyListeners = true) {\n      this.root.hasTreeAnimated = true;\n      if (this.root.isUpdateBlocked()) {\n        this.options.onExitComplete && this.options.onExitComplete();\n        return;\n      }\n      !this.root.isUpdating && this.root.startUpdate();\n      if (this.isLayoutDirty) return;\n      this.isLayoutDirty = true;\n      for (let i = 0; i < this.path.length; i++) {\n        const node = this.path[i];\n        node.shouldResetTransform = true;\n        node.updateScroll(\"snapshot\");\n        if (node.options.layoutRoot) {\n          node.willUpdate(false);\n        }\n      }\n      const {\n        layoutId,\n        layout\n      } = this.options;\n      if (layoutId === undefined && !layout) return;\n      const transformTemplate = this.getTransformTemplate();\n      this.prevTransformTemplateValue = transformTemplate ? transformTemplate(this.latestValues, \"\") : undefined;\n      this.updateSnapshot();\n      shouldNotifyListeners && this.notifyListeners(\"willUpdate\");\n    }\n    update() {\n      this.updateScheduled = false;\n      const updateWasBlocked = this.isUpdateBlocked();\n      // When doing an instant transition, we skip the layout update,\n      // but should still clean up the measurements so that the next\n      // snapshot could be taken correctly.\n      if (updateWasBlocked) {\n        this.unblockUpdate();\n        this.clearAllSnapshots();\n        this.nodes.forEach(clearMeasurements);\n        return;\n      }\n      if (!this.isUpdating) {\n        this.nodes.forEach(clearIsLayoutDirty);\n      }\n      this.isUpdating = false;\n      /**\n       * Write\n       */\n      this.nodes.forEach(resetTransformStyle);\n      /**\n       * Read ==================\n       */\n      // Update layout measurements of updated children\n      this.nodes.forEach(updateLayout);\n      /**\n       * Write\n       */\n      // Notify listeners that the layout is updated\n      this.nodes.forEach(notifyLayoutUpdate);\n      this.clearAllSnapshots();\n      /**\n       * Manually flush any pending updates. Ideally\n       * we could leave this to the following requestAnimationFrame but this seems\n       * to leave a flash of incorrectly styled content.\n       */\n      const now = performance.now();\n      frameData.delta = clamp(0, 1000 / 60, now - frameData.timestamp);\n      frameData.timestamp = now;\n      frameData.isProcessing = true;\n      steps.update.process(frameData);\n      steps.preRender.process(frameData);\n      steps.render.process(frameData);\n      frameData.isProcessing = false;\n    }\n    didUpdate() {\n      if (!this.updateScheduled) {\n        this.updateScheduled = true;\n        queueMicrotask(() => this.update());\n      }\n    }\n    clearAllSnapshots() {\n      this.nodes.forEach(clearSnapshot);\n      this.sharedNodes.forEach(removeLeadSnapshots);\n    }\n    scheduleUpdateProjection() {\n      if (!this.projectionUpdateScheduled) {\n        this.projectionUpdateScheduled = true;\n        frame.preRender(this.updateProjection, false, true);\n      }\n    }\n    scheduleCheckAfterUnmount() {\n      /**\n       * If the unmounting node is in a layoutGroup and did trigger a willUpdate,\n       * we manually call didUpdate to give a chance to the siblings to animate.\n       * Otherwise, cleanup all snapshots to prevents future nodes from reusing them.\n       */\n      frame.postRender(() => {\n        if (this.isLayoutDirty) {\n          this.root.didUpdate();\n        } else {\n          this.root.checkUpdateFailed();\n        }\n      });\n    }\n    /**\n     * Update measurements\n     */\n    updateSnapshot() {\n      if (this.snapshot || !this.instance) return;\n      this.snapshot = this.measure();\n    }\n    updateLayout() {\n      if (!this.instance) return;\n      // TODO: Incorporate into a forwarded scroll offset\n      this.updateScroll();\n      if (!(this.options.alwaysMeasureLayout && this.isLead()) && !this.isLayoutDirty) {\n        return;\n      }\n      /**\n       * When a node is mounted, it simply resumes from the prevLead's\n       * snapshot instead of taking a new one, but the ancestors scroll\n       * might have updated while the prevLead is unmounted. We need to\n       * update the scroll again to make sure the layout we measure is\n       * up to date.\n       */\n      if (this.resumeFrom && !this.resumeFrom.instance) {\n        for (let i = 0; i < this.path.length; i++) {\n          const node = this.path[i];\n          node.updateScroll();\n        }\n      }\n      const prevLayout = this.layout;\n      this.layout = this.measure(false);\n      this.layoutCorrected = createBox();\n      this.isLayoutDirty = false;\n      this.projectionDelta = undefined;\n      this.notifyListeners(\"measure\", this.layout.layoutBox);\n      const {\n        visualElement\n      } = this.options;\n      visualElement && visualElement.notify(\"LayoutMeasure\", this.layout.layoutBox, prevLayout ? prevLayout.layoutBox : undefined);\n    }\n    updateScroll(phase = \"measure\") {\n      let needsMeasurement = Boolean(this.options.layoutScroll && this.instance);\n      if (this.scroll && this.scroll.animationId === this.root.animationId && this.scroll.phase === phase) {\n        needsMeasurement = false;\n      }\n      if (needsMeasurement) {\n        this.scroll = {\n          animationId: this.root.animationId,\n          phase,\n          isRoot: checkIsScrollRoot(this.instance),\n          offset: measureScroll(this.instance)\n        };\n      }\n    }\n    resetTransform() {\n      if (!resetTransform) return;\n      const isResetRequested = this.isLayoutDirty || this.shouldResetTransform;\n      const hasProjection = this.projectionDelta && !isDeltaZero(this.projectionDelta);\n      const transformTemplate = this.getTransformTemplate();\n      const transformTemplateValue = transformTemplate ? transformTemplate(this.latestValues, \"\") : undefined;\n      const transformTemplateHasChanged = transformTemplateValue !== this.prevTransformTemplateValue;\n      if (isResetRequested && (hasProjection || hasTransform(this.latestValues) || transformTemplateHasChanged)) {\n        resetTransform(this.instance, transformTemplateValue);\n        this.shouldResetTransform = false;\n        this.scheduleRender();\n      }\n    }\n    measure(removeTransform = true) {\n      const pageBox = this.measurePageBox();\n      let layoutBox = this.removeElementScroll(pageBox);\n      /**\n       * Measurements taken during the pre-render stage\n       * still have transforms applied so we remove them\n       * via calculation.\n       */\n      if (removeTransform) {\n        layoutBox = this.removeTransform(layoutBox);\n      }\n      roundBox(layoutBox);\n      return {\n        animationId: this.root.animationId,\n        measuredBox: pageBox,\n        layoutBox,\n        latestValues: {},\n        source: this.id\n      };\n    }\n    measurePageBox() {\n      const {\n        visualElement\n      } = this.options;\n      if (!visualElement) return createBox();\n      const box = visualElement.measureViewportBox();\n      // Remove viewport scroll to give page-relative coordinates\n      const {\n        scroll\n      } = this.root;\n      if (scroll) {\n        translateAxis(box.x, scroll.offset.x);\n        translateAxis(box.y, scroll.offset.y);\n      }\n      return box;\n    }\n    removeElementScroll(box) {\n      const boxWithoutScroll = createBox();\n      copyBoxInto(boxWithoutScroll, box);\n      /**\n       * Performance TODO: Keep a cumulative scroll offset down the tree\n       * rather than loop back up the path.\n       */\n      for (let i = 0; i < this.path.length; i++) {\n        const node = this.path[i];\n        const {\n          scroll,\n          options\n        } = node;\n        if (node !== this.root && scroll && options.layoutScroll) {\n          /**\n           * If this is a new scroll root, we want to remove all previous scrolls\n           * from the viewport box.\n           */\n          if (scroll.isRoot) {\n            copyBoxInto(boxWithoutScroll, box);\n            const {\n              scroll: rootScroll\n            } = this.root;\n            /**\n             * Undo the application of page scroll that was originally added\n             * to the measured bounding box.\n             */\n            if (rootScroll) {\n              translateAxis(boxWithoutScroll.x, -rootScroll.offset.x);\n              translateAxis(boxWithoutScroll.y, -rootScroll.offset.y);\n            }\n          }\n          translateAxis(boxWithoutScroll.x, scroll.offset.x);\n          translateAxis(boxWithoutScroll.y, scroll.offset.y);\n        }\n      }\n      return boxWithoutScroll;\n    }\n    applyTransform(box, transformOnly = false) {\n      const withTransforms = createBox();\n      copyBoxInto(withTransforms, box);\n      for (let i = 0; i < this.path.length; i++) {\n        const node = this.path[i];\n        if (!transformOnly && node.options.layoutScroll && node.scroll && node !== node.root) {\n          transformBox(withTransforms, {\n            x: -node.scroll.offset.x,\n            y: -node.scroll.offset.y\n          });\n        }\n        if (!hasTransform(node.latestValues)) continue;\n        transformBox(withTransforms, node.latestValues);\n      }\n      if (hasTransform(this.latestValues)) {\n        transformBox(withTransforms, this.latestValues);\n      }\n      return withTransforms;\n    }\n    removeTransform(box) {\n      const boxWithoutTransform = createBox();\n      copyBoxInto(boxWithoutTransform, box);\n      for (let i = 0; i < this.path.length; i++) {\n        const node = this.path[i];\n        if (!node.instance) continue;\n        if (!hasTransform(node.latestValues)) continue;\n        hasScale(node.latestValues) && node.updateSnapshot();\n        const sourceBox = createBox();\n        const nodeBox = node.measurePageBox();\n        copyBoxInto(sourceBox, nodeBox);\n        removeBoxTransforms(boxWithoutTransform, node.latestValues, node.snapshot ? node.snapshot.layoutBox : undefined, sourceBox);\n      }\n      if (hasTransform(this.latestValues)) {\n        removeBoxTransforms(boxWithoutTransform, this.latestValues);\n      }\n      return boxWithoutTransform;\n    }\n    setTargetDelta(delta) {\n      this.targetDelta = delta;\n      this.root.scheduleUpdateProjection();\n      this.isProjectionDirty = true;\n    }\n    setOptions(options) {\n      this.options = {\n        ...this.options,\n        ...options,\n        crossfade: options.crossfade !== undefined ? options.crossfade : true\n      };\n    }\n    clearMeasurements() {\n      this.scroll = undefined;\n      this.layout = undefined;\n      this.snapshot = undefined;\n      this.prevTransformTemplateValue = undefined;\n      this.targetDelta = undefined;\n      this.target = undefined;\n      this.isLayoutDirty = false;\n    }\n    forceRelativeParentToResolveTarget() {\n      if (!this.relativeParent) return;\n      /**\n       * If the parent target isn't up-to-date, force it to update.\n       * This is an unfortunate de-optimisation as it means any updating relative\n       * projection will cause all the relative parents to recalculate back\n       * up the tree.\n       */\n      if (this.relativeParent.resolvedRelativeTargetAt !== frameData.timestamp) {\n        this.relativeParent.resolveTargetDelta(true);\n      }\n    }\n    resolveTargetDelta(forceRecalculation = false) {\n      var _a;\n      /**\n       * Once the dirty status of nodes has been spread through the tree, we also\n       * need to check if we have a shared node of a different depth that has itself\n       * been dirtied.\n       */\n      const lead = this.getLead();\n      this.isProjectionDirty || (this.isProjectionDirty = lead.isProjectionDirty);\n      this.isTransformDirty || (this.isTransformDirty = lead.isTransformDirty);\n      this.isSharedProjectionDirty || (this.isSharedProjectionDirty = lead.isSharedProjectionDirty);\n      const isShared = Boolean(this.resumingFrom) || this !== lead;\n      /**\n       * We don't use transform for this step of processing so we don't\n       * need to check whether any nodes have changed transform.\n       */\n      const canSkip = !(forceRecalculation || isShared && this.isSharedProjectionDirty || this.isProjectionDirty || ((_a = this.parent) === null || _a === void 0 ? void 0 : _a.isProjectionDirty) || this.attemptToResolveRelativeTarget);\n      if (canSkip) return;\n      const {\n        layout,\n        layoutId\n      } = this.options;\n      /**\n       * If we have no layout, we can't perform projection, so early return\n       */\n      if (!this.layout || !(layout || layoutId)) return;\n      this.resolvedRelativeTargetAt = frameData.timestamp;\n      /**\n       * If we don't have a targetDelta but do have a layout, we can attempt to resolve\n       * a relativeParent. This will allow a component to perform scale correction\n       * even if no animation has started.\n       */\n      // TODO If this is unsuccessful this currently happens every frame\n      if (!this.targetDelta && !this.relativeTarget) {\n        // TODO: This is a semi-repetition of further down this function, make DRY\n        const relativeParent = this.getClosestProjectingParent();\n        if (relativeParent && relativeParent.layout && this.animationProgress !== 1) {\n          this.relativeParent = relativeParent;\n          this.forceRelativeParentToResolveTarget();\n          this.relativeTarget = createBox();\n          this.relativeTargetOrigin = createBox();\n          calcRelativePosition(this.relativeTargetOrigin, this.layout.layoutBox, relativeParent.layout.layoutBox);\n          copyBoxInto(this.relativeTarget, this.relativeTargetOrigin);\n        } else {\n          this.relativeParent = this.relativeTarget = undefined;\n        }\n      }\n      /**\n       * If we have no relative target or no target delta our target isn't valid\n       * for this frame.\n       */\n      if (!this.relativeTarget && !this.targetDelta) return;\n      /**\n       * Lazy-init target data structure\n       */\n      if (!this.target) {\n        this.target = createBox();\n        this.targetWithTransforms = createBox();\n      }\n      /**\n       * If we've got a relative box for this component, resolve it into a target relative to the parent.\n       */\n      if (this.relativeTarget && this.relativeTargetOrigin && this.relativeParent && this.relativeParent.target) {\n        this.forceRelativeParentToResolveTarget();\n        calcRelativeBox(this.target, this.relativeTarget, this.relativeParent.target);\n        /**\n         * If we've only got a targetDelta, resolve it into a target\n         */\n      } else if (this.targetDelta) {\n        if (Boolean(this.resumingFrom)) {\n          // TODO: This is creating a new object every frame\n          this.target = this.applyTransform(this.layout.layoutBox);\n        } else {\n          copyBoxInto(this.target, this.layout.layoutBox);\n        }\n        applyBoxDelta(this.target, this.targetDelta);\n      } else {\n        /**\n         * If no target, use own layout as target\n         */\n        copyBoxInto(this.target, this.layout.layoutBox);\n      }\n      /**\n       * If we've been told to attempt to resolve a relative target, do so.\n       */\n      if (this.attemptToResolveRelativeTarget) {\n        this.attemptToResolveRelativeTarget = false;\n        const relativeParent = this.getClosestProjectingParent();\n        if (relativeParent && Boolean(relativeParent.resumingFrom) === Boolean(this.resumingFrom) && !relativeParent.options.layoutScroll && relativeParent.target && this.animationProgress !== 1) {\n          this.relativeParent = relativeParent;\n          this.forceRelativeParentToResolveTarget();\n          this.relativeTarget = createBox();\n          this.relativeTargetOrigin = createBox();\n          calcRelativePosition(this.relativeTargetOrigin, this.target, relativeParent.target);\n          copyBoxInto(this.relativeTarget, this.relativeTargetOrigin);\n        } else {\n          this.relativeParent = this.relativeTarget = undefined;\n        }\n      }\n      /**\n       * Increase debug counter for resolved target deltas\n       */\n      projectionFrameData.resolvedTargetDeltas++;\n    }\n    getClosestProjectingParent() {\n      if (!this.parent || hasScale(this.parent.latestValues) || has2DTranslate(this.parent.latestValues)) {\n        return undefined;\n      }\n      if (this.parent.isProjecting()) {\n        return this.parent;\n      } else {\n        return this.parent.getClosestProjectingParent();\n      }\n    }\n    isProjecting() {\n      return Boolean((this.relativeTarget || this.targetDelta || this.options.layoutRoot) && this.layout);\n    }\n    calcProjection() {\n      var _a;\n      const lead = this.getLead();\n      const isShared = Boolean(this.resumingFrom) || this !== lead;\n      let canSkip = true;\n      /**\n       * If this is a normal layout animation and neither this node nor its nearest projecting\n       * is dirty then we can't skip.\n       */\n      if (this.isProjectionDirty || ((_a = this.parent) === null || _a === void 0 ? void 0 : _a.isProjectionDirty)) {\n        canSkip = false;\n      }\n      /**\n       * If this is a shared layout animation and this node's shared projection is dirty then\n       * we can't skip.\n       */\n      if (isShared && (this.isSharedProjectionDirty || this.isTransformDirty)) {\n        canSkip = false;\n      }\n      /**\n       * If we have resolved the target this frame we must recalculate the\n       * projection to ensure it visually represents the internal calculations.\n       */\n      if (this.resolvedRelativeTargetAt === frameData.timestamp) {\n        canSkip = false;\n      }\n      if (canSkip) return;\n      const {\n        layout,\n        layoutId\n      } = this.options;\n      /**\n       * If this section of the tree isn't animating we can\n       * delete our target sources for the following frame.\n       */\n      this.isTreeAnimating = Boolean(this.parent && this.parent.isTreeAnimating || this.currentAnimation || this.pendingAnimation);\n      if (!this.isTreeAnimating) {\n        this.targetDelta = this.relativeTarget = undefined;\n      }\n      if (!this.layout || !(layout || layoutId)) return;\n      /**\n       * Reset the corrected box with the latest values from box, as we're then going\n       * to perform mutative operations on it.\n       */\n      copyBoxInto(this.layoutCorrected, this.layout.layoutBox);\n      /**\n       * Record previous tree scales before updating.\n       */\n      const prevTreeScaleX = this.treeScale.x;\n      const prevTreeScaleY = this.treeScale.y;\n      /**\n       * Apply all the parent deltas to this box to produce the corrected box. This\n       * is the layout box, as it will appear on screen as a result of the transforms of its parents.\n       */\n      applyTreeDeltas(this.layoutCorrected, this.treeScale, this.path, isShared);\n      /**\n       * If this layer needs to perform scale correction but doesn't have a target,\n       * use the layout as the target.\n       */\n      if (lead.layout && !lead.target && (this.treeScale.x !== 1 || this.treeScale.y !== 1)) {\n        lead.target = lead.layout.layoutBox;\n      }\n      const {\n        target\n      } = lead;\n      if (!target) {\n        /**\n         * If we don't have a target to project into, but we were previously\n         * projecting, we want to remove the stored transform and schedule\n         * a render to ensure the elements reflect the removed transform.\n         */\n        if (this.projectionTransform) {\n          this.projectionDelta = createDelta();\n          this.projectionTransform = \"none\";\n          this.scheduleRender();\n        }\n        return;\n      }\n      if (!this.projectionDelta) {\n        this.projectionDelta = createDelta();\n        this.projectionDeltaWithTransform = createDelta();\n      }\n      const prevProjectionTransform = this.projectionTransform;\n      /**\n       * Update the delta between the corrected box and the target box before user-set transforms were applied.\n       * This will allow us to calculate the corrected borderRadius and boxShadow to compensate\n       * for our layout reprojection, but still allow them to be scaled correctly by the user.\n       * It might be that to simplify this we may want to accept that user-set scale is also corrected\n       * and we wouldn't have to keep and calc both deltas, OR we could support a user setting\n       * to allow people to choose whether these styles are corrected based on just the\n       * layout reprojection or the final bounding box.\n       */\n      calcBoxDelta(this.projectionDelta, this.layoutCorrected, target, this.latestValues);\n      this.projectionTransform = buildProjectionTransform(this.projectionDelta, this.treeScale);\n      if (this.projectionTransform !== prevProjectionTransform || this.treeScale.x !== prevTreeScaleX || this.treeScale.y !== prevTreeScaleY) {\n        this.hasProjected = true;\n        this.scheduleRender();\n        this.notifyListeners(\"projectionUpdate\", target);\n      }\n      /**\n       * Increase debug counter for recalculated projections\n       */\n      projectionFrameData.recalculatedProjection++;\n    }\n    hide() {\n      this.isVisible = false;\n      // TODO: Schedule render\n    }\n    show() {\n      this.isVisible = true;\n      // TODO: Schedule render\n    }\n    scheduleRender(notifyAll = true) {\n      this.options.scheduleRender && this.options.scheduleRender();\n      if (notifyAll) {\n        const stack = this.getStack();\n        stack && stack.scheduleRender();\n      }\n      if (this.resumingFrom && !this.resumingFrom.instance) {\n        this.resumingFrom = undefined;\n      }\n    }\n    setAnimationOrigin(delta, hasOnlyRelativeTargetChanged = false) {\n      const snapshot = this.snapshot;\n      const snapshotLatestValues = snapshot ? snapshot.latestValues : {};\n      const mixedValues = {\n        ...this.latestValues\n      };\n      const targetDelta = createDelta();\n      if (!this.relativeParent || !this.relativeParent.options.layoutRoot) {\n        this.relativeTarget = this.relativeTargetOrigin = undefined;\n      }\n      this.attemptToResolveRelativeTarget = !hasOnlyRelativeTargetChanged;\n      const relativeLayout = createBox();\n      const snapshotSource = snapshot ? snapshot.source : undefined;\n      const layoutSource = this.layout ? this.layout.source : undefined;\n      const isSharedLayoutAnimation = snapshotSource !== layoutSource;\n      const stack = this.getStack();\n      const isOnlyMember = !stack || stack.members.length <= 1;\n      const shouldCrossfadeOpacity = Boolean(isSharedLayoutAnimation && !isOnlyMember && this.options.crossfade === true && !this.path.some(hasOpacityCrossfade));\n      this.animationProgress = 0;\n      let prevRelativeTarget;\n      this.mixTargetDelta = latest => {\n        const progress = latest / 1000;\n        mixAxisDelta(targetDelta.x, delta.x, progress);\n        mixAxisDelta(targetDelta.y, delta.y, progress);\n        this.setTargetDelta(targetDelta);\n        if (this.relativeTarget && this.relativeTargetOrigin && this.layout && this.relativeParent && this.relativeParent.layout) {\n          calcRelativePosition(relativeLayout, this.layout.layoutBox, this.relativeParent.layout.layoutBox);\n          mixBox(this.relativeTarget, this.relativeTargetOrigin, relativeLayout, progress);\n          /**\n           * If this is an unchanged relative target we can consider the\n           * projection not dirty.\n           */\n          if (prevRelativeTarget && boxEquals(this.relativeTarget, prevRelativeTarget)) {\n            this.isProjectionDirty = false;\n          }\n          if (!prevRelativeTarget) prevRelativeTarget = createBox();\n          copyBoxInto(prevRelativeTarget, this.relativeTarget);\n        }\n        if (isSharedLayoutAnimation) {\n          this.animationValues = mixedValues;\n          mixValues(mixedValues, snapshotLatestValues, this.latestValues, progress, shouldCrossfadeOpacity, isOnlyMember);\n        }\n        this.root.scheduleUpdateProjection();\n        this.scheduleRender();\n        this.animationProgress = progress;\n      };\n      this.mixTargetDelta(this.options.layoutRoot ? 1000 : 0);\n    }\n    startAnimation(options) {\n      this.notifyListeners(\"animationStart\");\n      this.currentAnimation && this.currentAnimation.stop();\n      if (this.resumingFrom && this.resumingFrom.currentAnimation) {\n        this.resumingFrom.currentAnimation.stop();\n      }\n      if (this.pendingAnimation) {\n        cancelFrame(this.pendingAnimation);\n        this.pendingAnimation = undefined;\n      }\n      /**\n       * Start the animation in the next frame to have a frame with progress 0,\n       * where the target is the same as when the animation started, so we can\n       * calculate the relative positions correctly for instant transitions.\n       */\n      this.pendingAnimation = frame.update(() => {\n        globalProjectionState.hasAnimatedSinceResize = true;\n        this.currentAnimation = animateSingleValue(0, animationTarget, {\n          ...options,\n          onUpdate: latest => {\n            this.mixTargetDelta(latest);\n            options.onUpdate && options.onUpdate(latest);\n          },\n          onComplete: () => {\n            options.onComplete && options.onComplete();\n            this.completeAnimation();\n          }\n        });\n        if (this.resumingFrom) {\n          this.resumingFrom.currentAnimation = this.currentAnimation;\n        }\n        this.pendingAnimation = undefined;\n      });\n    }\n    completeAnimation() {\n      if (this.resumingFrom) {\n        this.resumingFrom.currentAnimation = undefined;\n        this.resumingFrom.preserveOpacity = undefined;\n      }\n      const stack = this.getStack();\n      stack && stack.exitAnimationComplete();\n      this.resumingFrom = this.currentAnimation = this.animationValues = undefined;\n      this.notifyListeners(\"animationComplete\");\n    }\n    finishAnimation() {\n      if (this.currentAnimation) {\n        this.mixTargetDelta && this.mixTargetDelta(animationTarget);\n        this.currentAnimation.stop();\n      }\n      this.completeAnimation();\n    }\n    applyTransformsToTarget() {\n      const lead = this.getLead();\n      let {\n        targetWithTransforms,\n        target,\n        layout,\n        latestValues\n      } = lead;\n      if (!targetWithTransforms || !target || !layout) return;\n      /**\n       * If we're only animating position, and this element isn't the lead element,\n       * then instead of projecting into the lead box we instead want to calculate\n       * a new target that aligns the two boxes but maintains the layout shape.\n       */\n      if (this !== lead && this.layout && layout && shouldAnimatePositionOnly(this.options.animationType, this.layout.layoutBox, layout.layoutBox)) {\n        target = this.target || createBox();\n        const xLength = calcLength(this.layout.layoutBox.x);\n        target.x.min = lead.target.x.min;\n        target.x.max = target.x.min + xLength;\n        const yLength = calcLength(this.layout.layoutBox.y);\n        target.y.min = lead.target.y.min;\n        target.y.max = target.y.min + yLength;\n      }\n      copyBoxInto(targetWithTransforms, target);\n      /**\n       * Apply the latest user-set transforms to the targetBox to produce the targetBoxFinal.\n       * This is the final box that we will then project into by calculating a transform delta and\n       * applying it to the corrected box.\n       */\n      transformBox(targetWithTransforms, latestValues);\n      /**\n       * Update the delta between the corrected box and the final target box, after\n       * user-set transforms are applied to it. This will be used by the renderer to\n       * create a transform style that will reproject the element from its layout layout\n       * into the desired bounding box.\n       */\n      calcBoxDelta(this.projectionDeltaWithTransform, this.layoutCorrected, targetWithTransforms, latestValues);\n    }\n    registerSharedNode(layoutId, node) {\n      if (!this.sharedNodes.has(layoutId)) {\n        this.sharedNodes.set(layoutId, new NodeStack());\n      }\n      const stack = this.sharedNodes.get(layoutId);\n      stack.add(node);\n      const config = node.options.initialPromotionConfig;\n      node.promote({\n        transition: config ? config.transition : undefined,\n        preserveFollowOpacity: config && config.shouldPreserveFollowOpacity ? config.shouldPreserveFollowOpacity(node) : undefined\n      });\n    }\n    isLead() {\n      const stack = this.getStack();\n      return stack ? stack.lead === this : true;\n    }\n    getLead() {\n      var _a;\n      const {\n        layoutId\n      } = this.options;\n      return layoutId ? ((_a = this.getStack()) === null || _a === void 0 ? void 0 : _a.lead) || this : this;\n    }\n    getPrevLead() {\n      var _a;\n      const {\n        layoutId\n      } = this.options;\n      return layoutId ? (_a = this.getStack()) === null || _a === void 0 ? void 0 : _a.prevLead : undefined;\n    }\n    getStack() {\n      const {\n        layoutId\n      } = this.options;\n      if (layoutId) return this.root.sharedNodes.get(layoutId);\n    }\n    promote({\n      needsReset,\n      transition,\n      preserveFollowOpacity\n    } = {}) {\n      const stack = this.getStack();\n      if (stack) stack.promote(this, preserveFollowOpacity);\n      if (needsReset) {\n        this.projectionDelta = undefined;\n        this.needsReset = true;\n      }\n      if (transition) this.setOptions({\n        transition\n      });\n    }\n    relegate() {\n      const stack = this.getStack();\n      if (stack) {\n        return stack.relegate(this);\n      } else {\n        return false;\n      }\n    }\n    resetRotation() {\n      const {\n        visualElement\n      } = this.options;\n      if (!visualElement) return;\n      // If there's no detected rotation values, we can early return without a forced render.\n      let hasRotate = false;\n      /**\n       * An unrolled check for rotation values. Most elements don't have any rotation and\n       * skipping the nested loop and new object creation is 50% faster.\n       */\n      const {\n        latestValues\n      } = visualElement;\n      if (latestValues.rotate || latestValues.rotateX || latestValues.rotateY || latestValues.rotateZ) {\n        hasRotate = true;\n      }\n      // If there's no rotation values, we don't need to do any more.\n      if (!hasRotate) return;\n      const resetValues = {};\n      // Check the rotate value of all axes and reset to 0\n      for (let i = 0; i < transformAxes.length; i++) {\n        const key = \"rotate\" + transformAxes[i];\n        // Record the rotation and then temporarily set it to 0\n        if (latestValues[key]) {\n          resetValues[key] = latestValues[key];\n          visualElement.setStaticValue(key, 0);\n        }\n      }\n      // Force a render of this element to apply the transform with all rotations\n      // set to 0.\n      visualElement.render();\n      // Put back all the values we reset\n      for (const key in resetValues) {\n        visualElement.setStaticValue(key, resetValues[key]);\n      }\n      // Schedule a render for the next frame. This ensures we won't visually\n      // see the element with the reset rotate value applied.\n      visualElement.scheduleRender();\n    }\n    getProjectionStyles(styleProp) {\n      var _a, _b;\n      if (!this.instance || this.isSVG) return undefined;\n      if (!this.isVisible) {\n        return hiddenVisibility;\n      }\n      const styles = {\n        visibility: \"\"\n      };\n      const transformTemplate = this.getTransformTemplate();\n      if (this.needsReset) {\n        this.needsReset = false;\n        styles.opacity = \"\";\n        styles.pointerEvents = resolveMotionValue(styleProp === null || styleProp === void 0 ? void 0 : styleProp.pointerEvents) || \"\";\n        styles.transform = transformTemplate ? transformTemplate(this.latestValues, \"\") : \"none\";\n        return styles;\n      }\n      const lead = this.getLead();\n      if (!this.projectionDelta || !this.layout || !lead.target) {\n        const emptyStyles = {};\n        if (this.options.layoutId) {\n          emptyStyles.opacity = this.latestValues.opacity !== undefined ? this.latestValues.opacity : 1;\n          emptyStyles.pointerEvents = resolveMotionValue(styleProp === null || styleProp === void 0 ? void 0 : styleProp.pointerEvents) || \"\";\n        }\n        if (this.hasProjected && !hasTransform(this.latestValues)) {\n          emptyStyles.transform = transformTemplate ? transformTemplate({}, \"\") : \"none\";\n          this.hasProjected = false;\n        }\n        return emptyStyles;\n      }\n      const valuesToRender = lead.animationValues || lead.latestValues;\n      this.applyTransformsToTarget();\n      styles.transform = buildProjectionTransform(this.projectionDeltaWithTransform, this.treeScale, valuesToRender);\n      if (transformTemplate) {\n        styles.transform = transformTemplate(valuesToRender, styles.transform);\n      }\n      const {\n        x,\n        y\n      } = this.projectionDelta;\n      styles.transformOrigin = `${x.origin * 100}% ${y.origin * 100}% 0`;\n      if (lead.animationValues) {\n        /**\n         * If the lead component is animating, assign this either the entering/leaving\n         * opacity\n         */\n        styles.opacity = lead === this ? (_b = (_a = valuesToRender.opacity) !== null && _a !== void 0 ? _a : this.latestValues.opacity) !== null && _b !== void 0 ? _b : 1 : this.preserveOpacity ? this.latestValues.opacity : valuesToRender.opacityExit;\n      } else {\n        /**\n         * Or we're not animating at all, set the lead component to its layout\n         * opacity and other components to hidden.\n         */\n        styles.opacity = lead === this ? valuesToRender.opacity !== undefined ? valuesToRender.opacity : \"\" : valuesToRender.opacityExit !== undefined ? valuesToRender.opacityExit : 0;\n      }\n      /**\n       * Apply scale correction\n       */\n      for (const key in scaleCorrectors) {\n        if (valuesToRender[key] === undefined) continue;\n        const {\n          correct,\n          applyTo\n        } = scaleCorrectors[key];\n        /**\n         * Only apply scale correction to the value if we have an\n         * active projection transform. Otherwise these values become\n         * vulnerable to distortion if the element changes size without\n         * a corresponding layout animation.\n         */\n        const corrected = styles.transform === \"none\" ? valuesToRender[key] : correct(valuesToRender[key], lead);\n        if (applyTo) {\n          const num = applyTo.length;\n          for (let i = 0; i < num; i++) {\n            styles[applyTo[i]] = corrected;\n          }\n        } else {\n          styles[key] = corrected;\n        }\n      }\n      /**\n       * Disable pointer events on follow components. This is to ensure\n       * that if a follow component covers a lead component it doesn't block\n       * pointer events on the lead.\n       */\n      if (this.options.layoutId) {\n        styles.pointerEvents = lead === this ? resolveMotionValue(styleProp === null || styleProp === void 0 ? void 0 : styleProp.pointerEvents) || \"\" : \"none\";\n      }\n      return styles;\n    }\n    clearSnapshot() {\n      this.resumeFrom = this.snapshot = undefined;\n    }\n    // Only run on root\n    resetTree() {\n      this.root.nodes.forEach(node => {\n        var _a;\n        return (_a = node.currentAnimation) === null || _a === void 0 ? void 0 : _a.stop();\n      });\n      this.root.nodes.forEach(clearMeasurements);\n      this.root.sharedNodes.clear();\n    }\n  };\n}\nfunction updateLayout(node) {\n  node.updateLayout();\n}\nfunction notifyLayoutUpdate(node) {\n  var _a;\n  const snapshot = ((_a = node.resumeFrom) === null || _a === void 0 ? void 0 : _a.snapshot) || node.snapshot;\n  if (node.isLead() && node.layout && snapshot && node.hasListeners(\"didUpdate\")) {\n    const {\n      layoutBox: layout,\n      measuredBox: measuredLayout\n    } = node.layout;\n    const {\n      animationType\n    } = node.options;\n    const isShared = snapshot.source !== node.layout.source;\n    // TODO Maybe we want to also resize the layout snapshot so we don't trigger\n    // animations for instance if layout=\"size\" and an element has only changed position\n    if (animationType === \"size\") {\n      eachAxis(axis => {\n        const axisSnapshot = isShared ? snapshot.measuredBox[axis] : snapshot.layoutBox[axis];\n        const length = calcLength(axisSnapshot);\n        axisSnapshot.min = layout[axis].min;\n        axisSnapshot.max = axisSnapshot.min + length;\n      });\n    } else if (shouldAnimatePositionOnly(animationType, snapshot.layoutBox, layout)) {\n      eachAxis(axis => {\n        const axisSnapshot = isShared ? snapshot.measuredBox[axis] : snapshot.layoutBox[axis];\n        const length = calcLength(layout[axis]);\n        axisSnapshot.max = axisSnapshot.min + length;\n        /**\n         * Ensure relative target gets resized and rerendererd\n         */\n        if (node.relativeTarget && !node.currentAnimation) {\n          node.isProjectionDirty = true;\n          node.relativeTarget[axis].max = node.relativeTarget[axis].min + length;\n        }\n      });\n    }\n    const layoutDelta = createDelta();\n    calcBoxDelta(layoutDelta, layout, snapshot.layoutBox);\n    const visualDelta = createDelta();\n    if (isShared) {\n      calcBoxDelta(visualDelta, node.applyTransform(measuredLayout, true), snapshot.measuredBox);\n    } else {\n      calcBoxDelta(visualDelta, layout, snapshot.layoutBox);\n    }\n    const hasLayoutChanged = !isDeltaZero(layoutDelta);\n    let hasRelativeTargetChanged = false;\n    if (!node.resumeFrom) {\n      const relativeParent = node.getClosestProjectingParent();\n      /**\n       * If the relativeParent is itself resuming from a different element then\n       * the relative snapshot is not relavent\n       */\n      if (relativeParent && !relativeParent.resumeFrom) {\n        const {\n          snapshot: parentSnapshot,\n          layout: parentLayout\n        } = relativeParent;\n        if (parentSnapshot && parentLayout) {\n          const relativeSnapshot = createBox();\n          calcRelativePosition(relativeSnapshot, snapshot.layoutBox, parentSnapshot.layoutBox);\n          const relativeLayout = createBox();\n          calcRelativePosition(relativeLayout, layout, parentLayout.layoutBox);\n          if (!boxEqualsRounded(relativeSnapshot, relativeLayout)) {\n            hasRelativeTargetChanged = true;\n          }\n          if (relativeParent.options.layoutRoot) {\n            node.relativeTarget = relativeLayout;\n            node.relativeTargetOrigin = relativeSnapshot;\n            node.relativeParent = relativeParent;\n          }\n        }\n      }\n    }\n    node.notifyListeners(\"didUpdate\", {\n      layout,\n      snapshot,\n      delta: visualDelta,\n      layoutDelta,\n      hasLayoutChanged,\n      hasRelativeTargetChanged\n    });\n  } else if (node.isLead()) {\n    const {\n      onExitComplete\n    } = node.options;\n    onExitComplete && onExitComplete();\n  }\n  /**\n   * Clearing transition\n   * TODO: Investigate why this transition is being passed in as {type: false } from Framer\n   * and why we need it at all\n   */\n  node.options.transition = undefined;\n}\nfunction propagateDirtyNodes(node) {\n  /**\n   * Increase debug counter for nodes encountered this frame\n   */\n  projectionFrameData.totalNodes++;\n  if (!node.parent) return;\n  /**\n   * If this node isn't projecting, propagate isProjectionDirty. It will have\n   * no performance impact but it will allow the next child that *is* projecting\n   * but *isn't* dirty to just check its parent to see if *any* ancestor needs\n   * correcting.\n   */\n  if (!node.isProjecting()) {\n    node.isProjectionDirty = node.parent.isProjectionDirty;\n  }\n  /**\n   * Propagate isSharedProjectionDirty and isTransformDirty\n   * throughout the whole tree. A future revision can take another look at\n   * this but for safety we still recalcualte shared nodes.\n   */\n  node.isSharedProjectionDirty || (node.isSharedProjectionDirty = Boolean(node.isProjectionDirty || node.parent.isProjectionDirty || node.parent.isSharedProjectionDirty));\n  node.isTransformDirty || (node.isTransformDirty = node.parent.isTransformDirty);\n}\nfunction cleanDirtyNodes(node) {\n  node.isProjectionDirty = node.isSharedProjectionDirty = node.isTransformDirty = false;\n}\nfunction clearSnapshot(node) {\n  node.clearSnapshot();\n}\nfunction clearMeasurements(node) {\n  node.clearMeasurements();\n}\nfunction clearIsLayoutDirty(node) {\n  node.isLayoutDirty = false;\n}\nfunction resetTransformStyle(node) {\n  const {\n    visualElement\n  } = node.options;\n  if (visualElement && visualElement.getProps().onBeforeLayoutMeasure) {\n    visualElement.notify(\"BeforeLayoutMeasure\");\n  }\n  node.resetTransform();\n}\nfunction finishAnimation(node) {\n  node.finishAnimation();\n  node.targetDelta = node.relativeTarget = node.target = undefined;\n  node.isProjectionDirty = true;\n}\nfunction resolveTargetDelta(node) {\n  node.resolveTargetDelta();\n}\nfunction calcProjection(node) {\n  node.calcProjection();\n}\nfunction resetRotation(node) {\n  node.resetRotation();\n}\nfunction removeLeadSnapshots(stack) {\n  stack.removeLeadSnapshot();\n}\nfunction mixAxisDelta(output, delta, p) {\n  output.translate = mix(delta.translate, 0, p);\n  output.scale = mix(delta.scale, 1, p);\n  output.origin = delta.origin;\n  output.originPoint = delta.originPoint;\n}\nfunction mixAxis(output, from, to, p) {\n  output.min = mix(from.min, to.min, p);\n  output.max = mix(from.max, to.max, p);\n}\nfunction mixBox(output, from, to, p) {\n  mixAxis(output.x, from.x, to.x, p);\n  mixAxis(output.y, from.y, to.y, p);\n}\nfunction hasOpacityCrossfade(node) {\n  return node.animationValues && node.animationValues.opacityExit !== undefined;\n}\nconst defaultLayoutTransition = {\n  duration: 0.45,\n  ease: [0.4, 0, 0.1, 1]\n};\nconst userAgentContains = string => typeof navigator !== \"undefined\" && navigator.userAgent.toLowerCase().includes(string);\n/**\n * Measured bounding boxes must be rounded in Safari and\n * left untouched in Chrome, otherwise non-integer layouts within scaled-up elements\n * can appear to jump.\n */\nconst roundPoint = userAgentContains(\"applewebkit/\") && !userAgentContains(\"chrome/\") ? Math.round : noop;\nfunction roundAxis(axis) {\n  // Round to the nearest .5 pixels to support subpixel layouts\n  axis.min = roundPoint(axis.min);\n  axis.max = roundPoint(axis.max);\n}\nfunction roundBox(box) {\n  roundAxis(box.x);\n  roundAxis(box.y);\n}\nfunction shouldAnimatePositionOnly(animationType, snapshot, layout) {\n  return animationType === \"position\" || animationType === \"preserve-aspect\" && !isNear(aspectRatio(snapshot), aspectRatio(layout), 0.2);\n}\nexport { cleanDirtyNodes, createProjectionNode, mixAxis, mixAxisDelta, mixBox, propagateDirtyNodes };", "map": {"version": 3, "names": ["SubscriptionManager", "mixValues", "copyBoxInto", "translateAxis", "transformBox", "applyBoxDelta", "applyTreeDeltas", "calcRelativePosition", "calcRelativeBox", "calcBoxDelta", "calcLength", "isNear", "removeBoxTransforms", "createBox", "create<PERSON><PERSON><PERSON>", "getValueTransition", "boxEqualsRounded", "isDeltaZero", "aspectRatio", "boxEquals", "NodeStack", "scaleCorrectors", "buildProjectionTransform", "eachAxis", "hasTransform", "hasScale", "has2DTranslate", "FlatTree", "resolveMotionValue", "globalProjectionState", "delay", "mix", "record", "isSVGElement", "animateSingleValue", "clamp", "cancelFrame", "frameData", "steps", "frame", "noop", "transformAxes", "hiddenVisibility", "visibility", "animationTarget", "id", "projectionFrameData", "type", "totalNodes", "resolvedTargetDeltas", "recalculatedProjection", "createProjectionNode", "attachResizeListener", "defaultParent", "measureScroll", "checkIsScrollRoot", "resetTransform", "ProjectionNode", "constructor", "latestValues", "parent", "animationId", "children", "Set", "options", "isTreeAnimating", "isAnimationBlocked", "isLayoutDirty", "isProjectionDirty", "isSharedProjectionDirty", "isTransformDirty", "updateManuallyBlocked", "updateBlockedByResize", "isUpdating", "isSVG", "needsReset", "shouldResetTransform", "treeScale", "x", "y", "eventHandlers", "Map", "hasTreeAnimated", "updateScheduled", "projectionUpdateScheduled", "checkUpdateFailed", "clearAllSnapshots", "updateProjection", "nodes", "for<PERSON>ach", "propagateDirtyNodes", "resolveTargetDel<PERSON>", "calcProjection", "cleanDirtyNodes", "hasProjected", "isVisible", "animationProgress", "sharedNodes", "root", "path", "depth", "i", "length", "addEventListener", "name", "handler", "has", "set", "get", "add", "notifyListeners", "args", "subscriptionManager", "notify", "hasListeners", "mount", "instance", "layoutId", "layout", "visualElement", "current", "cancelDelay", "resizeUnblockUpdate", "hasAnimatedSinceResize", "finishAnimation", "registerSharedNode", "animate", "delta", "hasLayoutChanged", "hasRelativeTargetChanged", "newLayout", "isTreeAnimationBlocked", "target", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "layoutTransition", "transition", "getDefaultTransition", "defaultLayoutTransition", "onLayoutAnimationStart", "onLayoutAnimationComplete", "getProps", "targetChanged", "targetLayout", "hasOnlyRelativeTargetChanged", "layoutRoot", "resumeFrom", "currentAnimation", "resumingFrom", "setAnimationOrigin", "animationOptions", "onPlay", "onComplete", "shouldReduceMotion", "startAnimation", "isLead", "onExitComplete", "unmount", "willUpdate", "remove", "stack", "getStack", "delete", "blockUpdate", "unblockUpdate", "isUpdateBlocked", "startUpdate", "resetRotation", "getTransformTemplate", "transformTemplate", "shouldNotifyListeners", "node", "updateScroll", "prevTransformTemplateValue", "updateSnapshot", "update", "updateWasBlocked", "clearMeasurements", "clearIsLayoutDirty", "resetTransformStyle", "updateLayout", "notifyLayoutUpdate", "now", "performance", "timestamp", "isProcessing", "process", "preRender", "render", "didUpdate", "queueMicrotask", "clearSnapshot", "removeLeadSnapshots", "scheduleUpdateProjection", "scheduleCheckAfterUnmount", "postRender", "snapshot", "measure", "alwaysMeasureLayout", "prevLayout", "layoutCorrected", "projectionDel<PERSON>", "layoutBox", "phase", "needsMeasurement", "Boolean", "layoutScroll", "scroll", "isRoot", "offset", "isResetRequested", "hasProjection", "transformTemplateValue", "transformTemplateHasChanged", "scheduleRender", "removeTransform", "pageBox", "measurePageBox", "removeElementScroll", "roundBox", "measuredBox", "source", "box", "measureViewportBox", "boxWithoutScroll", "rootScroll", "applyTransform", "transformOnly", "withTransforms", "boxWithoutTransform", "sourceBox", "nodeBox", "set<PERSON>argetD<PERSON><PERSON>", "targetDel<PERSON>", "setOptions", "crossfade", "forceRelativeParentToResolveTarget", "relativeParent", "resolvedRelativeTargetAt", "forceRecalculation", "_a", "lead", "getLead", "isShared", "canSkip", "attemptToResolveRelativeTarget", "getClosestProjectingParent", "relativeTarget<PERSON><PERSON>in", "targetWithTransforms", "isProjecting", "pendingAnimation", "prevTreeScaleX", "prevTreeScaleY", "projectionTransform", "projectionDeltaWithTransform", "prevProjectionTransform", "hide", "show", "notifyAll", "snapshotLatestValues", "mixedValues", "relativeLayout", "snapshotSource", "layoutSource", "isSharedLayoutAnimation", "isOnlyMember", "members", "shouldCrossfadeOpacity", "some", "hasOpacityCrossfade", "prevRelativeTarget", "mixTargetDelta", "latest", "progress", "mixAxisDelta", "mixBox", "animationValues", "stop", "onUpdate", "completeAnimation", "preserveOpacity", "exitAnimationComplete", "applyTransformsToTarget", "shouldAnimatePositionOnly", "animationType", "xLength", "min", "max", "y<PERSON><PERSON><PERSON>", "config", "initialPromotionConfig", "promote", "preserveFollowOpacity", "shouldPreserveFollowOpacity", "getPrevLead", "prevLead", "relegate", "hasRotate", "rotate", "rotateX", "rotateY", "rotateZ", "resetValues", "key", "setStaticValue", "getProjectionStyles", "styleProp", "_b", "styles", "opacity", "pointerEvents", "transform", "emptyStyles", "valuesToRender", "transform<PERSON><PERSON>in", "origin", "opacityExit", "correct", "applyTo", "corrected", "num", "resetTree", "clear", "measuredLayout", "axis", "axisSnapshot", "<PERSON><PERSON><PERSON><PERSON>", "visualD<PERSON><PERSON>", "parentSnapshot", "parentLayout", "relativeSnapshot", "onBeforeLayoutMeasure", "removeLeadSnapshot", "output", "p", "translate", "scale", "originPoint", "mixAxis", "from", "to", "duration", "ease", "userAgentContains", "string", "navigator", "userAgent", "toLowerCase", "includes", "roundPoint", "Math", "round", "roundAxis"], "sources": ["D:/menasa/frontend/node_modules/framer-motion/dist/es/projection/node/create-projection-node.mjs"], "sourcesContent": ["import { SubscriptionManager } from '../../utils/subscription-manager.mjs';\nimport { mixValues } from '../animation/mix-values.mjs';\nimport { copyBoxInto } from '../geometry/copy.mjs';\nimport { translateAxis, transformBox, applyBoxDelta, applyTreeDeltas } from '../geometry/delta-apply.mjs';\nimport { calcRelativePosition, calcRelativeBox, calcBoxDelta, calcLength, isNear } from '../geometry/delta-calc.mjs';\nimport { removeBoxTransforms } from '../geometry/delta-remove.mjs';\nimport { createBox, createDelta } from '../geometry/models.mjs';\nimport { getValueTransition } from '../../animation/utils/transitions.mjs';\nimport { boxEqualsRounded, isDeltaZero, aspectRatio, boxEquals } from '../geometry/utils.mjs';\nimport { NodeStack } from '../shared/stack.mjs';\nimport { scaleCorrectors } from '../styles/scale-correction.mjs';\nimport { buildProjectionTransform } from '../styles/transform.mjs';\nimport { eachAxis } from '../utils/each-axis.mjs';\nimport { hasTransform, hasScale, has2DTranslate } from '../utils/has-transform.mjs';\nimport { FlatTree } from '../../render/utils/flat-tree.mjs';\nimport { resolveMotionValue } from '../../value/utils/resolve-motion-value.mjs';\nimport { globalProjectionState } from './state.mjs';\nimport { delay } from '../../utils/delay.mjs';\nimport { mix } from '../../utils/mix.mjs';\nimport { record } from '../../debug/record.mjs';\nimport { isSVGElement } from '../../render/dom/utils/is-svg-element.mjs';\nimport { animateSingleValue } from '../../animation/interfaces/single-value.mjs';\nimport { clamp } from '../../utils/clamp.mjs';\nimport { cancelFrame, frameData, steps, frame } from '../../frameloop/frame.mjs';\nimport { noop } from '../../utils/noop.mjs';\n\nconst transformAxes = [\"\", \"X\", \"Y\", \"Z\"];\nconst hiddenVisibility = { visibility: \"hidden\" };\n/**\n * We use 1000 as the animation target as 0-1000 maps better to pixels than 0-1\n * which has a noticeable difference in spring animations\n */\nconst animationTarget = 1000;\nlet id = 0;\n/**\n * Use a mutable data object for debug data so as to not create a new\n * object every frame.\n */\nconst projectionFrameData = {\n    type: \"projectionFrame\",\n    totalNodes: 0,\n    resolvedTargetDeltas: 0,\n    recalculatedProjection: 0,\n};\nfunction createProjectionNode({ attachResizeListener, defaultParent, measureScroll, checkIsScrollRoot, resetTransform, }) {\n    return class ProjectionNode {\n        constructor(latestValues = {}, parent = defaultParent === null || defaultParent === void 0 ? void 0 : defaultParent()) {\n            /**\n             * A unique ID generated for every projection node.\n             */\n            this.id = id++;\n            /**\n             * An id that represents a unique session instigated by startUpdate.\n             */\n            this.animationId = 0;\n            /**\n             * A Set containing all this component's children. This is used to iterate\n             * through the children.\n             *\n             * TODO: This could be faster to iterate as a flat array stored on the root node.\n             */\n            this.children = new Set();\n            /**\n             * Options for the node. We use this to configure what kind of layout animations\n             * we should perform (if any).\n             */\n            this.options = {};\n            /**\n             * We use this to detect when its safe to shut down part of a projection tree.\n             * We have to keep projecting children for scale correction and relative projection\n             * until all their parents stop performing layout animations.\n             */\n            this.isTreeAnimating = false;\n            this.isAnimationBlocked = false;\n            /**\n             * Flag to true if we think this layout has been changed. We can't always know this,\n             * currently we set it to true every time a component renders, or if it has a layoutDependency\n             * if that has changed between renders. Additionally, components can be grouped by LayoutGroup\n             * and if one node is dirtied, they all are.\n             */\n            this.isLayoutDirty = false;\n            /**\n             * Flag to true if we think the projection calculations for this node needs\n             * recalculating as a result of an updated transform or layout animation.\n             */\n            this.isProjectionDirty = false;\n            /**\n             * Flag to true if the layout *or* transform has changed. This then gets propagated\n             * throughout the projection tree, forcing any element below to recalculate on the next frame.\n             */\n            this.isSharedProjectionDirty = false;\n            /**\n             * Flag transform dirty. This gets propagated throughout the whole tree but is only\n             * respected by shared nodes.\n             */\n            this.isTransformDirty = false;\n            /**\n             * Block layout updates for instant layout transitions throughout the tree.\n             */\n            this.updateManuallyBlocked = false;\n            this.updateBlockedByResize = false;\n            /**\n             * Set to true between the start of the first `willUpdate` call and the end of the `didUpdate`\n             * call.\n             */\n            this.isUpdating = false;\n            /**\n             * If this is an SVG element we currently disable projection transforms\n             */\n            this.isSVG = false;\n            /**\n             * Flag to true (during promotion) if a node doing an instant layout transition needs to reset\n             * its projection styles.\n             */\n            this.needsReset = false;\n            /**\n             * Flags whether this node should have its transform reset prior to measuring.\n             */\n            this.shouldResetTransform = false;\n            /**\n             * An object representing the calculated contextual/accumulated/tree scale.\n             * This will be used to scale calculcated projection transforms, as these are\n             * calculated in screen-space but need to be scaled for elements to layoutly\n             * make it to their calculated destinations.\n             *\n             * TODO: Lazy-init\n             */\n            this.treeScale = { x: 1, y: 1 };\n            /**\n             *\n             */\n            this.eventHandlers = new Map();\n            this.hasTreeAnimated = false;\n            // Note: Currently only running on root node\n            this.updateScheduled = false;\n            this.projectionUpdateScheduled = false;\n            this.checkUpdateFailed = () => {\n                if (this.isUpdating) {\n                    this.isUpdating = false;\n                    this.clearAllSnapshots();\n                }\n            };\n            /**\n             * This is a multi-step process as shared nodes might be of different depths. Nodes\n             * are sorted by depth order, so we need to resolve the entire tree before moving to\n             * the next step.\n             */\n            this.updateProjection = () => {\n                this.projectionUpdateScheduled = false;\n                /**\n                 * Reset debug counts. Manually resetting rather than creating a new\n                 * object each frame.\n                 */\n                projectionFrameData.totalNodes =\n                    projectionFrameData.resolvedTargetDeltas =\n                        projectionFrameData.recalculatedProjection =\n                            0;\n                this.nodes.forEach(propagateDirtyNodes);\n                this.nodes.forEach(resolveTargetDelta);\n                this.nodes.forEach(calcProjection);\n                this.nodes.forEach(cleanDirtyNodes);\n                record(projectionFrameData);\n            };\n            this.hasProjected = false;\n            this.isVisible = true;\n            this.animationProgress = 0;\n            /**\n             * Shared layout\n             */\n            // TODO Only running on root node\n            this.sharedNodes = new Map();\n            this.latestValues = latestValues;\n            this.root = parent ? parent.root || parent : this;\n            this.path = parent ? [...parent.path, parent] : [];\n            this.parent = parent;\n            this.depth = parent ? parent.depth + 1 : 0;\n            for (let i = 0; i < this.path.length; i++) {\n                this.path[i].shouldResetTransform = true;\n            }\n            if (this.root === this)\n                this.nodes = new FlatTree();\n        }\n        addEventListener(name, handler) {\n            if (!this.eventHandlers.has(name)) {\n                this.eventHandlers.set(name, new SubscriptionManager());\n            }\n            return this.eventHandlers.get(name).add(handler);\n        }\n        notifyListeners(name, ...args) {\n            const subscriptionManager = this.eventHandlers.get(name);\n            subscriptionManager && subscriptionManager.notify(...args);\n        }\n        hasListeners(name) {\n            return this.eventHandlers.has(name);\n        }\n        /**\n         * Lifecycles\n         */\n        mount(instance, isLayoutDirty = this.root.hasTreeAnimated) {\n            if (this.instance)\n                return;\n            this.isSVG = isSVGElement(instance);\n            this.instance = instance;\n            const { layoutId, layout, visualElement } = this.options;\n            if (visualElement && !visualElement.current) {\n                visualElement.mount(instance);\n            }\n            this.root.nodes.add(this);\n            this.parent && this.parent.children.add(this);\n            if (isLayoutDirty && (layout || layoutId)) {\n                this.isLayoutDirty = true;\n            }\n            if (attachResizeListener) {\n                let cancelDelay;\n                const resizeUnblockUpdate = () => (this.root.updateBlockedByResize = false);\n                attachResizeListener(instance, () => {\n                    this.root.updateBlockedByResize = true;\n                    cancelDelay && cancelDelay();\n                    cancelDelay = delay(resizeUnblockUpdate, 250);\n                    if (globalProjectionState.hasAnimatedSinceResize) {\n                        globalProjectionState.hasAnimatedSinceResize = false;\n                        this.nodes.forEach(finishAnimation);\n                    }\n                });\n            }\n            if (layoutId) {\n                this.root.registerSharedNode(layoutId, this);\n            }\n            // Only register the handler if it requires layout animation\n            if (this.options.animate !== false &&\n                visualElement &&\n                (layoutId || layout)) {\n                this.addEventListener(\"didUpdate\", ({ delta, hasLayoutChanged, hasRelativeTargetChanged, layout: newLayout, }) => {\n                    if (this.isTreeAnimationBlocked()) {\n                        this.target = undefined;\n                        this.relativeTarget = undefined;\n                        return;\n                    }\n                    // TODO: Check here if an animation exists\n                    const layoutTransition = this.options.transition ||\n                        visualElement.getDefaultTransition() ||\n                        defaultLayoutTransition;\n                    const { onLayoutAnimationStart, onLayoutAnimationComplete, } = visualElement.getProps();\n                    /**\n                     * The target layout of the element might stay the same,\n                     * but its position relative to its parent has changed.\n                     */\n                    const targetChanged = !this.targetLayout ||\n                        !boxEqualsRounded(this.targetLayout, newLayout) ||\n                        hasRelativeTargetChanged;\n                    /**\n                     * If the layout hasn't seemed to have changed, it might be that the\n                     * element is visually in the same place in the document but its position\n                     * relative to its parent has indeed changed. So here we check for that.\n                     */\n                    const hasOnlyRelativeTargetChanged = !hasLayoutChanged && hasRelativeTargetChanged;\n                    if (this.options.layoutRoot ||\n                        (this.resumeFrom && this.resumeFrom.instance) ||\n                        hasOnlyRelativeTargetChanged ||\n                        (hasLayoutChanged &&\n                            (targetChanged || !this.currentAnimation))) {\n                        if (this.resumeFrom) {\n                            this.resumingFrom = this.resumeFrom;\n                            this.resumingFrom.resumingFrom = undefined;\n                        }\n                        this.setAnimationOrigin(delta, hasOnlyRelativeTargetChanged);\n                        const animationOptions = {\n                            ...getValueTransition(layoutTransition, \"layout\"),\n                            onPlay: onLayoutAnimationStart,\n                            onComplete: onLayoutAnimationComplete,\n                        };\n                        if (visualElement.shouldReduceMotion ||\n                            this.options.layoutRoot) {\n                            animationOptions.delay = 0;\n                            animationOptions.type = false;\n                        }\n                        this.startAnimation(animationOptions);\n                    }\n                    else {\n                        /**\n                         * If the layout hasn't changed and we have an animation that hasn't started yet,\n                         * finish it immediately. Otherwise it will be animating from a location\n                         * that was probably never commited to screen and look like a jumpy box.\n                         */\n                        if (!hasLayoutChanged) {\n                            finishAnimation(this);\n                        }\n                        if (this.isLead() && this.options.onExitComplete) {\n                            this.options.onExitComplete();\n                        }\n                    }\n                    this.targetLayout = newLayout;\n                });\n            }\n        }\n        unmount() {\n            this.options.layoutId && this.willUpdate();\n            this.root.nodes.remove(this);\n            const stack = this.getStack();\n            stack && stack.remove(this);\n            this.parent && this.parent.children.delete(this);\n            this.instance = undefined;\n            cancelFrame(this.updateProjection);\n        }\n        // only on the root\n        blockUpdate() {\n            this.updateManuallyBlocked = true;\n        }\n        unblockUpdate() {\n            this.updateManuallyBlocked = false;\n        }\n        isUpdateBlocked() {\n            return this.updateManuallyBlocked || this.updateBlockedByResize;\n        }\n        isTreeAnimationBlocked() {\n            return (this.isAnimationBlocked ||\n                (this.parent && this.parent.isTreeAnimationBlocked()) ||\n                false);\n        }\n        // Note: currently only running on root node\n        startUpdate() {\n            if (this.isUpdateBlocked())\n                return;\n            this.isUpdating = true;\n            this.nodes && this.nodes.forEach(resetRotation);\n            this.animationId++;\n        }\n        getTransformTemplate() {\n            const { visualElement } = this.options;\n            return visualElement && visualElement.getProps().transformTemplate;\n        }\n        willUpdate(shouldNotifyListeners = true) {\n            this.root.hasTreeAnimated = true;\n            if (this.root.isUpdateBlocked()) {\n                this.options.onExitComplete && this.options.onExitComplete();\n                return;\n            }\n            !this.root.isUpdating && this.root.startUpdate();\n            if (this.isLayoutDirty)\n                return;\n            this.isLayoutDirty = true;\n            for (let i = 0; i < this.path.length; i++) {\n                const node = this.path[i];\n                node.shouldResetTransform = true;\n                node.updateScroll(\"snapshot\");\n                if (node.options.layoutRoot) {\n                    node.willUpdate(false);\n                }\n            }\n            const { layoutId, layout } = this.options;\n            if (layoutId === undefined && !layout)\n                return;\n            const transformTemplate = this.getTransformTemplate();\n            this.prevTransformTemplateValue = transformTemplate\n                ? transformTemplate(this.latestValues, \"\")\n                : undefined;\n            this.updateSnapshot();\n            shouldNotifyListeners && this.notifyListeners(\"willUpdate\");\n        }\n        update() {\n            this.updateScheduled = false;\n            const updateWasBlocked = this.isUpdateBlocked();\n            // When doing an instant transition, we skip the layout update,\n            // but should still clean up the measurements so that the next\n            // snapshot could be taken correctly.\n            if (updateWasBlocked) {\n                this.unblockUpdate();\n                this.clearAllSnapshots();\n                this.nodes.forEach(clearMeasurements);\n                return;\n            }\n            if (!this.isUpdating) {\n                this.nodes.forEach(clearIsLayoutDirty);\n            }\n            this.isUpdating = false;\n            /**\n             * Write\n             */\n            this.nodes.forEach(resetTransformStyle);\n            /**\n             * Read ==================\n             */\n            // Update layout measurements of updated children\n            this.nodes.forEach(updateLayout);\n            /**\n             * Write\n             */\n            // Notify listeners that the layout is updated\n            this.nodes.forEach(notifyLayoutUpdate);\n            this.clearAllSnapshots();\n            /**\n             * Manually flush any pending updates. Ideally\n             * we could leave this to the following requestAnimationFrame but this seems\n             * to leave a flash of incorrectly styled content.\n             */\n            const now = performance.now();\n            frameData.delta = clamp(0, 1000 / 60, now - frameData.timestamp);\n            frameData.timestamp = now;\n            frameData.isProcessing = true;\n            steps.update.process(frameData);\n            steps.preRender.process(frameData);\n            steps.render.process(frameData);\n            frameData.isProcessing = false;\n        }\n        didUpdate() {\n            if (!this.updateScheduled) {\n                this.updateScheduled = true;\n                queueMicrotask(() => this.update());\n            }\n        }\n        clearAllSnapshots() {\n            this.nodes.forEach(clearSnapshot);\n            this.sharedNodes.forEach(removeLeadSnapshots);\n        }\n        scheduleUpdateProjection() {\n            if (!this.projectionUpdateScheduled) {\n                this.projectionUpdateScheduled = true;\n                frame.preRender(this.updateProjection, false, true);\n            }\n        }\n        scheduleCheckAfterUnmount() {\n            /**\n             * If the unmounting node is in a layoutGroup and did trigger a willUpdate,\n             * we manually call didUpdate to give a chance to the siblings to animate.\n             * Otherwise, cleanup all snapshots to prevents future nodes from reusing them.\n             */\n            frame.postRender(() => {\n                if (this.isLayoutDirty) {\n                    this.root.didUpdate();\n                }\n                else {\n                    this.root.checkUpdateFailed();\n                }\n            });\n        }\n        /**\n         * Update measurements\n         */\n        updateSnapshot() {\n            if (this.snapshot || !this.instance)\n                return;\n            this.snapshot = this.measure();\n        }\n        updateLayout() {\n            if (!this.instance)\n                return;\n            // TODO: Incorporate into a forwarded scroll offset\n            this.updateScroll();\n            if (!(this.options.alwaysMeasureLayout && this.isLead()) &&\n                !this.isLayoutDirty) {\n                return;\n            }\n            /**\n             * When a node is mounted, it simply resumes from the prevLead's\n             * snapshot instead of taking a new one, but the ancestors scroll\n             * might have updated while the prevLead is unmounted. We need to\n             * update the scroll again to make sure the layout we measure is\n             * up to date.\n             */\n            if (this.resumeFrom && !this.resumeFrom.instance) {\n                for (let i = 0; i < this.path.length; i++) {\n                    const node = this.path[i];\n                    node.updateScroll();\n                }\n            }\n            const prevLayout = this.layout;\n            this.layout = this.measure(false);\n            this.layoutCorrected = createBox();\n            this.isLayoutDirty = false;\n            this.projectionDelta = undefined;\n            this.notifyListeners(\"measure\", this.layout.layoutBox);\n            const { visualElement } = this.options;\n            visualElement &&\n                visualElement.notify(\"LayoutMeasure\", this.layout.layoutBox, prevLayout ? prevLayout.layoutBox : undefined);\n        }\n        updateScroll(phase = \"measure\") {\n            let needsMeasurement = Boolean(this.options.layoutScroll && this.instance);\n            if (this.scroll &&\n                this.scroll.animationId === this.root.animationId &&\n                this.scroll.phase === phase) {\n                needsMeasurement = false;\n            }\n            if (needsMeasurement) {\n                this.scroll = {\n                    animationId: this.root.animationId,\n                    phase,\n                    isRoot: checkIsScrollRoot(this.instance),\n                    offset: measureScroll(this.instance),\n                };\n            }\n        }\n        resetTransform() {\n            if (!resetTransform)\n                return;\n            const isResetRequested = this.isLayoutDirty || this.shouldResetTransform;\n            const hasProjection = this.projectionDelta && !isDeltaZero(this.projectionDelta);\n            const transformTemplate = this.getTransformTemplate();\n            const transformTemplateValue = transformTemplate\n                ? transformTemplate(this.latestValues, \"\")\n                : undefined;\n            const transformTemplateHasChanged = transformTemplateValue !== this.prevTransformTemplateValue;\n            if (isResetRequested &&\n                (hasProjection ||\n                    hasTransform(this.latestValues) ||\n                    transformTemplateHasChanged)) {\n                resetTransform(this.instance, transformTemplateValue);\n                this.shouldResetTransform = false;\n                this.scheduleRender();\n            }\n        }\n        measure(removeTransform = true) {\n            const pageBox = this.measurePageBox();\n            let layoutBox = this.removeElementScroll(pageBox);\n            /**\n             * Measurements taken during the pre-render stage\n             * still have transforms applied so we remove them\n             * via calculation.\n             */\n            if (removeTransform) {\n                layoutBox = this.removeTransform(layoutBox);\n            }\n            roundBox(layoutBox);\n            return {\n                animationId: this.root.animationId,\n                measuredBox: pageBox,\n                layoutBox,\n                latestValues: {},\n                source: this.id,\n            };\n        }\n        measurePageBox() {\n            const { visualElement } = this.options;\n            if (!visualElement)\n                return createBox();\n            const box = visualElement.measureViewportBox();\n            // Remove viewport scroll to give page-relative coordinates\n            const { scroll } = this.root;\n            if (scroll) {\n                translateAxis(box.x, scroll.offset.x);\n                translateAxis(box.y, scroll.offset.y);\n            }\n            return box;\n        }\n        removeElementScroll(box) {\n            const boxWithoutScroll = createBox();\n            copyBoxInto(boxWithoutScroll, box);\n            /**\n             * Performance TODO: Keep a cumulative scroll offset down the tree\n             * rather than loop back up the path.\n             */\n            for (let i = 0; i < this.path.length; i++) {\n                const node = this.path[i];\n                const { scroll, options } = node;\n                if (node !== this.root && scroll && options.layoutScroll) {\n                    /**\n                     * If this is a new scroll root, we want to remove all previous scrolls\n                     * from the viewport box.\n                     */\n                    if (scroll.isRoot) {\n                        copyBoxInto(boxWithoutScroll, box);\n                        const { scroll: rootScroll } = this.root;\n                        /**\n                         * Undo the application of page scroll that was originally added\n                         * to the measured bounding box.\n                         */\n                        if (rootScroll) {\n                            translateAxis(boxWithoutScroll.x, -rootScroll.offset.x);\n                            translateAxis(boxWithoutScroll.y, -rootScroll.offset.y);\n                        }\n                    }\n                    translateAxis(boxWithoutScroll.x, scroll.offset.x);\n                    translateAxis(boxWithoutScroll.y, scroll.offset.y);\n                }\n            }\n            return boxWithoutScroll;\n        }\n        applyTransform(box, transformOnly = false) {\n            const withTransforms = createBox();\n            copyBoxInto(withTransforms, box);\n            for (let i = 0; i < this.path.length; i++) {\n                const node = this.path[i];\n                if (!transformOnly &&\n                    node.options.layoutScroll &&\n                    node.scroll &&\n                    node !== node.root) {\n                    transformBox(withTransforms, {\n                        x: -node.scroll.offset.x,\n                        y: -node.scroll.offset.y,\n                    });\n                }\n                if (!hasTransform(node.latestValues))\n                    continue;\n                transformBox(withTransforms, node.latestValues);\n            }\n            if (hasTransform(this.latestValues)) {\n                transformBox(withTransforms, this.latestValues);\n            }\n            return withTransforms;\n        }\n        removeTransform(box) {\n            const boxWithoutTransform = createBox();\n            copyBoxInto(boxWithoutTransform, box);\n            for (let i = 0; i < this.path.length; i++) {\n                const node = this.path[i];\n                if (!node.instance)\n                    continue;\n                if (!hasTransform(node.latestValues))\n                    continue;\n                hasScale(node.latestValues) && node.updateSnapshot();\n                const sourceBox = createBox();\n                const nodeBox = node.measurePageBox();\n                copyBoxInto(sourceBox, nodeBox);\n                removeBoxTransforms(boxWithoutTransform, node.latestValues, node.snapshot ? node.snapshot.layoutBox : undefined, sourceBox);\n            }\n            if (hasTransform(this.latestValues)) {\n                removeBoxTransforms(boxWithoutTransform, this.latestValues);\n            }\n            return boxWithoutTransform;\n        }\n        setTargetDelta(delta) {\n            this.targetDelta = delta;\n            this.root.scheduleUpdateProjection();\n            this.isProjectionDirty = true;\n        }\n        setOptions(options) {\n            this.options = {\n                ...this.options,\n                ...options,\n                crossfade: options.crossfade !== undefined ? options.crossfade : true,\n            };\n        }\n        clearMeasurements() {\n            this.scroll = undefined;\n            this.layout = undefined;\n            this.snapshot = undefined;\n            this.prevTransformTemplateValue = undefined;\n            this.targetDelta = undefined;\n            this.target = undefined;\n            this.isLayoutDirty = false;\n        }\n        forceRelativeParentToResolveTarget() {\n            if (!this.relativeParent)\n                return;\n            /**\n             * If the parent target isn't up-to-date, force it to update.\n             * This is an unfortunate de-optimisation as it means any updating relative\n             * projection will cause all the relative parents to recalculate back\n             * up the tree.\n             */\n            if (this.relativeParent.resolvedRelativeTargetAt !==\n                frameData.timestamp) {\n                this.relativeParent.resolveTargetDelta(true);\n            }\n        }\n        resolveTargetDelta(forceRecalculation = false) {\n            var _a;\n            /**\n             * Once the dirty status of nodes has been spread through the tree, we also\n             * need to check if we have a shared node of a different depth that has itself\n             * been dirtied.\n             */\n            const lead = this.getLead();\n            this.isProjectionDirty || (this.isProjectionDirty = lead.isProjectionDirty);\n            this.isTransformDirty || (this.isTransformDirty = lead.isTransformDirty);\n            this.isSharedProjectionDirty || (this.isSharedProjectionDirty = lead.isSharedProjectionDirty);\n            const isShared = Boolean(this.resumingFrom) || this !== lead;\n            /**\n             * We don't use transform for this step of processing so we don't\n             * need to check whether any nodes have changed transform.\n             */\n            const canSkip = !(forceRecalculation ||\n                (isShared && this.isSharedProjectionDirty) ||\n                this.isProjectionDirty ||\n                ((_a = this.parent) === null || _a === void 0 ? void 0 : _a.isProjectionDirty) ||\n                this.attemptToResolveRelativeTarget);\n            if (canSkip)\n                return;\n            const { layout, layoutId } = this.options;\n            /**\n             * If we have no layout, we can't perform projection, so early return\n             */\n            if (!this.layout || !(layout || layoutId))\n                return;\n            this.resolvedRelativeTargetAt = frameData.timestamp;\n            /**\n             * If we don't have a targetDelta but do have a layout, we can attempt to resolve\n             * a relativeParent. This will allow a component to perform scale correction\n             * even if no animation has started.\n             */\n            // TODO If this is unsuccessful this currently happens every frame\n            if (!this.targetDelta && !this.relativeTarget) {\n                // TODO: This is a semi-repetition of further down this function, make DRY\n                const relativeParent = this.getClosestProjectingParent();\n                if (relativeParent &&\n                    relativeParent.layout &&\n                    this.animationProgress !== 1) {\n                    this.relativeParent = relativeParent;\n                    this.forceRelativeParentToResolveTarget();\n                    this.relativeTarget = createBox();\n                    this.relativeTargetOrigin = createBox();\n                    calcRelativePosition(this.relativeTargetOrigin, this.layout.layoutBox, relativeParent.layout.layoutBox);\n                    copyBoxInto(this.relativeTarget, this.relativeTargetOrigin);\n                }\n                else {\n                    this.relativeParent = this.relativeTarget = undefined;\n                }\n            }\n            /**\n             * If we have no relative target or no target delta our target isn't valid\n             * for this frame.\n             */\n            if (!this.relativeTarget && !this.targetDelta)\n                return;\n            /**\n             * Lazy-init target data structure\n             */\n            if (!this.target) {\n                this.target = createBox();\n                this.targetWithTransforms = createBox();\n            }\n            /**\n             * If we've got a relative box for this component, resolve it into a target relative to the parent.\n             */\n            if (this.relativeTarget &&\n                this.relativeTargetOrigin &&\n                this.relativeParent &&\n                this.relativeParent.target) {\n                this.forceRelativeParentToResolveTarget();\n                calcRelativeBox(this.target, this.relativeTarget, this.relativeParent.target);\n                /**\n                 * If we've only got a targetDelta, resolve it into a target\n                 */\n            }\n            else if (this.targetDelta) {\n                if (Boolean(this.resumingFrom)) {\n                    // TODO: This is creating a new object every frame\n                    this.target = this.applyTransform(this.layout.layoutBox);\n                }\n                else {\n                    copyBoxInto(this.target, this.layout.layoutBox);\n                }\n                applyBoxDelta(this.target, this.targetDelta);\n            }\n            else {\n                /**\n                 * If no target, use own layout as target\n                 */\n                copyBoxInto(this.target, this.layout.layoutBox);\n            }\n            /**\n             * If we've been told to attempt to resolve a relative target, do so.\n             */\n            if (this.attemptToResolveRelativeTarget) {\n                this.attemptToResolveRelativeTarget = false;\n                const relativeParent = this.getClosestProjectingParent();\n                if (relativeParent &&\n                    Boolean(relativeParent.resumingFrom) ===\n                        Boolean(this.resumingFrom) &&\n                    !relativeParent.options.layoutScroll &&\n                    relativeParent.target &&\n                    this.animationProgress !== 1) {\n                    this.relativeParent = relativeParent;\n                    this.forceRelativeParentToResolveTarget();\n                    this.relativeTarget = createBox();\n                    this.relativeTargetOrigin = createBox();\n                    calcRelativePosition(this.relativeTargetOrigin, this.target, relativeParent.target);\n                    copyBoxInto(this.relativeTarget, this.relativeTargetOrigin);\n                }\n                else {\n                    this.relativeParent = this.relativeTarget = undefined;\n                }\n            }\n            /**\n             * Increase debug counter for resolved target deltas\n             */\n            projectionFrameData.resolvedTargetDeltas++;\n        }\n        getClosestProjectingParent() {\n            if (!this.parent ||\n                hasScale(this.parent.latestValues) ||\n                has2DTranslate(this.parent.latestValues)) {\n                return undefined;\n            }\n            if (this.parent.isProjecting()) {\n                return this.parent;\n            }\n            else {\n                return this.parent.getClosestProjectingParent();\n            }\n        }\n        isProjecting() {\n            return Boolean((this.relativeTarget ||\n                this.targetDelta ||\n                this.options.layoutRoot) &&\n                this.layout);\n        }\n        calcProjection() {\n            var _a;\n            const lead = this.getLead();\n            const isShared = Boolean(this.resumingFrom) || this !== lead;\n            let canSkip = true;\n            /**\n             * If this is a normal layout animation and neither this node nor its nearest projecting\n             * is dirty then we can't skip.\n             */\n            if (this.isProjectionDirty || ((_a = this.parent) === null || _a === void 0 ? void 0 : _a.isProjectionDirty)) {\n                canSkip = false;\n            }\n            /**\n             * If this is a shared layout animation and this node's shared projection is dirty then\n             * we can't skip.\n             */\n            if (isShared &&\n                (this.isSharedProjectionDirty || this.isTransformDirty)) {\n                canSkip = false;\n            }\n            /**\n             * If we have resolved the target this frame we must recalculate the\n             * projection to ensure it visually represents the internal calculations.\n             */\n            if (this.resolvedRelativeTargetAt === frameData.timestamp) {\n                canSkip = false;\n            }\n            if (canSkip)\n                return;\n            const { layout, layoutId } = this.options;\n            /**\n             * If this section of the tree isn't animating we can\n             * delete our target sources for the following frame.\n             */\n            this.isTreeAnimating = Boolean((this.parent && this.parent.isTreeAnimating) ||\n                this.currentAnimation ||\n                this.pendingAnimation);\n            if (!this.isTreeAnimating) {\n                this.targetDelta = this.relativeTarget = undefined;\n            }\n            if (!this.layout || !(layout || layoutId))\n                return;\n            /**\n             * Reset the corrected box with the latest values from box, as we're then going\n             * to perform mutative operations on it.\n             */\n            copyBoxInto(this.layoutCorrected, this.layout.layoutBox);\n            /**\n             * Record previous tree scales before updating.\n             */\n            const prevTreeScaleX = this.treeScale.x;\n            const prevTreeScaleY = this.treeScale.y;\n            /**\n             * Apply all the parent deltas to this box to produce the corrected box. This\n             * is the layout box, as it will appear on screen as a result of the transforms of its parents.\n             */\n            applyTreeDeltas(this.layoutCorrected, this.treeScale, this.path, isShared);\n            /**\n             * If this layer needs to perform scale correction but doesn't have a target,\n             * use the layout as the target.\n             */\n            if (lead.layout &&\n                !lead.target &&\n                (this.treeScale.x !== 1 || this.treeScale.y !== 1)) {\n                lead.target = lead.layout.layoutBox;\n            }\n            const { target } = lead;\n            if (!target) {\n                /**\n                 * If we don't have a target to project into, but we were previously\n                 * projecting, we want to remove the stored transform and schedule\n                 * a render to ensure the elements reflect the removed transform.\n                 */\n                if (this.projectionTransform) {\n                    this.projectionDelta = createDelta();\n                    this.projectionTransform = \"none\";\n                    this.scheduleRender();\n                }\n                return;\n            }\n            if (!this.projectionDelta) {\n                this.projectionDelta = createDelta();\n                this.projectionDeltaWithTransform = createDelta();\n            }\n            const prevProjectionTransform = this.projectionTransform;\n            /**\n             * Update the delta between the corrected box and the target box before user-set transforms were applied.\n             * This will allow us to calculate the corrected borderRadius and boxShadow to compensate\n             * for our layout reprojection, but still allow them to be scaled correctly by the user.\n             * It might be that to simplify this we may want to accept that user-set scale is also corrected\n             * and we wouldn't have to keep and calc both deltas, OR we could support a user setting\n             * to allow people to choose whether these styles are corrected based on just the\n             * layout reprojection or the final bounding box.\n             */\n            calcBoxDelta(this.projectionDelta, this.layoutCorrected, target, this.latestValues);\n            this.projectionTransform = buildProjectionTransform(this.projectionDelta, this.treeScale);\n            if (this.projectionTransform !== prevProjectionTransform ||\n                this.treeScale.x !== prevTreeScaleX ||\n                this.treeScale.y !== prevTreeScaleY) {\n                this.hasProjected = true;\n                this.scheduleRender();\n                this.notifyListeners(\"projectionUpdate\", target);\n            }\n            /**\n             * Increase debug counter for recalculated projections\n             */\n            projectionFrameData.recalculatedProjection++;\n        }\n        hide() {\n            this.isVisible = false;\n            // TODO: Schedule render\n        }\n        show() {\n            this.isVisible = true;\n            // TODO: Schedule render\n        }\n        scheduleRender(notifyAll = true) {\n            this.options.scheduleRender && this.options.scheduleRender();\n            if (notifyAll) {\n                const stack = this.getStack();\n                stack && stack.scheduleRender();\n            }\n            if (this.resumingFrom && !this.resumingFrom.instance) {\n                this.resumingFrom = undefined;\n            }\n        }\n        setAnimationOrigin(delta, hasOnlyRelativeTargetChanged = false) {\n            const snapshot = this.snapshot;\n            const snapshotLatestValues = snapshot\n                ? snapshot.latestValues\n                : {};\n            const mixedValues = { ...this.latestValues };\n            const targetDelta = createDelta();\n            if (!this.relativeParent ||\n                !this.relativeParent.options.layoutRoot) {\n                this.relativeTarget = this.relativeTargetOrigin = undefined;\n            }\n            this.attemptToResolveRelativeTarget = !hasOnlyRelativeTargetChanged;\n            const relativeLayout = createBox();\n            const snapshotSource = snapshot ? snapshot.source : undefined;\n            const layoutSource = this.layout ? this.layout.source : undefined;\n            const isSharedLayoutAnimation = snapshotSource !== layoutSource;\n            const stack = this.getStack();\n            const isOnlyMember = !stack || stack.members.length <= 1;\n            const shouldCrossfadeOpacity = Boolean(isSharedLayoutAnimation &&\n                !isOnlyMember &&\n                this.options.crossfade === true &&\n                !this.path.some(hasOpacityCrossfade));\n            this.animationProgress = 0;\n            let prevRelativeTarget;\n            this.mixTargetDelta = (latest) => {\n                const progress = latest / 1000;\n                mixAxisDelta(targetDelta.x, delta.x, progress);\n                mixAxisDelta(targetDelta.y, delta.y, progress);\n                this.setTargetDelta(targetDelta);\n                if (this.relativeTarget &&\n                    this.relativeTargetOrigin &&\n                    this.layout &&\n                    this.relativeParent &&\n                    this.relativeParent.layout) {\n                    calcRelativePosition(relativeLayout, this.layout.layoutBox, this.relativeParent.layout.layoutBox);\n                    mixBox(this.relativeTarget, this.relativeTargetOrigin, relativeLayout, progress);\n                    /**\n                     * If this is an unchanged relative target we can consider the\n                     * projection not dirty.\n                     */\n                    if (prevRelativeTarget &&\n                        boxEquals(this.relativeTarget, prevRelativeTarget)) {\n                        this.isProjectionDirty = false;\n                    }\n                    if (!prevRelativeTarget)\n                        prevRelativeTarget = createBox();\n                    copyBoxInto(prevRelativeTarget, this.relativeTarget);\n                }\n                if (isSharedLayoutAnimation) {\n                    this.animationValues = mixedValues;\n                    mixValues(mixedValues, snapshotLatestValues, this.latestValues, progress, shouldCrossfadeOpacity, isOnlyMember);\n                }\n                this.root.scheduleUpdateProjection();\n                this.scheduleRender();\n                this.animationProgress = progress;\n            };\n            this.mixTargetDelta(this.options.layoutRoot ? 1000 : 0);\n        }\n        startAnimation(options) {\n            this.notifyListeners(\"animationStart\");\n            this.currentAnimation && this.currentAnimation.stop();\n            if (this.resumingFrom && this.resumingFrom.currentAnimation) {\n                this.resumingFrom.currentAnimation.stop();\n            }\n            if (this.pendingAnimation) {\n                cancelFrame(this.pendingAnimation);\n                this.pendingAnimation = undefined;\n            }\n            /**\n             * Start the animation in the next frame to have a frame with progress 0,\n             * where the target is the same as when the animation started, so we can\n             * calculate the relative positions correctly for instant transitions.\n             */\n            this.pendingAnimation = frame.update(() => {\n                globalProjectionState.hasAnimatedSinceResize = true;\n                this.currentAnimation = animateSingleValue(0, animationTarget, {\n                    ...options,\n                    onUpdate: (latest) => {\n                        this.mixTargetDelta(latest);\n                        options.onUpdate && options.onUpdate(latest);\n                    },\n                    onComplete: () => {\n                        options.onComplete && options.onComplete();\n                        this.completeAnimation();\n                    },\n                });\n                if (this.resumingFrom) {\n                    this.resumingFrom.currentAnimation = this.currentAnimation;\n                }\n                this.pendingAnimation = undefined;\n            });\n        }\n        completeAnimation() {\n            if (this.resumingFrom) {\n                this.resumingFrom.currentAnimation = undefined;\n                this.resumingFrom.preserveOpacity = undefined;\n            }\n            const stack = this.getStack();\n            stack && stack.exitAnimationComplete();\n            this.resumingFrom =\n                this.currentAnimation =\n                    this.animationValues =\n                        undefined;\n            this.notifyListeners(\"animationComplete\");\n        }\n        finishAnimation() {\n            if (this.currentAnimation) {\n                this.mixTargetDelta && this.mixTargetDelta(animationTarget);\n                this.currentAnimation.stop();\n            }\n            this.completeAnimation();\n        }\n        applyTransformsToTarget() {\n            const lead = this.getLead();\n            let { targetWithTransforms, target, layout, latestValues } = lead;\n            if (!targetWithTransforms || !target || !layout)\n                return;\n            /**\n             * If we're only animating position, and this element isn't the lead element,\n             * then instead of projecting into the lead box we instead want to calculate\n             * a new target that aligns the two boxes but maintains the layout shape.\n             */\n            if (this !== lead &&\n                this.layout &&\n                layout &&\n                shouldAnimatePositionOnly(this.options.animationType, this.layout.layoutBox, layout.layoutBox)) {\n                target = this.target || createBox();\n                const xLength = calcLength(this.layout.layoutBox.x);\n                target.x.min = lead.target.x.min;\n                target.x.max = target.x.min + xLength;\n                const yLength = calcLength(this.layout.layoutBox.y);\n                target.y.min = lead.target.y.min;\n                target.y.max = target.y.min + yLength;\n            }\n            copyBoxInto(targetWithTransforms, target);\n            /**\n             * Apply the latest user-set transforms to the targetBox to produce the targetBoxFinal.\n             * This is the final box that we will then project into by calculating a transform delta and\n             * applying it to the corrected box.\n             */\n            transformBox(targetWithTransforms, latestValues);\n            /**\n             * Update the delta between the corrected box and the final target box, after\n             * user-set transforms are applied to it. This will be used by the renderer to\n             * create a transform style that will reproject the element from its layout layout\n             * into the desired bounding box.\n             */\n            calcBoxDelta(this.projectionDeltaWithTransform, this.layoutCorrected, targetWithTransforms, latestValues);\n        }\n        registerSharedNode(layoutId, node) {\n            if (!this.sharedNodes.has(layoutId)) {\n                this.sharedNodes.set(layoutId, new NodeStack());\n            }\n            const stack = this.sharedNodes.get(layoutId);\n            stack.add(node);\n            const config = node.options.initialPromotionConfig;\n            node.promote({\n                transition: config ? config.transition : undefined,\n                preserveFollowOpacity: config && config.shouldPreserveFollowOpacity\n                    ? config.shouldPreserveFollowOpacity(node)\n                    : undefined,\n            });\n        }\n        isLead() {\n            const stack = this.getStack();\n            return stack ? stack.lead === this : true;\n        }\n        getLead() {\n            var _a;\n            const { layoutId } = this.options;\n            return layoutId ? ((_a = this.getStack()) === null || _a === void 0 ? void 0 : _a.lead) || this : this;\n        }\n        getPrevLead() {\n            var _a;\n            const { layoutId } = this.options;\n            return layoutId ? (_a = this.getStack()) === null || _a === void 0 ? void 0 : _a.prevLead : undefined;\n        }\n        getStack() {\n            const { layoutId } = this.options;\n            if (layoutId)\n                return this.root.sharedNodes.get(layoutId);\n        }\n        promote({ needsReset, transition, preserveFollowOpacity, } = {}) {\n            const stack = this.getStack();\n            if (stack)\n                stack.promote(this, preserveFollowOpacity);\n            if (needsReset) {\n                this.projectionDelta = undefined;\n                this.needsReset = true;\n            }\n            if (transition)\n                this.setOptions({ transition });\n        }\n        relegate() {\n            const stack = this.getStack();\n            if (stack) {\n                return stack.relegate(this);\n            }\n            else {\n                return false;\n            }\n        }\n        resetRotation() {\n            const { visualElement } = this.options;\n            if (!visualElement)\n                return;\n            // If there's no detected rotation values, we can early return without a forced render.\n            let hasRotate = false;\n            /**\n             * An unrolled check for rotation values. Most elements don't have any rotation and\n             * skipping the nested loop and new object creation is 50% faster.\n             */\n            const { latestValues } = visualElement;\n            if (latestValues.rotate ||\n                latestValues.rotateX ||\n                latestValues.rotateY ||\n                latestValues.rotateZ) {\n                hasRotate = true;\n            }\n            // If there's no rotation values, we don't need to do any more.\n            if (!hasRotate)\n                return;\n            const resetValues = {};\n            // Check the rotate value of all axes and reset to 0\n            for (let i = 0; i < transformAxes.length; i++) {\n                const key = \"rotate\" + transformAxes[i];\n                // Record the rotation and then temporarily set it to 0\n                if (latestValues[key]) {\n                    resetValues[key] = latestValues[key];\n                    visualElement.setStaticValue(key, 0);\n                }\n            }\n            // Force a render of this element to apply the transform with all rotations\n            // set to 0.\n            visualElement.render();\n            // Put back all the values we reset\n            for (const key in resetValues) {\n                visualElement.setStaticValue(key, resetValues[key]);\n            }\n            // Schedule a render for the next frame. This ensures we won't visually\n            // see the element with the reset rotate value applied.\n            visualElement.scheduleRender();\n        }\n        getProjectionStyles(styleProp) {\n            var _a, _b;\n            if (!this.instance || this.isSVG)\n                return undefined;\n            if (!this.isVisible) {\n                return hiddenVisibility;\n            }\n            const styles = {\n                visibility: \"\",\n            };\n            const transformTemplate = this.getTransformTemplate();\n            if (this.needsReset) {\n                this.needsReset = false;\n                styles.opacity = \"\";\n                styles.pointerEvents =\n                    resolveMotionValue(styleProp === null || styleProp === void 0 ? void 0 : styleProp.pointerEvents) || \"\";\n                styles.transform = transformTemplate\n                    ? transformTemplate(this.latestValues, \"\")\n                    : \"none\";\n                return styles;\n            }\n            const lead = this.getLead();\n            if (!this.projectionDelta || !this.layout || !lead.target) {\n                const emptyStyles = {};\n                if (this.options.layoutId) {\n                    emptyStyles.opacity =\n                        this.latestValues.opacity !== undefined\n                            ? this.latestValues.opacity\n                            : 1;\n                    emptyStyles.pointerEvents =\n                        resolveMotionValue(styleProp === null || styleProp === void 0 ? void 0 : styleProp.pointerEvents) || \"\";\n                }\n                if (this.hasProjected && !hasTransform(this.latestValues)) {\n                    emptyStyles.transform = transformTemplate\n                        ? transformTemplate({}, \"\")\n                        : \"none\";\n                    this.hasProjected = false;\n                }\n                return emptyStyles;\n            }\n            const valuesToRender = lead.animationValues || lead.latestValues;\n            this.applyTransformsToTarget();\n            styles.transform = buildProjectionTransform(this.projectionDeltaWithTransform, this.treeScale, valuesToRender);\n            if (transformTemplate) {\n                styles.transform = transformTemplate(valuesToRender, styles.transform);\n            }\n            const { x, y } = this.projectionDelta;\n            styles.transformOrigin = `${x.origin * 100}% ${y.origin * 100}% 0`;\n            if (lead.animationValues) {\n                /**\n                 * If the lead component is animating, assign this either the entering/leaving\n                 * opacity\n                 */\n                styles.opacity =\n                    lead === this\n                        ? (_b = (_a = valuesToRender.opacity) !== null && _a !== void 0 ? _a : this.latestValues.opacity) !== null && _b !== void 0 ? _b : 1\n                        : this.preserveOpacity\n                            ? this.latestValues.opacity\n                            : valuesToRender.opacityExit;\n            }\n            else {\n                /**\n                 * Or we're not animating at all, set the lead component to its layout\n                 * opacity and other components to hidden.\n                 */\n                styles.opacity =\n                    lead === this\n                        ? valuesToRender.opacity !== undefined\n                            ? valuesToRender.opacity\n                            : \"\"\n                        : valuesToRender.opacityExit !== undefined\n                            ? valuesToRender.opacityExit\n                            : 0;\n            }\n            /**\n             * Apply scale correction\n             */\n            for (const key in scaleCorrectors) {\n                if (valuesToRender[key] === undefined)\n                    continue;\n                const { correct, applyTo } = scaleCorrectors[key];\n                /**\n                 * Only apply scale correction to the value if we have an\n                 * active projection transform. Otherwise these values become\n                 * vulnerable to distortion if the element changes size without\n                 * a corresponding layout animation.\n                 */\n                const corrected = styles.transform === \"none\"\n                    ? valuesToRender[key]\n                    : correct(valuesToRender[key], lead);\n                if (applyTo) {\n                    const num = applyTo.length;\n                    for (let i = 0; i < num; i++) {\n                        styles[applyTo[i]] = corrected;\n                    }\n                }\n                else {\n                    styles[key] = corrected;\n                }\n            }\n            /**\n             * Disable pointer events on follow components. This is to ensure\n             * that if a follow component covers a lead component it doesn't block\n             * pointer events on the lead.\n             */\n            if (this.options.layoutId) {\n                styles.pointerEvents =\n                    lead === this\n                        ? resolveMotionValue(styleProp === null || styleProp === void 0 ? void 0 : styleProp.pointerEvents) || \"\"\n                        : \"none\";\n            }\n            return styles;\n        }\n        clearSnapshot() {\n            this.resumeFrom = this.snapshot = undefined;\n        }\n        // Only run on root\n        resetTree() {\n            this.root.nodes.forEach((node) => { var _a; return (_a = node.currentAnimation) === null || _a === void 0 ? void 0 : _a.stop(); });\n            this.root.nodes.forEach(clearMeasurements);\n            this.root.sharedNodes.clear();\n        }\n    };\n}\nfunction updateLayout(node) {\n    node.updateLayout();\n}\nfunction notifyLayoutUpdate(node) {\n    var _a;\n    const snapshot = ((_a = node.resumeFrom) === null || _a === void 0 ? void 0 : _a.snapshot) || node.snapshot;\n    if (node.isLead() &&\n        node.layout &&\n        snapshot &&\n        node.hasListeners(\"didUpdate\")) {\n        const { layoutBox: layout, measuredBox: measuredLayout } = node.layout;\n        const { animationType } = node.options;\n        const isShared = snapshot.source !== node.layout.source;\n        // TODO Maybe we want to also resize the layout snapshot so we don't trigger\n        // animations for instance if layout=\"size\" and an element has only changed position\n        if (animationType === \"size\") {\n            eachAxis((axis) => {\n                const axisSnapshot = isShared\n                    ? snapshot.measuredBox[axis]\n                    : snapshot.layoutBox[axis];\n                const length = calcLength(axisSnapshot);\n                axisSnapshot.min = layout[axis].min;\n                axisSnapshot.max = axisSnapshot.min + length;\n            });\n        }\n        else if (shouldAnimatePositionOnly(animationType, snapshot.layoutBox, layout)) {\n            eachAxis((axis) => {\n                const axisSnapshot = isShared\n                    ? snapshot.measuredBox[axis]\n                    : snapshot.layoutBox[axis];\n                const length = calcLength(layout[axis]);\n                axisSnapshot.max = axisSnapshot.min + length;\n                /**\n                 * Ensure relative target gets resized and rerendererd\n                 */\n                if (node.relativeTarget && !node.currentAnimation) {\n                    node.isProjectionDirty = true;\n                    node.relativeTarget[axis].max =\n                        node.relativeTarget[axis].min + length;\n                }\n            });\n        }\n        const layoutDelta = createDelta();\n        calcBoxDelta(layoutDelta, layout, snapshot.layoutBox);\n        const visualDelta = createDelta();\n        if (isShared) {\n            calcBoxDelta(visualDelta, node.applyTransform(measuredLayout, true), snapshot.measuredBox);\n        }\n        else {\n            calcBoxDelta(visualDelta, layout, snapshot.layoutBox);\n        }\n        const hasLayoutChanged = !isDeltaZero(layoutDelta);\n        let hasRelativeTargetChanged = false;\n        if (!node.resumeFrom) {\n            const relativeParent = node.getClosestProjectingParent();\n            /**\n             * If the relativeParent is itself resuming from a different element then\n             * the relative snapshot is not relavent\n             */\n            if (relativeParent && !relativeParent.resumeFrom) {\n                const { snapshot: parentSnapshot, layout: parentLayout } = relativeParent;\n                if (parentSnapshot && parentLayout) {\n                    const relativeSnapshot = createBox();\n                    calcRelativePosition(relativeSnapshot, snapshot.layoutBox, parentSnapshot.layoutBox);\n                    const relativeLayout = createBox();\n                    calcRelativePosition(relativeLayout, layout, parentLayout.layoutBox);\n                    if (!boxEqualsRounded(relativeSnapshot, relativeLayout)) {\n                        hasRelativeTargetChanged = true;\n                    }\n                    if (relativeParent.options.layoutRoot) {\n                        node.relativeTarget = relativeLayout;\n                        node.relativeTargetOrigin = relativeSnapshot;\n                        node.relativeParent = relativeParent;\n                    }\n                }\n            }\n        }\n        node.notifyListeners(\"didUpdate\", {\n            layout,\n            snapshot,\n            delta: visualDelta,\n            layoutDelta,\n            hasLayoutChanged,\n            hasRelativeTargetChanged,\n        });\n    }\n    else if (node.isLead()) {\n        const { onExitComplete } = node.options;\n        onExitComplete && onExitComplete();\n    }\n    /**\n     * Clearing transition\n     * TODO: Investigate why this transition is being passed in as {type: false } from Framer\n     * and why we need it at all\n     */\n    node.options.transition = undefined;\n}\nfunction propagateDirtyNodes(node) {\n    /**\n     * Increase debug counter for nodes encountered this frame\n     */\n    projectionFrameData.totalNodes++;\n    if (!node.parent)\n        return;\n    /**\n     * If this node isn't projecting, propagate isProjectionDirty. It will have\n     * no performance impact but it will allow the next child that *is* projecting\n     * but *isn't* dirty to just check its parent to see if *any* ancestor needs\n     * correcting.\n     */\n    if (!node.isProjecting()) {\n        node.isProjectionDirty = node.parent.isProjectionDirty;\n    }\n    /**\n     * Propagate isSharedProjectionDirty and isTransformDirty\n     * throughout the whole tree. A future revision can take another look at\n     * this but for safety we still recalcualte shared nodes.\n     */\n    node.isSharedProjectionDirty || (node.isSharedProjectionDirty = Boolean(node.isProjectionDirty ||\n        node.parent.isProjectionDirty ||\n        node.parent.isSharedProjectionDirty));\n    node.isTransformDirty || (node.isTransformDirty = node.parent.isTransformDirty);\n}\nfunction cleanDirtyNodes(node) {\n    node.isProjectionDirty =\n        node.isSharedProjectionDirty =\n            node.isTransformDirty =\n                false;\n}\nfunction clearSnapshot(node) {\n    node.clearSnapshot();\n}\nfunction clearMeasurements(node) {\n    node.clearMeasurements();\n}\nfunction clearIsLayoutDirty(node) {\n    node.isLayoutDirty = false;\n}\nfunction resetTransformStyle(node) {\n    const { visualElement } = node.options;\n    if (visualElement && visualElement.getProps().onBeforeLayoutMeasure) {\n        visualElement.notify(\"BeforeLayoutMeasure\");\n    }\n    node.resetTransform();\n}\nfunction finishAnimation(node) {\n    node.finishAnimation();\n    node.targetDelta = node.relativeTarget = node.target = undefined;\n    node.isProjectionDirty = true;\n}\nfunction resolveTargetDelta(node) {\n    node.resolveTargetDelta();\n}\nfunction calcProjection(node) {\n    node.calcProjection();\n}\nfunction resetRotation(node) {\n    node.resetRotation();\n}\nfunction removeLeadSnapshots(stack) {\n    stack.removeLeadSnapshot();\n}\nfunction mixAxisDelta(output, delta, p) {\n    output.translate = mix(delta.translate, 0, p);\n    output.scale = mix(delta.scale, 1, p);\n    output.origin = delta.origin;\n    output.originPoint = delta.originPoint;\n}\nfunction mixAxis(output, from, to, p) {\n    output.min = mix(from.min, to.min, p);\n    output.max = mix(from.max, to.max, p);\n}\nfunction mixBox(output, from, to, p) {\n    mixAxis(output.x, from.x, to.x, p);\n    mixAxis(output.y, from.y, to.y, p);\n}\nfunction hasOpacityCrossfade(node) {\n    return (node.animationValues && node.animationValues.opacityExit !== undefined);\n}\nconst defaultLayoutTransition = {\n    duration: 0.45,\n    ease: [0.4, 0, 0.1, 1],\n};\nconst userAgentContains = (string) => typeof navigator !== \"undefined\" &&\n    navigator.userAgent.toLowerCase().includes(string);\n/**\n * Measured bounding boxes must be rounded in Safari and\n * left untouched in Chrome, otherwise non-integer layouts within scaled-up elements\n * can appear to jump.\n */\nconst roundPoint = userAgentContains(\"applewebkit/\") && !userAgentContains(\"chrome/\")\n    ? Math.round\n    : noop;\nfunction roundAxis(axis) {\n    // Round to the nearest .5 pixels to support subpixel layouts\n    axis.min = roundPoint(axis.min);\n    axis.max = roundPoint(axis.max);\n}\nfunction roundBox(box) {\n    roundAxis(box.x);\n    roundAxis(box.y);\n}\nfunction shouldAnimatePositionOnly(animationType, snapshot, layout) {\n    return (animationType === \"position\" ||\n        (animationType === \"preserve-aspect\" &&\n            !isNear(aspectRatio(snapshot), aspectRatio(layout), 0.2)));\n}\n\nexport { cleanDirtyNodes, createProjectionNode, mixAxis, mixAxisDelta, mixBox, propagateDirtyNodes };\n"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,sCAAsC;AAC1E,SAASC,SAAS,QAAQ,6BAA6B;AACvD,SAASC,WAAW,QAAQ,sBAAsB;AAClD,SAASC,aAAa,EAAEC,YAAY,EAAEC,aAAa,EAAEC,eAAe,QAAQ,6BAA6B;AACzG,SAASC,oBAAoB,EAAEC,eAAe,EAAEC,YAAY,EAAEC,UAAU,EAAEC,MAAM,QAAQ,4BAA4B;AACpH,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,SAAS,EAAEC,WAAW,QAAQ,wBAAwB;AAC/D,SAASC,kBAAkB,QAAQ,uCAAuC;AAC1E,SAASC,gBAAgB,EAAEC,WAAW,EAAEC,WAAW,EAAEC,SAAS,QAAQ,uBAAuB;AAC7F,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,wBAAwB,QAAQ,yBAAyB;AAClE,SAASC,QAAQ,QAAQ,wBAAwB;AACjD,SAASC,YAAY,EAAEC,QAAQ,EAAEC,cAAc,QAAQ,4BAA4B;AACnF,SAASC,QAAQ,QAAQ,kCAAkC;AAC3D,SAASC,kBAAkB,QAAQ,4CAA4C;AAC/E,SAASC,qBAAqB,QAAQ,aAAa;AACnD,SAASC,KAAK,QAAQ,uBAAuB;AAC7C,SAASC,GAAG,QAAQ,qBAAqB;AACzC,SAASC,MAAM,QAAQ,wBAAwB;AAC/C,SAASC,YAAY,QAAQ,2CAA2C;AACxE,SAASC,kBAAkB,QAAQ,6CAA6C;AAChF,SAASC,KAAK,QAAQ,uBAAuB;AAC7C,SAASC,WAAW,EAAEC,SAAS,EAAEC,KAAK,EAAEC,KAAK,QAAQ,2BAA2B;AAChF,SAASC,IAAI,QAAQ,sBAAsB;AAE3C,MAAMC,aAAa,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACzC,MAAMC,gBAAgB,GAAG;EAAEC,UAAU,EAAE;AAAS,CAAC;AACjD;AACA;AACA;AACA;AACA,MAAMC,eAAe,GAAG,IAAI;AAC5B,IAAIC,EAAE,GAAG,CAAC;AACV;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,GAAG;EACxBC,IAAI,EAAE,iBAAiB;EACvBC,UAAU,EAAE,CAAC;EACbC,oBAAoB,EAAE,CAAC;EACvBC,sBAAsB,EAAE;AAC5B,CAAC;AACD,SAASC,oBAAoBA,CAAC;EAAEC,oBAAoB;EAAEC,aAAa;EAAEC,aAAa;EAAEC,iBAAiB;EAAEC;AAAgB,CAAC,EAAE;EACtH,OAAO,MAAMC,cAAc,CAAC;IACxBC,WAAWA,CAACC,YAAY,GAAG,CAAC,CAAC,EAAEC,MAAM,GAAGP,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC,CAAC,EAAE;MACnH;AACZ;AACA;MACY,IAAI,CAACR,EAAE,GAAGA,EAAE,EAAE;MACd;AACZ;AACA;MACY,IAAI,CAACgB,WAAW,GAAG,CAAC;MACpB;AACZ;AACA;AACA;AACA;AACA;MACY,IAAI,CAACC,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;MACzB;AACZ;AACA;AACA;MACY,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC;MACjB;AACZ;AACA;AACA;AACA;MACY,IAAI,CAACC,eAAe,GAAG,KAAK;MAC5B,IAAI,CAACC,kBAAkB,GAAG,KAAK;MAC/B;AACZ;AACA;AACA;AACA;AACA;MACY,IAAI,CAACC,aAAa,GAAG,KAAK;MAC1B;AACZ;AACA;AACA;MACY,IAAI,CAACC,iBAAiB,GAAG,KAAK;MAC9B;AACZ;AACA;AACA;MACY,IAAI,CAACC,uBAAuB,GAAG,KAAK;MACpC;AACZ;AACA;AACA;MACY,IAAI,CAACC,gBAAgB,GAAG,KAAK;MAC7B;AACZ;AACA;MACY,IAAI,CAACC,qBAAqB,GAAG,KAAK;MAClC,IAAI,CAACC,qBAAqB,GAAG,KAAK;MAClC;AACZ;AACA;AACA;MACY,IAAI,CAACC,UAAU,GAAG,KAAK;MACvB;AACZ;AACA;MACY,IAAI,CAACC,KAAK,GAAG,KAAK;MAClB;AACZ;AACA;AACA;MACY,IAAI,CAACC,UAAU,GAAG,KAAK;MACvB;AACZ;AACA;MACY,IAAI,CAACC,oBAAoB,GAAG,KAAK;MACjC;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;MACY,IAAI,CAACC,SAAS,GAAG;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC;MAC/B;AACZ;AACA;MACY,IAAI,CAACC,aAAa,GAAG,IAAIC,GAAG,CAAC,CAAC;MAC9B,IAAI,CAACC,eAAe,GAAG,KAAK;MAC5B;MACA,IAAI,CAACC,eAAe,GAAG,KAAK;MAC5B,IAAI,CAACC,yBAAyB,GAAG,KAAK;MACtC,IAAI,CAACC,iBAAiB,GAAG,MAAM;QAC3B,IAAI,IAAI,CAACZ,UAAU,EAAE;UACjB,IAAI,CAACA,UAAU,GAAG,KAAK;UACvB,IAAI,CAACa,iBAAiB,CAAC,CAAC;QAC5B;MACJ,CAAC;MACD;AACZ;AACA;AACA;AACA;MACY,IAAI,CAACC,gBAAgB,GAAG,MAAM;QAC1B,IAAI,CAACH,yBAAyB,GAAG,KAAK;QACtC;AAChB;AACA;AACA;QACgBtC,mBAAmB,CAACE,UAAU,GAC1BF,mBAAmB,CAACG,oBAAoB,GACpCH,mBAAmB,CAACI,sBAAsB,GACtC,CAAC;QACb,IAAI,CAACsC,KAAK,CAACC,OAAO,CAACC,mBAAmB,CAAC;QACvC,IAAI,CAACF,KAAK,CAACC,OAAO,CAACE,kBAAkB,CAAC;QACtC,IAAI,CAACH,KAAK,CAACC,OAAO,CAACG,cAAc,CAAC;QAClC,IAAI,CAACJ,KAAK,CAACC,OAAO,CAACI,eAAe,CAAC;QACnC7D,MAAM,CAACc,mBAAmB,CAAC;MAC/B,CAAC;MACD,IAAI,CAACgD,YAAY,GAAG,KAAK;MACzB,IAAI,CAACC,SAAS,GAAG,IAAI;MACrB,IAAI,CAACC,iBAAiB,GAAG,CAAC;MAC1B;AACZ;AACA;MACY;MACA,IAAI,CAACC,WAAW,GAAG,IAAIhB,GAAG,CAAC,CAAC;MAC5B,IAAI,CAACtB,YAAY,GAAGA,YAAY;MAChC,IAAI,CAACuC,IAAI,GAAGtC,MAAM,GAAGA,MAAM,CAACsC,IAAI,IAAItC,MAAM,GAAG,IAAI;MACjD,IAAI,CAACuC,IAAI,GAAGvC,MAAM,GAAG,CAAC,GAAGA,MAAM,CAACuC,IAAI,EAAEvC,MAAM,CAAC,GAAG,EAAE;MAClD,IAAI,CAACA,MAAM,GAAGA,MAAM;MACpB,IAAI,CAACwC,KAAK,GAAGxC,MAAM,GAAGA,MAAM,CAACwC,KAAK,GAAG,CAAC,GAAG,CAAC;MAC1C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACF,IAAI,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;QACvC,IAAI,CAACF,IAAI,CAACE,CAAC,CAAC,CAACzB,oBAAoB,GAAG,IAAI;MAC5C;MACA,IAAI,IAAI,CAACsB,IAAI,KAAK,IAAI,EAClB,IAAI,CAACV,KAAK,GAAG,IAAI7D,QAAQ,CAAC,CAAC;IACnC;IACA4E,gBAAgBA,CAACC,IAAI,EAAEC,OAAO,EAAE;MAC5B,IAAI,CAAC,IAAI,CAACzB,aAAa,CAAC0B,GAAG,CAACF,IAAI,CAAC,EAAE;QAC/B,IAAI,CAACxB,aAAa,CAAC2B,GAAG,CAACH,IAAI,EAAE,IAAIxG,mBAAmB,CAAC,CAAC,CAAC;MAC3D;MACA,OAAO,IAAI,CAACgF,aAAa,CAAC4B,GAAG,CAACJ,IAAI,CAAC,CAACK,GAAG,CAACJ,OAAO,CAAC;IACpD;IACAK,eAAeA,CAACN,IAAI,EAAE,GAAGO,IAAI,EAAE;MAC3B,MAAMC,mBAAmB,GAAG,IAAI,CAAChC,aAAa,CAAC4B,GAAG,CAACJ,IAAI,CAAC;MACxDQ,mBAAmB,IAAIA,mBAAmB,CAACC,MAAM,CAAC,GAAGF,IAAI,CAAC;IAC9D;IACAG,YAAYA,CAACV,IAAI,EAAE;MACf,OAAO,IAAI,CAACxB,aAAa,CAAC0B,GAAG,CAACF,IAAI,CAAC;IACvC;IACA;AACR;AACA;IACQW,KAAKA,CAACC,QAAQ,EAAEjD,aAAa,GAAG,IAAI,CAAC+B,IAAI,CAAChB,eAAe,EAAE;MACvD,IAAI,IAAI,CAACkC,QAAQ,EACb;MACJ,IAAI,CAAC1C,KAAK,GAAGzC,YAAY,CAACmF,QAAQ,CAAC;MACnC,IAAI,CAACA,QAAQ,GAAGA,QAAQ;MACxB,MAAM;QAAEC,QAAQ;QAAEC,MAAM;QAAEC;MAAc,CAAC,GAAG,IAAI,CAACvD,OAAO;MACxD,IAAIuD,aAAa,IAAI,CAACA,aAAa,CAACC,OAAO,EAAE;QACzCD,aAAa,CAACJ,KAAK,CAACC,QAAQ,CAAC;MACjC;MACA,IAAI,CAAClB,IAAI,CAACV,KAAK,CAACqB,GAAG,CAAC,IAAI,CAAC;MACzB,IAAI,CAACjD,MAAM,IAAI,IAAI,CAACA,MAAM,CAACE,QAAQ,CAAC+C,GAAG,CAAC,IAAI,CAAC;MAC7C,IAAI1C,aAAa,KAAKmD,MAAM,IAAID,QAAQ,CAAC,EAAE;QACvC,IAAI,CAAClD,aAAa,GAAG,IAAI;MAC7B;MACA,IAAIf,oBAAoB,EAAE;QACtB,IAAIqE,WAAW;QACf,MAAMC,mBAAmB,GAAGA,CAAA,KAAO,IAAI,CAACxB,IAAI,CAAC1B,qBAAqB,GAAG,KAAM;QAC3EpB,oBAAoB,CAACgE,QAAQ,EAAE,MAAM;UACjC,IAAI,CAAClB,IAAI,CAAC1B,qBAAqB,GAAG,IAAI;UACtCiD,WAAW,IAAIA,WAAW,CAAC,CAAC;UAC5BA,WAAW,GAAG3F,KAAK,CAAC4F,mBAAmB,EAAE,GAAG,CAAC;UAC7C,IAAI7F,qBAAqB,CAAC8F,sBAAsB,EAAE;YAC9C9F,qBAAqB,CAAC8F,sBAAsB,GAAG,KAAK;YACpD,IAAI,CAACnC,KAAK,CAACC,OAAO,CAACmC,eAAe,CAAC;UACvC;QACJ,CAAC,CAAC;MACN;MACA,IAAIP,QAAQ,EAAE;QACV,IAAI,CAACnB,IAAI,CAAC2B,kBAAkB,CAACR,QAAQ,EAAE,IAAI,CAAC;MAChD;MACA;MACA,IAAI,IAAI,CAACrD,OAAO,CAAC8D,OAAO,KAAK,KAAK,IAC9BP,aAAa,KACZF,QAAQ,IAAIC,MAAM,CAAC,EAAE;QACtB,IAAI,CAACf,gBAAgB,CAAC,WAAW,EAAE,CAAC;UAAEwB,KAAK;UAAEC,gBAAgB;UAAEC,wBAAwB;UAAEX,MAAM,EAAEY;QAAW,CAAC,KAAK;UAC9G,IAAI,IAAI,CAACC,sBAAsB,CAAC,CAAC,EAAE;YAC/B,IAAI,CAACC,MAAM,GAAGC,SAAS;YACvB,IAAI,CAACC,cAAc,GAAGD,SAAS;YAC/B;UACJ;UACA;UACA,MAAME,gBAAgB,GAAG,IAAI,CAACvE,OAAO,CAACwE,UAAU,IAC5CjB,aAAa,CAACkB,oBAAoB,CAAC,CAAC,IACpCC,uBAAuB;UAC3B,MAAM;YAAEC,sBAAsB;YAAEC;UAA2B,CAAC,GAAGrB,aAAa,CAACsB,QAAQ,CAAC,CAAC;UACvF;AACpB;AACA;AACA;UACoB,MAAMC,aAAa,GAAG,CAAC,IAAI,CAACC,YAAY,IACpC,CAAC/H,gBAAgB,CAAC,IAAI,CAAC+H,YAAY,EAAEb,SAAS,CAAC,IAC/CD,wBAAwB;UAC5B;AACpB;AACA;AACA;AACA;UACoB,MAAMe,4BAA4B,GAAG,CAAChB,gBAAgB,IAAIC,wBAAwB;UAClF,IAAI,IAAI,CAACjE,OAAO,CAACiF,UAAU,IACtB,IAAI,CAACC,UAAU,IAAI,IAAI,CAACA,UAAU,CAAC9B,QAAS,IAC7C4B,4BAA4B,IAC3BhB,gBAAgB,KACZc,aAAa,IAAI,CAAC,IAAI,CAACK,gBAAgB,CAAE,EAAE;YAChD,IAAI,IAAI,CAACD,UAAU,EAAE;cACjB,IAAI,CAACE,YAAY,GAAG,IAAI,CAACF,UAAU;cACnC,IAAI,CAACE,YAAY,CAACA,YAAY,GAAGf,SAAS;YAC9C;YACA,IAAI,CAACgB,kBAAkB,CAACtB,KAAK,EAAEiB,4BAA4B,CAAC;YAC5D,MAAMM,gBAAgB,GAAG;cACrB,GAAGvI,kBAAkB,CAACwH,gBAAgB,EAAE,QAAQ,CAAC;cACjDgB,MAAM,EAAEZ,sBAAsB;cAC9Ba,UAAU,EAAEZ;YAChB,CAAC;YACD,IAAIrB,aAAa,CAACkC,kBAAkB,IAChC,IAAI,CAACzF,OAAO,CAACiF,UAAU,EAAE;cACzBK,gBAAgB,CAACxH,KAAK,GAAG,CAAC;cAC1BwH,gBAAgB,CAACvG,IAAI,GAAG,KAAK;YACjC;YACA,IAAI,CAAC2G,cAAc,CAACJ,gBAAgB,CAAC;UACzC,CAAC,MACI;YACD;AACxB;AACA;AACA;AACA;YACwB,IAAI,CAACtB,gBAAgB,EAAE;cACnBJ,eAAe,CAAC,IAAI,CAAC;YACzB;YACA,IAAI,IAAI,CAAC+B,MAAM,CAAC,CAAC,IAAI,IAAI,CAAC3F,OAAO,CAAC4F,cAAc,EAAE;cAC9C,IAAI,CAAC5F,OAAO,CAAC4F,cAAc,CAAC,CAAC;YACjC;UACJ;UACA,IAAI,CAACb,YAAY,GAAGb,SAAS;QACjC,CAAC,CAAC;MACN;IACJ;IACA2B,OAAOA,CAAA,EAAG;MACN,IAAI,CAAC7F,OAAO,CAACqD,QAAQ,IAAI,IAAI,CAACyC,UAAU,CAAC,CAAC;MAC1C,IAAI,CAAC5D,IAAI,CAACV,KAAK,CAACuE,MAAM,CAAC,IAAI,CAAC;MAC5B,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC7BD,KAAK,IAAIA,KAAK,CAACD,MAAM,CAAC,IAAI,CAAC;MAC3B,IAAI,CAACnG,MAAM,IAAI,IAAI,CAACA,MAAM,CAACE,QAAQ,CAACoG,MAAM,CAAC,IAAI,CAAC;MAChD,IAAI,CAAC9C,QAAQ,GAAGiB,SAAS;MACzBjG,WAAW,CAAC,IAAI,CAACmD,gBAAgB,CAAC;IACtC;IACA;IACA4E,WAAWA,CAAA,EAAG;MACV,IAAI,CAAC5F,qBAAqB,GAAG,IAAI;IACrC;IACA6F,aAAaA,CAAA,EAAG;MACZ,IAAI,CAAC7F,qBAAqB,GAAG,KAAK;IACtC;IACA8F,eAAeA,CAAA,EAAG;MACd,OAAO,IAAI,CAAC9F,qBAAqB,IAAI,IAAI,CAACC,qBAAqB;IACnE;IACA2D,sBAAsBA,CAAA,EAAG;MACrB,OAAQ,IAAI,CAACjE,kBAAkB,IAC1B,IAAI,CAACN,MAAM,IAAI,IAAI,CAACA,MAAM,CAACuE,sBAAsB,CAAC,CAAE,IACrD,KAAK;IACb;IACA;IACAmC,WAAWA,CAAA,EAAG;MACV,IAAI,IAAI,CAACD,eAAe,CAAC,CAAC,EACtB;MACJ,IAAI,CAAC5F,UAAU,GAAG,IAAI;MACtB,IAAI,CAACe,KAAK,IAAI,IAAI,CAACA,KAAK,CAACC,OAAO,CAAC8E,aAAa,CAAC;MAC/C,IAAI,CAAC1G,WAAW,EAAE;IACtB;IACA2G,oBAAoBA,CAAA,EAAG;MACnB,MAAM;QAAEjD;MAAc,CAAC,GAAG,IAAI,CAACvD,OAAO;MACtC,OAAOuD,aAAa,IAAIA,aAAa,CAACsB,QAAQ,CAAC,CAAC,CAAC4B,iBAAiB;IACtE;IACAX,UAAUA,CAACY,qBAAqB,GAAG,IAAI,EAAE;MACrC,IAAI,CAACxE,IAAI,CAAChB,eAAe,GAAG,IAAI;MAChC,IAAI,IAAI,CAACgB,IAAI,CAACmE,eAAe,CAAC,CAAC,EAAE;QAC7B,IAAI,CAACrG,OAAO,CAAC4F,cAAc,IAAI,IAAI,CAAC5F,OAAO,CAAC4F,cAAc,CAAC,CAAC;QAC5D;MACJ;MACA,CAAC,IAAI,CAAC1D,IAAI,CAACzB,UAAU,IAAI,IAAI,CAACyB,IAAI,CAACoE,WAAW,CAAC,CAAC;MAChD,IAAI,IAAI,CAACnG,aAAa,EAClB;MACJ,IAAI,CAACA,aAAa,GAAG,IAAI;MACzB,KAAK,IAAIkC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACF,IAAI,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;QACvC,MAAMsE,IAAI,GAAG,IAAI,CAACxE,IAAI,CAACE,CAAC,CAAC;QACzBsE,IAAI,CAAC/F,oBAAoB,GAAG,IAAI;QAChC+F,IAAI,CAACC,YAAY,CAAC,UAAU,CAAC;QAC7B,IAAID,IAAI,CAAC3G,OAAO,CAACiF,UAAU,EAAE;UACzB0B,IAAI,CAACb,UAAU,CAAC,KAAK,CAAC;QAC1B;MACJ;MACA,MAAM;QAAEzC,QAAQ;QAAEC;MAAO,CAAC,GAAG,IAAI,CAACtD,OAAO;MACzC,IAAIqD,QAAQ,KAAKgB,SAAS,IAAI,CAACf,MAAM,EACjC;MACJ,MAAMmD,iBAAiB,GAAG,IAAI,CAACD,oBAAoB,CAAC,CAAC;MACrD,IAAI,CAACK,0BAA0B,GAAGJ,iBAAiB,GAC7CA,iBAAiB,CAAC,IAAI,CAAC9G,YAAY,EAAE,EAAE,CAAC,GACxC0E,SAAS;MACf,IAAI,CAACyC,cAAc,CAAC,CAAC;MACrBJ,qBAAqB,IAAI,IAAI,CAAC5D,eAAe,CAAC,YAAY,CAAC;IAC/D;IACAiE,MAAMA,CAAA,EAAG;MACL,IAAI,CAAC5F,eAAe,GAAG,KAAK;MAC5B,MAAM6F,gBAAgB,GAAG,IAAI,CAACX,eAAe,CAAC,CAAC;MAC/C;MACA;MACA;MACA,IAAIW,gBAAgB,EAAE;QAClB,IAAI,CAACZ,aAAa,CAAC,CAAC;QACpB,IAAI,CAAC9E,iBAAiB,CAAC,CAAC;QACxB,IAAI,CAACE,KAAK,CAACC,OAAO,CAACwF,iBAAiB,CAAC;QACrC;MACJ;MACA,IAAI,CAAC,IAAI,CAACxG,UAAU,EAAE;QAClB,IAAI,CAACe,KAAK,CAACC,OAAO,CAACyF,kBAAkB,CAAC;MAC1C;MACA,IAAI,CAACzG,UAAU,GAAG,KAAK;MACvB;AACZ;AACA;MACY,IAAI,CAACe,KAAK,CAACC,OAAO,CAAC0F,mBAAmB,CAAC;MACvC;AACZ;AACA;MACY;MACA,IAAI,CAAC3F,KAAK,CAACC,OAAO,CAAC2F,YAAY,CAAC;MAChC;AACZ;AACA;MACY;MACA,IAAI,CAAC5F,KAAK,CAACC,OAAO,CAAC4F,kBAAkB,CAAC;MACtC,IAAI,CAAC/F,iBAAiB,CAAC,CAAC;MACxB;AACZ;AACA;AACA;AACA;MACY,MAAMgG,GAAG,GAAGC,WAAW,CAACD,GAAG,CAAC,CAAC;MAC7BjJ,SAAS,CAAC0F,KAAK,GAAG5F,KAAK,CAAC,CAAC,EAAE,IAAI,GAAG,EAAE,EAAEmJ,GAAG,GAAGjJ,SAAS,CAACmJ,SAAS,CAAC;MAChEnJ,SAAS,CAACmJ,SAAS,GAAGF,GAAG;MACzBjJ,SAAS,CAACoJ,YAAY,GAAG,IAAI;MAC7BnJ,KAAK,CAACyI,MAAM,CAACW,OAAO,CAACrJ,SAAS,CAAC;MAC/BC,KAAK,CAACqJ,SAAS,CAACD,OAAO,CAACrJ,SAAS,CAAC;MAClCC,KAAK,CAACsJ,MAAM,CAACF,OAAO,CAACrJ,SAAS,CAAC;MAC/BA,SAAS,CAACoJ,YAAY,GAAG,KAAK;IAClC;IACAI,SAASA,CAAA,EAAG;MACR,IAAI,CAAC,IAAI,CAAC1G,eAAe,EAAE;QACvB,IAAI,CAACA,eAAe,GAAG,IAAI;QAC3B2G,cAAc,CAAC,MAAM,IAAI,CAACf,MAAM,CAAC,CAAC,CAAC;MACvC;IACJ;IACAzF,iBAAiBA,CAAA,EAAG;MAChB,IAAI,CAACE,KAAK,CAACC,OAAO,CAACsG,aAAa,CAAC;MACjC,IAAI,CAAC9F,WAAW,CAACR,OAAO,CAACuG,mBAAmB,CAAC;IACjD;IACAC,wBAAwBA,CAAA,EAAG;MACvB,IAAI,CAAC,IAAI,CAAC7G,yBAAyB,EAAE;QACjC,IAAI,CAACA,yBAAyB,GAAG,IAAI;QACrC7C,KAAK,CAACoJ,SAAS,CAAC,IAAI,CAACpG,gBAAgB,EAAE,KAAK,EAAE,IAAI,CAAC;MACvD;IACJ;IACA2G,yBAAyBA,CAAA,EAAG;MACxB;AACZ;AACA;AACA;AACA;MACY3J,KAAK,CAAC4J,UAAU,CAAC,MAAM;QACnB,IAAI,IAAI,CAAChI,aAAa,EAAE;UACpB,IAAI,CAAC+B,IAAI,CAAC2F,SAAS,CAAC,CAAC;QACzB,CAAC,MACI;UACD,IAAI,CAAC3F,IAAI,CAACb,iBAAiB,CAAC,CAAC;QACjC;MACJ,CAAC,CAAC;IACN;IACA;AACR;AACA;IACQyF,cAAcA,CAAA,EAAG;MACb,IAAI,IAAI,CAACsB,QAAQ,IAAI,CAAC,IAAI,CAAChF,QAAQ,EAC/B;MACJ,IAAI,CAACgF,QAAQ,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC;IAClC;IACAjB,YAAYA,CAAA,EAAG;MACX,IAAI,CAAC,IAAI,CAAChE,QAAQ,EACd;MACJ;MACA,IAAI,CAACwD,YAAY,CAAC,CAAC;MACnB,IAAI,EAAE,IAAI,CAAC5G,OAAO,CAACsI,mBAAmB,IAAI,IAAI,CAAC3C,MAAM,CAAC,CAAC,CAAC,IACpD,CAAC,IAAI,CAACxF,aAAa,EAAE;QACrB;MACJ;MACA;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,IAAI,IAAI,CAAC+E,UAAU,IAAI,CAAC,IAAI,CAACA,UAAU,CAAC9B,QAAQ,EAAE;QAC9C,KAAK,IAAIf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACF,IAAI,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;UACvC,MAAMsE,IAAI,GAAG,IAAI,CAACxE,IAAI,CAACE,CAAC,CAAC;UACzBsE,IAAI,CAACC,YAAY,CAAC,CAAC;QACvB;MACJ;MACA,MAAM2B,UAAU,GAAG,IAAI,CAACjF,MAAM;MAC9B,IAAI,CAACA,MAAM,GAAG,IAAI,CAAC+E,OAAO,CAAC,KAAK,CAAC;MACjC,IAAI,CAACG,eAAe,GAAG3L,SAAS,CAAC,CAAC;MAClC,IAAI,CAACsD,aAAa,GAAG,KAAK;MAC1B,IAAI,CAACsI,eAAe,GAAGpE,SAAS;MAChC,IAAI,CAACvB,eAAe,CAAC,SAAS,EAAE,IAAI,CAACQ,MAAM,CAACoF,SAAS,CAAC;MACtD,MAAM;QAAEnF;MAAc,CAAC,GAAG,IAAI,CAACvD,OAAO;MACtCuD,aAAa,IACTA,aAAa,CAACN,MAAM,CAAC,eAAe,EAAE,IAAI,CAACK,MAAM,CAACoF,SAAS,EAAEH,UAAU,GAAGA,UAAU,CAACG,SAAS,GAAGrE,SAAS,CAAC;IACnH;IACAuC,YAAYA,CAAC+B,KAAK,GAAG,SAAS,EAAE;MAC5B,IAAIC,gBAAgB,GAAGC,OAAO,CAAC,IAAI,CAAC7I,OAAO,CAAC8I,YAAY,IAAI,IAAI,CAAC1F,QAAQ,CAAC;MAC1E,IAAI,IAAI,CAAC2F,MAAM,IACX,IAAI,CAACA,MAAM,CAAClJ,WAAW,KAAK,IAAI,CAACqC,IAAI,CAACrC,WAAW,IACjD,IAAI,CAACkJ,MAAM,CAACJ,KAAK,KAAKA,KAAK,EAAE;QAC7BC,gBAAgB,GAAG,KAAK;MAC5B;MACA,IAAIA,gBAAgB,EAAE;QAClB,IAAI,CAACG,MAAM,GAAG;UACVlJ,WAAW,EAAE,IAAI,CAACqC,IAAI,CAACrC,WAAW;UAClC8I,KAAK;UACLK,MAAM,EAAEzJ,iBAAiB,CAAC,IAAI,CAAC6D,QAAQ,CAAC;UACxC6F,MAAM,EAAE3J,aAAa,CAAC,IAAI,CAAC8D,QAAQ;QACvC,CAAC;MACL;IACJ;IACA5D,cAAcA,CAAA,EAAG;MACb,IAAI,CAACA,cAAc,EACf;MACJ,MAAM0J,gBAAgB,GAAG,IAAI,CAAC/I,aAAa,IAAI,IAAI,CAACS,oBAAoB;MACxE,MAAMuI,aAAa,GAAG,IAAI,CAACV,eAAe,IAAI,CAACxL,WAAW,CAAC,IAAI,CAACwL,eAAe,CAAC;MAChF,MAAMhC,iBAAiB,GAAG,IAAI,CAACD,oBAAoB,CAAC,CAAC;MACrD,MAAM4C,sBAAsB,GAAG3C,iBAAiB,GAC1CA,iBAAiB,CAAC,IAAI,CAAC9G,YAAY,EAAE,EAAE,CAAC,GACxC0E,SAAS;MACf,MAAMgF,2BAA2B,GAAGD,sBAAsB,KAAK,IAAI,CAACvC,0BAA0B;MAC9F,IAAIqC,gBAAgB,KACfC,aAAa,IACV3L,YAAY,CAAC,IAAI,CAACmC,YAAY,CAAC,IAC/B0J,2BAA2B,CAAC,EAAE;QAClC7J,cAAc,CAAC,IAAI,CAAC4D,QAAQ,EAAEgG,sBAAsB,CAAC;QACrD,IAAI,CAACxI,oBAAoB,GAAG,KAAK;QACjC,IAAI,CAAC0I,cAAc,CAAC,CAAC;MACzB;IACJ;IACAjB,OAAOA,CAACkB,eAAe,GAAG,IAAI,EAAE;MAC5B,MAAMC,OAAO,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;MACrC,IAAIf,SAAS,GAAG,IAAI,CAACgB,mBAAmB,CAACF,OAAO,CAAC;MACjD;AACZ;AACA;AACA;AACA;MACY,IAAID,eAAe,EAAE;QACjBb,SAAS,GAAG,IAAI,CAACa,eAAe,CAACb,SAAS,CAAC;MAC/C;MACAiB,QAAQ,CAACjB,SAAS,CAAC;MACnB,OAAO;QACH7I,WAAW,EAAE,IAAI,CAACqC,IAAI,CAACrC,WAAW;QAClC+J,WAAW,EAAEJ,OAAO;QACpBd,SAAS;QACT/I,YAAY,EAAE,CAAC,CAAC;QAChBkK,MAAM,EAAE,IAAI,CAAChL;MACjB,CAAC;IACL;IACA4K,cAAcA,CAAA,EAAG;MACb,MAAM;QAAElG;MAAc,CAAC,GAAG,IAAI,CAACvD,OAAO;MACtC,IAAI,CAACuD,aAAa,EACd,OAAO1G,SAAS,CAAC,CAAC;MACtB,MAAMiN,GAAG,GAAGvG,aAAa,CAACwG,kBAAkB,CAAC,CAAC;MAC9C;MACA,MAAM;QAAEhB;MAAO,CAAC,GAAG,IAAI,CAAC7G,IAAI;MAC5B,IAAI6G,MAAM,EAAE;QACR5M,aAAa,CAAC2N,GAAG,CAAChJ,CAAC,EAAEiI,MAAM,CAACE,MAAM,CAACnI,CAAC,CAAC;QACrC3E,aAAa,CAAC2N,GAAG,CAAC/I,CAAC,EAAEgI,MAAM,CAACE,MAAM,CAAClI,CAAC,CAAC;MACzC;MACA,OAAO+I,GAAG;IACd;IACAJ,mBAAmBA,CAACI,GAAG,EAAE;MACrB,MAAME,gBAAgB,GAAGnN,SAAS,CAAC,CAAC;MACpCX,WAAW,CAAC8N,gBAAgB,EAAEF,GAAG,CAAC;MAClC;AACZ;AACA;AACA;MACY,KAAK,IAAIzH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACF,IAAI,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;QACvC,MAAMsE,IAAI,GAAG,IAAI,CAACxE,IAAI,CAACE,CAAC,CAAC;QACzB,MAAM;UAAE0G,MAAM;UAAE/I;QAAQ,CAAC,GAAG2G,IAAI;QAChC,IAAIA,IAAI,KAAK,IAAI,CAACzE,IAAI,IAAI6G,MAAM,IAAI/I,OAAO,CAAC8I,YAAY,EAAE;UACtD;AACpB;AACA;AACA;UACoB,IAAIC,MAAM,CAACC,MAAM,EAAE;YACf9M,WAAW,CAAC8N,gBAAgB,EAAEF,GAAG,CAAC;YAClC,MAAM;cAAEf,MAAM,EAAEkB;YAAW,CAAC,GAAG,IAAI,CAAC/H,IAAI;YACxC;AACxB;AACA;AACA;YACwB,IAAI+H,UAAU,EAAE;cACZ9N,aAAa,CAAC6N,gBAAgB,CAAClJ,CAAC,EAAE,CAACmJ,UAAU,CAAChB,MAAM,CAACnI,CAAC,CAAC;cACvD3E,aAAa,CAAC6N,gBAAgB,CAACjJ,CAAC,EAAE,CAACkJ,UAAU,CAAChB,MAAM,CAAClI,CAAC,CAAC;YAC3D;UACJ;UACA5E,aAAa,CAAC6N,gBAAgB,CAAClJ,CAAC,EAAEiI,MAAM,CAACE,MAAM,CAACnI,CAAC,CAAC;UAClD3E,aAAa,CAAC6N,gBAAgB,CAACjJ,CAAC,EAAEgI,MAAM,CAACE,MAAM,CAAClI,CAAC,CAAC;QACtD;MACJ;MACA,OAAOiJ,gBAAgB;IAC3B;IACAE,cAAcA,CAACJ,GAAG,EAAEK,aAAa,GAAG,KAAK,EAAE;MACvC,MAAMC,cAAc,GAAGvN,SAAS,CAAC,CAAC;MAClCX,WAAW,CAACkO,cAAc,EAAEN,GAAG,CAAC;MAChC,KAAK,IAAIzH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACF,IAAI,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;QACvC,MAAMsE,IAAI,GAAG,IAAI,CAACxE,IAAI,CAACE,CAAC,CAAC;QACzB,IAAI,CAAC8H,aAAa,IACdxD,IAAI,CAAC3G,OAAO,CAAC8I,YAAY,IACzBnC,IAAI,CAACoC,MAAM,IACXpC,IAAI,KAAKA,IAAI,CAACzE,IAAI,EAAE;UACpB9F,YAAY,CAACgO,cAAc,EAAE;YACzBtJ,CAAC,EAAE,CAAC6F,IAAI,CAACoC,MAAM,CAACE,MAAM,CAACnI,CAAC;YACxBC,CAAC,EAAE,CAAC4F,IAAI,CAACoC,MAAM,CAACE,MAAM,CAAClI;UAC3B,CAAC,CAAC;QACN;QACA,IAAI,CAACvD,YAAY,CAACmJ,IAAI,CAAChH,YAAY,CAAC,EAChC;QACJvD,YAAY,CAACgO,cAAc,EAAEzD,IAAI,CAAChH,YAAY,CAAC;MACnD;MACA,IAAInC,YAAY,CAAC,IAAI,CAACmC,YAAY,CAAC,EAAE;QACjCvD,YAAY,CAACgO,cAAc,EAAE,IAAI,CAACzK,YAAY,CAAC;MACnD;MACA,OAAOyK,cAAc;IACzB;IACAb,eAAeA,CAACO,GAAG,EAAE;MACjB,MAAMO,mBAAmB,GAAGxN,SAAS,CAAC,CAAC;MACvCX,WAAW,CAACmO,mBAAmB,EAAEP,GAAG,CAAC;MACrC,KAAK,IAAIzH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACF,IAAI,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;QACvC,MAAMsE,IAAI,GAAG,IAAI,CAACxE,IAAI,CAACE,CAAC,CAAC;QACzB,IAAI,CAACsE,IAAI,CAACvD,QAAQ,EACd;QACJ,IAAI,CAAC5F,YAAY,CAACmJ,IAAI,CAAChH,YAAY,CAAC,EAChC;QACJlC,QAAQ,CAACkJ,IAAI,CAAChH,YAAY,CAAC,IAAIgH,IAAI,CAACG,cAAc,CAAC,CAAC;QACpD,MAAMwD,SAAS,GAAGzN,SAAS,CAAC,CAAC;QAC7B,MAAM0N,OAAO,GAAG5D,IAAI,CAAC8C,cAAc,CAAC,CAAC;QACrCvN,WAAW,CAACoO,SAAS,EAAEC,OAAO,CAAC;QAC/B3N,mBAAmB,CAACyN,mBAAmB,EAAE1D,IAAI,CAAChH,YAAY,EAAEgH,IAAI,CAACyB,QAAQ,GAAGzB,IAAI,CAACyB,QAAQ,CAACM,SAAS,GAAGrE,SAAS,EAAEiG,SAAS,CAAC;MAC/H;MACA,IAAI9M,YAAY,CAAC,IAAI,CAACmC,YAAY,CAAC,EAAE;QACjC/C,mBAAmB,CAACyN,mBAAmB,EAAE,IAAI,CAAC1K,YAAY,CAAC;MAC/D;MACA,OAAO0K,mBAAmB;IAC9B;IACAG,cAAcA,CAACzG,KAAK,EAAE;MAClB,IAAI,CAAC0G,WAAW,GAAG1G,KAAK;MACxB,IAAI,CAAC7B,IAAI,CAAC+F,wBAAwB,CAAC,CAAC;MACpC,IAAI,CAAC7H,iBAAiB,GAAG,IAAI;IACjC;IACAsK,UAAUA,CAAC1K,OAAO,EAAE;MAChB,IAAI,CAACA,OAAO,GAAG;QACX,GAAG,IAAI,CAACA,OAAO;QACf,GAAGA,OAAO;QACV2K,SAAS,EAAE3K,OAAO,CAAC2K,SAAS,KAAKtG,SAAS,GAAGrE,OAAO,CAAC2K,SAAS,GAAG;MACrE,CAAC;IACL;IACA1D,iBAAiBA,CAAA,EAAG;MAChB,IAAI,CAAC8B,MAAM,GAAG1E,SAAS;MACvB,IAAI,CAACf,MAAM,GAAGe,SAAS;MACvB,IAAI,CAAC+D,QAAQ,GAAG/D,SAAS;MACzB,IAAI,CAACwC,0BAA0B,GAAGxC,SAAS;MAC3C,IAAI,CAACoG,WAAW,GAAGpG,SAAS;MAC5B,IAAI,CAACD,MAAM,GAAGC,SAAS;MACvB,IAAI,CAAClE,aAAa,GAAG,KAAK;IAC9B;IACAyK,kCAAkCA,CAAA,EAAG;MACjC,IAAI,CAAC,IAAI,CAACC,cAAc,EACpB;MACJ;AACZ;AACA;AACA;AACA;AACA;MACY,IAAI,IAAI,CAACA,cAAc,CAACC,wBAAwB,KAC5CzM,SAAS,CAACmJ,SAAS,EAAE;QACrB,IAAI,CAACqD,cAAc,CAAClJ,kBAAkB,CAAC,IAAI,CAAC;MAChD;IACJ;IACAA,kBAAkBA,CAACoJ,kBAAkB,GAAG,KAAK,EAAE;MAC3C,IAAIC,EAAE;MACN;AACZ;AACA;AACA;AACA;MACY,MAAMC,IAAI,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC;MAC3B,IAAI,CAAC9K,iBAAiB,KAAK,IAAI,CAACA,iBAAiB,GAAG6K,IAAI,CAAC7K,iBAAiB,CAAC;MAC3E,IAAI,CAACE,gBAAgB,KAAK,IAAI,CAACA,gBAAgB,GAAG2K,IAAI,CAAC3K,gBAAgB,CAAC;MACxE,IAAI,CAACD,uBAAuB,KAAK,IAAI,CAACA,uBAAuB,GAAG4K,IAAI,CAAC5K,uBAAuB,CAAC;MAC7F,MAAM8K,QAAQ,GAAGtC,OAAO,CAAC,IAAI,CAACzD,YAAY,CAAC,IAAI,IAAI,KAAK6F,IAAI;MAC5D;AACZ;AACA;AACA;MACY,MAAMG,OAAO,GAAG,EAAEL,kBAAkB,IAC/BI,QAAQ,IAAI,IAAI,CAAC9K,uBAAwB,IAC1C,IAAI,CAACD,iBAAiB,KACrB,CAAC4K,EAAE,GAAG,IAAI,CAACpL,MAAM,MAAM,IAAI,IAAIoL,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC5K,iBAAiB,CAAC,IAC9E,IAAI,CAACiL,8BAA8B,CAAC;MACxC,IAAID,OAAO,EACP;MACJ,MAAM;QAAE9H,MAAM;QAAED;MAAS,CAAC,GAAG,IAAI,CAACrD,OAAO;MACzC;AACZ;AACA;MACY,IAAI,CAAC,IAAI,CAACsD,MAAM,IAAI,EAAEA,MAAM,IAAID,QAAQ,CAAC,EACrC;MACJ,IAAI,CAACyH,wBAAwB,GAAGzM,SAAS,CAACmJ,SAAS;MACnD;AACZ;AACA;AACA;AACA;MACY;MACA,IAAI,CAAC,IAAI,CAACiD,WAAW,IAAI,CAAC,IAAI,CAACnG,cAAc,EAAE;QAC3C;QACA,MAAMuG,cAAc,GAAG,IAAI,CAACS,0BAA0B,CAAC,CAAC;QACxD,IAAIT,cAAc,IACdA,cAAc,CAACvH,MAAM,IACrB,IAAI,CAACtB,iBAAiB,KAAK,CAAC,EAAE;UAC9B,IAAI,CAAC6I,cAAc,GAAGA,cAAc;UACpC,IAAI,CAACD,kCAAkC,CAAC,CAAC;UACzC,IAAI,CAACtG,cAAc,GAAGzH,SAAS,CAAC,CAAC;UACjC,IAAI,CAAC0O,oBAAoB,GAAG1O,SAAS,CAAC,CAAC;UACvCN,oBAAoB,CAAC,IAAI,CAACgP,oBAAoB,EAAE,IAAI,CAACjI,MAAM,CAACoF,SAAS,EAAEmC,cAAc,CAACvH,MAAM,CAACoF,SAAS,CAAC;UACvGxM,WAAW,CAAC,IAAI,CAACoI,cAAc,EAAE,IAAI,CAACiH,oBAAoB,CAAC;QAC/D,CAAC,MACI;UACD,IAAI,CAACV,cAAc,GAAG,IAAI,CAACvG,cAAc,GAAGD,SAAS;QACzD;MACJ;MACA;AACZ;AACA;AACA;MACY,IAAI,CAAC,IAAI,CAACC,cAAc,IAAI,CAAC,IAAI,CAACmG,WAAW,EACzC;MACJ;AACZ;AACA;MACY,IAAI,CAAC,IAAI,CAACrG,MAAM,EAAE;QACd,IAAI,CAACA,MAAM,GAAGvH,SAAS,CAAC,CAAC;QACzB,IAAI,CAAC2O,oBAAoB,GAAG3O,SAAS,CAAC,CAAC;MAC3C;MACA;AACZ;AACA;MACY,IAAI,IAAI,CAACyH,cAAc,IACnB,IAAI,CAACiH,oBAAoB,IACzB,IAAI,CAACV,cAAc,IACnB,IAAI,CAACA,cAAc,CAACzG,MAAM,EAAE;QAC5B,IAAI,CAACwG,kCAAkC,CAAC,CAAC;QACzCpO,eAAe,CAAC,IAAI,CAAC4H,MAAM,EAAE,IAAI,CAACE,cAAc,EAAE,IAAI,CAACuG,cAAc,CAACzG,MAAM,CAAC;QAC7E;AAChB;AACA;MACY,CAAC,MACI,IAAI,IAAI,CAACqG,WAAW,EAAE;QACvB,IAAI5B,OAAO,CAAC,IAAI,CAACzD,YAAY,CAAC,EAAE;UAC5B;UACA,IAAI,CAAChB,MAAM,GAAG,IAAI,CAAC8F,cAAc,CAAC,IAAI,CAAC5G,MAAM,CAACoF,SAAS,CAAC;QAC5D,CAAC,MACI;UACDxM,WAAW,CAAC,IAAI,CAACkI,MAAM,EAAE,IAAI,CAACd,MAAM,CAACoF,SAAS,CAAC;QACnD;QACArM,aAAa,CAAC,IAAI,CAAC+H,MAAM,EAAE,IAAI,CAACqG,WAAW,CAAC;MAChD,CAAC,MACI;QACD;AAChB;AACA;QACgBvO,WAAW,CAAC,IAAI,CAACkI,MAAM,EAAE,IAAI,CAACd,MAAM,CAACoF,SAAS,CAAC;MACnD;MACA;AACZ;AACA;MACY,IAAI,IAAI,CAAC2C,8BAA8B,EAAE;QACrC,IAAI,CAACA,8BAA8B,GAAG,KAAK;QAC3C,MAAMR,cAAc,GAAG,IAAI,CAACS,0BAA0B,CAAC,CAAC;QACxD,IAAIT,cAAc,IACdhC,OAAO,CAACgC,cAAc,CAACzF,YAAY,CAAC,KAChCyD,OAAO,CAAC,IAAI,CAACzD,YAAY,CAAC,IAC9B,CAACyF,cAAc,CAAC7K,OAAO,CAAC8I,YAAY,IACpC+B,cAAc,CAACzG,MAAM,IACrB,IAAI,CAACpC,iBAAiB,KAAK,CAAC,EAAE;UAC9B,IAAI,CAAC6I,cAAc,GAAGA,cAAc;UACpC,IAAI,CAACD,kCAAkC,CAAC,CAAC;UACzC,IAAI,CAACtG,cAAc,GAAGzH,SAAS,CAAC,CAAC;UACjC,IAAI,CAAC0O,oBAAoB,GAAG1O,SAAS,CAAC,CAAC;UACvCN,oBAAoB,CAAC,IAAI,CAACgP,oBAAoB,EAAE,IAAI,CAACnH,MAAM,EAAEyG,cAAc,CAACzG,MAAM,CAAC;UACnFlI,WAAW,CAAC,IAAI,CAACoI,cAAc,EAAE,IAAI,CAACiH,oBAAoB,CAAC;QAC/D,CAAC,MACI;UACD,IAAI,CAACV,cAAc,GAAG,IAAI,CAACvG,cAAc,GAAGD,SAAS;QACzD;MACJ;MACA;AACZ;AACA;MACYvF,mBAAmB,CAACG,oBAAoB,EAAE;IAC9C;IACAqM,0BAA0BA,CAAA,EAAG;MACzB,IAAI,CAAC,IAAI,CAAC1L,MAAM,IACZnC,QAAQ,CAAC,IAAI,CAACmC,MAAM,CAACD,YAAY,CAAC,IAClCjC,cAAc,CAAC,IAAI,CAACkC,MAAM,CAACD,YAAY,CAAC,EAAE;QAC1C,OAAO0E,SAAS;MACpB;MACA,IAAI,IAAI,CAACzE,MAAM,CAAC6L,YAAY,CAAC,CAAC,EAAE;QAC5B,OAAO,IAAI,CAAC7L,MAAM;MACtB,CAAC,MACI;QACD,OAAO,IAAI,CAACA,MAAM,CAAC0L,0BAA0B,CAAC,CAAC;MACnD;IACJ;IACAG,YAAYA,CAAA,EAAG;MACX,OAAO5C,OAAO,CAAC,CAAC,IAAI,CAACvE,cAAc,IAC/B,IAAI,CAACmG,WAAW,IAChB,IAAI,CAACzK,OAAO,CAACiF,UAAU,KACvB,IAAI,CAAC3B,MAAM,CAAC;IACpB;IACA1B,cAAcA,CAAA,EAAG;MACb,IAAIoJ,EAAE;MACN,MAAMC,IAAI,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC;MAC3B,MAAMC,QAAQ,GAAGtC,OAAO,CAAC,IAAI,CAACzD,YAAY,CAAC,IAAI,IAAI,KAAK6F,IAAI;MAC5D,IAAIG,OAAO,GAAG,IAAI;MAClB;AACZ;AACA;AACA;MACY,IAAI,IAAI,CAAChL,iBAAiB,KAAK,CAAC4K,EAAE,GAAG,IAAI,CAACpL,MAAM,MAAM,IAAI,IAAIoL,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC5K,iBAAiB,CAAC,EAAE;QAC1GgL,OAAO,GAAG,KAAK;MACnB;MACA;AACZ;AACA;AACA;MACY,IAAID,QAAQ,KACP,IAAI,CAAC9K,uBAAuB,IAAI,IAAI,CAACC,gBAAgB,CAAC,EAAE;QACzD8K,OAAO,GAAG,KAAK;MACnB;MACA;AACZ;AACA;AACA;MACY,IAAI,IAAI,CAACN,wBAAwB,KAAKzM,SAAS,CAACmJ,SAAS,EAAE;QACvD4D,OAAO,GAAG,KAAK;MACnB;MACA,IAAIA,OAAO,EACP;MACJ,MAAM;QAAE9H,MAAM;QAAED;MAAS,CAAC,GAAG,IAAI,CAACrD,OAAO;MACzC;AACZ;AACA;AACA;MACY,IAAI,CAACC,eAAe,GAAG4I,OAAO,CAAE,IAAI,CAACjJ,MAAM,IAAI,IAAI,CAACA,MAAM,CAACK,eAAe,IACtE,IAAI,CAACkF,gBAAgB,IACrB,IAAI,CAACuG,gBAAgB,CAAC;MAC1B,IAAI,CAAC,IAAI,CAACzL,eAAe,EAAE;QACvB,IAAI,CAACwK,WAAW,GAAG,IAAI,CAACnG,cAAc,GAAGD,SAAS;MACtD;MACA,IAAI,CAAC,IAAI,CAACf,MAAM,IAAI,EAAEA,MAAM,IAAID,QAAQ,CAAC,EACrC;MACJ;AACZ;AACA;AACA;MACYnH,WAAW,CAAC,IAAI,CAACsM,eAAe,EAAE,IAAI,CAAClF,MAAM,CAACoF,SAAS,CAAC;MACxD;AACZ;AACA;MACY,MAAMiD,cAAc,GAAG,IAAI,CAAC9K,SAAS,CAACC,CAAC;MACvC,MAAM8K,cAAc,GAAG,IAAI,CAAC/K,SAAS,CAACE,CAAC;MACvC;AACZ;AACA;AACA;MACYzE,eAAe,CAAC,IAAI,CAACkM,eAAe,EAAE,IAAI,CAAC3H,SAAS,EAAE,IAAI,CAACsB,IAAI,EAAEgJ,QAAQ,CAAC;MAC1E;AACZ;AACA;AACA;MACY,IAAIF,IAAI,CAAC3H,MAAM,IACX,CAAC2H,IAAI,CAAC7G,MAAM,KACX,IAAI,CAACvD,SAAS,CAACC,CAAC,KAAK,CAAC,IAAI,IAAI,CAACD,SAAS,CAACE,CAAC,KAAK,CAAC,CAAC,EAAE;QACpDkK,IAAI,CAAC7G,MAAM,GAAG6G,IAAI,CAAC3H,MAAM,CAACoF,SAAS;MACvC;MACA,MAAM;QAAEtE;MAAO,CAAC,GAAG6G,IAAI;MACvB,IAAI,CAAC7G,MAAM,EAAE;QACT;AAChB;AACA;AACA;AACA;QACgB,IAAI,IAAI,CAACyH,mBAAmB,EAAE;UAC1B,IAAI,CAACpD,eAAe,GAAG3L,WAAW,CAAC,CAAC;UACpC,IAAI,CAAC+O,mBAAmB,GAAG,MAAM;UACjC,IAAI,CAACvC,cAAc,CAAC,CAAC;QACzB;QACA;MACJ;MACA,IAAI,CAAC,IAAI,CAACb,eAAe,EAAE;QACvB,IAAI,CAACA,eAAe,GAAG3L,WAAW,CAAC,CAAC;QACpC,IAAI,CAACgP,4BAA4B,GAAGhP,WAAW,CAAC,CAAC;MACrD;MACA,MAAMiP,uBAAuB,GAAG,IAAI,CAACF,mBAAmB;MACxD;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACYpP,YAAY,CAAC,IAAI,CAACgM,eAAe,EAAE,IAAI,CAACD,eAAe,EAAEpE,MAAM,EAAE,IAAI,CAACzE,YAAY,CAAC;MACnF,IAAI,CAACkM,mBAAmB,GAAGvO,wBAAwB,CAAC,IAAI,CAACmL,eAAe,EAAE,IAAI,CAAC5H,SAAS,CAAC;MACzF,IAAI,IAAI,CAACgL,mBAAmB,KAAKE,uBAAuB,IACpD,IAAI,CAAClL,SAAS,CAACC,CAAC,KAAK6K,cAAc,IACnC,IAAI,CAAC9K,SAAS,CAACE,CAAC,KAAK6K,cAAc,EAAE;QACrC,IAAI,CAAC9J,YAAY,GAAG,IAAI;QACxB,IAAI,CAACwH,cAAc,CAAC,CAAC;QACrB,IAAI,CAACxG,eAAe,CAAC,kBAAkB,EAAEsB,MAAM,CAAC;MACpD;MACA;AACZ;AACA;MACYtF,mBAAmB,CAACI,sBAAsB,EAAE;IAChD;IACA8M,IAAIA,CAAA,EAAG;MACH,IAAI,CAACjK,SAAS,GAAG,KAAK;MACtB;IACJ;IACAkK,IAAIA,CAAA,EAAG;MACH,IAAI,CAAClK,SAAS,GAAG,IAAI;MACrB;IACJ;IACAuH,cAAcA,CAAC4C,SAAS,GAAG,IAAI,EAAE;MAC7B,IAAI,CAAClM,OAAO,CAACsJ,cAAc,IAAI,IAAI,CAACtJ,OAAO,CAACsJ,cAAc,CAAC,CAAC;MAC5D,IAAI4C,SAAS,EAAE;QACX,MAAMlG,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;QAC7BD,KAAK,IAAIA,KAAK,CAACsD,cAAc,CAAC,CAAC;MACnC;MACA,IAAI,IAAI,CAAClE,YAAY,IAAI,CAAC,IAAI,CAACA,YAAY,CAAChC,QAAQ,EAAE;QAClD,IAAI,CAACgC,YAAY,GAAGf,SAAS;MACjC;IACJ;IACAgB,kBAAkBA,CAACtB,KAAK,EAAEiB,4BAA4B,GAAG,KAAK,EAAE;MAC5D,MAAMoD,QAAQ,GAAG,IAAI,CAACA,QAAQ;MAC9B,MAAM+D,oBAAoB,GAAG/D,QAAQ,GAC/BA,QAAQ,CAACzI,YAAY,GACrB,CAAC,CAAC;MACR,MAAMyM,WAAW,GAAG;QAAE,GAAG,IAAI,CAACzM;MAAa,CAAC;MAC5C,MAAM8K,WAAW,GAAG3N,WAAW,CAAC,CAAC;MACjC,IAAI,CAAC,IAAI,CAAC+N,cAAc,IACpB,CAAC,IAAI,CAACA,cAAc,CAAC7K,OAAO,CAACiF,UAAU,EAAE;QACzC,IAAI,CAACX,cAAc,GAAG,IAAI,CAACiH,oBAAoB,GAAGlH,SAAS;MAC/D;MACA,IAAI,CAACgH,8BAA8B,GAAG,CAACrG,4BAA4B;MACnE,MAAMqH,cAAc,GAAGxP,SAAS,CAAC,CAAC;MAClC,MAAMyP,cAAc,GAAGlE,QAAQ,GAAGA,QAAQ,CAACyB,MAAM,GAAGxF,SAAS;MAC7D,MAAMkI,YAAY,GAAG,IAAI,CAACjJ,MAAM,GAAG,IAAI,CAACA,MAAM,CAACuG,MAAM,GAAGxF,SAAS;MACjE,MAAMmI,uBAAuB,GAAGF,cAAc,KAAKC,YAAY;MAC/D,MAAMvG,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC7B,MAAMwG,YAAY,GAAG,CAACzG,KAAK,IAAIA,KAAK,CAAC0G,OAAO,CAACpK,MAAM,IAAI,CAAC;MACxD,MAAMqK,sBAAsB,GAAG9D,OAAO,CAAC2D,uBAAuB,IAC1D,CAACC,YAAY,IACb,IAAI,CAACzM,OAAO,CAAC2K,SAAS,KAAK,IAAI,IAC/B,CAAC,IAAI,CAACxI,IAAI,CAACyK,IAAI,CAACC,mBAAmB,CAAC,CAAC;MACzC,IAAI,CAAC7K,iBAAiB,GAAG,CAAC;MAC1B,IAAI8K,kBAAkB;MACtB,IAAI,CAACC,cAAc,GAAIC,MAAM,IAAK;QAC9B,MAAMC,QAAQ,GAAGD,MAAM,GAAG,IAAI;QAC9BE,YAAY,CAACzC,WAAW,CAAC3J,CAAC,EAAEiD,KAAK,CAACjD,CAAC,EAAEmM,QAAQ,CAAC;QAC9CC,YAAY,CAACzC,WAAW,CAAC1J,CAAC,EAAEgD,KAAK,CAAChD,CAAC,EAAEkM,QAAQ,CAAC;QAC9C,IAAI,CAACzC,cAAc,CAACC,WAAW,CAAC;QAChC,IAAI,IAAI,CAACnG,cAAc,IACnB,IAAI,CAACiH,oBAAoB,IACzB,IAAI,CAACjI,MAAM,IACX,IAAI,CAACuH,cAAc,IACnB,IAAI,CAACA,cAAc,CAACvH,MAAM,EAAE;UAC5B/G,oBAAoB,CAAC8P,cAAc,EAAE,IAAI,CAAC/I,MAAM,CAACoF,SAAS,EAAE,IAAI,CAACmC,cAAc,CAACvH,MAAM,CAACoF,SAAS,CAAC;UACjGyE,MAAM,CAAC,IAAI,CAAC7I,cAAc,EAAE,IAAI,CAACiH,oBAAoB,EAAEc,cAAc,EAAEY,QAAQ,CAAC;UAChF;AACpB;AACA;AACA;UACoB,IAAIH,kBAAkB,IAClB3P,SAAS,CAAC,IAAI,CAACmH,cAAc,EAAEwI,kBAAkB,CAAC,EAAE;YACpD,IAAI,CAAC1M,iBAAiB,GAAG,KAAK;UAClC;UACA,IAAI,CAAC0M,kBAAkB,EACnBA,kBAAkB,GAAGjQ,SAAS,CAAC,CAAC;UACpCX,WAAW,CAAC4Q,kBAAkB,EAAE,IAAI,CAACxI,cAAc,CAAC;QACxD;QACA,IAAIkI,uBAAuB,EAAE;UACzB,IAAI,CAACY,eAAe,GAAGhB,WAAW;UAClCnQ,SAAS,CAACmQ,WAAW,EAAED,oBAAoB,EAAE,IAAI,CAACxM,YAAY,EAAEsN,QAAQ,EAAEN,sBAAsB,EAAEF,YAAY,CAAC;QACnH;QACA,IAAI,CAACvK,IAAI,CAAC+F,wBAAwB,CAAC,CAAC;QACpC,IAAI,CAACqB,cAAc,CAAC,CAAC;QACrB,IAAI,CAACtH,iBAAiB,GAAGiL,QAAQ;MACrC,CAAC;MACD,IAAI,CAACF,cAAc,CAAC,IAAI,CAAC/M,OAAO,CAACiF,UAAU,GAAG,IAAI,GAAG,CAAC,CAAC;IAC3D;IACAS,cAAcA,CAAC1F,OAAO,EAAE;MACpB,IAAI,CAAC8C,eAAe,CAAC,gBAAgB,CAAC;MACtC,IAAI,CAACqC,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAACkI,IAAI,CAAC,CAAC;MACrD,IAAI,IAAI,CAACjI,YAAY,IAAI,IAAI,CAACA,YAAY,CAACD,gBAAgB,EAAE;QACzD,IAAI,CAACC,YAAY,CAACD,gBAAgB,CAACkI,IAAI,CAAC,CAAC;MAC7C;MACA,IAAI,IAAI,CAAC3B,gBAAgB,EAAE;QACvBtN,WAAW,CAAC,IAAI,CAACsN,gBAAgB,CAAC;QAClC,IAAI,CAACA,gBAAgB,GAAGrH,SAAS;MACrC;MACA;AACZ;AACA;AACA;AACA;MACY,IAAI,CAACqH,gBAAgB,GAAGnN,KAAK,CAACwI,MAAM,CAAC,MAAM;QACvClJ,qBAAqB,CAAC8F,sBAAsB,GAAG,IAAI;QACnD,IAAI,CAACwB,gBAAgB,GAAGjH,kBAAkB,CAAC,CAAC,EAAEU,eAAe,EAAE;UAC3D,GAAGoB,OAAO;UACVsN,QAAQ,EAAGN,MAAM,IAAK;YAClB,IAAI,CAACD,cAAc,CAACC,MAAM,CAAC;YAC3BhN,OAAO,CAACsN,QAAQ,IAAItN,OAAO,CAACsN,QAAQ,CAACN,MAAM,CAAC;UAChD,CAAC;UACDxH,UAAU,EAAEA,CAAA,KAAM;YACdxF,OAAO,CAACwF,UAAU,IAAIxF,OAAO,CAACwF,UAAU,CAAC,CAAC;YAC1C,IAAI,CAAC+H,iBAAiB,CAAC,CAAC;UAC5B;QACJ,CAAC,CAAC;QACF,IAAI,IAAI,CAACnI,YAAY,EAAE;UACnB,IAAI,CAACA,YAAY,CAACD,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;QAC9D;QACA,IAAI,CAACuG,gBAAgB,GAAGrH,SAAS;MACrC,CAAC,CAAC;IACN;IACAkJ,iBAAiBA,CAAA,EAAG;MAChB,IAAI,IAAI,CAACnI,YAAY,EAAE;QACnB,IAAI,CAACA,YAAY,CAACD,gBAAgB,GAAGd,SAAS;QAC9C,IAAI,CAACe,YAAY,CAACoI,eAAe,GAAGnJ,SAAS;MACjD;MACA,MAAM2B,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC7BD,KAAK,IAAIA,KAAK,CAACyH,qBAAqB,CAAC,CAAC;MACtC,IAAI,CAACrI,YAAY,GACb,IAAI,CAACD,gBAAgB,GACjB,IAAI,CAACiI,eAAe,GAChB/I,SAAS;MACrB,IAAI,CAACvB,eAAe,CAAC,mBAAmB,CAAC;IAC7C;IACAc,eAAeA,CAAA,EAAG;MACd,IAAI,IAAI,CAACuB,gBAAgB,EAAE;QACvB,IAAI,CAAC4H,cAAc,IAAI,IAAI,CAACA,cAAc,CAACnO,eAAe,CAAC;QAC3D,IAAI,CAACuG,gBAAgB,CAACkI,IAAI,CAAC,CAAC;MAChC;MACA,IAAI,CAACE,iBAAiB,CAAC,CAAC;IAC5B;IACAG,uBAAuBA,CAAA,EAAG;MACtB,MAAMzC,IAAI,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC;MAC3B,IAAI;QAAEM,oBAAoB;QAAEpH,MAAM;QAAEd,MAAM;QAAE3D;MAAa,CAAC,GAAGsL,IAAI;MACjE,IAAI,CAACO,oBAAoB,IAAI,CAACpH,MAAM,IAAI,CAACd,MAAM,EAC3C;MACJ;AACZ;AACA;AACA;AACA;MACY,IAAI,IAAI,KAAK2H,IAAI,IACb,IAAI,CAAC3H,MAAM,IACXA,MAAM,IACNqK,yBAAyB,CAAC,IAAI,CAAC3N,OAAO,CAAC4N,aAAa,EAAE,IAAI,CAACtK,MAAM,CAACoF,SAAS,EAAEpF,MAAM,CAACoF,SAAS,CAAC,EAAE;QAChGtE,MAAM,GAAG,IAAI,CAACA,MAAM,IAAIvH,SAAS,CAAC,CAAC;QACnC,MAAMgR,OAAO,GAAGnR,UAAU,CAAC,IAAI,CAAC4G,MAAM,CAACoF,SAAS,CAAC5H,CAAC,CAAC;QACnDsD,MAAM,CAACtD,CAAC,CAACgN,GAAG,GAAG7C,IAAI,CAAC7G,MAAM,CAACtD,CAAC,CAACgN,GAAG;QAChC1J,MAAM,CAACtD,CAAC,CAACiN,GAAG,GAAG3J,MAAM,CAACtD,CAAC,CAACgN,GAAG,GAAGD,OAAO;QACrC,MAAMG,OAAO,GAAGtR,UAAU,CAAC,IAAI,CAAC4G,MAAM,CAACoF,SAAS,CAAC3H,CAAC,CAAC;QACnDqD,MAAM,CAACrD,CAAC,CAAC+M,GAAG,GAAG7C,IAAI,CAAC7G,MAAM,CAACrD,CAAC,CAAC+M,GAAG;QAChC1J,MAAM,CAACrD,CAAC,CAACgN,GAAG,GAAG3J,MAAM,CAACrD,CAAC,CAAC+M,GAAG,GAAGE,OAAO;MACzC;MACA9R,WAAW,CAACsP,oBAAoB,EAAEpH,MAAM,CAAC;MACzC;AACZ;AACA;AACA;AACA;MACYhI,YAAY,CAACoP,oBAAoB,EAAE7L,YAAY,CAAC;MAChD;AACZ;AACA;AACA;AACA;AACA;MACYlD,YAAY,CAAC,IAAI,CAACqP,4BAA4B,EAAE,IAAI,CAACtD,eAAe,EAAEgD,oBAAoB,EAAE7L,YAAY,CAAC;IAC7G;IACAkE,kBAAkBA,CAACR,QAAQ,EAAEsD,IAAI,EAAE;MAC/B,IAAI,CAAC,IAAI,CAAC1E,WAAW,CAACS,GAAG,CAACW,QAAQ,CAAC,EAAE;QACjC,IAAI,CAACpB,WAAW,CAACU,GAAG,CAACU,QAAQ,EAAE,IAAIjG,SAAS,CAAC,CAAC,CAAC;MACnD;MACA,MAAM4I,KAAK,GAAG,IAAI,CAAC/D,WAAW,CAACW,GAAG,CAACS,QAAQ,CAAC;MAC5C2C,KAAK,CAACnD,GAAG,CAAC8D,IAAI,CAAC;MACf,MAAMsH,MAAM,GAAGtH,IAAI,CAAC3G,OAAO,CAACkO,sBAAsB;MAClDvH,IAAI,CAACwH,OAAO,CAAC;QACT3J,UAAU,EAAEyJ,MAAM,GAAGA,MAAM,CAACzJ,UAAU,GAAGH,SAAS;QAClD+J,qBAAqB,EAAEH,MAAM,IAAIA,MAAM,CAACI,2BAA2B,GAC7DJ,MAAM,CAACI,2BAA2B,CAAC1H,IAAI,CAAC,GACxCtC;MACV,CAAC,CAAC;IACN;IACAsB,MAAMA,CAAA,EAAG;MACL,MAAMK,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC7B,OAAOD,KAAK,GAAGA,KAAK,CAACiF,IAAI,KAAK,IAAI,GAAG,IAAI;IAC7C;IACAC,OAAOA,CAAA,EAAG;MACN,IAAIF,EAAE;MACN,MAAM;QAAE3H;MAAS,CAAC,GAAG,IAAI,CAACrD,OAAO;MACjC,OAAOqD,QAAQ,GAAG,CAAC,CAAC2H,EAAE,GAAG,IAAI,CAAC/E,QAAQ,CAAC,CAAC,MAAM,IAAI,IAAI+E,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,IAAI,KAAK,IAAI,GAAG,IAAI;IAC1G;IACAqD,WAAWA,CAAA,EAAG;MACV,IAAItD,EAAE;MACN,MAAM;QAAE3H;MAAS,CAAC,GAAG,IAAI,CAACrD,OAAO;MACjC,OAAOqD,QAAQ,GAAG,CAAC2H,EAAE,GAAG,IAAI,CAAC/E,QAAQ,CAAC,CAAC,MAAM,IAAI,IAAI+E,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACuD,QAAQ,GAAGlK,SAAS;IACzG;IACA4B,QAAQA,CAAA,EAAG;MACP,MAAM;QAAE5C;MAAS,CAAC,GAAG,IAAI,CAACrD,OAAO;MACjC,IAAIqD,QAAQ,EACR,OAAO,IAAI,CAACnB,IAAI,CAACD,WAAW,CAACW,GAAG,CAACS,QAAQ,CAAC;IAClD;IACA8K,OAAOA,CAAC;MAAExN,UAAU;MAAE6D,UAAU;MAAE4J;IAAuB,CAAC,GAAG,CAAC,CAAC,EAAE;MAC7D,MAAMpI,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC7B,IAAID,KAAK,EACLA,KAAK,CAACmI,OAAO,CAAC,IAAI,EAAEC,qBAAqB,CAAC;MAC9C,IAAIzN,UAAU,EAAE;QACZ,IAAI,CAAC8H,eAAe,GAAGpE,SAAS;QAChC,IAAI,CAAC1D,UAAU,GAAG,IAAI;MAC1B;MACA,IAAI6D,UAAU,EACV,IAAI,CAACkG,UAAU,CAAC;QAAElG;MAAW,CAAC,CAAC;IACvC;IACAgK,QAAQA,CAAA,EAAG;MACP,MAAMxI,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC7B,IAAID,KAAK,EAAE;QACP,OAAOA,KAAK,CAACwI,QAAQ,CAAC,IAAI,CAAC;MAC/B,CAAC,MACI;QACD,OAAO,KAAK;MAChB;IACJ;IACAjI,aAAaA,CAAA,EAAG;MACZ,MAAM;QAAEhD;MAAc,CAAC,GAAG,IAAI,CAACvD,OAAO;MACtC,IAAI,CAACuD,aAAa,EACd;MACJ;MACA,IAAIkL,SAAS,GAAG,KAAK;MACrB;AACZ;AACA;AACA;MACY,MAAM;QAAE9O;MAAa,CAAC,GAAG4D,aAAa;MACtC,IAAI5D,YAAY,CAAC+O,MAAM,IACnB/O,YAAY,CAACgP,OAAO,IACpBhP,YAAY,CAACiP,OAAO,IACpBjP,YAAY,CAACkP,OAAO,EAAE;QACtBJ,SAAS,GAAG,IAAI;MACpB;MACA;MACA,IAAI,CAACA,SAAS,EACV;MACJ,MAAMK,WAAW,GAAG,CAAC,CAAC;MACtB;MACA,KAAK,IAAIzM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG5D,aAAa,CAAC6D,MAAM,EAAED,CAAC,EAAE,EAAE;QAC3C,MAAM0M,GAAG,GAAG,QAAQ,GAAGtQ,aAAa,CAAC4D,CAAC,CAAC;QACvC;QACA,IAAI1C,YAAY,CAACoP,GAAG,CAAC,EAAE;UACnBD,WAAW,CAACC,GAAG,CAAC,GAAGpP,YAAY,CAACoP,GAAG,CAAC;UACpCxL,aAAa,CAACyL,cAAc,CAACD,GAAG,EAAE,CAAC,CAAC;QACxC;MACJ;MACA;MACA;MACAxL,aAAa,CAACqE,MAAM,CAAC,CAAC;MACtB;MACA,KAAK,MAAMmH,GAAG,IAAID,WAAW,EAAE;QAC3BvL,aAAa,CAACyL,cAAc,CAACD,GAAG,EAAED,WAAW,CAACC,GAAG,CAAC,CAAC;MACvD;MACA;MACA;MACAxL,aAAa,CAAC+F,cAAc,CAAC,CAAC;IAClC;IACA2F,mBAAmBA,CAACC,SAAS,EAAE;MAC3B,IAAIlE,EAAE,EAAEmE,EAAE;MACV,IAAI,CAAC,IAAI,CAAC/L,QAAQ,IAAI,IAAI,CAAC1C,KAAK,EAC5B,OAAO2D,SAAS;MACpB,IAAI,CAAC,IAAI,CAACtC,SAAS,EAAE;QACjB,OAAOrD,gBAAgB;MAC3B;MACA,MAAM0Q,MAAM,GAAG;QACXzQ,UAAU,EAAE;MAChB,CAAC;MACD,MAAM8H,iBAAiB,GAAG,IAAI,CAACD,oBAAoB,CAAC,CAAC;MACrD,IAAI,IAAI,CAAC7F,UAAU,EAAE;QACjB,IAAI,CAACA,UAAU,GAAG,KAAK;QACvByO,MAAM,CAACC,OAAO,GAAG,EAAE;QACnBD,MAAM,CAACE,aAAa,GAChB1R,kBAAkB,CAACsR,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACI,aAAa,CAAC,IAAI,EAAE;QAC3GF,MAAM,CAACG,SAAS,GAAG9I,iBAAiB,GAC9BA,iBAAiB,CAAC,IAAI,CAAC9G,YAAY,EAAE,EAAE,CAAC,GACxC,MAAM;QACZ,OAAOyP,MAAM;MACjB;MACA,MAAMnE,IAAI,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC;MAC3B,IAAI,CAAC,IAAI,CAACzC,eAAe,IAAI,CAAC,IAAI,CAACnF,MAAM,IAAI,CAAC2H,IAAI,CAAC7G,MAAM,EAAE;QACvD,MAAMoL,WAAW,GAAG,CAAC,CAAC;QACtB,IAAI,IAAI,CAACxP,OAAO,CAACqD,QAAQ,EAAE;UACvBmM,WAAW,CAACH,OAAO,GACf,IAAI,CAAC1P,YAAY,CAAC0P,OAAO,KAAKhL,SAAS,GACjC,IAAI,CAAC1E,YAAY,CAAC0P,OAAO,GACzB,CAAC;UACXG,WAAW,CAACF,aAAa,GACrB1R,kBAAkB,CAACsR,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACI,aAAa,CAAC,IAAI,EAAE;QAC/G;QACA,IAAI,IAAI,CAACxN,YAAY,IAAI,CAACtE,YAAY,CAAC,IAAI,CAACmC,YAAY,CAAC,EAAE;UACvD6P,WAAW,CAACD,SAAS,GAAG9I,iBAAiB,GACnCA,iBAAiB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GACzB,MAAM;UACZ,IAAI,CAAC3E,YAAY,GAAG,KAAK;QAC7B;QACA,OAAO0N,WAAW;MACtB;MACA,MAAMC,cAAc,GAAGxE,IAAI,CAACmC,eAAe,IAAInC,IAAI,CAACtL,YAAY;MAChE,IAAI,CAAC+N,uBAAuB,CAAC,CAAC;MAC9B0B,MAAM,CAACG,SAAS,GAAGjS,wBAAwB,CAAC,IAAI,CAACwO,4BAA4B,EAAE,IAAI,CAACjL,SAAS,EAAE4O,cAAc,CAAC;MAC9G,IAAIhJ,iBAAiB,EAAE;QACnB2I,MAAM,CAACG,SAAS,GAAG9I,iBAAiB,CAACgJ,cAAc,EAAEL,MAAM,CAACG,SAAS,CAAC;MAC1E;MACA,MAAM;QAAEzO,CAAC;QAAEC;MAAE,CAAC,GAAG,IAAI,CAAC0H,eAAe;MACrC2G,MAAM,CAACM,eAAe,GAAG,GAAG5O,CAAC,CAAC6O,MAAM,GAAG,GAAG,KAAK5O,CAAC,CAAC4O,MAAM,GAAG,GAAG,KAAK;MAClE,IAAI1E,IAAI,CAACmC,eAAe,EAAE;QACtB;AAChB;AACA;AACA;QACgBgC,MAAM,CAACC,OAAO,GACVpE,IAAI,KAAK,IAAI,GACP,CAACkE,EAAE,GAAG,CAACnE,EAAE,GAAGyE,cAAc,CAACJ,OAAO,MAAM,IAAI,IAAIrE,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,IAAI,CAACrL,YAAY,CAAC0P,OAAO,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,GAClI,IAAI,CAAC3B,eAAe,GAChB,IAAI,CAAC7N,YAAY,CAAC0P,OAAO,GACzBI,cAAc,CAACG,WAAW;MAC5C,CAAC,MACI;QACD;AAChB;AACA;AACA;QACgBR,MAAM,CAACC,OAAO,GACVpE,IAAI,KAAK,IAAI,GACPwE,cAAc,CAACJ,OAAO,KAAKhL,SAAS,GAChCoL,cAAc,CAACJ,OAAO,GACtB,EAAE,GACNI,cAAc,CAACG,WAAW,KAAKvL,SAAS,GACpCoL,cAAc,CAACG,WAAW,GAC1B,CAAC;MACnB;MACA;AACZ;AACA;MACY,KAAK,MAAMb,GAAG,IAAI1R,eAAe,EAAE;QAC/B,IAAIoS,cAAc,CAACV,GAAG,CAAC,KAAK1K,SAAS,EACjC;QACJ,MAAM;UAAEwL,OAAO;UAAEC;QAAQ,CAAC,GAAGzS,eAAe,CAAC0R,GAAG,CAAC;QACjD;AAChB;AACA;AACA;AACA;AACA;QACgB,MAAMgB,SAAS,GAAGX,MAAM,CAACG,SAAS,KAAK,MAAM,GACvCE,cAAc,CAACV,GAAG,CAAC,GACnBc,OAAO,CAACJ,cAAc,CAACV,GAAG,CAAC,EAAE9D,IAAI,CAAC;QACxC,IAAI6E,OAAO,EAAE;UACT,MAAME,GAAG,GAAGF,OAAO,CAACxN,MAAM;UAC1B,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2N,GAAG,EAAE3N,CAAC,EAAE,EAAE;YAC1B+M,MAAM,CAACU,OAAO,CAACzN,CAAC,CAAC,CAAC,GAAG0N,SAAS;UAClC;QACJ,CAAC,MACI;UACDX,MAAM,CAACL,GAAG,CAAC,GAAGgB,SAAS;QAC3B;MACJ;MACA;AACZ;AACA;AACA;AACA;MACY,IAAI,IAAI,CAAC/P,OAAO,CAACqD,QAAQ,EAAE;QACvB+L,MAAM,CAACE,aAAa,GAChBrE,IAAI,KAAK,IAAI,GACPrN,kBAAkB,CAACsR,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACI,aAAa,CAAC,IAAI,EAAE,GACvG,MAAM;MACpB;MACA,OAAOF,MAAM;IACjB;IACArH,aAAaA,CAAA,EAAG;MACZ,IAAI,CAAC7C,UAAU,GAAG,IAAI,CAACkD,QAAQ,GAAG/D,SAAS;IAC/C;IACA;IACA4L,SAASA,CAAA,EAAG;MACR,IAAI,CAAC/N,IAAI,CAACV,KAAK,CAACC,OAAO,CAAEkF,IAAI,IAAK;QAAE,IAAIqE,EAAE;QAAE,OAAO,CAACA,EAAE,GAAGrE,IAAI,CAACxB,gBAAgB,MAAM,IAAI,IAAI6F,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACqC,IAAI,CAAC,CAAC;MAAE,CAAC,CAAC;MAClI,IAAI,CAACnL,IAAI,CAACV,KAAK,CAACC,OAAO,CAACwF,iBAAiB,CAAC;MAC1C,IAAI,CAAC/E,IAAI,CAACD,WAAW,CAACiO,KAAK,CAAC,CAAC;IACjC;EACJ,CAAC;AACL;AACA,SAAS9I,YAAYA,CAACT,IAAI,EAAE;EACxBA,IAAI,CAACS,YAAY,CAAC,CAAC;AACvB;AACA,SAASC,kBAAkBA,CAACV,IAAI,EAAE;EAC9B,IAAIqE,EAAE;EACN,MAAM5C,QAAQ,GAAG,CAAC,CAAC4C,EAAE,GAAGrE,IAAI,CAACzB,UAAU,MAAM,IAAI,IAAI8F,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC5C,QAAQ,KAAKzB,IAAI,CAACyB,QAAQ;EAC3G,IAAIzB,IAAI,CAAChB,MAAM,CAAC,CAAC,IACbgB,IAAI,CAACrD,MAAM,IACX8E,QAAQ,IACRzB,IAAI,CAACzD,YAAY,CAAC,WAAW,CAAC,EAAE;IAChC,MAAM;MAAEwF,SAAS,EAAEpF,MAAM;MAAEsG,WAAW,EAAEuG;IAAe,CAAC,GAAGxJ,IAAI,CAACrD,MAAM;IACtE,MAAM;MAAEsK;IAAc,CAAC,GAAGjH,IAAI,CAAC3G,OAAO;IACtC,MAAMmL,QAAQ,GAAG/C,QAAQ,CAACyB,MAAM,KAAKlD,IAAI,CAACrD,MAAM,CAACuG,MAAM;IACvD;IACA;IACA,IAAI+D,aAAa,KAAK,MAAM,EAAE;MAC1BrQ,QAAQ,CAAE6S,IAAI,IAAK;QACf,MAAMC,YAAY,GAAGlF,QAAQ,GACvB/C,QAAQ,CAACwB,WAAW,CAACwG,IAAI,CAAC,GAC1BhI,QAAQ,CAACM,SAAS,CAAC0H,IAAI,CAAC;QAC9B,MAAM9N,MAAM,GAAG5F,UAAU,CAAC2T,YAAY,CAAC;QACvCA,YAAY,CAACvC,GAAG,GAAGxK,MAAM,CAAC8M,IAAI,CAAC,CAACtC,GAAG;QACnCuC,YAAY,CAACtC,GAAG,GAAGsC,YAAY,CAACvC,GAAG,GAAGxL,MAAM;MAChD,CAAC,CAAC;IACN,CAAC,MACI,IAAIqL,yBAAyB,CAACC,aAAa,EAAExF,QAAQ,CAACM,SAAS,EAAEpF,MAAM,CAAC,EAAE;MAC3E/F,QAAQ,CAAE6S,IAAI,IAAK;QACf,MAAMC,YAAY,GAAGlF,QAAQ,GACvB/C,QAAQ,CAACwB,WAAW,CAACwG,IAAI,CAAC,GAC1BhI,QAAQ,CAACM,SAAS,CAAC0H,IAAI,CAAC;QAC9B,MAAM9N,MAAM,GAAG5F,UAAU,CAAC4G,MAAM,CAAC8M,IAAI,CAAC,CAAC;QACvCC,YAAY,CAACtC,GAAG,GAAGsC,YAAY,CAACvC,GAAG,GAAGxL,MAAM;QAC5C;AAChB;AACA;QACgB,IAAIqE,IAAI,CAACrC,cAAc,IAAI,CAACqC,IAAI,CAACxB,gBAAgB,EAAE;UAC/CwB,IAAI,CAACvG,iBAAiB,GAAG,IAAI;UAC7BuG,IAAI,CAACrC,cAAc,CAAC8L,IAAI,CAAC,CAACrC,GAAG,GACzBpH,IAAI,CAACrC,cAAc,CAAC8L,IAAI,CAAC,CAACtC,GAAG,GAAGxL,MAAM;QAC9C;MACJ,CAAC,CAAC;IACN;IACA,MAAMgO,WAAW,GAAGxT,WAAW,CAAC,CAAC;IACjCL,YAAY,CAAC6T,WAAW,EAAEhN,MAAM,EAAE8E,QAAQ,CAACM,SAAS,CAAC;IACrD,MAAM6H,WAAW,GAAGzT,WAAW,CAAC,CAAC;IACjC,IAAIqO,QAAQ,EAAE;MACV1O,YAAY,CAAC8T,WAAW,EAAE5J,IAAI,CAACuD,cAAc,CAACiG,cAAc,EAAE,IAAI,CAAC,EAAE/H,QAAQ,CAACwB,WAAW,CAAC;IAC9F,CAAC,MACI;MACDnN,YAAY,CAAC8T,WAAW,EAAEjN,MAAM,EAAE8E,QAAQ,CAACM,SAAS,CAAC;IACzD;IACA,MAAM1E,gBAAgB,GAAG,CAAC/G,WAAW,CAACqT,WAAW,CAAC;IAClD,IAAIrM,wBAAwB,GAAG,KAAK;IACpC,IAAI,CAAC0C,IAAI,CAACzB,UAAU,EAAE;MAClB,MAAM2F,cAAc,GAAGlE,IAAI,CAAC2E,0BAA0B,CAAC,CAAC;MACxD;AACZ;AACA;AACA;MACY,IAAIT,cAAc,IAAI,CAACA,cAAc,CAAC3F,UAAU,EAAE;QAC9C,MAAM;UAAEkD,QAAQ,EAAEoI,cAAc;UAAElN,MAAM,EAAEmN;QAAa,CAAC,GAAG5F,cAAc;QACzE,IAAI2F,cAAc,IAAIC,YAAY,EAAE;UAChC,MAAMC,gBAAgB,GAAG7T,SAAS,CAAC,CAAC;UACpCN,oBAAoB,CAACmU,gBAAgB,EAAEtI,QAAQ,CAACM,SAAS,EAAE8H,cAAc,CAAC9H,SAAS,CAAC;UACpF,MAAM2D,cAAc,GAAGxP,SAAS,CAAC,CAAC;UAClCN,oBAAoB,CAAC8P,cAAc,EAAE/I,MAAM,EAAEmN,YAAY,CAAC/H,SAAS,CAAC;UACpE,IAAI,CAAC1L,gBAAgB,CAAC0T,gBAAgB,EAAErE,cAAc,CAAC,EAAE;YACrDpI,wBAAwB,GAAG,IAAI;UACnC;UACA,IAAI4G,cAAc,CAAC7K,OAAO,CAACiF,UAAU,EAAE;YACnC0B,IAAI,CAACrC,cAAc,GAAG+H,cAAc;YACpC1F,IAAI,CAAC4E,oBAAoB,GAAGmF,gBAAgB;YAC5C/J,IAAI,CAACkE,cAAc,GAAGA,cAAc;UACxC;QACJ;MACJ;IACJ;IACAlE,IAAI,CAAC7D,eAAe,CAAC,WAAW,EAAE;MAC9BQ,MAAM;MACN8E,QAAQ;MACRrE,KAAK,EAAEwM,WAAW;MAClBD,WAAW;MACXtM,gBAAgB;MAChBC;IACJ,CAAC,CAAC;EACN,CAAC,MACI,IAAI0C,IAAI,CAAChB,MAAM,CAAC,CAAC,EAAE;IACpB,MAAM;MAAEC;IAAe,CAAC,GAAGe,IAAI,CAAC3G,OAAO;IACvC4F,cAAc,IAAIA,cAAc,CAAC,CAAC;EACtC;EACA;AACJ;AACA;AACA;AACA;EACIe,IAAI,CAAC3G,OAAO,CAACwE,UAAU,GAAGH,SAAS;AACvC;AACA,SAAS3C,mBAAmBA,CAACiF,IAAI,EAAE;EAC/B;AACJ;AACA;EACI7H,mBAAmB,CAACE,UAAU,EAAE;EAChC,IAAI,CAAC2H,IAAI,CAAC/G,MAAM,EACZ;EACJ;AACJ;AACA;AACA;AACA;AACA;EACI,IAAI,CAAC+G,IAAI,CAAC8E,YAAY,CAAC,CAAC,EAAE;IACtB9E,IAAI,CAACvG,iBAAiB,GAAGuG,IAAI,CAAC/G,MAAM,CAACQ,iBAAiB;EAC1D;EACA;AACJ;AACA;AACA;AACA;EACIuG,IAAI,CAACtG,uBAAuB,KAAKsG,IAAI,CAACtG,uBAAuB,GAAGwI,OAAO,CAAClC,IAAI,CAACvG,iBAAiB,IAC1FuG,IAAI,CAAC/G,MAAM,CAACQ,iBAAiB,IAC7BuG,IAAI,CAAC/G,MAAM,CAACS,uBAAuB,CAAC,CAAC;EACzCsG,IAAI,CAACrG,gBAAgB,KAAKqG,IAAI,CAACrG,gBAAgB,GAAGqG,IAAI,CAAC/G,MAAM,CAACU,gBAAgB,CAAC;AACnF;AACA,SAASuB,eAAeA,CAAC8E,IAAI,EAAE;EAC3BA,IAAI,CAACvG,iBAAiB,GAClBuG,IAAI,CAACtG,uBAAuB,GACxBsG,IAAI,CAACrG,gBAAgB,GACjB,KAAK;AACrB;AACA,SAASyH,aAAaA,CAACpB,IAAI,EAAE;EACzBA,IAAI,CAACoB,aAAa,CAAC,CAAC;AACxB;AACA,SAASd,iBAAiBA,CAACN,IAAI,EAAE;EAC7BA,IAAI,CAACM,iBAAiB,CAAC,CAAC;AAC5B;AACA,SAASC,kBAAkBA,CAACP,IAAI,EAAE;EAC9BA,IAAI,CAACxG,aAAa,GAAG,KAAK;AAC9B;AACA,SAASgH,mBAAmBA,CAACR,IAAI,EAAE;EAC/B,MAAM;IAAEpD;EAAc,CAAC,GAAGoD,IAAI,CAAC3G,OAAO;EACtC,IAAIuD,aAAa,IAAIA,aAAa,CAACsB,QAAQ,CAAC,CAAC,CAAC8L,qBAAqB,EAAE;IACjEpN,aAAa,CAACN,MAAM,CAAC,qBAAqB,CAAC;EAC/C;EACA0D,IAAI,CAACnH,cAAc,CAAC,CAAC;AACzB;AACA,SAASoE,eAAeA,CAAC+C,IAAI,EAAE;EAC3BA,IAAI,CAAC/C,eAAe,CAAC,CAAC;EACtB+C,IAAI,CAAC8D,WAAW,GAAG9D,IAAI,CAACrC,cAAc,GAAGqC,IAAI,CAACvC,MAAM,GAAGC,SAAS;EAChEsC,IAAI,CAACvG,iBAAiB,GAAG,IAAI;AACjC;AACA,SAASuB,kBAAkBA,CAACgF,IAAI,EAAE;EAC9BA,IAAI,CAAChF,kBAAkB,CAAC,CAAC;AAC7B;AACA,SAASC,cAAcA,CAAC+E,IAAI,EAAE;EAC1BA,IAAI,CAAC/E,cAAc,CAAC,CAAC;AACzB;AACA,SAAS2E,aAAaA,CAACI,IAAI,EAAE;EACzBA,IAAI,CAACJ,aAAa,CAAC,CAAC;AACxB;AACA,SAASyB,mBAAmBA,CAAChC,KAAK,EAAE;EAChCA,KAAK,CAAC4K,kBAAkB,CAAC,CAAC;AAC9B;AACA,SAAS1D,YAAYA,CAAC2D,MAAM,EAAE9M,KAAK,EAAE+M,CAAC,EAAE;EACpCD,MAAM,CAACE,SAAS,GAAGhT,GAAG,CAACgG,KAAK,CAACgN,SAAS,EAAE,CAAC,EAAED,CAAC,CAAC;EAC7CD,MAAM,CAACG,KAAK,GAAGjT,GAAG,CAACgG,KAAK,CAACiN,KAAK,EAAE,CAAC,EAAEF,CAAC,CAAC;EACrCD,MAAM,CAAClB,MAAM,GAAG5L,KAAK,CAAC4L,MAAM;EAC5BkB,MAAM,CAACI,WAAW,GAAGlN,KAAK,CAACkN,WAAW;AAC1C;AACA,SAASC,OAAOA,CAACL,MAAM,EAAEM,IAAI,EAAEC,EAAE,EAAEN,CAAC,EAAE;EAClCD,MAAM,CAAC/C,GAAG,GAAG/P,GAAG,CAACoT,IAAI,CAACrD,GAAG,EAAEsD,EAAE,CAACtD,GAAG,EAAEgD,CAAC,CAAC;EACrCD,MAAM,CAAC9C,GAAG,GAAGhQ,GAAG,CAACoT,IAAI,CAACpD,GAAG,EAAEqD,EAAE,CAACrD,GAAG,EAAE+C,CAAC,CAAC;AACzC;AACA,SAAS3D,MAAMA,CAAC0D,MAAM,EAAEM,IAAI,EAAEC,EAAE,EAAEN,CAAC,EAAE;EACjCI,OAAO,CAACL,MAAM,CAAC/P,CAAC,EAAEqQ,IAAI,CAACrQ,CAAC,EAAEsQ,EAAE,CAACtQ,CAAC,EAAEgQ,CAAC,CAAC;EAClCI,OAAO,CAACL,MAAM,CAAC9P,CAAC,EAAEoQ,IAAI,CAACpQ,CAAC,EAAEqQ,EAAE,CAACrQ,CAAC,EAAE+P,CAAC,CAAC;AACtC;AACA,SAASjE,mBAAmBA,CAAClG,IAAI,EAAE;EAC/B,OAAQA,IAAI,CAACyG,eAAe,IAAIzG,IAAI,CAACyG,eAAe,CAACwC,WAAW,KAAKvL,SAAS;AAClF;AACA,MAAMK,uBAAuB,GAAG;EAC5B2M,QAAQ,EAAE,IAAI;EACdC,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC;AACzB,CAAC;AACD,MAAMC,iBAAiB,GAAIC,MAAM,IAAK,OAAOC,SAAS,KAAK,WAAW,IAClEA,SAAS,CAACC,SAAS,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACJ,MAAM,CAAC;AACtD;AACA;AACA;AACA;AACA;AACA,MAAMK,UAAU,GAAGN,iBAAiB,CAAC,cAAc,CAAC,IAAI,CAACA,iBAAiB,CAAC,SAAS,CAAC,GAC/EO,IAAI,CAACC,KAAK,GACVvT,IAAI;AACV,SAASwT,SAASA,CAAC5B,IAAI,EAAE;EACrB;EACAA,IAAI,CAACtC,GAAG,GAAG+D,UAAU,CAACzB,IAAI,CAACtC,GAAG,CAAC;EAC/BsC,IAAI,CAACrC,GAAG,GAAG8D,UAAU,CAACzB,IAAI,CAACrC,GAAG,CAAC;AACnC;AACA,SAASpE,QAAQA,CAACG,GAAG,EAAE;EACnBkI,SAAS,CAAClI,GAAG,CAAChJ,CAAC,CAAC;EAChBkR,SAAS,CAAClI,GAAG,CAAC/I,CAAC,CAAC;AACpB;AACA,SAAS4M,yBAAyBA,CAACC,aAAa,EAAExF,QAAQ,EAAE9E,MAAM,EAAE;EAChE,OAAQsK,aAAa,KAAK,UAAU,IAC/BA,aAAa,KAAK,iBAAiB,IAChC,CAACjR,MAAM,CAACO,WAAW,CAACkL,QAAQ,CAAC,EAAElL,WAAW,CAACoG,MAAM,CAAC,EAAE,GAAG,CAAE;AACrE;AAEA,SAASzB,eAAe,EAAE1C,oBAAoB,EAAE+R,OAAO,EAAEhE,YAAY,EAAEC,MAAM,EAAEzL,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}