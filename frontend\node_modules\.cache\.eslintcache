[{"D:\\menasa\\frontend\\src\\index.js": "1", "D:\\menasa\\frontend\\src\\App.js": "2", "D:\\menasa\\frontend\\src\\components\\ProtectedRoute.js": "3", "D:\\menasa\\frontend\\src\\components\\Navbar.js": "4", "D:\\menasa\\frontend\\src\\components\\ErrorBoundary.js": "5", "D:\\menasa\\frontend\\src\\contexts\\AuthContext.js": "6", "D:\\menasa\\frontend\\src\\contexts\\ThemeContext.js": "7", "D:\\menasa\\frontend\\src\\pages\\Login.js": "8", "D:\\menasa\\frontend\\src\\pages\\AdminDashboard.js": "9", "D:\\menasa\\frontend\\src\\pages\\StudentDashboard.js": "10", "D:\\menasa\\frontend\\src\\components\\pwa\\PWAInstallPrompt.js": "11", "D:\\menasa\\frontend\\src\\pages\\FileViewer.js": "12", "D:\\menasa\\frontend\\src\\components\\FilterPanel.js": "13", "D:\\menasa\\frontend\\src\\components\\FileCard.js": "14", "D:\\menasa\\frontend\\src\\components\\notifications\\NotificationCenter.js": "15", "D:\\menasa\\frontend\\src\\lib\\supabase.js": "16", "D:\\menasa\\frontend\\src\\utils\\security.js": "17", "D:\\menasa\\frontend\\src\\utils\\validation.js": "18", "D:\\menasa\\frontend\\src\\services\\api.js": "19", "D:\\menasa\\frontend\\src\\styles\\GlobalStyles.js": "20", "D:\\menasa\\frontend\\src\\styles\\theme.js": "21", "D:\\menasa\\frontend\\src\\components\\admin\\Statistics.js": "22", "D:\\menasa\\frontend\\src\\components\\admin\\FileManagement.js": "23", "D:\\menasa\\frontend\\src\\components\\admin\\Sidebar.js": "24", "D:\\menasa\\frontend\\src\\components\\admin\\FileUpload.js": "25", "D:\\menasa\\frontend\\src\\components\\reports\\ReportsCenter.js": "26", "D:\\menasa\\frontend\\src\\components\\admin\\UserManagement.js": "27", "D:\\menasa\\frontend\\src\\components\\ui\\AnimatedComponents.js": "28", "D:\\menasa\\frontend\\src\\components\\search\\AdvancedSearch.js": "29", "D:\\menasa\\frontend\\src\\components\\backup\\BackupCenter.js": "30", "D:\\menasa\\frontend\\src\\components\\ui\\index.js": "31", "D:\\menasa\\frontend\\src\\components\\comments\\CommentsSection.js": "32", "D:\\menasa\\frontend\\src\\components\\sharing\\ShareModal.js": "33", "D:\\menasa\\frontend\\src\\components\\ui\\Button.js": "34", "D:\\menasa\\frontend\\src\\components\\ui\\Input.js": "35", "D:\\menasa\\frontend\\src\\components\\ui\\Card.js": "36"}, {"size": 254, "mtime": 1752622827917, "results": "37", "hashOfConfig": "38"}, {"size": 2733, "mtime": 1752621504937, "results": "39", "hashOfConfig": "38"}, {"size": 1822, "mtime": 1752615515692, "results": "40", "hashOfConfig": "38"}, {"size": 9817, "mtime": 1752620097745, "results": "41", "hashOfConfig": "38"}, {"size": 4873, "mtime": 1752618948983, "results": "42", "hashOfConfig": "38"}, {"size": 6896, "mtime": 1752621867657, "results": "43", "hashOfConfig": "38"}, {"size": 1637, "mtime": 1752615487412, "results": "44", "hashOfConfig": "38"}, {"size": 9679, "mtime": 1752619104953, "results": "45", "hashOfConfig": "38"}, {"size": 1925, "mtime": 1752621249916, "results": "46", "hashOfConfig": "38"}, {"size": 12848, "mtime": 1752623227256, "results": "47", "hashOfConfig": "38"}, {"size": 10589, "mtime": 1752621459888, "results": "48", "hashOfConfig": "38"}, {"size": 11983, "mtime": 1752620507225, "results": "49", "hashOfConfig": "38"}, {"size": 12761, "mtime": 1752615711226, "results": "50", "hashOfConfig": "38"}, {"size": 9767, "mtime": 1752621070994, "results": "51", "hashOfConfig": "38"}, {"size": 10406, "mtime": 1752619858901, "results": "52", "hashOfConfig": "38"}, {"size": 3502, "mtime": 1752617826837, "results": "53", "hashOfConfig": "38"}, {"size": 10616, "mtime": 1752623243185, "results": "54", "hashOfConfig": "38"}, {"size": 7035, "mtime": 1752618993902, "results": "55", "hashOfConfig": "38"}, {"size": 28303, "mtime": 1752621197774, "results": "56", "hashOfConfig": "38"}, {"size": 8255, "mtime": 1752616572208, "results": "57", "hashOfConfig": "38"}, {"size": 4105, "mtime": 1752615360867, "results": "58", "hashOfConfig": "38"}, {"size": 15470, "mtime": 1752618342041, "results": "59", "hashOfConfig": "38"}, {"size": 14820, "mtime": 1752618606673, "results": "60", "hashOfConfig": "38"}, {"size": 7504, "mtime": 1752623046423, "results": "61", "hashOfConfig": "38"}, {"size": 16636, "mtime": 1752618669840, "results": "62", "hashOfConfig": "38"}, {"size": 12185, "mtime": 1752623155007, "results": "63", "hashOfConfig": "38"}, {"size": 14353, "mtime": 1752618462041, "results": "64", "hashOfConfig": "38"}, {"size": 6860, "mtime": 1752616507817, "results": "65", "hashOfConfig": "38"}, {"size": 15317, "mtime": 1752620175944, "results": "66", "hashOfConfig": "38"}, {"size": 16002, "mtime": 1752621162780, "results": "67", "hashOfConfig": "38"}, {"size": 240, "mtime": 1752616522748, "results": "68", "hashOfConfig": "38"}, {"size": 11854, "mtime": 1752620333603, "results": "69", "hashOfConfig": "38"}, {"size": 12927, "mtime": 1752620721219, "results": "70", "hashOfConfig": "38"}, {"size": 5472, "mtime": 1752615413906, "results": "71", "hashOfConfig": "38"}, {"size": 5349, "mtime": 1752615440253, "results": "72", "hashOfConfig": "38"}, {"size": 4955, "mtime": 1752615579964, "results": "73", "hashOfConfig": "38"}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1sqrxn5", {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\menasa\\frontend\\src\\index.js", [], [], "D:\\menasa\\frontend\\src\\App.js", [], [], "D:\\menasa\\frontend\\src\\components\\ProtectedRoute.js", [], [], "D:\\menasa\\frontend\\src\\components\\Navbar.js", [], [], "D:\\menasa\\frontend\\src\\components\\ErrorBoundary.js", [], [], "D:\\menasa\\frontend\\src\\contexts\\AuthContext.js", ["182"], [], "D:\\menasa\\frontend\\src\\contexts\\ThemeContext.js", [], [], "D:\\menasa\\frontend\\src\\pages\\Login.js", ["183", "184", "185"], [], "D:\\menasa\\frontend\\src\\pages\\AdminDashboard.js", [], [], "D:\\menasa\\frontend\\src\\pages\\StudentDashboard.js", ["186", "187"], [], "D:\\menasa\\frontend\\src\\components\\pwa\\PWAInstallPrompt.js", ["188", "189"], [], "D:\\menasa\\frontend\\src\\pages\\FileViewer.js", [], [], "D:\\menasa\\frontend\\src\\components\\FilterPanel.js", [], [], "D:\\menasa\\frontend\\src\\components\\FileCard.js", [], [], "D:\\menasa\\frontend\\src\\components\\notifications\\NotificationCenter.js", ["190", "191"], [], "D:\\menasa\\frontend\\src\\lib\\supabase.js", [], [], "D:\\menasa\\frontend\\src\\utils\\security.js", [], [], "D:\\menasa\\frontend\\src\\utils\\validation.js", ["192"], [], "D:\\menasa\\frontend\\src\\services\\api.js", ["193", "194", "195", "196"], [], "D:\\menasa\\frontend\\src\\styles\\GlobalStyles.js", [], [], "D:\\menasa\\frontend\\src\\styles\\theme.js", [], [], "D:\\menasa\\frontend\\src\\components\\admin\\Statistics.js", ["197", "198", "199", "200"], [], "D:\\menasa\\frontend\\src\\components\\admin\\FileManagement.js", ["201", "202", "203", "204", "205"], [], "D:\\menasa\\frontend\\src\\components\\admin\\Sidebar.js", ["206"], [], "D:\\menasa\\frontend\\src\\components\\admin\\FileUpload.js", ["207"], [], "D:\\menasa\\frontend\\src\\components\\reports\\ReportsCenter.js", ["208", "209", "210", "211", "212", "213", "214"], [], "D:\\menasa\\frontend\\src\\components\\admin\\UserManagement.js", ["215", "216", "217", "218", "219", "220"], [], "D:\\menasa\\frontend\\src\\components\\ui\\AnimatedComponents.js", ["221"], [], "D:\\menasa\\frontend\\src\\components\\search\\AdvancedSearch.js", ["222", "223", "224", "225", "226", "227", "228", "229"], [], "D:\\menasa\\frontend\\src\\components\\backup\\BackupCenter.js", ["230", "231"], [], "D:\\menasa\\frontend\\src\\components\\ui\\index.js", [], [], "D:\\menasa\\frontend\\src\\components\\comments\\CommentsSection.js", ["232", "233", "234", "235"], [], "D:\\menasa\\frontend\\src\\components\\sharing\\ShareModal.js", ["236"], [], "D:\\menasa\\frontend\\src\\components\\ui\\Button.js", [], [], "D:\\menasa\\frontend\\src\\components\\ui\\Input.js", ["237"], [], "D:\\menasa\\frontend\\src\\components\\ui\\Card.js", [], [], {"ruleId": "238", "severity": 1, "message": "239", "line": 65, "column": 6, "nodeType": "240", "endLine": 65, "endColumn": 8, "suggestions": "241"}, {"ruleId": "242", "severity": 1, "message": "243", "line": 167, "column": 10, "nodeType": "244", "messageId": "245", "endLine": 167, "endColumn": 21}, {"ruleId": "242", "severity": 1, "message": "246", "line": 168, "column": 10, "nodeType": "244", "messageId": "245", "endLine": 168, "endColumn": 17}, {"ruleId": "242", "severity": 1, "message": "247", "line": 168, "column": 19, "nodeType": "244", "messageId": "245", "endLine": 168, "endColumn": 29}, {"ruleId": "242", "severity": 1, "message": "248", "line": 15, "column": 17, "nodeType": "244", "messageId": "245", "endLine": 15, "endColumn": 22}, {"ruleId": "242", "severity": 1, "message": "249", "line": 172, "column": 23, "nodeType": "244", "messageId": "245", "endLine": 172, "endColumn": 37}, {"ruleId": "242", "severity": 1, "message": "250", "line": 7, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 7, "endColumn": 15}, {"ruleId": "242", "severity": 1, "message": "251", "line": 8, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 8, "endColumn": 12}, {"ruleId": "242", "severity": 1, "message": "252", "line": 1, "column": 27, "nodeType": "244", "messageId": "245", "endLine": 1, "endColumn": 36}, {"ruleId": "242", "severity": 1, "message": "253", "line": 10, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 10, "endColumn": 13}, {"ruleId": "254", "severity": 1, "message": "255", "line": 215, "column": 1, "nodeType": "256", "endLine": 223, "endColumn": 3}, {"ruleId": "242", "severity": 1, "message": "257", "line": 197, "column": 21, "nodeType": "244", "messageId": "245", "endLine": 197, "endColumn": 31}, {"ruleId": "242", "severity": 1, "message": "258", "line": 305, "column": 17, "nodeType": "244", "messageId": "245", "endLine": 305, "endColumn": 21}, {"ruleId": "242", "severity": 1, "message": "258", "line": 775, "column": 17, "nodeType": "244", "messageId": "245", "endLine": 775, "endColumn": 21}, {"ruleId": "254", "severity": 1, "message": "255", "line": 1050, "column": 1, "nodeType": "256", "endLine": 1050, "endColumn": 81}, {"ruleId": "242", "severity": 1, "message": "259", "line": 10, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 10, "endColumn": 8}, {"ruleId": "242", "severity": 1, "message": "260", "line": 12, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 12, "endColumn": 13}, {"ruleId": "242", "severity": 1, "message": "261", "line": 219, "column": 39, "nodeType": "244", "messageId": "245", "endLine": 219, "endColumn": 51}, {"ruleId": "242", "severity": 1, "message": "262", "line": 219, "column": 60, "nodeType": "244", "messageId": "245", "endLine": 219, "endColumn": 70}, {"ruleId": "242", "severity": 1, "message": "263", "line": 8, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 8, "endColumn": 11}, {"ruleId": "242", "severity": 1, "message": "260", "line": 13, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 13, "endColumn": 13}, {"ruleId": "242", "severity": 1, "message": "264", "line": 14, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 14, "endColumn": 9}, {"ruleId": "242", "severity": 1, "message": "265", "line": 15, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 15, "endColumn": 17}, {"ruleId": "242", "severity": 1, "message": "266", "line": 21, "column": 16, "nodeType": "244", "messageId": "245", "endLine": 21, "endColumn": 22}, {"ruleId": "242", "severity": 1, "message": "267", "line": 12, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 12, "endColumn": 15}, {"ruleId": "242", "severity": 1, "message": "268", "line": 217, "column": 13, "nodeType": "244", "messageId": "245", "endLine": 217, "endColumn": 19}, {"ruleId": "242", "severity": 1, "message": "260", "line": 9, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 9, "endColumn": 13}, {"ruleId": "242", "severity": 1, "message": "263", "line": 15, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 15, "endColumn": 11}, {"ruleId": "242", "severity": 1, "message": "269", "line": 20, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 20, "endColumn": 12}, {"ruleId": "242", "severity": 1, "message": "270", "line": 21, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 21, "endColumn": 7}, {"ruleId": "242", "severity": 1, "message": "271", "line": 24, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 24, "endColumn": 11}, {"ruleId": "242", "severity": 1, "message": "272", "line": 25, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 25, "endColumn": 6}, {"ruleId": "242", "severity": 1, "message": "273", "line": 33, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 33, "endColumn": 9}, {"ruleId": "242", "severity": 1, "message": "263", "line": 8, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 8, "endColumn": 11}, {"ruleId": "242", "severity": 1, "message": "274", "line": 12, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 12, "endColumn": 9}, {"ruleId": "242", "severity": 1, "message": "260", "line": 13, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 13, "endColumn": 13}, {"ruleId": "242", "severity": 1, "message": "265", "line": 16, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 16, "endColumn": 17}, {"ruleId": "242", "severity": 1, "message": "275", "line": 176, "column": 7, "nodeType": "244", "messageId": "245", "endLine": 176, "endColumn": 18}, {"ruleId": "242", "severity": 1, "message": "276", "line": 310, "column": 9, "nodeType": "244", "messageId": "245", "endLine": 310, "endColumn": 22}, {"ruleId": "254", "severity": 1, "message": "255", "line": 296, "column": 1, "nodeType": "256", "endLine": 313, "endColumn": 3}, {"ruleId": "242", "severity": 1, "message": "263", "line": 6, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 6, "endColumn": 11}, {"ruleId": "242", "severity": 1, "message": "277", "line": 9, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 9, "endColumn": 9}, {"ruleId": "242", "severity": 1, "message": "278", "line": 10, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 10, "endColumn": 13}, {"ruleId": "242", "severity": 1, "message": "264", "line": 11, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 11, "endColumn": 9}, {"ruleId": "242", "severity": 1, "message": "279", "line": 15, "column": 18, "nodeType": "244", "messageId": "245", "endLine": 15, "endColumn": 23}, {"ruleId": "242", "severity": 1, "message": "280", "line": 131, "column": 7, "nodeType": "244", "messageId": "245", "endLine": 131, "endColumn": 21}, {"ruleId": "242", "severity": 1, "message": "281", "line": 137, "column": 7, "nodeType": "244", "messageId": "245", "endLine": 137, "endColumn": 17}, {"ruleId": "242", "severity": 1, "message": "282", "line": 264, "column": 9, "nodeType": "244", "messageId": "245", "endLine": 264, "endColumn": 26}, {"ruleId": "242", "severity": 1, "message": "283", "line": 9, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 9, "endColumn": 10}, {"ruleId": "242", "severity": 1, "message": "284", "line": 12, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 12, "endColumn": 18}, {"ruleId": "242", "severity": 1, "message": "285", "line": 3, "column": 18, "nodeType": "244", "messageId": "245", "endLine": 3, "endColumn": 33}, {"ruleId": "242", "severity": 1, "message": "265", "line": 9, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 9, "endColumn": 17}, {"ruleId": "242", "severity": 1, "message": "286", "line": 10, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 10, "endColumn": 9}, {"ruleId": "242", "severity": 1, "message": "287", "line": 12, "column": 3, "nodeType": "244", "messageId": "245", "endLine": 12, "endColumn": 10}, {"ruleId": "238", "severity": 1, "message": "288", "line": 233, "column": 6, "nodeType": "240", "endLine": 233, "endColumn": 41, "suggestions": "289"}, {"ruleId": "242", "severity": 1, "message": "290", "line": 158, "column": 10, "nodeType": "244", "messageId": "245", "endLine": 158, "endColumn": 19}, "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'handleAuthStateChange' and 'logout'. Either include them or remove the dependency array.", "ArrayExpression", ["291"], "no-unused-vars", "'fieldErrors' is assigned a value but never used.", "Identifier", "unusedVar", "'touched' is assigned a value but never used.", "'setTouched' is assigned a value but never used.", "'admin' is defined but never used.", "'setShowFilters' is assigned a value but never used.", "'FiSmartphone' is defined but never used.", "'FiMonitor' is defined but never used.", "'useEffect' is defined but never used.", "'FiSettings' is defined but never used.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "'uploadData' is assigned a value but never used.", "'data' is assigned a value but never used.", "'FiEye' is defined but never used.", "'FiCalendar' is defined but never used.", "'statsLoading' is assigned a value but never used.", "'statsError' is assigned a value but never used.", "'FiFilter' is defined but never used.", "'FiUser' is defined but never used.", "'FiMoreVertical' is defined but never used.", "'Button' is defined but never used.", "'FiTrendingUp' is defined but never used.", "'result' is assigned a value but never used.", "'LineChart' is defined but never used.", "'Line' is defined but never used.", "'BarChart' is defined but never used.", "'Bar' is defined but never used.", "'Legend' is defined but never used.", "'FiMail' is defined but never used.", "'StatusBadge' is assigned a value but never used.", "'getStatusText' is assigned a value but never used.", "'FiStar' is defined but never used.", "'FiDownload' is defined but never used.", "'Input' is defined but never used.", "'RangeContainer' is assigned a value but never used.", "'RangeInput' is assigned a value but never used.", "'handleRangeChange' is assigned a value but never used.", "'FiClock' is defined but never used.", "'FiAlertTriangle' is defined but never used.", "'AnimatePresence' is defined but never used.", "'FiEdit' is defined but never used.", "'FiReply' is defined but never used.", "React Hook useEffect has a missing dependency: 'generateShareLink'. Either include it or remove the dependency array.", ["292"], "'isFocused' is assigned a value but never used.", {"desc": "293", "fix": "294"}, {"desc": "295", "fix": "296"}, "Update the dependencies array to be: [handleAuthStateChange, logout]", {"range": "297", "text": "298"}, "Update the dependencies array to be: [isOpen, file, shareType, settings, generateShareLink]", {"range": "299", "text": "300"}, [1846, 1848], "[handleAuthStateChange, logout]", [6199, 6234], "[isOpen, file, shareType, settings, generateShareLink]"]