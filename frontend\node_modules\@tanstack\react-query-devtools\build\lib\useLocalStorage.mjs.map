{"version": 3, "file": "useLocalStorage.mjs", "sources": ["../../src/useLocalStorage.ts"], "sourcesContent": ["import * as React from 'react'\n\nconst getItem = (key: string): unknown => {\n  try {\n    const itemValue = localStorage.getItem(key)\n    if (typeof itemValue === 'string') {\n      return JSON.parse(itemValue)\n    }\n    return undefined\n  } catch {\n    return undefined\n  }\n}\n\nexport default function useLocalStorage<T>(\n  key: string,\n  defaultValue: T | undefined,\n): [T | undefined, (newVal: T | ((prevVal: T) => T)) => void] {\n  const [value, setValue] = React.useState<T>()\n\n  React.useEffect(() => {\n    const initialValue = getItem(key) as T | undefined\n\n    if (typeof initialValue === 'undefined' || initialValue === null) {\n      setValue(\n        typeof defaultValue === 'function' ? defaultValue() : defaultValue,\n      )\n    } else {\n      setValue(initialValue)\n    }\n  }, [defaultValue, key])\n\n  const setter = React.useCallback(\n    (updater: any) => {\n      setValue((old) => {\n        let newVal = updater\n\n        if (typeof updater == 'function') {\n          newVal = updater(old)\n        }\n        try {\n          localStorage.setItem(key, JSON.stringify(newVal))\n        } catch {}\n\n        return newVal\n      })\n    },\n    [key],\n  )\n\n  return [value, setter]\n}\n"], "names": ["getItem", "key", "itemValue", "localStorage", "JSON", "parse", "undefined", "useLocalStorage", "defaultValue", "value", "setValue", "React", "useState", "useEffect", "initialValue", "setter", "useCallback", "updater", "old", "newVal", "setItem", "stringify"], "mappings": ";;AAEA,MAAMA,OAAO,GAAIC,GAAD,IAA0B;EACxC,IAAI;AACF,IAAA,MAAMC,SAAS,GAAGC,YAAY,CAACH,OAAb,CAAqBC,GAArB,CAAlB,CAAA;;AACA,IAAA,IAAI,OAAOC,SAAP,KAAqB,QAAzB,EAAmC;AACjC,MAAA,OAAOE,IAAI,CAACC,KAAL,CAAWH,SAAX,CAAP,CAAA;AACD,KAAA;;AACD,IAAA,OAAOI,SAAP,CAAA;AACD,GAND,CAME,MAAM;AACN,IAAA,OAAOA,SAAP,CAAA;AACD,GAAA;AACF,CAVD,CAAA;;AAYe,SAASC,eAAT,CACbN,GADa,EAEbO,YAFa,EAG+C;EAC5D,MAAM,CAACC,KAAD,EAAQC,QAAR,IAAoBC,KAAK,CAACC,QAAN,EAA1B,CAAA;EAEAD,KAAK,CAACE,SAAN,CAAgB,MAAM;AACpB,IAAA,MAAMC,YAAY,GAAGd,OAAO,CAACC,GAAD,CAA5B,CAAA;;IAEA,IAAI,OAAOa,YAAP,KAAwB,WAAxB,IAAuCA,YAAY,KAAK,IAA5D,EAAkE;MAChEJ,QAAQ,CACN,OAAOF,YAAP,KAAwB,UAAxB,GAAqCA,YAAY,EAAjD,GAAsDA,YADhD,CAAR,CAAA;AAGD,KAJD,MAIO;MACLE,QAAQ,CAACI,YAAD,CAAR,CAAA;AACD,KAAA;AACF,GAVD,EAUG,CAACN,YAAD,EAAeP,GAAf,CAVH,CAAA,CAAA;AAYA,EAAA,MAAMc,MAAM,GAAGJ,KAAK,CAACK,WAAN,CACZC,OAAD,IAAkB;IAChBP,QAAQ,CAAEQ,GAAD,IAAS;MAChB,IAAIC,MAAM,GAAGF,OAAb,CAAA;;AAEA,MAAA,IAAI,OAAOA,OAAP,IAAkB,UAAtB,EAAkC;AAChCE,QAAAA,MAAM,GAAGF,OAAO,CAACC,GAAD,CAAhB,CAAA;AACD,OAAA;;MACD,IAAI;QACFf,YAAY,CAACiB,OAAb,CAAqBnB,GAArB,EAA0BG,IAAI,CAACiB,SAAL,CAAeF,MAAf,CAA1B,CAAA,CAAA;OADF,CAEE,MAAM,EAAE;;AAEV,MAAA,OAAOA,MAAP,CAAA;AACD,KAXO,CAAR,CAAA;AAYD,GAdY,EAeb,CAAClB,GAAD,CAfa,CAAf,CAAA;AAkBA,EAAA,OAAO,CAACQ,KAAD,EAAQM,MAAR,CAAP,CAAA;AACD;;;;"}