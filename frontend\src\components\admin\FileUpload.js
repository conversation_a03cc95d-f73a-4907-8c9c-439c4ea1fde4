import React, { useState } from 'react';
import { useDropzone } from 'react-dropzone';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { FiUpload, FiFile, FiX, FiCheck, FiAlertCircle } from 'react-icons/fi';
import { files } from '../../services/api';
import { toast } from 'react-toastify';
import { Card, Button, Input } from '../ui';

const UploadContainer = styled.div`
  padding: ${({ theme }) => theme.spacing[6]};
`;

const PageHeader = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing[8]};
`;

const PageTitle = styled.h1`
  font-size: ${({ theme }) => theme.fontSizes['3xl']};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  color: ${({ theme }) => theme.colors.gray[900]};
  margin-bottom: ${({ theme }) => theme.spacing[2]};
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[3]};
`;

const PageSubtitle = styled.p`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  color: ${({ theme }) => theme.colors.gray[600]};
`;

const UploadCard = styled(Card)`
  margin-bottom: ${({ theme }) => theme.spacing[6]};
`;

const DropZone = styled.div`
  border: 2px dashed #ddd;
  border-radius: 10px;
  padding: 40px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    border-color: #007bff;
    background: #f8f9fa;
  }
`;

const ProgressBar = styled.div`
  width: 100%;
  height: 6px;
  background: #e9ecef;
  border-radius: 3px;
  overflow: hidden;
  margin: 10px 0;
`;

const Progress = styled.div`
  height: 100%;
  background: #007bff;
  width: ${props => props.progress}%;
  transition: width 0.3s ease;
`;

function FileUpload() {
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploading, setUploading] = useState(false);

  const onDrop = async (acceptedFiles) => {
    const file = acceptedFiles[0];
    if (!file) return;

    setUploading(true);
    setUploadProgress(0);

    try {
      await uploadFile(file, {
        onUploadProgress: (progressEvent) => {
          const progress = Math.round(
            (progressEvent.loaded * 100) / progressEvent.total
          );
          setUploadProgress(progress);
        }
      });
      
      toast.success('تم رفع الملف بنجاح!');
      setUploadProgress(0);
    } catch (error) {
      toast.error('فشل في رفع الملف');
    } finally {
      setUploading(false);
    }
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    multiple: false
  });

  return (
    <UploadContainer>
      <h2>رفع ملف جديد</h2>
      
      <DropZone {...getRootProps()}>
        <input {...getInputProps()} />
        <FiUpload size={48} color="#007bff" />
        <p>اسحب الملف هنا أو انقر للاختيار</p>
        <small>يدعم: PDF, DOC, PPT, MP4, MP3</small>
      </DropZone>

      {uploading && (
        <div>
          <ProgressBar>
            <Progress progress={uploadProgress} />
          </ProgressBar>
          <p>{uploadProgress}% مكتمل</p>
        </div>
      )}
    </UploadContainer>
  );
}

export default FileUpload;