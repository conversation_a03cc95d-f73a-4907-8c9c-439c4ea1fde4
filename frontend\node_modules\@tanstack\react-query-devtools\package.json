{"name": "@tanstack/react-query-devtools", "version": "4.40.1", "description": "Developer tools to interact with and visualize the TanStack/react-query cache", "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/TanStack/query.git", "directory": "packages/react-query-devtools"}, "homepage": "https://tanstack.com/query", "funding": {"type": "github", "url": "https://github.com/sponsors/tanner<PERSON>ley"}, "types": "build/lib/index.d.ts", "main": "build/lib/index.js", "module": "build/lib/index.esm.js", "exports": {".": {"types": "./build/lib/index.d.ts", "import": "./build/lib/index.mjs", "default": "./build/lib/index.js"}, "./production": {"types": "./build/lib/index.d.ts", "import": "./build/lib/index.prod.mjs", "default": "./build/lib/index.prod.js"}, "./build/lib/index.prod.js": {"types": "./build/lib/index.d.ts", "import": "./build/lib/index.prod.mjs", "default": "./build/lib/index.prod.js"}, "./package.json": "./package.json"}, "sideEffects": false, "files": ["build/lib/*", "build/umd/*", "src"], "devDependencies": {"@types/react": "^18.0.14", "@types/react-dom": "^18.0.5", "@types/use-sync-external-store": "^0.0.3", "react": "^18.2.0", "react-17": "npm:react@^17.0.2", "react-dom": "^18.2.0", "react-dom-17": "npm:react-dom@^17.0.2", "react-error-boundary": "^3.1.4", "@tanstack/react-query": "4.40.1"}, "dependencies": {"@tanstack/match-sorter-utils": "^8.7.0", "superjson": "^1.10.0", "use-sync-external-store": "^1.2.0"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0", "@tanstack/react-query": "^4.40.1"}, "scripts": {}}