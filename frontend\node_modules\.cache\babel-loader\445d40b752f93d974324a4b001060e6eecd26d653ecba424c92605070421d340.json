{"ast": null, "code": "export function isWellBehavedNumber(n) {\n  return Number.isFinite(n);\n}\nexport function isPositiveNumber(n) {\n  return typeof n === 'number' && n > 0 && Number.isFinite(n);\n}", "map": {"version": 3, "names": ["isWellBehavedNumber", "n", "Number", "isFinite", "isPositiveNumber"], "sources": ["D:/menasa/frontend/node_modules/recharts/es6/util/isWellBehavedNumber.js"], "sourcesContent": ["export function isWellBehavedNumber(n) {\n  return Number.isFinite(n);\n}\nexport function isPositiveNumber(n) {\n  return typeof n === 'number' && n > 0 && Number.isFinite(n);\n}"], "mappings": "AAAA,OAAO,SAASA,mBAAmBA,CAACC,CAAC,EAAE;EACrC,OAAOC,MAAM,CAACC,QAAQ,CAACF,CAAC,CAAC;AAC3B;AACA,OAAO,SAASG,gBAAgBA,CAACH,CAAC,EAAE;EAClC,OAAO,OAAOA,CAAC,KAAK,QAAQ,IAAIA,CAAC,GAAG,CAAC,IAAIC,MAAM,CAACC,QAAQ,CAACF,CAAC,CAAC;AAC7D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}