import React, { useEffect } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import {
  FiUsers,
  FiFile,
  FiDownload,
  FiTrendingUp,
  FiEye,
  FiStar,
  FiCalendar,
  FiActivity
} from 'react-icons/fi';
import { Card } from '../ui';
import { admin } from '../../services/api';
import { subscribeToFiles, subscribeToRatings, subscribeToDownloads } from '../../lib/supabase';
import { toast } from 'react-toastify';

const StatsContainer = styled.div`
  padding: ${({ theme }) => theme.spacing[6]};
`;

const PageHeader = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing[8]};
`;

const PageTitle = styled.h1`
  font-size: ${({ theme }) => theme.fontSizes['3xl']};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  color: ${({ theme }) => theme.colors.gray[900]};
  margin-bottom: ${({ theme }) => theme.spacing[2]};
`;

const PageSubtitle = styled.p`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  color: ${({ theme }) => theme.colors.gray[600]};
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: ${({ theme }) => theme.spacing[6]};
  margin-bottom: ${({ theme }) => theme.spacing[8]};
`;

const StatCard = styled(Card)`
  position: relative;
  overflow: hidden;
  transition: all ${({ theme }) => theme.transitions.normal};
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: ${({ theme }) => theme.shadows.xl};
  }
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: ${({ color }) => color};
  }
`;

const StatContent = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

const StatInfo = styled.div`
  flex: 1;
`;

const StatNumber = styled.div`
  font-size: ${({ theme }) => theme.fontSizes['3xl']};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  color: ${({ theme }) => theme.colors.gray[900]};
  margin-bottom: ${({ theme }) => theme.spacing[1]};
`;

const StatLabel = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.base};
  color: ${({ theme }) => theme.colors.gray[600]};
  margin-bottom: ${({ theme }) => theme.spacing[2]};
`;

const StatChange = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[1]};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  
  ${({ positive, theme }) =>
    positive
      ? `color: ${theme.colors.success[600]};`
      : `color: ${theme.colors.error[600]};`
  }
`;

const StatIcon = styled.div`
  width: 60px;
  height: 60px;
  border-radius: ${({ theme }) => theme.borderRadius.xl};
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: ${({ theme }) => theme.fontSizes['2xl']};
  color: white;
  background: ${({ color }) => color};
  box-shadow: ${({ theme }) => theme.shadows.md};
`;

const ChartsSection = styled.div`
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: ${({ theme }) => theme.spacing[6]};
  margin-bottom: ${({ theme }) => theme.spacing[8]};
  
  @media (max-width: ${({ theme }) => theme.breakpoints.lg}) {
    grid-template-columns: 1fr;
  }
`;

const ChartCard = styled(Card)`
  min-height: 400px;
`;

const ChartHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: ${({ theme }) => theme.spacing[6]};
`;

const ChartTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes.xl};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  color: ${({ theme }) => theme.colors.gray[900]};
`;

const ChartPlaceholder = styled.div`
  height: 300px;
  background: linear-gradient(135deg, 
    ${({ theme }) => theme.colors.gray[100]} 0%, 
    ${({ theme }) => theme.colors.gray[50]} 100%);
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${({ theme }) => theme.colors.gray[500]};
  font-size: ${({ theme }) => theme.fontSizes.lg};
  border: 2px dashed ${({ theme }) => theme.colors.gray[300]};
`;

const RecentActivity = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: ${({ theme }) => theme.spacing[6]};
`;

const ActivityCard = styled(Card)`
  max-height: 500px;
  overflow: hidden;
`;

const ActivityList = styled.div`
  max-height: 350px;
  overflow-y: auto;
`;

const ActivityItem = styled(motion.div)`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[3]};
  padding: ${({ theme }) => theme.spacing[3]} 0;
  border-bottom: 1px solid ${({ theme }) => theme.colors.gray[100]};
  
  &:last-child {
    border-bottom: none;
  }
`;

const ActivityIcon = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: ${({ color, theme }) => color || theme.colors.gray[100]};
  color: white;
  font-size: ${({ theme }) => theme.fontSizes.base};
`;

const ActivityContent = styled.div`
  flex: 1;
`;

const ActivityText = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.gray[700]};
  margin-bottom: ${({ theme }) => theme.spacing[1]};
`;

const ActivityTime = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.xs};
  color: ${({ theme }) => theme.colors.gray[500]};
`;

const Statistics = () => {
  const queryClient = useQueryClient();

  // جلب الإحصائيات العامة
  const { data: statsData, isLoading: statsLoading, error: statsError } = useQuery({
    queryKey: ['admin-stats'],
    queryFn: admin.getStats,
    select: (data) => data.data,
    onError: (error) => {
      toast.error('فشل في تحميل الإحصائيات');
      console.error('Stats error:', error);
    },
    refetchInterval: 30000, // تحديث كل 30 ثانية
  });

  // جلب الأنشطة الحديثة
  const { data: activitiesData, isLoading: activitiesLoading } = useQuery({
    queryKey: ['recent-activities'],
    queryFn: admin.getRecentActivities,
    select: (data) => data.data,
    onError: (error) => {
      console.error('Activities error:', error);
    },
    refetchInterval: 60000, // تحديث كل دقيقة
  });

  // جلب الملفات الشائعة
  const { data: popularFiles } = useQuery({
    queryKey: ['popular-files'],
    queryFn: admin.getPopularFiles,
    select: (data) => data.data,
    onError: (error) => {
      console.error('Popular files error:', error);
    },
  });

  // الاشتراك في التحديثات المباشرة
  useEffect(() => {
    const filesSubscription = subscribeToFiles(() => {
      queryClient.invalidateQueries(['admin-stats']);
      queryClient.invalidateQueries(['recent-activities']);
      queryClient.invalidateQueries(['popular-files']);
    });

    const ratingsSubscription = subscribeToRatings(() => {
      queryClient.invalidateQueries(['admin-stats']);
      queryClient.invalidateQueries(['recent-activities']);
    });

    const downloadsSubscription = subscribeToDownloads(() => {
      queryClient.invalidateQueries(['admin-stats']);
      queryClient.invalidateQueries(['recent-activities']);
    });

    return () => {
      filesSubscription.unsubscribe();
      ratingsSubscription.unsubscribe();
      downloadsSubscription.unsubscribe();
    };
  }, [queryClient]);

  const formatNumber = (num) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num?.toString() || '0';
  };

  const formatTime = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now - date) / (1000 * 60));

    if (diffInMinutes < 1) return 'الآن';
    if (diffInMinutes < 60) return `منذ ${diffInMinutes} دقيقة`;
    if (diffInMinutes < 1440) return `منذ ${Math.floor(diffInMinutes / 60)} ساعة`;
    return `منذ ${Math.floor(diffInMinutes / 1440)} يوم`;
  };

  const getActivityIcon = (activityType) => {
    switch (activityType) {
      case 'upload': return <FiFile />;
      case 'download': return <FiDownload />;
      case 'rating': return <FiStar />;
      default: return <FiActivity />;
    }
  };

  const getActivityColor = (activityType) => {
    switch (activityType) {
      case 'upload': return '#3B82F6';
      case 'download': return '#F59E0B';
      case 'rating': return '#EF4444';
      default: return '#6B7280';
    }
  };

  const getActivityText = (activity) => {
    switch (activity.activity_type) {
      case 'upload':
        return `تم رفع ملف جديد: "${activity.file_title}"`;
      case 'download':
        return `تم تحميل ملف: "${activity.file_title}" بواسطة ${activity.user_name}`;
      case 'rating':
        return `تقييم جديد لملف: "${activity.file_title}" بواسطة ${activity.user_name}`;
      default:
        return `نشاط جديد: ${activity.file_title}`;
    }
  };

  const stats = [
    {
      icon: <FiUsers />,
      number: formatNumber(statsData?.total_users),
      label: 'إجمالي المستخدمين',
      change: '+12%',
      positive: true,
      color: 'linear-gradient(135deg, #3B82F6, #1D4ED8)'
    },
    {
      icon: <FiFile />,
      number: formatNumber(statsData?.total_files),
      label: 'إجمالي الملفات',
      change: '+8%',
      positive: true,
      color: 'linear-gradient(135deg, #10B981, #047857)'
    },
    {
      icon: <FiDownload />,
      number: formatNumber(statsData?.total_downloads),
      label: 'إجمالي التحميلات',
      change: '+25%',
      positive: true,
      color: 'linear-gradient(135deg, #F59E0B, #D97706)'
    },
    {
      icon: <FiStar />,
      number: statsData?.overall_average_rating?.toFixed(1) || '0.0',
      label: 'متوسط التقييم',
      change: '+0.2',
      positive: true,
      color: 'linear-gradient(135deg, #EF4444, #DC2626)'
    }
  ];

  return (
    <StatsContainer>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <PageHeader>
          <PageTitle>لوحة التحكم</PageTitle>
          <PageSubtitle>نظرة عامة على إحصائيات النظام والأنشطة الحديثة</PageSubtitle>
        </PageHeader>

        <StatsGrid>
          {stats.map((stat, index) => (
            <StatCard
              key={index}
              as={motion.div}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              color={stat.color}
              padding="lg"
            >
              <StatContent>
                <StatInfo>
                  <StatNumber>{stat.number}</StatNumber>
                  <StatLabel>{stat.label}</StatLabel>
                  <StatChange positive={stat.positive}>
                    <FiTrendingUp size={16} />
                    {stat.change} من الشهر الماضي
                  </StatChange>
                </StatInfo>
                <StatIcon color={stat.color}>
                  {stat.icon}
                </StatIcon>
              </StatContent>
            </StatCard>
          ))}
        </StatsGrid>

        <ChartsSection>
          <ChartCard padding="lg">
            <ChartHeader>
              <ChartTitle>إحصائيات التحميلات الشهرية</ChartTitle>
            </ChartHeader>
            <ChartPlaceholder>
              <div style={{ textAlign: 'center' }}>
                <FiActivity size={48} style={{ marginBottom: '16px', opacity: 0.5 }} />
                <div>الرسم البياني سيتم إضافته قريباً</div>
              </div>
            </ChartPlaceholder>
          </ChartCard>

          <ChartCard padding="lg">
            <ChartHeader>
              <ChartTitle>توزيع أنواع الملفات</ChartTitle>
            </ChartHeader>
            <ChartPlaceholder>
              <div style={{ textAlign: 'center' }}>
                <FiActivity size={48} style={{ marginBottom: '16px', opacity: 0.5 }} />
                <div>الرسم البياني سيتم إضافته قريباً</div>
              </div>
            </ChartPlaceholder>
          </ChartCard>
        </ChartsSection>

        <RecentActivity>
          <ActivityCard padding="lg">
            <ChartHeader>
              <ChartTitle>الأنشطة الحديثة</ChartTitle>
            </ChartHeader>
            <ActivityList>
              {activitiesLoading ? (
                <div style={{ textAlign: 'center', padding: '20px', color: '#6b7280' }}>
                  جاري تحميل الأنشطة...
                </div>
              ) : activitiesData?.length > 0 ? (
                activitiesData.map((activity, index) => (
                  <ActivityItem
                    key={`${activity.activity_type}-${activity.file_id}-${index}`}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <ActivityIcon color={getActivityColor(activity.activity_type)}>
                      {getActivityIcon(activity.activity_type)}
                    </ActivityIcon>
                    <ActivityContent>
                      <ActivityText>{getActivityText(activity)}</ActivityText>
                      <ActivityTime>{formatTime(activity.activity_time)}</ActivityTime>
                    </ActivityContent>
                  </ActivityItem>
                ))
              ) : (
                <div style={{ textAlign: 'center', padding: '20px', color: '#6b7280' }}>
                  لا توجد أنشطة حديثة
                </div>
              )}
            </ActivityList>
          </ActivityCard>

          <ActivityCard padding="lg">
            <ChartHeader>
              <ChartTitle>الملفات الأكثر تحميلاً</ChartTitle>
            </ChartHeader>
            <ActivityList>
              {popularFiles?.length > 0 ? (
                popularFiles.slice(0, 5).map((file, index) => (
                  <ActivityItem
                    key={file.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <ActivityIcon color="#3B82F6">
                      <FiFile />
                    </ActivityIcon>
                    <ActivityContent>
                      <ActivityText>{file.title}</ActivityText>
                      <ActivityTime>{formatNumber(file.download_count)} تحميل</ActivityTime>
                    </ActivityContent>
                  </ActivityItem>
                ))
              ) : (
                <div style={{ textAlign: 'center', padding: '20px', color: '#6b7280' }}>
                  لا توجد ملفات
                </div>
              )}
            </ActivityList>
          </ActivityCard>
        </RecentActivity>
      </motion.div>
    </StatsContainer>
  );
};

export default Statistics;
