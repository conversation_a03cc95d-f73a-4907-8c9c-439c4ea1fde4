{"ast": null, "code": "import get from 'es-toolkit/compat/get';\nimport { Children, isValidElement } from 'react';\nimport { isFragment } from 'react-is';\nimport { isNullish } from './DataUtils';\nimport { FilteredElementKeyMap, SVGElementPropKeys, EventKeys } from './types';\nexport var SCALE_TYPES = ['auto', 'linear', 'pow', 'sqrt', 'log', 'identity', 'time', 'band', 'point', 'ordinal', 'quantile', 'quantize', 'utc', 'sequential', 'threshold'];\n\n/**\n * @deprecated instead find another approach that does not depend on displayName.\n * Get the display name of a component\n * @param  {Object} Comp Specified Component\n * @return {String}      Display name of Component\n */\nexport var getDisplayName = Comp => {\n  if (typeof Comp === 'string') {\n    return Comp;\n  }\n  if (!Comp) {\n    return '';\n  }\n  return Comp.displayName || Comp.name || 'Component';\n};\n\n// `toArray` gets called multiple times during the render\n// so we can memoize last invocation (since reference to `children` is the same)\nvar lastChildren = null;\nvar lastResult = null;\n\n/**\n * @deprecated instead find another approach that does not require reading React Elements from DOM.\n *\n * @param children do not use\n * @return deprecated do not use\n */\nexport var toArray = children => {\n  if (children === lastChildren && Array.isArray(lastResult)) {\n    return lastResult;\n  }\n  var result = [];\n  Children.forEach(children, child => {\n    if (isNullish(child)) return;\n    if (isFragment(child)) {\n      result = result.concat(toArray(child.props.children));\n    } else {\n      // @ts-expect-error this could still be Iterable<ReactNode> and TS does not like that\n      result.push(child);\n    }\n  });\n  lastResult = result;\n  lastChildren = children;\n  return result;\n};\n\n/**\n * @deprecated instead find another approach that does not require reading React Elements from DOM.\n *\n * Find and return all matched children by type.\n * `type` must be a React.ComponentType\n *\n * @param children do not use\n * @param type do not use\n * @return deprecated do not use\n */\nexport function findAllByType(children, type) {\n  var result = [];\n  var types = [];\n  if (Array.isArray(type)) {\n    types = type.map(t => getDisplayName(t));\n  } else {\n    types = [getDisplayName(type)];\n  }\n  toArray(children).forEach(child => {\n    var childType = get(child, 'type.displayName') || get(child, 'type.name');\n    // ts-expect-error toArray and lodash.get are not compatible. Let's get rid of the whole findAllByType function\n    if (types.indexOf(childType) !== -1) {\n      result.push(child);\n    }\n  });\n  return result;\n}\nexport var isClipDot = dot => {\n  if (dot && typeof dot === 'object' && 'clipDot' in dot) {\n    return Boolean(dot.clipDot);\n  }\n  return true;\n};\n\n/**\n * Checks if the property is valid to spread onto an SVG element or onto a specific component\n * @param {unknown} property property value currently being compared\n * @param {string} key property key currently being compared\n * @param {boolean} includeEvents if events are included in spreadable props\n * @param {boolean} svgElementType checks against map of SVG element types to attributes\n * @returns {boolean} is prop valid\n */\nexport var isValidSpreadableProp = (property, key, includeEvents, svgElementType) => {\n  var _ref;\n  /**\n   * If the svg element type is explicitly included, check against the filtered element key map\n   * to determine if there are attributes that should only exist on that element type.\n   * @todo Add an internal cjs version of https://github.com/wooorm/svg-element-attributes for full coverage.\n   */\n  var matchingElementTypeKeys = (_ref = svgElementType && (FilteredElementKeyMap === null || FilteredElementKeyMap === void 0 ? void 0 : FilteredElementKeyMap[svgElementType])) !== null && _ref !== void 0 ? _ref : [];\n  return key.startsWith('data-') || typeof property !== 'function' && (svgElementType && matchingElementTypeKeys.includes(key) || SVGElementPropKeys.includes(key)) || includeEvents && EventKeys.includes(key);\n};\nexport var filterProps = (props, includeEvents, svgElementType) => {\n  if (!props || typeof props === 'function' || typeof props === 'boolean') {\n    return null;\n  }\n  var inputProps = props;\n  if (/*#__PURE__*/isValidElement(props)) {\n    inputProps = props.props;\n  }\n  if (typeof inputProps !== 'object' && typeof inputProps !== 'function') {\n    return null;\n  }\n  var out = {};\n\n  /**\n   * Props are blindly spread onto SVG elements. This loop filters out properties that we don't want to spread.\n   * Items filtered out are as follows:\n   *   - functions in properties that are SVG attributes (functions are included when includeEvents is true)\n   *   - props that are SVG attributes but don't matched the passed svgElementType\n   *   - any prop that is not in SVGElementPropKeys (or in EventKeys if includeEvents is true)\n   */\n  Object.keys(inputProps).forEach(key => {\n    var _inputProps;\n    if (isValidSpreadableProp((_inputProps = inputProps) === null || _inputProps === void 0 ? void 0 : _inputProps[key], key, includeEvents, svgElementType)) {\n      out[key] = inputProps[key];\n    }\n  });\n  return out;\n};", "map": {"version": 3, "names": ["get", "Children", "isValidElement", "isFragment", "<PERSON><PERSON><PERSON><PERSON>", "FilteredElementKeyMap", "SVGElementPropKeys", "EventKeys", "SCALE_TYPES", "getDisplayName", "Comp", "displayName", "name", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lastResult", "toArray", "children", "Array", "isArray", "result", "for<PERSON>ach", "child", "concat", "props", "push", "findAllByType", "type", "types", "map", "t", "childType", "indexOf", "isClipDot", "dot", "Boolean", "clipDot", "isValidSpreadableProp", "property", "key", "includeEvents", "svgElementType", "_ref", "matchingElementTypeKeys", "startsWith", "includes", "filterProps", "inputProps", "out", "Object", "keys", "_inputProps"], "sources": ["D:/menasa/frontend/node_modules/recharts/es6/util/ReactUtils.js"], "sourcesContent": ["import get from 'es-toolkit/compat/get';\nimport { Children, isValidElement } from 'react';\nimport { isFragment } from 'react-is';\nimport { isNullish } from './DataUtils';\nimport { FilteredElementKeyMap, SVGElementPropKeys, EventKeys } from './types';\nexport var SCALE_TYPES = ['auto', 'linear', 'pow', 'sqrt', 'log', 'identity', 'time', 'band', 'point', 'ordinal', 'quantile', 'quantize', 'utc', 'sequential', 'threshold'];\n\n/**\n * @deprecated instead find another approach that does not depend on displayName.\n * Get the display name of a component\n * @param  {Object} Comp Specified Component\n * @return {String}      Display name of Component\n */\nexport var getDisplayName = Comp => {\n  if (typeof Comp === 'string') {\n    return Comp;\n  }\n  if (!Comp) {\n    return '';\n  }\n  return Comp.displayName || Comp.name || 'Component';\n};\n\n// `toArray` gets called multiple times during the render\n// so we can memoize last invocation (since reference to `children` is the same)\nvar lastChildren = null;\nvar lastResult = null;\n\n/**\n * @deprecated instead find another approach that does not require reading React Elements from DOM.\n *\n * @param children do not use\n * @return deprecated do not use\n */\nexport var toArray = children => {\n  if (children === lastChildren && Array.isArray(lastResult)) {\n    return lastResult;\n  }\n  var result = [];\n  Children.forEach(children, child => {\n    if (isNullish(child)) return;\n    if (isFragment(child)) {\n      result = result.concat(toArray(child.props.children));\n    } else {\n      // @ts-expect-error this could still be Iterable<ReactNode> and TS does not like that\n      result.push(child);\n    }\n  });\n  lastResult = result;\n  lastChildren = children;\n  return result;\n};\n\n/**\n * @deprecated instead find another approach that does not require reading React Elements from DOM.\n *\n * Find and return all matched children by type.\n * `type` must be a React.ComponentType\n *\n * @param children do not use\n * @param type do not use\n * @return deprecated do not use\n */\nexport function findAllByType(children, type) {\n  var result = [];\n  var types = [];\n  if (Array.isArray(type)) {\n    types = type.map(t => getDisplayName(t));\n  } else {\n    types = [getDisplayName(type)];\n  }\n  toArray(children).forEach(child => {\n    var childType = get(child, 'type.displayName') || get(child, 'type.name');\n    // ts-expect-error toArray and lodash.get are not compatible. Let's get rid of the whole findAllByType function\n    if (types.indexOf(childType) !== -1) {\n      result.push(child);\n    }\n  });\n  return result;\n}\nexport var isClipDot = dot => {\n  if (dot && typeof dot === 'object' && 'clipDot' in dot) {\n    return Boolean(dot.clipDot);\n  }\n  return true;\n};\n\n/**\n * Checks if the property is valid to spread onto an SVG element or onto a specific component\n * @param {unknown} property property value currently being compared\n * @param {string} key property key currently being compared\n * @param {boolean} includeEvents if events are included in spreadable props\n * @param {boolean} svgElementType checks against map of SVG element types to attributes\n * @returns {boolean} is prop valid\n */\nexport var isValidSpreadableProp = (property, key, includeEvents, svgElementType) => {\n  var _ref;\n  /**\n   * If the svg element type is explicitly included, check against the filtered element key map\n   * to determine if there are attributes that should only exist on that element type.\n   * @todo Add an internal cjs version of https://github.com/wooorm/svg-element-attributes for full coverage.\n   */\n  var matchingElementTypeKeys = (_ref = svgElementType && (FilteredElementKeyMap === null || FilteredElementKeyMap === void 0 ? void 0 : FilteredElementKeyMap[svgElementType])) !== null && _ref !== void 0 ? _ref : [];\n  return key.startsWith('data-') || typeof property !== 'function' && (svgElementType && matchingElementTypeKeys.includes(key) || SVGElementPropKeys.includes(key)) || includeEvents && EventKeys.includes(key);\n};\nexport var filterProps = (props, includeEvents, svgElementType) => {\n  if (!props || typeof props === 'function' || typeof props === 'boolean') {\n    return null;\n  }\n  var inputProps = props;\n  if (/*#__PURE__*/isValidElement(props)) {\n    inputProps = props.props;\n  }\n  if (typeof inputProps !== 'object' && typeof inputProps !== 'function') {\n    return null;\n  }\n  var out = {};\n\n  /**\n   * Props are blindly spread onto SVG elements. This loop filters out properties that we don't want to spread.\n   * Items filtered out are as follows:\n   *   - functions in properties that are SVG attributes (functions are included when includeEvents is true)\n   *   - props that are SVG attributes but don't matched the passed svgElementType\n   *   - any prop that is not in SVGElementPropKeys (or in EventKeys if includeEvents is true)\n   */\n  Object.keys(inputProps).forEach(key => {\n    var _inputProps;\n    if (isValidSpreadableProp((_inputProps = inputProps) === null || _inputProps === void 0 ? void 0 : _inputProps[key], key, includeEvents, svgElementType)) {\n      out[key] = inputProps[key];\n    }\n  });\n  return out;\n};"], "mappings": "AAAA,OAAOA,GAAG,MAAM,uBAAuB;AACvC,SAASC,QAAQ,EAAEC,cAAc,QAAQ,OAAO;AAChD,SAASC,UAAU,QAAQ,UAAU;AACrC,SAASC,SAAS,QAAQ,aAAa;AACvC,SAASC,qBAAqB,EAAEC,kBAAkB,EAAEC,SAAS,QAAQ,SAAS;AAC9E,OAAO,IAAIC,WAAW,GAAG,CAAC,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,KAAK,EAAE,YAAY,EAAE,WAAW,CAAC;;AAE3K;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,cAAc,GAAGC,IAAI,IAAI;EAClC,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC5B,OAAOA,IAAI;EACb;EACA,IAAI,CAACA,IAAI,EAAE;IACT,OAAO,EAAE;EACX;EACA,OAAOA,IAAI,CAACC,WAAW,IAAID,IAAI,CAACE,IAAI,IAAI,WAAW;AACrD,CAAC;;AAED;AACA;AACA,IAAIC,YAAY,GAAG,IAAI;AACvB,IAAIC,UAAU,GAAG,IAAI;;AAErB;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,OAAO,GAAGC,QAAQ,IAAI;EAC/B,IAAIA,QAAQ,KAAKH,YAAY,IAAII,KAAK,CAACC,OAAO,CAACJ,UAAU,CAAC,EAAE;IAC1D,OAAOA,UAAU;EACnB;EACA,IAAIK,MAAM,GAAG,EAAE;EACflB,QAAQ,CAACmB,OAAO,CAACJ,QAAQ,EAAEK,KAAK,IAAI;IAClC,IAAIjB,SAAS,CAACiB,KAAK,CAAC,EAAE;IACtB,IAAIlB,UAAU,CAACkB,KAAK,CAAC,EAAE;MACrBF,MAAM,GAAGA,MAAM,CAACG,MAAM,CAACP,OAAO,CAACM,KAAK,CAACE,KAAK,CAACP,QAAQ,CAAC,CAAC;IACvD,CAAC,MAAM;MACL;MACAG,MAAM,CAACK,IAAI,CAACH,KAAK,CAAC;IACpB;EACF,CAAC,CAAC;EACFP,UAAU,GAAGK,MAAM;EACnBN,YAAY,GAAGG,QAAQ;EACvB,OAAOG,MAAM;AACf,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASM,aAAaA,CAACT,QAAQ,EAAEU,IAAI,EAAE;EAC5C,IAAIP,MAAM,GAAG,EAAE;EACf,IAAIQ,KAAK,GAAG,EAAE;EACd,IAAIV,KAAK,CAACC,OAAO,CAACQ,IAAI,CAAC,EAAE;IACvBC,KAAK,GAAGD,IAAI,CAACE,GAAG,CAACC,CAAC,IAAIpB,cAAc,CAACoB,CAAC,CAAC,CAAC;EAC1C,CAAC,MAAM;IACLF,KAAK,GAAG,CAAClB,cAAc,CAACiB,IAAI,CAAC,CAAC;EAChC;EACAX,OAAO,CAACC,QAAQ,CAAC,CAACI,OAAO,CAACC,KAAK,IAAI;IACjC,IAAIS,SAAS,GAAG9B,GAAG,CAACqB,KAAK,EAAE,kBAAkB,CAAC,IAAIrB,GAAG,CAACqB,KAAK,EAAE,WAAW,CAAC;IACzE;IACA,IAAIM,KAAK,CAACI,OAAO,CAACD,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE;MACnCX,MAAM,CAACK,IAAI,CAACH,KAAK,CAAC;IACpB;EACF,CAAC,CAAC;EACF,OAAOF,MAAM;AACf;AACA,OAAO,IAAIa,SAAS,GAAGC,GAAG,IAAI;EAC5B,IAAIA,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,SAAS,IAAIA,GAAG,EAAE;IACtD,OAAOC,OAAO,CAACD,GAAG,CAACE,OAAO,CAAC;EAC7B;EACA,OAAO,IAAI;AACb,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,qBAAqB,GAAGA,CAACC,QAAQ,EAAEC,GAAG,EAAEC,aAAa,EAAEC,cAAc,KAAK;EACnF,IAAIC,IAAI;EACR;AACF;AACA;AACA;AACA;EACE,IAAIC,uBAAuB,GAAG,CAACD,IAAI,GAAGD,cAAc,KAAKnC,qBAAqB,KAAK,IAAI,IAAIA,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACmC,cAAc,CAAC,CAAC,MAAM,IAAI,IAAIC,IAAI,KAAK,KAAK,CAAC,GAAGA,IAAI,GAAG,EAAE;EACtN,OAAOH,GAAG,CAACK,UAAU,CAAC,OAAO,CAAC,IAAI,OAAON,QAAQ,KAAK,UAAU,KAAKG,cAAc,IAAIE,uBAAuB,CAACE,QAAQ,CAACN,GAAG,CAAC,IAAIhC,kBAAkB,CAACsC,QAAQ,CAACN,GAAG,CAAC,CAAC,IAAIC,aAAa,IAAIhC,SAAS,CAACqC,QAAQ,CAACN,GAAG,CAAC;AAC/M,CAAC;AACD,OAAO,IAAIO,WAAW,GAAGA,CAACtB,KAAK,EAAEgB,aAAa,EAAEC,cAAc,KAAK;EACjE,IAAI,CAACjB,KAAK,IAAI,OAAOA,KAAK,KAAK,UAAU,IAAI,OAAOA,KAAK,KAAK,SAAS,EAAE;IACvE,OAAO,IAAI;EACb;EACA,IAAIuB,UAAU,GAAGvB,KAAK;EACtB,IAAI,aAAarB,cAAc,CAACqB,KAAK,CAAC,EAAE;IACtCuB,UAAU,GAAGvB,KAAK,CAACA,KAAK;EAC1B;EACA,IAAI,OAAOuB,UAAU,KAAK,QAAQ,IAAI,OAAOA,UAAU,KAAK,UAAU,EAAE;IACtE,OAAO,IAAI;EACb;EACA,IAAIC,GAAG,GAAG,CAAC,CAAC;;EAEZ;AACF;AACA;AACA;AACA;AACA;AACA;EACEC,MAAM,CAACC,IAAI,CAACH,UAAU,CAAC,CAAC1B,OAAO,CAACkB,GAAG,IAAI;IACrC,IAAIY,WAAW;IACf,IAAId,qBAAqB,CAAC,CAACc,WAAW,GAAGJ,UAAU,MAAM,IAAI,IAAII,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACZ,GAAG,CAAC,EAAEA,GAAG,EAAEC,aAAa,EAAEC,cAAc,CAAC,EAAE;MACxJO,GAAG,CAACT,GAAG,CAAC,GAAGQ,UAAU,CAACR,GAAG,CAAC;IAC5B;EACF,CAAC,CAAC;EACF,OAAOS,GAAG;AACZ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}