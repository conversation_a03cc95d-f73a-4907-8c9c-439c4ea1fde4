import { useEffect, useRef } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { supabase } from '../lib/supabase';
import { toast } from 'react-toastify';

// Hook للاشتراك في التحديثات المباشرة للملفات
export const useRealtimeFiles = (options = {}) => {
  const queryClient = useQueryClient();
  const subscriptionRef = useRef(null);
  const { 
    showNotifications = true, 
    onFileAdded, 
    onFileUpdated, 
    onFileDeleted 
  } = options;

  useEffect(() => {
    // إنشاء الاشتراك
    subscriptionRef.current = supabase
      .channel('files-realtime')
      .on('postgres_changes', 
        { 
          event: 'INSERT', 
          schema: 'public', 
          table: 'files' 
        }, 
        (payload) => {
          console.log('New file added:', payload.new);
          
          // تحديث cache
          queryClient.invalidateQueries(['files']);
          queryClient.invalidateQueries(['admin-files']);
          queryClient.invalidateQueries(['admin-stats']);
          
          // إشعار
          if (showNotifications) {
            toast.success(`تم إضافة ملف جديد: ${payload.new.title}`);
          }
          
          // callback مخصص
          onFileAdded?.(payload.new);
        }
      )
      .on('postgres_changes', 
        { 
          event: 'UPDATE', 
          schema: 'public', 
          table: 'files' 
        }, 
        (payload) => {
          console.log('File updated:', payload.new);
          
          // تحديث cache
          queryClient.invalidateQueries(['files']);
          queryClient.invalidateQueries(['admin-files']);
          
          // callback مخصص
          onFileUpdated?.(payload.new, payload.old);
        }
      )
      .on('postgres_changes', 
        { 
          event: 'DELETE', 
          schema: 'public', 
          table: 'files' 
        }, 
        (payload) => {
          console.log('File deleted:', payload.old);
          
          // تحديث cache
          queryClient.invalidateQueries(['files']);
          queryClient.invalidateQueries(['admin-files']);
          queryClient.invalidateQueries(['admin-stats']);
          
          // إشعار
          if (showNotifications) {
            toast.info(`تم حذف ملف: ${payload.old.title}`);
          }
          
          // callback مخصص
          onFileDeleted?.(payload.old);
        }
      )
      .subscribe();

    // تنظيف الاشتراك عند إلغاء التحميل
    return () => {
      if (subscriptionRef.current) {
        subscriptionRef.current.unsubscribe();
      }
    };
  }, [queryClient, showNotifications, onFileAdded, onFileUpdated, onFileDeleted]);

  return subscriptionRef.current;
};

// Hook للاشتراك في التحديثات المباشرة للتقييمات
export const useRealtimeRatings = (options = {}) => {
  const queryClient = useQueryClient();
  const subscriptionRef = useRef(null);
  const { showNotifications = false, onRatingAdded, onRatingUpdated } = options;

  useEffect(() => {
    subscriptionRef.current = supabase
      .channel('ratings-realtime')
      .on('postgres_changes', 
        { 
          event: '*', 
          schema: 'public', 
          table: 'ratings' 
        }, 
        (payload) => {
          console.log('Rating changed:', payload);
          
          // تحديث cache
          queryClient.invalidateQueries(['files']);
          queryClient.invalidateQueries(['admin-files']);
          queryClient.invalidateQueries(['admin-stats']);
          
          if (payload.eventType === 'INSERT') {
            onRatingAdded?.(payload.new);
          } else if (payload.eventType === 'UPDATE') {
            onRatingUpdated?.(payload.new, payload.old);
          }
        }
      )
      .subscribe();

    return () => {
      if (subscriptionRef.current) {
        subscriptionRef.current.unsubscribe();
      }
    };
  }, [queryClient, showNotifications, onRatingAdded, onRatingUpdated]);

  return subscriptionRef.current;
};

// Hook للاشتراك في التحديثات المباشرة للتحميلات
export const useRealtimeDownloads = (options = {}) => {
  const queryClient = useQueryClient();
  const subscriptionRef = useRef(null);
  const { showNotifications = false, onDownloadAdded } = options;

  useEffect(() => {
    subscriptionRef.current = supabase
      .channel('downloads-realtime')
      .on('postgres_changes', 
        { 
          event: 'INSERT', 
          schema: 'public', 
          table: 'downloads' 
        }, 
        (payload) => {
          console.log('New download:', payload.new);
          
          // تحديث cache
          queryClient.invalidateQueries(['files']);
          queryClient.invalidateQueries(['admin-files']);
          queryClient.invalidateQueries(['admin-stats']);
          queryClient.invalidateQueries(['user-stats']);
          
          onDownloadAdded?.(payload.new);
        }
      )
      .subscribe();

    return () => {
      if (subscriptionRef.current) {
        subscriptionRef.current.unsubscribe();
      }
    };
  }, [queryClient, showNotifications, onDownloadAdded]);

  return subscriptionRef.current;
};

// Hook للاشتراك في التحديثات المباشرة للمستخدمين (للمديرين)
export const useRealtimeUsers = (options = {}) => {
  const queryClient = useQueryClient();
  const subscriptionRef = useRef(null);
  const { showNotifications = true, onUserAdded, onUserUpdated } = options;

  useEffect(() => {
    subscriptionRef.current = supabase
      .channel('users-realtime')
      .on('postgres_changes', 
        { 
          event: 'INSERT', 
          schema: 'public', 
          table: 'profiles' 
        }, 
        (payload) => {
          console.log('New user registered:', payload.new);
          
          // تحديث cache
          queryClient.invalidateQueries(['users']);
          queryClient.invalidateQueries(['admin-stats']);
          
          // إشعار للمديرين
          if (showNotifications) {
            toast.info(`مستخدم جديد: ${payload.new.full_name || payload.new.email}`);
          }
          
          onUserAdded?.(payload.new);
        }
      )
      .on('postgres_changes', 
        { 
          event: 'UPDATE', 
          schema: 'public', 
          table: 'profiles' 
        }, 
        (payload) => {
          console.log('User updated:', payload.new);
          
          // تحديث cache
          queryClient.invalidateQueries(['users']);
          
          onUserUpdated?.(payload.new, payload.old);
        }
      )
      .subscribe();

    return () => {
      if (subscriptionRef.current) {
        subscriptionRef.current.unsubscribe();
      }
    };
  }, [queryClient, showNotifications, onUserAdded, onUserUpdated]);

  return subscriptionRef.current;
};

// Hook شامل للتحديثات المباشرة (يجمع كل الاشتراكات)
export const useRealtimeSubscriptions = (options = {}) => {
  const { 
    enableFiles = true,
    enableRatings = true,
    enableDownloads = true,
    enableUsers = false, // فقط للمديرين
    ...restOptions 
  } = options;

  const filesSubscription = useRealtimeFiles(enableFiles ? restOptions : { showNotifications: false });
  const ratingsSubscription = useRealtimeRatings(enableRatings ? restOptions : { showNotifications: false });
  const downloadsSubscription = useRealtimeDownloads(enableDownloads ? restOptions : { showNotifications: false });
  const usersSubscription = useRealtimeUsers(enableUsers ? restOptions : { showNotifications: false });

  return {
    files: filesSubscription,
    ratings: ratingsSubscription,
    downloads: downloadsSubscription,
    users: usersSubscription
  };
};

// Hook للحصول على حالة الاتصال المباشر
export const useRealtimeStatus = () => {
  const [status, setStatus] = useState('DISCONNECTED');

  useEffect(() => {
    const channel = supabase.channel('status-check');
    
    channel
      .on('presence', { event: 'sync' }, () => {
        setStatus('CONNECTED');
      })
      .on('presence', { event: 'join' }, () => {
        setStatus('CONNECTED');
      })
      .on('presence', { event: 'leave' }, () => {
        setStatus('DISCONNECTED');
      })
      .subscribe((status) => {
        setStatus(status);
      });

    return () => {
      channel.unsubscribe();
    };
  }, []);

  return status;
};

export default {
  useRealtimeFiles,
  useRealtimeRatings,
  useRealtimeDownloads,
  useRealtimeUsers,
  useRealtimeSubscriptions,
  useRealtimeStatus
};
