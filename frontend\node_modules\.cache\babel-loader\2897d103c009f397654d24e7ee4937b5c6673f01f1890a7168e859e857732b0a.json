{"ast": null, "code": "import * as React from 'react';\nfunction ScreenReader({\n  text\n}) {\n  return /*#__PURE__*/React.createElement(\"span\", {\n    style: {\n      position: 'absolute',\n      width: '0.1px',\n      height: '0.1px',\n      overflow: 'hidden'\n    }\n  }, text);\n}\nexport { ScreenReader as default };", "map": {"version": 3, "names": ["ScreenReader", "text", "React", "createElement", "style", "position", "width", "height", "overflow"], "sources": ["D:\\menasa\\frontend\\node_modules\\@tanstack\\react-query-devtools\\src\\screenreader.tsx"], "sourcesContent": ["import * as React from 'react'\n\nexport default function ScreenReader({ text }: { text: string }) {\n  return (\n    <span\n      style={{\n        position: 'absolute',\n        width: '0.1px',\n        height: '0.1px',\n        overflow: 'hidden',\n      }}\n    >\n      {text}\n    </span>\n  )\n}\n"], "mappings": ";AAEe,SAASA,YAATA,CAAsB;EAAEC;AAAF,CAAtB,EAAkD;EAC/D,oBACEC,KAAA,CAAAC,aAAA;IACEC,KAAK,EAAE;MACLC,QAAQ,EAAE,UADL;MAELC,KAAK,EAAE,OAFF;MAGLC,MAAM,EAAE,OAHH;MAILC,QAAQ,EAAE;IAJL;EADT,GAQGP,IARH,CADF;AAYD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}