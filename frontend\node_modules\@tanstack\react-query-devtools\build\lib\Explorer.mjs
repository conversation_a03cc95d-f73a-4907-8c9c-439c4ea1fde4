'use client';
import { extends as _extends } from './_virtual/_rollupPluginBabelHelpers.mjs';
import * as React from 'react';
import SuperJSON from 'superjson';
import { displayValue, styled } from './utils.mjs';

const Entry = styled('div', {
  fontFamily: 'Menlo, monospace',
  fontSize: '1em',
  lineHeight: '1.7',
  outline: 'none',
  wordBreak: 'break-word'
});
const Label = styled('span', {
  color: 'white'
});
const LabelButton = styled('button', {
  cursor: 'pointer',
  color: 'white'
});
const ExpandButton = styled('button', {
  cursor: 'pointer',
  color: 'inherit',
  font: 'inherit',
  outline: 'inherit',
  background: 'transparent',
  border: 'none',
  padding: 0
});
const CopyButton = ({
  value
}) => {
  const [copyState, setCopyState] = React.useState('NoCopy');
  return /*#__PURE__*/React.createElement("button", {
    onClick: copyState === 'NoCopy' ? () => {
      navigator.clipboard.writeText(SuperJSON.stringify(value)).then(() => {
        setCopyState('SuccessCopy');
        setTimeout(() => {
          setCopyState('NoCopy');
        }, 1500);
      }, err => {
        console.error('Failed to copy: ', err);
        setCopyState('ErrorCopy');
        setTimeout(() => {
          setCopyState('NoCopy');
        }, 1500);
      });
    } : undefined,
    style: {
      cursor: 'pointer',
      color: 'inherit',
      font: 'inherit',
      outline: 'inherit',
      background: 'transparent',
      border: 'none',
      padding: 0
    }
  }, copyState === 'NoCopy' ? /*#__PURE__*/React.createElement(Copier, null) : copyState === 'SuccessCopy' ? /*#__PURE__*/React.createElement(CopiedCopier, null) : /*#__PURE__*/React.createElement(ErrorCopier, null));
};
const Value = styled('span', (_props, theme) => ({
  color: theme.danger
}));
const SubEntries = styled('div', {
  marginLeft: '.1em',
  paddingLeft: '1em',
  borderLeft: '2px solid rgba(0,0,0,.15)'
});
const Info = styled('span', {
  color: 'grey',
  fontSize: '.7em'
});
const Expander = ({
  expanded,
  style = {}
}) => /*#__PURE__*/React.createElement("span", {
  style: {
    display: 'inline-block',
    transition: 'all .1s ease',
    transform: "rotate(" + (expanded ? 90 : 0) + "deg) " + (style.transform || ''),
    ...style
  }
}, "\u25B6");

const Copier = () => /*#__PURE__*/React.createElement("span", {
  "aria-label": "Copy object to clipboard",
  title: "Copy object to clipboard",
  style: {
    paddingLeft: '1em'
  }
}, /*#__PURE__*/React.createElement("svg", {
  height: "12",
  viewBox: "0 0 16 12",
  width: "10"
}, /*#__PURE__*/React.createElement("path", {
  fill: "currentColor",
  d: "M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 010 1.5h-1.5a.25.25 0 00-.25.25v7.5c0 .*************.25h7.5a.25.25 0 00.25-.25v-1.5a.75.75 0 011.5 0v1.5A1.75 1.75 0 019.25 16h-7.5A1.75 1.75 0 010 14.25v-7.5z"
}), /*#__PURE__*/React.createElement("path", {
  fill: "currentColor",
  d: "M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0114.25 11h-7.5A1.75 1.75 0 015 9.25v-7.5zm1.75-.25a.25.25 0 00-.25.25v7.5c0 .*************.25h7.5a.25.25 0 00.25-.25v-7.5a.25.25 0 00-.25-.25h-7.5z"
})));

const ErrorCopier = () => /*#__PURE__*/React.createElement("span", {
  "aria-label": "Failed copying to clipboard",
  title: "Failed copying to clipboard",
  style: {
    paddingLeft: '1em',
    display: 'flex',
    alignItems: 'center'
  }
}, /*#__PURE__*/React.createElement("svg", {
  height: "12",
  viewBox: "0 0 16 12",
  width: "10",
  display: "block"
}, /*#__PURE__*/React.createElement("path", {
  fill: "red",
  d: "M3.72 3.72a.75.75 0 011.06 0L8 6.94l3.22-3.22a.75.75 0 111.06 1.06L9.06 8l3.22 3.22a.75.75 0 11-1.06 1.06L8 9.06l-3.22 3.22a.75.75 0 01-1.06-1.06L6.94 8 3.72 4.78a.75.75 0 010-1.06z"
})), /*#__PURE__*/React.createElement("span", {
  style: {
    color: 'red',
    fontSize: '12px',
    paddingLeft: '4px',
    position: 'relative',
    top: '2px'
  }
}, "See console"));

const CopiedCopier = () => /*#__PURE__*/React.createElement("span", {
  "aria-label": "Object copied to clipboard",
  title: "Object copied to clipboard",
  style: {
    paddingLeft: '1em',
    display: 'inline-block',
    verticalAlign: 'middle'
  }
}, /*#__PURE__*/React.createElement("svg", {
  height: "16",
  viewBox: "0 0 16 16",
  width: "16",
  display: "block"
}, /*#__PURE__*/React.createElement("path", {
  fill: "green",
  d: "M13.78 4.22a.75.75 0 010 1.06l-7.25 7.25a.75.75 0 01-1.06 0L2.22 9.28a.75.75 0 011.06-1.06L6 10.94l6.72-6.72a.75.75 0 011.06 0z"
})));

/**
 * Chunk elements in the array by size
 *
 * when the array cannot be chunked evenly by size, the last chunk will be
 * filled with the remaining elements
 *
 * @example
 * chunkArray(['a','b', 'c', 'd', 'e'], 2) // returns [['a','b'], ['c', 'd'], ['e']]
 */
function chunkArray(array, size) {
  if (size < 1) return [];
  let i = 0;
  const result = [];

  while (i < array.length) {
    result.push(array.slice(i, i + size));
    i = i + size;
  }

  return result;
}
const DefaultRenderer = ({
  handleEntry,
  label,
  value,
  subEntries = [],
  subEntryPages = [],
  type,
  expanded = false,
  copyable = false,
  toggleExpanded,
  pageSize
}) => {
  const [expandedPages, setExpandedPages] = React.useState([]);
  return /*#__PURE__*/React.createElement(Entry, {
    key: label
  }, subEntryPages.length ? /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(ExpandButton, {
    onClick: () => toggleExpanded()
  }, /*#__PURE__*/React.createElement(Expander, {
    expanded: expanded
  }), " ", label, ' ', /*#__PURE__*/React.createElement(Info, null, String(type).toLowerCase() === 'iterable' ? '(Iterable) ' : '', subEntries.length, " ", subEntries.length > 1 ? "items" : "item")), copyable ? /*#__PURE__*/React.createElement(CopyButton, {
    value: value
  }) : null, expanded ? subEntryPages.length === 1 ? /*#__PURE__*/React.createElement(SubEntries, null, subEntries.map(handleEntry)) : /*#__PURE__*/React.createElement(SubEntries, null, subEntryPages.map((entries, index) => /*#__PURE__*/React.createElement("div", {
    key: index
  }, /*#__PURE__*/React.createElement(Entry, null, /*#__PURE__*/React.createElement(LabelButton, {
    onClick: () => setExpandedPages(old => old.includes(index) ? old.filter(d => d !== index) : [...old, index])
  }, /*#__PURE__*/React.createElement(Expander, {
    expanded: expanded
  }), " [", index * pageSize, " ...", ' ', index * pageSize + pageSize - 1, "]"), expandedPages.includes(index) ? /*#__PURE__*/React.createElement(SubEntries, null, entries.map(handleEntry)) : null)))) : null) : /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Label, null, label, ":"), " ", /*#__PURE__*/React.createElement(Value, null, displayValue(value))));
};

function isIterable(x) {
  return Symbol.iterator in x;
}

function Explorer({
  value,
  defaultExpanded,
  renderer = DefaultRenderer,
  pageSize = 100,
  copyable = false,
  ...rest
}) {
  const [expanded, setExpanded] = React.useState(Boolean(defaultExpanded));
  const toggleExpanded = React.useCallback(() => setExpanded(old => !old), []);
  let type = typeof value;
  let subEntries = [];

  const makeProperty = sub => {
    const subDefaultExpanded = defaultExpanded === true ? {
      [sub.label]: true
    } : defaultExpanded == null ? void 0 : defaultExpanded[sub.label];
    return { ...sub,
      defaultExpanded: subDefaultExpanded
    };
  };

  if (Array.isArray(value)) {
    type = 'array';
    subEntries = value.map((d, i) => makeProperty({
      label: i.toString(),
      value: d
    }));
  } else if (value !== null && typeof value === 'object' && isIterable(value) && typeof value[Symbol.iterator] === 'function') {
    type = 'Iterable';
    subEntries = Array.from(value, (val, i) => makeProperty({
      label: i.toString(),
      value: val
    }));
  } else if (typeof value === 'object' && value !== null) {
    type = 'object';
    subEntries = Object.entries(value).map(([key, val]) => makeProperty({
      label: key,
      value: val
    }));
  }

  const subEntryPages = chunkArray(subEntries, pageSize);
  return renderer({
    handleEntry: entry => /*#__PURE__*/React.createElement(Explorer, _extends({
      key: entry.label,
      value: value,
      renderer: renderer,
      copyable: copyable
    }, rest, entry)),
    type,
    subEntries,
    subEntryPages,
    value,
    expanded,
    copyable,
    toggleExpanded,
    pageSize,
    ...rest
  });
}

export { CopyButton, DefaultRenderer, Entry, ExpandButton, Expander, Info, Label, LabelButton, SubEntries, Value, chunkArray, Explorer as default };
//# sourceMappingURL=Explorer.mjs.map
