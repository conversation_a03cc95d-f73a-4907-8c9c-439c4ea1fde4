{"ast": null, "code": "export var defaultPolarAngleAxisProps = {\n  allowDuplicatedCategory: true,\n  // if I set this to false then Tooltip synchronisation stops working in Radar, wtf\n  angleAxisId: 0,\n  axisLine: true,\n  cx: 0,\n  cy: 0,\n  orientation: 'outer',\n  reversed: false,\n  scale: 'auto',\n  tick: true,\n  tickLine: true,\n  tickSize: 8,\n  type: 'category'\n};", "map": {"version": 3, "names": ["defaultPolarAngleAxisProps", "allowDuplicatedCategory", "angleAxisId", "axisLine", "cx", "cy", "orientation", "reversed", "scale", "tick", "tickLine", "tickSize", "type"], "sources": ["D:/menasa/frontend/node_modules/recharts/es6/polar/defaultPolarAngleAxisProps.js"], "sourcesContent": ["export var defaultPolarAngleAxisProps = {\n  allowDuplicatedCategory: true,\n  // if I set this to false then Tooltip synchronisation stops working in Radar, wtf\n  angleAxisId: 0,\n  axisLine: true,\n  cx: 0,\n  cy: 0,\n  orientation: 'outer',\n  reversed: false,\n  scale: 'auto',\n  tick: true,\n  tickLine: true,\n  tickSize: 8,\n  type: 'category'\n};"], "mappings": "AAAA,OAAO,IAAIA,0BAA0B,GAAG;EACtCC,uBAAuB,EAAE,IAAI;EAC7B;EACAC,WAAW,EAAE,CAAC;EACdC,QAAQ,EAAE,IAAI;EACdC,EAAE,EAAE,CAAC;EACLC,EAAE,EAAE,CAAC;EACLC,WAAW,EAAE,OAAO;EACpBC,QAAQ,EAAE,KAAK;EACfC,KAAK,EAAE,MAAM;EACbC,IAAI,EAAE,IAAI;EACVC,QAAQ,EAAE,IAAI;EACdC,QAAQ,EAAE,CAAC;EACXC,IAAI,EAAE;AACR,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}