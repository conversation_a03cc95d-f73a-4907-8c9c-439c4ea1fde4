import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  FiDownload, 
  FiX, 
  FiSmartphone,
  FiMonitor,
  FiWifi,
  FiWifiOff
} from 'react-icons/fi';
import { Button } from '../ui';

const InstallPromptContainer = styled(motion.div)`
  position: fixed;
  bottom: ${({ theme }) => theme.spacing[4]};
  left: ${({ theme }) => theme.spacing[4]};
  right: ${({ theme }) => theme.spacing[4]};
  background: white;
  border-radius: ${({ theme }) => theme.borderRadius.xl};
  box-shadow: ${({ theme }) => theme.shadows.xl};
  border: 1px solid ${({ theme }) => theme.colors.gray[200]};
  padding: ${({ theme }) => theme.spacing[4]};
  z-index: 1000;
  max-width: 400px;
  margin: 0 auto;
  
  @media (min-width: ${({ theme }) => theme.breakpoints.md}) {
    left: auto;
    right: ${({ theme }) => theme.spacing[6]};
    bottom: ${({ theme }) => theme.spacing[6]};
    margin: 0;
  }
`;

const PromptHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: ${({ theme }) => theme.spacing[3]};
`;

const PromptIcon = styled.div`
  width: 48px;
  height: 48px;
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  background: linear-gradient(135deg, ${({ theme }) => theme.colors.primary[500]}, ${({ theme }) => theme.colors.primary[600]});
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: ${({ theme }) => theme.spacing[3]};
`;

const CloseButton = styled(motion.button)`
  background: none;
  border: none;
  padding: ${({ theme }) => theme.spacing[1]};
  border-radius: ${({ theme }) => theme.borderRadius.base};
  cursor: pointer;
  color: ${({ theme }) => theme.colors.gray[400]};
  
  &:hover {
    background: ${({ theme }) => theme.colors.gray[100]};
    color: ${({ theme }) => theme.colors.gray[600]};
  }
`;

const PromptTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  color: ${({ theme }) => theme.colors.gray[900]};
  margin: 0 0 ${({ theme }) => theme.spacing[2]} 0;
`;

const PromptDescription = styled.p`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.gray[600]};
  margin: 0 0 ${({ theme }) => theme.spacing[4]} 0;
  line-height: ${({ theme }) => theme.lineHeights.relaxed};
`;

const PromptActions = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing[2]};
`;

const ConnectionStatus = styled(motion.div)`
  position: fixed;
  top: ${({ theme }) => theme.spacing[4]};
  right: ${({ theme }) => theme.spacing[4]};
  background: ${({ isOnline, theme }) => 
    isOnline ? theme.colors.green[500] : theme.colors.red[500]};
  color: white;
  padding: ${({ theme }) => theme.spacing[2]} ${({ theme }) => theme.spacing[3]};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
  z-index: 1001;
`;

const UpdatePrompt = styled(motion.div)`
  position: fixed;
  top: ${({ theme }) => theme.spacing[4]};
  left: 50%;
  transform: translateX(-50%);
  background: ${({ theme }) => theme.colors.blue[500]};
  color: white;
  padding: ${({ theme }) => theme.spacing[3]} ${({ theme }) => theme.spacing[4]};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[3]};
  z-index: 1001;
  box-shadow: ${({ theme }) => theme.shadows.lg};
`;

const PWAInstallPrompt = () => {
  const [deferredPrompt, setDeferredPrompt] = useState(null);
  const [showInstallPrompt, setShowInstallPrompt] = useState(false);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [showConnectionStatus, setShowConnectionStatus] = useState(false);
  const [showUpdatePrompt, setShowUpdatePrompt] = useState(false);
  const [registration, setRegistration] = useState(null);

  useEffect(() => {
    // تسجيل Service Worker
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.register('/sw.js')
        .then((reg) => {
          console.log('Service Worker registered successfully');
          setRegistration(reg);

          // التحقق من وجود تحديثات
          reg.addEventListener('updatefound', () => {
            const newWorker = reg.installing;
            newWorker.addEventListener('statechange', () => {
              if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                setShowUpdatePrompt(true);
              }
            });
          });
        })
        .catch((error) => {
          console.error('Service Worker registration failed:', error);
        });
    }

    // معالجة حدث beforeinstallprompt
    const handleBeforeInstallPrompt = (e) => {
      e.preventDefault();
      setDeferredPrompt(e);
      
      // إظهار الدعوة للتثبيت بعد تأخير
      setTimeout(() => {
        setShowInstallPrompt(true);
      }, 5000);
    };

    // معالجة تغيير حالة الاتصال
    const handleOnline = () => {
      setIsOnline(true);
      setShowConnectionStatus(true);
      setTimeout(() => setShowConnectionStatus(false), 3000);
    };

    const handleOffline = () => {
      setIsOnline(false);
      setShowConnectionStatus(true);
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const handleInstallClick = async () => {
    if (!deferredPrompt) return;

    deferredPrompt.prompt();
    const { outcome } = await deferredPrompt.userChoice;
    
    if (outcome === 'accepted') {
      console.log('User accepted the install prompt');
    } else {
      console.log('User dismissed the install prompt');
    }
    
    setDeferredPrompt(null);
    setShowInstallPrompt(false);
  };

  const handleDismissInstall = () => {
    setShowInstallPrompt(false);
    // عدم إظهار الدعوة مرة أخرى لمدة أسبوع
    localStorage.setItem('installPromptDismissed', Date.now().toString());
  };

  const handleUpdateClick = () => {
    if (registration && registration.waiting) {
      registration.waiting.postMessage({ type: 'SKIP_WAITING' });
      window.location.reload();
    }
  };

  const handleDismissUpdate = () => {
    setShowUpdatePrompt(false);
  };

  // التحقق من عدم إظهار الدعوة إذا تم رفضها مؤخراً
  useEffect(() => {
    const dismissedTime = localStorage.getItem('installPromptDismissed');
    if (dismissedTime) {
      const weekInMs = 7 * 24 * 60 * 60 * 1000;
      if (Date.now() - parseInt(dismissedTime) < weekInMs) {
        setShowInstallPrompt(false);
      }
    }
  }, []);

  return (
    <>
      {/* دعوة التثبيت */}
      <AnimatePresence>
        {showInstallPrompt && deferredPrompt && (
          <InstallPromptContainer
            initial={{ opacity: 0, y: 100 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 100 }}
            transition={{ type: "spring", stiffness: 300, damping: 30 }}
          >
            <PromptHeader>
              <PromptIcon>
                <FiDownload size={24} />
              </PromptIcon>
              <CloseButton
                onClick={handleDismissInstall}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <FiX size={20} />
              </CloseButton>
            </PromptHeader>
            
            <PromptTitle>تثبيت التطبيق</PromptTitle>
            <PromptDescription>
              احصل على تجربة أفضل وأسرع مع إمكانية الوصول دون اتصال بالإنترنت
            </PromptDescription>
            
            <PromptActions>
              <Button
                variant="primary"
                onClick={handleInstallClick}
                leftIcon={<FiDownload size={16} />}
              >
                تثبيت الآن
              </Button>
              <Button
                variant="outline"
                onClick={handleDismissInstall}
              >
                ليس الآن
              </Button>
            </PromptActions>
          </InstallPromptContainer>
        )}
      </AnimatePresence>

      {/* حالة الاتصال */}
      <AnimatePresence>
        {showConnectionStatus && (
          <ConnectionStatus
            isOnline={isOnline}
            initial={{ opacity: 0, y: -50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -50 }}
            transition={{ duration: 0.3 }}
          >
            {isOnline ? <FiWifi size={16} /> : <FiWifiOff size={16} />}
            {isOnline ? 'متصل بالإنترنت' : 'غير متصل - الوضع دون اتصال'}
          </ConnectionStatus>
        )}
      </AnimatePresence>

      {/* دعوة التحديث */}
      <AnimatePresence>
        {showUpdatePrompt && (
          <UpdatePrompt
            initial={{ opacity: 0, y: -50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -50 }}
            transition={{ duration: 0.3 }}
          >
            <span>تحديث جديد متاح للتطبيق</span>
            <div style={{ display: 'flex', gap: '8px' }}>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleUpdateClick}
                style={{ color: 'white', border: '1px solid white' }}
              >
                تحديث
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleDismissUpdate}
                style={{ color: 'white' }}
              >
                <FiX size={16} />
              </Button>
            </div>
          </UpdatePrompt>
        )}
      </AnimatePresence>
    </>
  );
};

export default PWAInstallPrompt;
