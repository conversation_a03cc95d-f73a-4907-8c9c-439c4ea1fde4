import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  FiShare2, 
  FiLink, 
  FiMail,
  FiCopy,
  FiCheck,
  FiX,
  FiUsers,
  FiGlobe,
  FiLock,
  FiClock,
  FiSettings
} from 'react-icons/fi';
import { Button, Input } from '../ui';
import { sharing } from '../../services/api';
import { toast } from 'react-toastify';

const ModalOverlay = styled(motion.div)`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: ${({ theme }) => theme.spacing[4]};
`;

const ModalContent = styled(motion.div)`
  background: white;
  border-radius: ${({ theme }) => theme.borderRadius.xl};
  padding: ${({ theme }) => theme.spacing[6]};
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: ${({ theme }) => theme.shadows.xl};
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing[6]};
  padding-bottom: ${({ theme }) => theme.spacing[4]};
  border-bottom: 1px solid ${({ theme }) => theme.colors.gray[200]};
`;

const ModalTitle = styled.h2`
  font-size: ${({ theme }) => theme.fontSizes.xl};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  color: ${({ theme }) => theme.colors.gray[900]};
  margin: 0;
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
`;

const CloseButton = styled(motion.button)`
  background: none;
  border: none;
  padding: ${({ theme }) => theme.spacing[1]};
  border-radius: ${({ theme }) => theme.borderRadius.base};
  cursor: pointer;
  color: ${({ theme }) => theme.colors.gray[400]};
  
  &:hover {
    background: ${({ theme }) => theme.colors.gray[100]};
    color: ${({ theme }) => theme.colors.gray[600]};
  }
`;

const ShareSection = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing[6]};
`;

const SectionTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  color: ${({ theme }) => theme.colors.gray[800]};
  margin: 0 0 ${({ theme }) => theme.spacing[4]} 0;
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
`;

const ShareOption = styled(motion.div)`
  background: ${({ theme }) => theme.colors.gray[50]};
  border: 1px solid ${({ theme }) => theme.colors.gray[200]};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  padding: ${({ theme }) => theme.spacing[4]};
  margin-bottom: ${({ theme }) => theme.spacing[3]};
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: ${({ theme }) => theme.colors.gray[100]};
    border-color: ${({ theme }) => theme.colors.primary[300]};
  }
  
  &.active {
    background: ${({ theme }) => theme.colors.primary[50]};
    border-color: ${({ theme }) => theme.colors.primary[500]};
  }
`;

const OptionHeader = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[3]};
  margin-bottom: ${({ theme }) => theme.spacing[2]};
`;

const OptionIcon = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: ${({ theme }) => theme.colors.primary[100]};
  color: ${({ theme }) => theme.colors.primary[600]};
  display: flex;
  align-items: center;
  justify-content: center;
`;

const OptionTitle = styled.h4`
  font-size: ${({ theme }) => theme.fontSizes.base};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  color: ${({ theme }) => theme.colors.gray[900]};
  margin: 0;
`;

const OptionDescription = styled.p`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.gray[600]};
  margin: 0;
  line-height: ${({ theme }) => theme.lineHeights.relaxed};
`;

const LinkContainer = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing[2]};
  margin-top: ${({ theme }) => theme.spacing[4]};
`;

const LinkInput = styled(Input)`
  flex: 1;
  font-family: monospace;
  font-size: ${({ theme }) => theme.fontSizes.sm};
`;

const CopyButton = styled(Button)`
  min-width: 100px;
`;

const SettingsContainer = styled.div`
  background: ${({ theme }) => theme.colors.gray[50]};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  padding: ${({ theme }) => theme.spacing[4]};
  margin-top: ${({ theme }) => theme.spacing[4]};
`;

const SettingRow = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing[3]};
  
  &:last-child {
    margin-bottom: 0;
  }
`;

const SettingLabel = styled.label`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.gray[700]};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
`;

const Toggle = styled.input`
  appearance: none;
  width: 44px;
  height: 24px;
  background: ${({ checked, theme }) => 
    checked ? theme.colors.primary[500] : theme.colors.gray[300]};
  border-radius: 12px;
  position: relative;
  cursor: pointer;
  transition: background 0.2s ease;
  
  &:before {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: white;
    top: 2px;
    left: ${({ checked }) => checked ? '22px' : '2px'};
    transition: left 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
`;

const ExpirySelect = styled.select`
  padding: ${({ theme }) => theme.spacing[2]};
  border: 1px solid ${({ theme }) => theme.colors.gray[300]};
  border-radius: ${({ theme }) => theme.borderRadius.base};
  background: white;
  font-size: ${({ theme }) => theme.fontSizes.sm};
`;

const ShareModal = ({ isOpen, onClose, file }) => {
  const [shareType, setShareType] = useState('public');
  const [shareLink, setShareLink] = useState('');
  const [copied, setCopied] = useState(false);
  const [settings, setSettings] = useState({
    allowDownload: true,
    requireAuth: false,
    expiresIn: '30', // days
    password: ''
  });

  useEffect(() => {
    if (isOpen && file) {
      generateShareLink();
    }
  }, [isOpen, file, shareType, settings]);

  const generateShareLink = async () => {
    try {
      const response = await sharing.createShareLink(file.id, {
        type: shareType,
        settings
      });
      setShareLink(response.data.shareUrl);
    } catch (error) {
      toast.error('فشل في إنشاء رابط المشاركة');
      console.error('Error generating share link:', error);
    }
  };

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(shareLink);
      setCopied(true);
      toast.success('تم نسخ الرابط');
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      toast.error('فشل في نسخ الرابط');
    }
  };

  const handleSettingChange = (key, value) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const shareOptions = [
    {
      id: 'public',
      icon: <FiGlobe size={20} />,
      title: 'رابط عام',
      description: 'يمكن لأي شخص لديه الرابط الوصول للملف'
    },
    {
      id: 'private',
      icon: <FiLock size={20} />,
      title: 'رابط خاص',
      description: 'يتطلب تسجيل الدخول للوصول للملف'
    },
    {
      id: 'users',
      icon: <FiUsers size={20} />,
      title: 'مستخدمين محددين',
      description: 'مشاركة مع مستخدمين محددين فقط'
    }
  ];

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <ModalOverlay
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        onClick={onClose}
      >
        <ModalContent
          initial={{ opacity: 0, scale: 0.9, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.9, y: 20 }}
          onClick={(e) => e.stopPropagation()}
        >
          <ModalHeader>
            <ModalTitle>
              <FiShare2 size={24} />
              مشاركة الملف
            </ModalTitle>
            <CloseButton
              onClick={onClose}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <FiX size={20} />
            </CloseButton>
          </ModalHeader>

          <ShareSection>
            <SectionTitle>
              <FiSettings size={18} />
              نوع المشاركة
            </SectionTitle>
            
            {shareOptions.map((option) => (
              <ShareOption
                key={option.id}
                className={shareType === option.id ? 'active' : ''}
                onClick={() => setShareType(option.id)}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <OptionHeader>
                  <OptionIcon>{option.icon}</OptionIcon>
                  <div>
                    <OptionTitle>{option.title}</OptionTitle>
                    <OptionDescription>{option.description}</OptionDescription>
                  </div>
                </OptionHeader>
              </ShareOption>
            ))}
          </ShareSection>

          {shareType !== 'users' && (
            <ShareSection>
              <SectionTitle>
                <FiLink size={18} />
                رابط المشاركة
              </SectionTitle>
              
              <LinkContainer>
                <LinkInput
                  value={shareLink}
                  readOnly
                  placeholder="جاري إنشاء الرابط..."
                />
                <CopyButton
                  variant={copied ? 'success' : 'outline'}
                  onClick={handleCopyLink}
                  disabled={!shareLink}
                  leftIcon={copied ? <FiCheck size={16} /> : <FiCopy size={16} />}
                >
                  {copied ? 'تم النسخ' : 'نسخ'}
                </CopyButton>
              </LinkContainer>

              <SettingsContainer>
                <SectionTitle style={{ fontSize: '16px', marginBottom: '16px' }}>
                  <FiSettings size={16} />
                  إعدادات المشاركة
                </SectionTitle>

                <SettingRow>
                  <SettingLabel>السماح بالتحميل</SettingLabel>
                  <Toggle
                    type="checkbox"
                    checked={settings.allowDownload}
                    onChange={(e) => handleSettingChange('allowDownload', e.target.checked)}
                  />
                </SettingRow>

                <SettingRow>
                  <SettingLabel>يتطلب تسجيل الدخول</SettingLabel>
                  <Toggle
                    type="checkbox"
                    checked={settings.requireAuth}
                    onChange={(e) => handleSettingChange('requireAuth', e.target.checked)}
                  />
                </SettingRow>

                <SettingRow>
                  <SettingLabel>
                    <FiClock size={14} style={{ marginLeft: '4px' }} />
                    انتهاء الصلاحية
                  </SettingLabel>
                  <ExpirySelect
                    value={settings.expiresIn}
                    onChange={(e) => handleSettingChange('expiresIn', e.target.value)}
                  >
                    <option value="1">يوم واحد</option>
                    <option value="7">أسبوع</option>
                    <option value="30">شهر</option>
                    <option value="90">3 أشهر</option>
                    <option value="0">بدون انتهاء</option>
                  </ExpirySelect>
                </SettingRow>
              </SettingsContainer>
            </ShareSection>
          )}

          {shareType === 'users' && (
            <ShareSection>
              <SectionTitle>
                <FiMail size={18} />
                مشاركة مع مستخدمين
              </SectionTitle>
              
              <Input
                placeholder="أدخل عناوين البريد الإلكتروني (مفصولة بفواصل)"
                multiline
                rows={3}
              />
              
              <div style={{ marginTop: '16px', display: 'flex', gap: '8px' }}>
                <Button variant="primary" style={{ flex: 1 }}>
                  إرسال دعوات
                </Button>
                <Button variant="outline">
                  إضافة رسالة
                </Button>
              </div>
            </ShareSection>
          )}
        </ModalContent>
      </ModalOverlay>
    </AnimatePresence>
  );
};

export default ShareModal;
