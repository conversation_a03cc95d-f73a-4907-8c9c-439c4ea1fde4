{"ast": null, "code": "var __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nimport { StorageApiError, StorageUnknownError } from './errors';\nimport { resolveResponse } from './helpers';\nconst _getErrorMessage = err => err.msg || err.message || err.error_description || err.error || JSON.stringify(err);\nconst handleError = (error, reject, options) => __awaiter(void 0, void 0, void 0, function* () {\n  const Res = yield resolveResponse();\n  if (error instanceof Res && !(options === null || options === void 0 ? void 0 : options.noResolveJson)) {\n    error.json().then(err => {\n      reject(new StorageApiError(_getErrorMessage(err), error.status || 500));\n    }).catch(err => {\n      reject(new StorageUnknownError(_getErrorMessage(err), err));\n    });\n  } else {\n    reject(new StorageUnknownError(_getErrorMessage(error), error));\n  }\n});\nconst _getRequestParams = (method, options, parameters, body) => {\n  const params = {\n    method,\n    headers: (options === null || options === void 0 ? void 0 : options.headers) || {}\n  };\n  if (method === 'GET') {\n    return params;\n  }\n  params.headers = Object.assign({\n    'Content-Type': 'application/json'\n  }, options === null || options === void 0 ? void 0 : options.headers);\n  if (body) {\n    params.body = JSON.stringify(body);\n  }\n  return Object.assign(Object.assign({}, params), parameters);\n};\nfunction _handleRequest(fetcher, method, url, options, parameters, body) {\n  return __awaiter(this, void 0, void 0, function* () {\n    return new Promise((resolve, reject) => {\n      fetcher(url, _getRequestParams(method, options, parameters, body)).then(result => {\n        if (!result.ok) throw result;\n        if (options === null || options === void 0 ? void 0 : options.noResolveJson) return result;\n        return result.json();\n      }).then(data => resolve(data)).catch(error => handleError(error, reject, options));\n    });\n  });\n}\nexport function get(fetcher, url, options, parameters) {\n  return __awaiter(this, void 0, void 0, function* () {\n    return _handleRequest(fetcher, 'GET', url, options, parameters);\n  });\n}\nexport function post(fetcher, url, body, options, parameters) {\n  return __awaiter(this, void 0, void 0, function* () {\n    return _handleRequest(fetcher, 'POST', url, options, parameters, body);\n  });\n}\nexport function put(fetcher, url, body, options, parameters) {\n  return __awaiter(this, void 0, void 0, function* () {\n    return _handleRequest(fetcher, 'PUT', url, options, parameters, body);\n  });\n}\nexport function head(fetcher, url, options, parameters) {\n  return __awaiter(this, void 0, void 0, function* () {\n    return _handleRequest(fetcher, 'HEAD', url, Object.assign(Object.assign({}, options), {\n      noResolveJson: true\n    }), parameters);\n  });\n}\nexport function remove(fetcher, url, body, options, parameters) {\n  return __awaiter(this, void 0, void 0, function* () {\n    return _handleRequest(fetcher, 'DELETE', url, options, parameters, body);\n  });\n}", "map": {"version": 3, "names": ["StorageApiError", "StorageUnknownError", "resolveResponse", "_getErrorMessage", "err", "msg", "message", "error_description", "error", "JSON", "stringify", "handleError", "reject", "options", "__awaiter", "Res", "noResolveJson", "json", "then", "status", "catch", "_getRequestParams", "method", "parameters", "body", "params", "headers", "Object", "assign", "_handleRequest", "fetcher", "url", "Promise", "resolve", "result", "ok", "data", "get", "post", "put", "head", "remove"], "sources": ["D:\\menasa\\frontend\\node_modules\\@supabase\\storage-js\\src\\lib\\fetch.ts"], "sourcesContent": ["import { StorageApiError, StorageUnknownError } from './errors'\nimport { resolveResponse } from './helpers'\nimport { FetchParameters } from './types'\n\nexport type Fetch = typeof fetch\n\nexport interface FetchOptions {\n  headers?: {\n    [key: string]: string\n  }\n  noResolveJson?: boolean\n}\n\nexport type RequestMethodType = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'HEAD'\n\nconst _getErrorMessage = (err: any): string =>\n  err.msg || err.message || err.error_description || err.error || JSON.stringify(err)\n\nconst handleError = async (\n  error: unknown,\n  reject: (reason?: any) => void,\n  options?: FetchOptions\n) => {\n  const Res = await resolveResponse()\n\n  if (error instanceof Res && !options?.noResolveJson) {\n    error\n      .json()\n      .then((err) => {\n        reject(new StorageApiError(_getErrorMessage(err), error.status || 500))\n      })\n      .catch((err) => {\n        reject(new StorageUnknownError(_getErrorMessage(err), err))\n      })\n  } else {\n    reject(new StorageUnknownError(_getErrorMessage(error), error))\n  }\n}\n\nconst _getRequestParams = (\n  method: RequestMethodType,\n  options?: FetchOptions,\n  parameters?: FetchParameters,\n  body?: object\n) => {\n  const params: { [k: string]: any } = { method, headers: options?.headers || {} }\n\n  if (method === 'GET') {\n    return params\n  }\n\n  params.headers = { 'Content-Type': 'application/json', ...options?.headers }\n\n  if (body) {\n    params.body = JSON.stringify(body)\n  }\n  return { ...params, ...parameters }\n}\n\nasync function _handleRequest(\n  fetcher: Fetch,\n  method: RequestMethodType,\n  url: string,\n  options?: FetchOptions,\n  parameters?: FetchParameters,\n  body?: object\n): Promise<any> {\n  return new Promise((resolve, reject) => {\n    fetcher(url, _getRequestParams(method, options, parameters, body))\n      .then((result) => {\n        if (!result.ok) throw result\n        if (options?.noResolveJson) return result\n        return result.json()\n      })\n      .then((data) => resolve(data))\n      .catch((error) => handleError(error, reject, options))\n  })\n}\n\nexport async function get(\n  fetcher: Fetch,\n  url: string,\n  options?: FetchOptions,\n  parameters?: FetchParameters\n): Promise<any> {\n  return _handleRequest(fetcher, 'GET', url, options, parameters)\n}\n\nexport async function post(\n  fetcher: Fetch,\n  url: string,\n  body: object,\n  options?: FetchOptions,\n  parameters?: FetchParameters\n): Promise<any> {\n  return _handleRequest(fetcher, 'POST', url, options, parameters, body)\n}\n\nexport async function put(\n  fetcher: Fetch,\n  url: string,\n  body: object,\n  options?: FetchOptions,\n  parameters?: FetchParameters\n): Promise<any> {\n  return _handleRequest(fetcher, 'PUT', url, options, parameters, body)\n}\n\nexport async function head(\n  fetcher: Fetch,\n  url: string,\n  options?: FetchOptions,\n  parameters?: FetchParameters\n): Promise<any> {\n  return _handleRequest(\n    fetcher,\n    'HEAD',\n    url,\n    {\n      ...options,\n      noResolveJson: true,\n    },\n    parameters\n  )\n}\n\nexport async function remove(\n  fetcher: Fetch,\n  url: string,\n  body: object,\n  options?: FetchOptions,\n  parameters?: FetchParameters\n): Promise<any> {\n  return _handleRequest(fetcher, 'DELETE', url, options, parameters, body)\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAASA,eAAe,EAAEC,mBAAmB,QAAQ,UAAU;AAC/D,SAASC,eAAe,QAAQ,WAAW;AAc3C,MAAMC,gBAAgB,GAAIC,GAAQ,IAChCA,GAAG,CAACC,GAAG,IAAID,GAAG,CAACE,OAAO,IAAIF,GAAG,CAACG,iBAAiB,IAAIH,GAAG,CAACI,KAAK,IAAIC,IAAI,CAACC,SAAS,CAACN,GAAG,CAAC;AAErF,MAAMO,WAAW,GAAGA,CAClBH,KAAc,EACdI,MAA8B,EAC9BC,OAAsB,KACpBC,SAAA;EACF,MAAMC,GAAG,GAAG,MAAMb,eAAe,EAAE;EAEnC,IAAIM,KAAK,YAAYO,GAAG,IAAI,EAACF,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEG,aAAa,GAAE;IACnDR,KAAK,CACFS,IAAI,EAAE,CACNC,IAAI,CAAEd,GAAG,IAAI;MACZQ,MAAM,CAAC,IAAIZ,eAAe,CAACG,gBAAgB,CAACC,GAAG,CAAC,EAAEI,KAAK,CAACW,MAAM,IAAI,GAAG,CAAC,CAAC;IACzE,CAAC,CAAC,CACDC,KAAK,CAAEhB,GAAG,IAAI;MACbQ,MAAM,CAAC,IAAIX,mBAAmB,CAACE,gBAAgB,CAACC,GAAG,CAAC,EAAEA,GAAG,CAAC,CAAC;IAC7D,CAAC,CAAC;GACL,MAAM;IACLQ,MAAM,CAAC,IAAIX,mBAAmB,CAACE,gBAAgB,CAACK,KAAK,CAAC,EAAEA,KAAK,CAAC,CAAC;;AAEnE,CAAC;AAED,MAAMa,iBAAiB,GAAGA,CACxBC,MAAyB,EACzBT,OAAsB,EACtBU,UAA4B,EAC5BC,IAAa,KACX;EACF,MAAMC,MAAM,GAAyB;IAAEH,MAAM;IAAEI,OAAO,EAAE,CAAAb,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEa,OAAO,KAAI;EAAE,CAAE;EAEhF,IAAIJ,MAAM,KAAK,KAAK,EAAE;IACpB,OAAOG,MAAM;;EAGfA,MAAM,CAACC,OAAO,GAAAC,MAAA,CAAAC,MAAA;IAAK,cAAc,EAAE;EAAkB,GAAKf,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEa,OAAO,CAAE;EAE5E,IAAIF,IAAI,EAAE;IACRC,MAAM,CAACD,IAAI,GAAGf,IAAI,CAACC,SAAS,CAACc,IAAI,CAAC;;EAEpC,OAAAG,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KAAYH,MAAM,GAAKF,UAAU;AACnC,CAAC;AAED,SAAeM,cAAcA,CAC3BC,OAAc,EACdR,MAAyB,EACzBS,GAAW,EACXlB,OAAsB,EACtBU,UAA4B,EAC5BC,IAAa;;IAEb,OAAO,IAAIQ,OAAO,CAAC,CAACC,OAAO,EAAErB,MAAM,KAAI;MACrCkB,OAAO,CAACC,GAAG,EAAEV,iBAAiB,CAACC,MAAM,EAAET,OAAO,EAAEU,UAAU,EAAEC,IAAI,CAAC,CAAC,CAC/DN,IAAI,CAAEgB,MAAM,IAAI;QACf,IAAI,CAACA,MAAM,CAACC,EAAE,EAAE,MAAMD,MAAM;QAC5B,IAAIrB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEG,aAAa,EAAE,OAAOkB,MAAM;QACzC,OAAOA,MAAM,CAACjB,IAAI,EAAE;MACtB,CAAC,CAAC,CACDC,IAAI,CAAEkB,IAAI,IAAKH,OAAO,CAACG,IAAI,CAAC,CAAC,CAC7BhB,KAAK,CAAEZ,KAAK,IAAKG,WAAW,CAACH,KAAK,EAAEI,MAAM,EAAEC,OAAO,CAAC,CAAC;IAC1D,CAAC,CAAC;EACJ,CAAC;;AAED,OAAM,SAAgBwB,GAAGA,CACvBP,OAAc,EACdC,GAAW,EACXlB,OAAsB,EACtBU,UAA4B;;IAE5B,OAAOM,cAAc,CAACC,OAAO,EAAE,KAAK,EAAEC,GAAG,EAAElB,OAAO,EAAEU,UAAU,CAAC;EACjE,CAAC;;AAED,OAAM,SAAgBe,IAAIA,CACxBR,OAAc,EACdC,GAAW,EACXP,IAAY,EACZX,OAAsB,EACtBU,UAA4B;;IAE5B,OAAOM,cAAc,CAACC,OAAO,EAAE,MAAM,EAAEC,GAAG,EAAElB,OAAO,EAAEU,UAAU,EAAEC,IAAI,CAAC;EACxE,CAAC;;AAED,OAAM,SAAgBe,GAAGA,CACvBT,OAAc,EACdC,GAAW,EACXP,IAAY,EACZX,OAAsB,EACtBU,UAA4B;;IAE5B,OAAOM,cAAc,CAACC,OAAO,EAAE,KAAK,EAAEC,GAAG,EAAElB,OAAO,EAAEU,UAAU,EAAEC,IAAI,CAAC;EACvE,CAAC;;AAED,OAAM,SAAgBgB,IAAIA,CACxBV,OAAc,EACdC,GAAW,EACXlB,OAAsB,EACtBU,UAA4B;;IAE5B,OAAOM,cAAc,CACnBC,OAAO,EACP,MAAM,EACNC,GAAG,EAAAJ,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KAEEf,OAAO;MACVG,aAAa,EAAE;IAAI,IAErBO,UAAU,CACX;EACH,CAAC;;AAED,OAAM,SAAgBkB,MAAMA,CAC1BX,OAAc,EACdC,GAAW,EACXP,IAAY,EACZX,OAAsB,EACtBU,UAA4B;;IAE5B,OAAOM,cAAc,CAACC,OAAO,EAAE,QAAQ,EAAEC,GAAG,EAAElB,OAAO,EAAEU,UAAU,EAAEC,IAAI,CAAC;EAC1E,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}