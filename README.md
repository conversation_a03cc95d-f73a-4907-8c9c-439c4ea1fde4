# 📚 مكتبة الملفات التعليمية

نظام إدارة الملفات التعليمية - منصة شاملة لتنظيم ومشاركة الملفات التعليمية مع واجهة مستخدم احترافية وحديثة.

## ✨ المميزات

### 🎨 تصميم احترافي وحديث
- واجهة مستخدم عصرية مع تصميم متجاوب
- نظام ألوان موحد ومتسق
- رسوم متحركة وتأثيرات بصرية جذابة
- دعم كامل للغة العربية مع اتجاه RTL

### 👥 إدارة المستخدمين
- نظام تسجيل دخول آمن
- أدوار مختلفة (طالب، مدير)
- إدارة شاملة للمستخدمين
- حماية الصفحات حسب الصلاحيات

### 📁 إدارة الملفات
- رفع الملفات بسهولة مع Drag & Drop
- دعم أنواع ملفات متعددة (PDF, DOC, PPT, MP4, MP3, صور)
- معاينة الملفات
- نظام تقييم وتفضيل الملفات
- إحصائيات التحميل

### 🔍 البحث والتصفية
- بحث متقدم في الملفات
- فلترة حسب النوع، المادة، الفصل الدراسي
- اقتراحات البحث الذكية
- حفظ عمليات البحث الأخيرة

### 📊 لوحة تحكم إدارية
- إحصائيات شاملة للنظام
- إدارة الملفات والمستخدمين
- تحليلات الاستخدام
- تقارير مفصلة

## 🛠️ التقنيات المستخدمة

### Frontend
- **React 18** - مكتبة JavaScript لبناء واجهات المستخدم
- **Styled Components** - CSS-in-JS للتصميم
- **Framer Motion** - مكتبة الرسوم المتحركة
- **React Router** - التنقل بين الصفحات
- **React Query** - إدارة حالة الخادم
- **React Dropzone** - رفع الملفات
- **React Icons** - مجموعة أيقونات شاملة
- **React Toastify** - إشعارات المستخدم
- **i18next** - دعم تعدد اللغات

### Backend
- **Node.js** - بيئة تشغيل JavaScript
- **Express.js** - إطار عمل الخادم
- **MongoDB** - قاعدة البيانات
- **Mongoose** - ODM لـ MongoDB
- **JWT** - المصادقة والتفويض
- **Multer** - رفع الملفات
- **bcryptjs** - تشفير كلمات المرور

## 🚀 التثبيت والتشغيل

### المتطلبات
- Node.js (الإصدار 16 أو أحدث)
- MongoDB
- npm أو yarn

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone https://github.com/your-username/educational-file-manager.git
cd educational-file-manager
```

2. **تثبيت التبعيات**
```bash
# تثبيت تبعيات المشروع الرئيسي
npm install

# تثبيت تبعيات Frontend
cd frontend
npm install

# تثبيت تبعيات Backend
cd ../backend
npm install
```

3. **إعداد متغيرات البيئة**
```bash
# في مجلد backend
cp .env.example .env
```

قم بتحرير ملف `.env` وإضافة:
```env
MONGODB_URI=mongodb://localhost:27017/educational-files
JWT_SECRET=your-secret-key
PORT=5000
```

4. **تشغيل قاعدة البيانات**
```bash
# تأكد من تشغيل MongoDB
mongod
```

5. **تشغيل التطبيق**
```bash
# من المجلد الرئيسي
npm run dev
```

سيتم تشغيل:
- Frontend على: http://localhost:3000
- Backend على: http://localhost:5000

## 📱 الاستجابة للأجهزة

التطبيق مُحسَّن للعمل على جميع الأجهزة:
- 📱 الهواتف الذكية (320px+)
- 📱 الأجهزة اللوحية (768px+)
- 💻 أجهزة الكمبيوتر المحمولة (1024px+)
- 🖥️ شاشات سطح المكتب (1280px+)

## 🎨 نظام التصميم

### الألوان
- **الأساسي**: درجات الأزرق (#0ea5e9)
- **الثانوي**: درجات البنفسجي (#d946ef)
- **النجاح**: درجات الأخضر (#22c55e)
- **التحذير**: درجات الأصفر (#f59e0b)
- **الخطأ**: درجات الأحمر (#ef4444)

### الخطوط
- **الأساسي**: Cairo (للنصوص العربية)
- **الثانوي**: Roboto (للنصوص الإنجليزية)
- **الكود**: Fira Code (للكود البرمجي)

## 🔧 البنية والمجلدات

```
educational-file-manager/
├── frontend/
│   ├── public/
│   ├── src/
│   │   ├── components/
│   │   │   ├── ui/           # مكونات UI أساسية
│   │   │   ├── admin/        # مكونات لوحة الإدارة
│   │   │   └── ...
│   │   ├── pages/            # صفحات التطبيق
│   │   ├── contexts/         # React Contexts
│   │   ├── services/         # خدمات API
│   │   ├── styles/           # ملفات التصميم
│   │   └── ...
│   └── package.json
├── backend/
│   ├── models/               # نماذج قاعدة البيانات
│   ├── routes/               # مسارات API
│   ├── middleware/           # Middleware functions
│   ├── uploads/              # مجلد الملفات المرفوعة
│   └── server.js
└── package.json
```

## 🧪 الاختبار

```bash
# تشغيل اختبارات Frontend
cd frontend
npm test

# تشغيل اختبارات Backend
cd backend
npm test
```

## 📦 البناء للإنتاج

```bash
# بناء Frontend
cd frontend
npm run build

# تشغيل Backend في وضع الإنتاج
cd backend
npm start
```

## 🤝 المساهمة

نرحب بمساهماتكم! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push للفرع (`git push origin feature/amazing-feature`)
5. فتح Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 👨‍💻 المطورون

- **اسم المطور** - [GitHub](https://github.com/username)

## 📞 التواصل

- البريد الإلكتروني: <EMAIL>
- الموقع: https://example.com
- Twitter: [@username](https://twitter.com/username)

## 🙏 شكر وتقدير

شكر خاص لجميع المساهمين والمكتبات مفتوحة المصدر المستخدمة في هذا المشروع.

---

⭐ إذا أعجبك هذا المشروع، لا تنس إعطاؤه نجمة على GitHub!
