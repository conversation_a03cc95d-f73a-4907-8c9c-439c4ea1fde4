import React from 'react';
import styled, { css } from 'styled-components';
import { motion } from 'framer-motion';

const ButtonBase = styled(motion.button)`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: ${({ theme }) => theme.spacing[2]};
  font-family: ${({ theme }) => theme.fonts.primary};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  transition: all ${({ theme }) => theme.transitions.fast};
  cursor: pointer;
  border: none;
  text-decoration: none;
  position: relative;
  overflow: hidden;

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  &:focus {
    outline: 2px solid ${({ theme }) => theme.colors.primary[500]};
    outline-offset: 2px;
  }

  /* Size Variants */
  ${({ size, theme }) => {
    switch (size) {
      case 'sm':
        return css`
          padding: ${theme.spacing[2]} ${theme.spacing[3]};
          font-size: ${theme.fontSizes.sm};
          min-height: 32px;
        `;
      case 'lg':
        return css`
          padding: ${theme.spacing[4]} ${theme.spacing[6]};
          font-size: ${theme.fontSizes.lg};
          min-height: 48px;
        `;
      case 'xl':
        return css`
          padding: ${theme.spacing[5]} ${theme.spacing[8]};
          font-size: ${theme.fontSizes.xl};
          min-height: 56px;
        `;
      default:
        return css`
          padding: ${theme.spacing[3]} ${theme.spacing[4]};
          font-size: ${theme.fontSizes.base};
          min-height: 40px;
        `;
    }
  }}

  /* Variant Styles */
  ${({ variant, theme }) => {
    switch (variant) {
      case 'primary':
        return css`
          background: linear-gradient(135deg, ${theme.colors.primary[500]}, ${theme.colors.primary[600]});
          color: ${theme.colors.white};
          box-shadow: ${theme.shadows.md};

          &:hover:not(:disabled) {
            background: linear-gradient(135deg, ${theme.colors.primary[600]}, ${theme.colors.primary[700]});
            box-shadow: ${theme.shadows.lg};
            transform: translateY(-1px);
          }

          &:active {
            transform: translateY(0);
            box-shadow: ${theme.shadows.base};
          }
        `;
      case 'secondary':
        return css`
          background: ${theme.colors.white};
          color: ${theme.colors.gray[700]};
          border: 1px solid ${theme.colors.gray[300]};
          box-shadow: ${theme.shadows.sm};

          &:hover:not(:disabled) {
            background: ${theme.colors.gray[50]};
            border-color: ${theme.colors.gray[400]};
            box-shadow: ${theme.shadows.md};
          }
        `;
      case 'outline':
        return css`
          background: transparent;
          color: ${theme.colors.primary[600]};
          border: 2px solid ${theme.colors.primary[500]};

          &:hover:not(:disabled) {
            background: ${theme.colors.primary[50]};
            border-color: ${theme.colors.primary[600]};
          }
        `;
      case 'ghost':
        return css`
          background: transparent;
          color: ${theme.colors.gray[600]};

          &:hover:not(:disabled) {
            background: ${theme.colors.gray[100]};
            color: ${theme.colors.gray[800]};
          }
        `;
      case 'danger':
        return css`
          background: linear-gradient(135deg, ${theme.colors.error[500]}, ${theme.colors.error[600]});
          color: ${theme.colors.white};
          box-shadow: ${theme.shadows.md};

          &:hover:not(:disabled) {
            background: linear-gradient(135deg, ${theme.colors.error[600]}, ${theme.colors.error[700]});
            box-shadow: ${theme.shadows.lg};
          }
        `;
      default:
        return css`
          background: ${theme.colors.gray[100]};
          color: ${theme.colors.gray[700]};

          &:hover:not(:disabled) {
            background: ${theme.colors.gray[200]};
          }
        `;
    }
  }}

  /* Full Width */
  ${({ fullWidth }) =>
    fullWidth &&
    css`
      width: 100%;
    `}

  /* Loading State */
  ${({ loading }) =>
    loading &&
    css`
      cursor: wait;
      opacity: 0.8;
    `}
`;

const LoadingSpinner = styled.div`
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;

  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }
`;

const Button = React.forwardRef(({
  children,
  variant = 'default',
  size = 'md',
  fullWidth = false,
  loading = false,
  disabled = false,
  leftIcon,
  rightIcon,
  onClick,
  type = 'button',
  ...props
}, ref) => {
  const handleClick = (e) => {
    if (loading || disabled) return;
    onClick?.(e);
  };

  return (
    <ButtonBase
      ref={ref}
      variant={variant}
      size={size}
      fullWidth={fullWidth}
      loading={loading}
      disabled={disabled || loading}
      onClick={handleClick}
      type={type}
      whileHover={{ scale: disabled || loading ? 1 : 1.02 }}
      whileTap={{ scale: disabled || loading ? 1 : 0.98 }}
      {...props}
    >
      {loading ? (
        <LoadingSpinner />
      ) : (
        <>
          {leftIcon && <span>{leftIcon}</span>}
          {children}
          {rightIcon && <span>{rightIcon}</span>}
        </>
      )}
    </ButtonBase>
  );
});

Button.displayName = 'Button';

export default Button;
