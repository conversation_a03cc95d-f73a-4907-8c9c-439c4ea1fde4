{"ast": null, "code": "import { createSlice } from '@reduxjs/toolkit';\nimport { castDraft } from 'immer';\nvar initialState = {\n  radiusAxis: {},\n  angleAxis: {}\n};\nvar polarAxisSlice = createSlice({\n  name: 'polarAxis',\n  initialState,\n  reducers: {\n    addRadiusAxis(state, action) {\n      state.radiusAxis[action.payload.id] = castDraft(action.payload);\n    },\n    removeRadiusAxis(state, action) {\n      delete state.radiusAxis[action.payload.id];\n    },\n    addAngleAxis(state, action) {\n      state.angleAxis[action.payload.id] = castDraft(action.payload);\n    },\n    removeAngleAxis(state, action) {\n      delete state.angleAxis[action.payload.id];\n    }\n  }\n});\nexport var {\n  addRadiusAxis,\n  removeRadiusAxis,\n  addAngleAxis,\n  removeAngleAxis\n} = polarAxisSlice.actions;\nexport var polarAxisReducer = polarAxisSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "castDraft", "initialState", "radiusAxis", "angleAxis", "polarAxisSlice", "name", "reducers", "addRadiusAxis", "state", "action", "payload", "id", "removeRadiusAxis", "addAngleAxis", "removeAngleAxis", "actions", "polarAxisReducer", "reducer"], "sources": ["D:/menasa/frontend/node_modules/recharts/es6/state/polarAxisSlice.js"], "sourcesContent": ["import { createSlice } from '@reduxjs/toolkit';\nimport { castDraft } from 'immer';\nvar initialState = {\n  radiusAxis: {},\n  angleAxis: {}\n};\nvar polarAxisSlice = createSlice({\n  name: 'polarAxis',\n  initialState,\n  reducers: {\n    addRadiusAxis(state, action) {\n      state.radiusAxis[action.payload.id] = castDraft(action.payload);\n    },\n    removeRadiusAxis(state, action) {\n      delete state.radiusAxis[action.payload.id];\n    },\n    addAngleAxis(state, action) {\n      state.angleAxis[action.payload.id] = castDraft(action.payload);\n    },\n    removeAngleAxis(state, action) {\n      delete state.angleAxis[action.payload.id];\n    }\n  }\n});\nexport var {\n  addRadiusAxis,\n  removeRadiusAxis,\n  addAngleAxis,\n  removeAngleAxis\n} = polarAxisSlice.actions;\nexport var polarAxisReducer = polarAxisSlice.reducer;"], "mappings": "AAAA,SAASA,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,SAAS,QAAQ,OAAO;AACjC,IAAIC,YAAY,GAAG;EACjBC,UAAU,EAAE,CAAC,CAAC;EACdC,SAAS,EAAE,CAAC;AACd,CAAC;AACD,IAAIC,cAAc,GAAGL,WAAW,CAAC;EAC/BM,IAAI,EAAE,WAAW;EACjBJ,YAAY;EACZK,QAAQ,EAAE;IACRC,aAAaA,CAACC,KAAK,EAAEC,MAAM,EAAE;MAC3BD,KAAK,CAACN,UAAU,CAACO,MAAM,CAACC,OAAO,CAACC,EAAE,CAAC,GAAGX,SAAS,CAACS,MAAM,CAACC,OAAO,CAAC;IACjE,CAAC;IACDE,gBAAgBA,CAACJ,KAAK,EAAEC,MAAM,EAAE;MAC9B,OAAOD,KAAK,CAACN,UAAU,CAACO,MAAM,CAACC,OAAO,CAACC,EAAE,CAAC;IAC5C,CAAC;IACDE,YAAYA,CAACL,KAAK,EAAEC,MAAM,EAAE;MAC1BD,KAAK,CAACL,SAAS,CAACM,MAAM,CAACC,OAAO,CAACC,EAAE,CAAC,GAAGX,SAAS,CAACS,MAAM,CAACC,OAAO,CAAC;IAChE,CAAC;IACDI,eAAeA,CAACN,KAAK,EAAEC,MAAM,EAAE;MAC7B,OAAOD,KAAK,CAACL,SAAS,CAACM,MAAM,CAACC,OAAO,CAACC,EAAE,CAAC;IAC3C;EACF;AACF,CAAC,CAAC;AACF,OAAO,IAAI;EACTJ,aAAa;EACbK,gBAAgB;EAChBC,YAAY;EACZC;AACF,CAAC,GAAGV,cAAc,CAACW,OAAO;AAC1B,OAAO,IAAIC,gBAAgB,GAAGZ,cAAc,CAACa,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}