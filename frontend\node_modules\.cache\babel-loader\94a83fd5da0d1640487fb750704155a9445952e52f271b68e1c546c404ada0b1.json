{"ast": null, "code": "'use client';\n\nimport { ReactQueryDevtools as ReactQueryDevtools$1, ReactQueryDevtoolsPanel as ReactQueryDevtoolsPanel$1 } from './devtools.mjs';\nconst ReactQueryDevtools = process.env.NODE_ENV !== 'development' ? function () {\n  return null;\n} : ReactQueryDevtools$1;\nconst ReactQueryDevtoolsPanel = process.env.NODE_ENV !== 'development' ? function () {\n  return null;\n} : ReactQueryDevtoolsPanel$1;\nexport { ReactQueryDevtools, ReactQueryDevtoolsPanel };", "map": {"version": 3, "names": ["ReactQueryDevtools", "process", "env", "NODE_ENV", "ReactQueryDevtools$1", "ReactQueryDevtoolsPanel", "ReactQueryDevtoolsPanel$1"], "sources": ["D:\\menasa\\frontend\\node_modules\\@tanstack\\react-query-devtools\\src\\index.ts"], "sourcesContent": ["'use client'\n\nimport * as devtools from './devtools'\n\nexport const ReactQueryDevtools: typeof devtools['ReactQueryDevtools'] =\n  process.env.NODE_ENV !== 'development'\n    ? function () {\n        return null\n      }\n    : devtools.ReactQueryDevtools\n\nexport const ReactQueryDevtoolsPanel: typeof devtools['ReactQueryDevtoolsPanel'] =\n  process.env.NODE_ENV !== 'development'\n    ? (function () {\n        return null\n      } as any)\n    : devtools.ReactQueryDevtoolsPanel\n"], "mappings": ";;;AAIO,MAAAA,kBAAA,GAAAC,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAGC;AACD,IAAAC,oBAAA;AAGA,MAAAC,uBAAA,GAAAJ,OAAA,CAAAC,GAAA,CAAAC,QAAA;EAGC;AACD,IAAAG,yBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}