{"ast": null, "code": "import { sqrt } from \"../math.js\";\nexport default {\n  draw(context, size) {\n    const w = sqrt(size);\n    const x = -w / 2;\n    context.rect(x, x, w, w);\n  }\n};", "map": {"version": 3, "names": ["sqrt", "draw", "context", "size", "w", "x", "rect"], "sources": ["D:/menasa/frontend/node_modules/d3-shape/src/symbol/square.js"], "sourcesContent": ["import {sqrt} from \"../math.js\";\n\nexport default {\n  draw(context, size) {\n    const w = sqrt(size);\n    const x = -w / 2;\n    context.rect(x, x, w, w);\n  }\n};\n"], "mappings": "AAAA,SAAQA,IAAI,QAAO,YAAY;AAE/B,eAAe;EACbC,IAAIA,CAACC,OAAO,EAAEC,IAAI,EAAE;IAClB,MAAMC,CAAC,GAAGJ,IAAI,CAACG,IAAI,CAAC;IACpB,MAAME,CAAC,GAAG,CAACD,CAAC,GAAG,CAAC;IAChBF,OAAO,CAACI,IAAI,CAACD,CAAC,EAAEA,CAAC,EAAED,CAAC,EAAEA,CAAC,CAAC;EAC1B;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}