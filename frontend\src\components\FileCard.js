import React, { useState } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import {
  FiDownload,
  FiEye,
  FiHeart,
  FiStar,
  FiFile,
  FiFileText,
  FiVideo,
  FiMusic,
  FiImage,
  FiCalendar,
  FiUser,
  FiShare2
} from 'react-icons/fi';
import { Card, Button } from './ui';
import { files } from '../services/api';
import ShareModal from './sharing/ShareModal';
import { toast } from 'react-toastify';

const FileCardContainer = styled(Card)`
  transition: all ${({ theme }) => theme.transitions.normal};
  cursor: pointer;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: ${({ theme }) => theme.shadows.xl};
  }
`;

const FileIcon = styled.div`
  width: 60px;
  height: 60px;
  border-radius: ${({ theme }) => theme.borderRadius.xl};
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  margin-bottom: ${({ theme }) => theme.spacing[4]};
  
  ${({ fileType, theme }) => {
    switch (fileType) {
      case 'pdf':
        return `background: linear-gradient(135deg, ${theme.colors.error[500]}, ${theme.colors.error[600]});`;
      case 'doc':
      case 'docx':
        return `background: linear-gradient(135deg, ${theme.colors.primary[500]}, ${theme.colors.primary[600]});`;
      case 'ppt':
      case 'pptx':
        return `background: linear-gradient(135deg, ${theme.colors.warning[500]}, ${theme.colors.warning[600]});`;
      case 'mp4':
      case 'avi':
      case 'mov':
        return `background: linear-gradient(135deg, ${theme.colors.secondary[500]}, ${theme.colors.secondary[600]});`;
      case 'mp3':
      case 'wav':
        return `background: linear-gradient(135deg, ${theme.colors.success[500]}, ${theme.colors.success[600]});`;
      case 'jpg':
      case 'png':
      case 'gif':
        return `background: linear-gradient(135deg, ${theme.colors.primary[400]}, ${theme.colors.primary[500]});`;
      default:
        return `background: linear-gradient(135deg, ${theme.colors.gray[500]}, ${theme.colors.gray[600]});`;
    }
  }}
`;

const FileTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  color: ${({ theme }) => theme.colors.gray[900]};
  margin-bottom: ${({ theme }) => theme.spacing[2]};
  line-height: ${({ theme }) => theme.lineHeights.tight};
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
`;

const FileDescription = styled.p`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.gray[600]};
  margin-bottom: ${({ theme }) => theme.spacing[4]};
  line-height: ${({ theme }) => theme.lineHeights.relaxed};
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
`;

const FileMetadata = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing[2]};
  margin-bottom: ${({ theme }) => theme.spacing[4]};
`;

const MetadataItem = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.gray[600]};
`;

const FileStats = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: ${({ theme }) => theme.spacing[4]};
  padding-top: ${({ theme }) => theme.spacing[3]};
  border-top: 1px solid ${({ theme }) => theme.colors.gray[100]};
`;

const StatItem = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[1]};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.gray[600]};
`;

const RatingStars = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[1]};
`;

const Star = styled(FiStar)`
  color: ${({ filled, theme }) => 
    filled ? theme.colors.warning[400] : theme.colors.gray[300]};
  fill: ${({ filled, theme }) => 
    filled ? theme.colors.warning[400] : 'none'};
`;

const ActionButtons = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing[2]};
`;

const FavoriteButton = styled(motion.button)`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: none;
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  background: ${({ isFavorite, theme }) => 
    isFavorite ? theme.colors.error[50] : theme.colors.gray[100]};
  color: ${({ isFavorite, theme }) => 
    isFavorite ? theme.colors.error[500] : theme.colors.gray[500]};
  cursor: pointer;
  transition: all ${({ theme }) => theme.transitions.fast};
  
  &:hover {
    background: ${({ isFavorite, theme }) => 
      isFavorite ? theme.colors.error[100] : theme.colors.gray[200]};
  }
`;

const FileCard = ({ file, onView, onDownload, onFavorite }) => {
  const [isDownloading, setIsDownloading] = useState(false);
  const [isFavorite, setIsFavorite] = useState(false);
  const [showShareModal, setShowShareModal] = useState(false);

  const getFileIcon = (fileType) => {
    const type = fileType?.toLowerCase();
    switch (type) {
      case 'pdf':
        return <FiFileText />;
      case 'doc':
      case 'docx':
        return <FiFile />;
      case 'ppt':
      case 'pptx':
        return <FiFile />;
      case 'mp4':
      case 'avi':
      case 'mov':
        return <FiVideo />;
      case 'mp3':
      case 'wav':
        return <FiMusic />;
      case 'jpg':
      case 'png':
      case 'gif':
        return <FiImage />;
      default:
        return <FiFile />;
    }
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA');
  };

  const handleDownload = async () => {
    if (isDownloading) return;
    
    setIsDownloading(true);
    try {
      const response = await files.download(file.id);
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', file.file_name || file.title);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
      
      toast.success('تم تحميل الملف بنجاح');
      onDownload?.(file);
    } catch (error) {
      toast.error('فشل في تحميل الملف');
    } finally {
      setIsDownloading(false);
    }
  };

  const handleFavorite = () => {
    setIsFavorite(!isFavorite);
    onFavorite?.(file, !isFavorite);
  };

  const renderStars = (rating) => {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      stars.push(
        <Star key={i} filled={i <= rating} size={14} />
      );
    }
    return stars;
  };

  return (
    <FileCardContainer
      as={motion.div}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ y: -4 }}
      padding="lg"
    >
      <FileIcon fileType={file.file_type || file.type}>
        {getFileIcon(file.file_type || file.type)}
      </FileIcon>

      <FileTitle>{file.title}</FileTitle>

      {file.description && (
        <FileDescription>{file.description}</FileDescription>
      )}

      <FileMetadata>
        <MetadataItem>
          <FiUser size={16} />
          <span>{file.uploadedBy?.name || file.uploader_name || 'غير محدد'}</span>
        </MetadataItem>
        <MetadataItem>
          <FiCalendar size={16} />
          <span>{formatDate(file.created_at || file.createdAt)}</span>
        </MetadataItem>
        <MetadataItem>
          <FiFile size={16} />
          <span>{formatFileSize(file.file_size || file.size)}</span>
        </MetadataItem>
      </FileMetadata>

      <FileStats>
        <StatItem>
          <FiDownload size={16} />
          <span>{file.download_count || file.downloadCount || 0}</span>
        </StatItem>

        <RatingStars>
          {renderStars(Math.round(file.average_rating || file.averageRating || 0))}
          <span style={{ marginRight: '4px', fontSize: '12px' }}>
            ({(file.average_rating || file.averageRating || 0).toFixed(1)})
          </span>
        </RatingStars>
      </FileStats>
      
      <ActionButtons>
        <Button
          variant="primary"
          size="sm"
          leftIcon={<FiEye size={16} />}
          onClick={() => onView?.(file)}
          style={{ flex: 1 }}
        >
          معاينة
        </Button>
        
        <Button
          variant="secondary"
          size="sm"
          leftIcon={<FiDownload size={16} />}
          loading={isDownloading}
          onClick={handleDownload}
          style={{ flex: 1 }}
        >
          تحميل
        </Button>
        
        <FavoriteButton
          onClick={() => setShowShareModal(true)}
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          title="مشاركة الملف"
        >
          <FiShare2 size={16} />
        </FavoriteButton>

        <FavoriteButton
          isFavorite={isFavorite}
          onClick={handleFavorite}
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
        >
          <FiHeart size={16} />
        </FavoriteButton>
      </ActionButtons>

      <ShareModal
        isOpen={showShareModal}
        onClose={() => setShowShareModal(false)}
        file={file}
      />
    </FileCardContainer>
  );
};

export default FileCard;
