{"ast": null, "code": "import { createSelector } from 'reselect';\nimport { combineAppliedNumericalValuesIncludingErrorValues, combineAppliedValues, combineAreasDomain, combineAxisDomain, combineAxisDomainWithNiceTicks, combineCategoricalDomain, combineDisplayedData, combineDomainOfStackGroups, combineDotsDomain, combineDuplicateDomain, combineGraphicalItemsData, combineGraphicalItemsSettings, combineLinesDomain, combineNiceTicks, combineNumericalDomain, combineRealScaleType, combineScaleFunction, combineStackGroups, filterGraphicalNotStackedItems, filterReferenceElements, getDomainDefinition, itemAxisPredicate, mergeDomains, selectAxisRange, selectAxisSettings, selectHasBar, selectReferenceAreas, selectReferenceDots, selectReferenceLines } from './axisSelectors';\nimport { selectChartLayout } from '../../context/chartLayoutContext';\nimport { isCategoricalAxis } from '../../util/ChartUtils';\nimport { selectChartDataWithIndexes } from './dataSelectors';\nimport { selectChartName, selectStackOffsetType } from './rootPropsSelectors';\nimport { mathSign } from '../../util/DataUtils';\nimport { combineAxisRangeWithReverse } from './combiners/combineAxisRangeWithReverse';\nimport { combineTooltipEventType, selectDefaultTooltipEventType, selectValidateTooltipEventTypes } from './selectTooltipEventType';\nimport { combineActiveLabel } from './combiners/combineActiveLabel';\nimport { selectTooltipSettings } from './selectTooltipSettings';\nimport { combineTooltipInteractionState } from './combiners/combineTooltipInteractionState';\nimport { combineActiveTooltipIndex } from './combiners/combineActiveTooltipIndex';\nimport { combineCoordinateForDefaultIndex } from './combiners/combineCoordinateForDefaultIndex';\nimport { selectChartHeight, selectChartWidth } from './containerSelectors';\nimport { selectChartOffsetInternal } from './selectChartOffsetInternal';\nimport { combineTooltipPayloadConfigurations } from './combiners/combineTooltipPayloadConfigurations';\nimport { selectTooltipPayloadSearcher } from './selectTooltipPayloadSearcher';\nimport { selectTooltipState } from './selectTooltipState';\nimport { combineTooltipPayload } from './combiners/combineTooltipPayload';\nexport var selectTooltipAxisType = state => {\n  var layout = selectChartLayout(state);\n  if (layout === 'horizontal') {\n    return 'xAxis';\n  }\n  if (layout === 'vertical') {\n    return 'yAxis';\n  }\n  if (layout === 'centric') {\n    return 'angleAxis';\n  }\n  return 'radiusAxis';\n};\nexport var selectTooltipAxisId = state => state.tooltip.settings.axisId;\nexport var selectTooltipAxis = state => {\n  var axisType = selectTooltipAxisType(state);\n  var axisId = selectTooltipAxisId(state);\n  return selectAxisSettings(state, axisType, axisId);\n};\nexport var selectTooltipAxisRealScaleType = createSelector([selectTooltipAxis, selectChartLayout, selectHasBar, selectChartName, selectTooltipAxisType], combineRealScaleType);\nexport var selectAllUnfilteredGraphicalItems = createSelector([state => state.graphicalItems.cartesianItems, state => state.graphicalItems.polarItems], (cartesianItems, polarItems) => [...cartesianItems, ...polarItems]);\nvar selectTooltipAxisPredicate = createSelector([selectTooltipAxisType, selectTooltipAxisId], itemAxisPredicate);\nexport var selectAllGraphicalItemsSettings = createSelector([selectAllUnfilteredGraphicalItems, selectTooltipAxis, selectTooltipAxisPredicate], combineGraphicalItemsSettings);\nexport var selectTooltipGraphicalItemsData = createSelector([selectAllGraphicalItemsSettings], combineGraphicalItemsData);\n\n/**\n * Data for tooltip always use the data with indexes set by a Brush,\n * and never accept the isPanorama flag:\n * because Tooltip never displays inside the panorama anyway\n * so we don't need to worry what would happen there.\n */\nexport var selectTooltipDisplayedData = createSelector([selectTooltipGraphicalItemsData, selectChartDataWithIndexes], combineDisplayedData);\nvar selectAllTooltipAppliedValues = createSelector([selectTooltipDisplayedData, selectTooltipAxis, selectAllGraphicalItemsSettings], combineAppliedValues);\nvar selectTooltipAxisDomainDefinition = createSelector([selectTooltipAxis], getDomainDefinition);\nvar selectTooltipStackGroups = createSelector([selectTooltipDisplayedData, selectAllGraphicalItemsSettings, selectStackOffsetType], combineStackGroups);\nvar selectTooltipDomainOfStackGroups = createSelector([selectTooltipStackGroups, selectChartDataWithIndexes, selectTooltipAxisType], combineDomainOfStackGroups);\nvar selectTooltipItemsSettingsExceptStacked = createSelector([selectAllGraphicalItemsSettings], filterGraphicalNotStackedItems);\nvar selectTooltipAllAppliedNumericalValuesIncludingErrorValues = createSelector([selectTooltipDisplayedData, selectTooltipAxis, selectTooltipItemsSettingsExceptStacked, selectTooltipAxisType], combineAppliedNumericalValuesIncludingErrorValues);\nvar selectReferenceDotsByTooltipAxis = createSelector([selectReferenceDots, selectTooltipAxisType, selectTooltipAxisId], filterReferenceElements);\nvar selectTooltipReferenceDotsDomain = createSelector([selectReferenceDotsByTooltipAxis, selectTooltipAxisType], combineDotsDomain);\nvar selectReferenceAreasByTooltipAxis = createSelector([selectReferenceAreas, selectTooltipAxisType, selectTooltipAxisId], filterReferenceElements);\nvar selectTooltipReferenceAreasDomain = createSelector([selectReferenceAreasByTooltipAxis, selectTooltipAxisType], combineAreasDomain);\nvar selectReferenceLinesByTooltipAxis = createSelector([selectReferenceLines, selectTooltipAxisType, selectTooltipAxisId], filterReferenceElements);\nvar selectTooltipReferenceLinesDomain = createSelector([selectReferenceLinesByTooltipAxis, selectTooltipAxisType], combineLinesDomain);\nvar selectTooltipReferenceElementsDomain = createSelector([selectTooltipReferenceDotsDomain, selectTooltipReferenceLinesDomain, selectTooltipReferenceAreasDomain], mergeDomains);\nvar selectTooltipNumericalDomain = createSelector([selectTooltipAxis, selectTooltipAxisDomainDefinition, selectTooltipDomainOfStackGroups, selectTooltipAllAppliedNumericalValuesIncludingErrorValues, selectTooltipReferenceElementsDomain], combineNumericalDomain);\nexport var selectTooltipAxisDomain = createSelector([selectTooltipAxis, selectChartLayout, selectTooltipDisplayedData, selectAllTooltipAppliedValues, selectStackOffsetType, selectTooltipAxisType, selectTooltipNumericalDomain], combineAxisDomain);\nvar selectTooltipNiceTicks = createSelector([selectTooltipAxisDomain, selectTooltipAxis, selectTooltipAxisRealScaleType], combineNiceTicks);\nexport var selectTooltipAxisDomainIncludingNiceTicks = createSelector([selectTooltipAxis, selectTooltipAxisDomain, selectTooltipNiceTicks, selectTooltipAxisType], combineAxisDomainWithNiceTicks);\nvar selectTooltipAxisRange = state => {\n  var axisType = selectTooltipAxisType(state);\n  var axisId = selectTooltipAxisId(state);\n  var isPanorama = false; // Tooltip never displays in panorama so this is safe to assume\n  return selectAxisRange(state, axisType, axisId, isPanorama);\n};\nexport var selectTooltipAxisRangeWithReverse = createSelector([selectTooltipAxis, selectTooltipAxisRange], combineAxisRangeWithReverse);\nexport var selectTooltipAxisScale = createSelector([selectTooltipAxis, selectTooltipAxisRealScaleType, selectTooltipAxisDomainIncludingNiceTicks, selectTooltipAxisRangeWithReverse], combineScaleFunction);\nvar selectTooltipDuplicateDomain = createSelector([selectChartLayout, selectAllTooltipAppliedValues, selectTooltipAxis, selectTooltipAxisType], combineDuplicateDomain);\nexport var selectTooltipCategoricalDomain = createSelector([selectChartLayout, selectAllTooltipAppliedValues, selectTooltipAxis, selectTooltipAxisType], combineCategoricalDomain);\nvar combineTicksOfTooltipAxis = (layout, axis, realScaleType, scale, range, duplicateDomain, categoricalDomain, axisType) => {\n  if (!axis) {\n    return undefined;\n  }\n  var {\n    type\n  } = axis;\n  var isCategorical = isCategoricalAxis(layout, axisType);\n  if (!scale) {\n    return undefined;\n  }\n  var offsetForBand = realScaleType === 'scaleBand' && scale.bandwidth ? scale.bandwidth() / 2 : 2;\n  var offset = type === 'category' && scale.bandwidth ? scale.bandwidth() / offsetForBand : 0;\n  offset = axisType === 'angleAxis' && range != null && (range === null || range === void 0 ? void 0 : range.length) >= 2 ? mathSign(range[0] - range[1]) * 2 * offset : offset;\n\n  // When axis is a categorical axis, but the type of axis is number or the scale of axis is not \"auto\"\n  if (isCategorical && categoricalDomain) {\n    return categoricalDomain.map((entry, index) => ({\n      coordinate: scale(entry) + offset,\n      value: entry,\n      index,\n      offset\n    }));\n  }\n\n  // When axis has duplicated text, serial numbers are used to generate scale\n  return scale.domain().map((entry, index) => ({\n    coordinate: scale(entry) + offset,\n    value: duplicateDomain ? duplicateDomain[entry] : entry,\n    index,\n    offset\n  }));\n};\nexport var selectTooltipAxisTicks = createSelector([selectChartLayout, selectTooltipAxis, selectTooltipAxisRealScaleType, selectTooltipAxisScale, selectTooltipAxisRange, selectTooltipDuplicateDomain, selectTooltipCategoricalDomain, selectTooltipAxisType], combineTicksOfTooltipAxis);\nvar selectTooltipEventType = createSelector([selectDefaultTooltipEventType, selectValidateTooltipEventTypes, selectTooltipSettings], (defaultTooltipEventType, validateTooltipEventType, settings) => combineTooltipEventType(settings.shared, defaultTooltipEventType, validateTooltipEventType));\nvar selectTooltipTrigger = state => state.tooltip.settings.trigger;\nvar selectDefaultIndex = state => state.tooltip.settings.defaultIndex;\nvar selectTooltipInteractionState = createSelector([selectTooltipState, selectTooltipEventType, selectTooltipTrigger, selectDefaultIndex], combineTooltipInteractionState);\nexport var selectActiveTooltipIndex = createSelector([selectTooltipInteractionState, selectTooltipDisplayedData], combineActiveTooltipIndex);\nexport var selectActiveLabel = createSelector([selectTooltipAxisTicks, selectActiveTooltipIndex], combineActiveLabel);\nexport var selectActiveTooltipDataKey = createSelector([selectTooltipInteractionState], tooltipInteraction => {\n  if (!tooltipInteraction) {\n    return undefined;\n  }\n  return tooltipInteraction.dataKey;\n});\nvar selectTooltipPayloadConfigurations = createSelector([selectTooltipState, selectTooltipEventType, selectTooltipTrigger, selectDefaultIndex], combineTooltipPayloadConfigurations);\nvar selectTooltipCoordinateForDefaultIndex = createSelector([selectChartWidth, selectChartHeight, selectChartLayout, selectChartOffsetInternal, selectTooltipAxisTicks, selectDefaultIndex, selectTooltipPayloadConfigurations, selectTooltipPayloadSearcher], combineCoordinateForDefaultIndex);\nexport var selectActiveTooltipCoordinate = createSelector([selectTooltipInteractionState, selectTooltipCoordinateForDefaultIndex], (tooltipInteractionState, defaultIndexCoordinate) => {\n  if (tooltipInteractionState !== null && tooltipInteractionState !== void 0 && tooltipInteractionState.coordinate) {\n    return tooltipInteractionState.coordinate;\n  }\n  return defaultIndexCoordinate;\n});\nexport var selectIsTooltipActive = createSelector([selectTooltipInteractionState], tooltipInteractionState => tooltipInteractionState.active);\nexport var selectActiveTooltipPayload = createSelector([selectTooltipPayloadConfigurations, selectActiveTooltipIndex, selectChartDataWithIndexes, selectTooltipAxis, selectActiveLabel, selectTooltipPayloadSearcher, selectTooltipEventType], combineTooltipPayload);\nexport var selectActiveTooltipDataPoints = createSelector([selectActiveTooltipPayload], payload => {\n  if (payload == null) {\n    return undefined;\n  }\n  var dataPoints = payload.map(p => p.payload).filter(p => p != null);\n  return Array.from(new Set(dataPoints));\n});", "map": {"version": 3, "names": ["createSelector", "combineAppliedNumericalValuesIncludingErrorValues", "combineAppliedValues", "combineAreasDomain", "combineAxisDomain", "combineAxisDomainWithNiceTicks", "combineCategoricalDomain", "combineDisplayedData", "combineDomainOfStackGroups", "combineDotsDomain", "combineDuplicateDomain", "combineGraphicalItemsData", "combineGraphicalItemsSettings", "combineLinesDomain", "combineNiceTicks", "combineNumericalDomain", "combineRealScaleType", "combineScaleFunction", "combineStackGroups", "filterGraphicalNotStackedItems", "filterReferenceElements", "getDomainDefinition", "itemAxisPredicate", "mergeDomains", "selectAxisRange", "selectAxisSettings", "selectHasBar", "selectReferenceAreas", "selectReferenceDots", "selectReferenceLines", "selectChartLayout", "isCategoricalAxis", "selectChartDataWithIndexes", "selectChartName", "selectStackOffsetType", "mathSign", "combineAxisRangeWithReverse", "combineTooltipEventType", "selectDefaultTooltipEventType", "selectValidateTooltipEventTypes", "combineActiveLabel", "selectTooltipSettings", "combineTooltipInteractionState", "combineActiveTooltipIndex", "combineCoordinateForDefaultIndex", "selectChartHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectChartOffsetInternal", "combineTooltipPayloadConfigurations", "selectTooltipPayloadSearcher", "selectTooltipState", "combineTooltipPayload", "selectTooltipAxisType", "state", "layout", "selectTooltipAxisId", "tooltip", "settings", "axisId", "selectTooltipAxis", "axisType", "selectTooltipAxisRealScaleType", "selectAllUnfilteredGraphicalItems", "graphicalItems", "cartesianItems", "polarItems", "selectTooltipAxisPredicate", "selectAllGraphicalItemsSettings", "selectTooltipGraphicalItemsData", "selectTooltipDisplayedData", "selectAllTooltipAppliedValues", "selectTooltipAxisDomainDefinition", "selectTooltipStackGroups", "selectTooltipDomainOfStackGroups", "selectTooltipItemsSettingsExceptStacked", "selectTooltipAllAppliedNumericalValuesIncludingErrorValues", "selectReferenceDotsByTooltipAxis", "selectTooltipReferenceDotsDomain", "selectReferenceAreasByTooltipAxis", "selectTooltipReferenceAreasDomain", "selectReferenceLinesByTooltipAxis", "selectTooltipReferenceLinesDomain", "selectTooltipReferenceElementsDomain", "selectTooltipNumericalDomain", "selectTooltipAxisDomain", "selectTooltipNiceTicks", "selectTooltipAxisDomainIncludingNiceTicks", "selectTooltipAxisRange", "isPanorama", "selectTooltipAxisRangeWithReverse", "selectTooltipAxisScale", "selectTooltipDuplicateDomain", "selectTooltipCategoricalDomain", "combineTicksOfTooltipAxis", "axis", "realScaleType", "scale", "range", "duplicateDomain", "categoricalDomain", "undefined", "type", "isCategorical", "offsetForBand", "bandwidth", "offset", "length", "map", "entry", "index", "coordinate", "value", "domain", "selectTooltipAxisTicks", "selectTooltipEventType", "defaultTooltipEventType", "validateTooltipEventType", "shared", "selectTooltipTrigger", "trigger", "selectDefaultIndex", "defaultIndex", "selectTooltipInteractionState", "selectActiveTooltipIndex", "selectActiveLabel", "selectActiveTooltipDataKey", "tooltipInteraction", "dataKey", "selectTooltipPayloadConfigurations", "selectTooltipCoordinateForDefaultIndex", "selectActiveTooltipCoordinate", "tooltipInteractionState", "defaultIndexCoordinate", "selectIsTooltipActive", "active", "selectActiveTooltipPayload", "selectActiveTooltipDataPoints", "payload", "dataPoints", "p", "filter", "Array", "from", "Set"], "sources": ["D:/menasa/frontend/node_modules/recharts/es6/state/selectors/tooltipSelectors.js"], "sourcesContent": ["import { createSelector } from 'reselect';\nimport { combineAppliedNumericalValuesIncludingErrorValues, combineAppliedValues, combineAreasDomain, combineAxisDomain, combineAxisDomainWithNiceTicks, combineCategoricalDomain, combineDisplayedData, combineDomainOfStackGroups, combineDotsDomain, combineDuplicateDomain, combineGraphicalItemsData, combineGraphicalItemsSettings, combineLinesDomain, combineNiceTicks, combineNumericalDomain, combineRealScaleType, combineScaleFunction, combineStackGroups, filterGraphicalNotStackedItems, filterReferenceElements, getDomainDefinition, itemAxisPredicate, mergeDomains, selectAxisRange, selectAxisSettings, selectHasBar, selectReferenceAreas, selectReferenceDots, selectReferenceLines } from './axisSelectors';\nimport { selectChartLayout } from '../../context/chartLayoutContext';\nimport { isCategoricalAxis } from '../../util/ChartUtils';\nimport { selectChartDataWithIndexes } from './dataSelectors';\nimport { selectChartName, selectStackOffsetType } from './rootPropsSelectors';\nimport { mathSign } from '../../util/DataUtils';\nimport { combineAxisRangeWithReverse } from './combiners/combineAxisRangeWithReverse';\nimport { combineTooltipEventType, selectDefaultTooltipEventType, selectValidateTooltipEventTypes } from './selectTooltipEventType';\nimport { combineActiveLabel } from './combiners/combineActiveLabel';\nimport { selectTooltipSettings } from './selectTooltipSettings';\nimport { combineTooltipInteractionState } from './combiners/combineTooltipInteractionState';\nimport { combineActiveTooltipIndex } from './combiners/combineActiveTooltipIndex';\nimport { combineCoordinateForDefaultIndex } from './combiners/combineCoordinateForDefaultIndex';\nimport { selectChartHeight, selectChartWidth } from './containerSelectors';\nimport { selectChartOffsetInternal } from './selectChartOffsetInternal';\nimport { combineTooltipPayloadConfigurations } from './combiners/combineTooltipPayloadConfigurations';\nimport { selectTooltipPayloadSearcher } from './selectTooltipPayloadSearcher';\nimport { selectTooltipState } from './selectTooltipState';\nimport { combineTooltipPayload } from './combiners/combineTooltipPayload';\nexport var selectTooltipAxisType = state => {\n  var layout = selectChartLayout(state);\n  if (layout === 'horizontal') {\n    return 'xAxis';\n  }\n  if (layout === 'vertical') {\n    return 'yAxis';\n  }\n  if (layout === 'centric') {\n    return 'angleAxis';\n  }\n  return 'radiusAxis';\n};\nexport var selectTooltipAxisId = state => state.tooltip.settings.axisId;\nexport var selectTooltipAxis = state => {\n  var axisType = selectTooltipAxisType(state);\n  var axisId = selectTooltipAxisId(state);\n  return selectAxisSettings(state, axisType, axisId);\n};\nexport var selectTooltipAxisRealScaleType = createSelector([selectTooltipAxis, selectChartLayout, selectHasBar, selectChartName, selectTooltipAxisType], combineRealScaleType);\nexport var selectAllUnfilteredGraphicalItems = createSelector([state => state.graphicalItems.cartesianItems, state => state.graphicalItems.polarItems], (cartesianItems, polarItems) => [...cartesianItems, ...polarItems]);\nvar selectTooltipAxisPredicate = createSelector([selectTooltipAxisType, selectTooltipAxisId], itemAxisPredicate);\nexport var selectAllGraphicalItemsSettings = createSelector([selectAllUnfilteredGraphicalItems, selectTooltipAxis, selectTooltipAxisPredicate], combineGraphicalItemsSettings);\nexport var selectTooltipGraphicalItemsData = createSelector([selectAllGraphicalItemsSettings], combineGraphicalItemsData);\n\n/**\n * Data for tooltip always use the data with indexes set by a Brush,\n * and never accept the isPanorama flag:\n * because Tooltip never displays inside the panorama anyway\n * so we don't need to worry what would happen there.\n */\nexport var selectTooltipDisplayedData = createSelector([selectTooltipGraphicalItemsData, selectChartDataWithIndexes], combineDisplayedData);\nvar selectAllTooltipAppliedValues = createSelector([selectTooltipDisplayedData, selectTooltipAxis, selectAllGraphicalItemsSettings], combineAppliedValues);\nvar selectTooltipAxisDomainDefinition = createSelector([selectTooltipAxis], getDomainDefinition);\nvar selectTooltipStackGroups = createSelector([selectTooltipDisplayedData, selectAllGraphicalItemsSettings, selectStackOffsetType], combineStackGroups);\nvar selectTooltipDomainOfStackGroups = createSelector([selectTooltipStackGroups, selectChartDataWithIndexes, selectTooltipAxisType], combineDomainOfStackGroups);\nvar selectTooltipItemsSettingsExceptStacked = createSelector([selectAllGraphicalItemsSettings], filterGraphicalNotStackedItems);\nvar selectTooltipAllAppliedNumericalValuesIncludingErrorValues = createSelector([selectTooltipDisplayedData, selectTooltipAxis, selectTooltipItemsSettingsExceptStacked, selectTooltipAxisType], combineAppliedNumericalValuesIncludingErrorValues);\nvar selectReferenceDotsByTooltipAxis = createSelector([selectReferenceDots, selectTooltipAxisType, selectTooltipAxisId], filterReferenceElements);\nvar selectTooltipReferenceDotsDomain = createSelector([selectReferenceDotsByTooltipAxis, selectTooltipAxisType], combineDotsDomain);\nvar selectReferenceAreasByTooltipAxis = createSelector([selectReferenceAreas, selectTooltipAxisType, selectTooltipAxisId], filterReferenceElements);\nvar selectTooltipReferenceAreasDomain = createSelector([selectReferenceAreasByTooltipAxis, selectTooltipAxisType], combineAreasDomain);\nvar selectReferenceLinesByTooltipAxis = createSelector([selectReferenceLines, selectTooltipAxisType, selectTooltipAxisId], filterReferenceElements);\nvar selectTooltipReferenceLinesDomain = createSelector([selectReferenceLinesByTooltipAxis, selectTooltipAxisType], combineLinesDomain);\nvar selectTooltipReferenceElementsDomain = createSelector([selectTooltipReferenceDotsDomain, selectTooltipReferenceLinesDomain, selectTooltipReferenceAreasDomain], mergeDomains);\nvar selectTooltipNumericalDomain = createSelector([selectTooltipAxis, selectTooltipAxisDomainDefinition, selectTooltipDomainOfStackGroups, selectTooltipAllAppliedNumericalValuesIncludingErrorValues, selectTooltipReferenceElementsDomain], combineNumericalDomain);\nexport var selectTooltipAxisDomain = createSelector([selectTooltipAxis, selectChartLayout, selectTooltipDisplayedData, selectAllTooltipAppliedValues, selectStackOffsetType, selectTooltipAxisType, selectTooltipNumericalDomain], combineAxisDomain);\nvar selectTooltipNiceTicks = createSelector([selectTooltipAxisDomain, selectTooltipAxis, selectTooltipAxisRealScaleType], combineNiceTicks);\nexport var selectTooltipAxisDomainIncludingNiceTicks = createSelector([selectTooltipAxis, selectTooltipAxisDomain, selectTooltipNiceTicks, selectTooltipAxisType], combineAxisDomainWithNiceTicks);\nvar selectTooltipAxisRange = state => {\n  var axisType = selectTooltipAxisType(state);\n  var axisId = selectTooltipAxisId(state);\n  var isPanorama = false; // Tooltip never displays in panorama so this is safe to assume\n  return selectAxisRange(state, axisType, axisId, isPanorama);\n};\nexport var selectTooltipAxisRangeWithReverse = createSelector([selectTooltipAxis, selectTooltipAxisRange], combineAxisRangeWithReverse);\nexport var selectTooltipAxisScale = createSelector([selectTooltipAxis, selectTooltipAxisRealScaleType, selectTooltipAxisDomainIncludingNiceTicks, selectTooltipAxisRangeWithReverse], combineScaleFunction);\nvar selectTooltipDuplicateDomain = createSelector([selectChartLayout, selectAllTooltipAppliedValues, selectTooltipAxis, selectTooltipAxisType], combineDuplicateDomain);\nexport var selectTooltipCategoricalDomain = createSelector([selectChartLayout, selectAllTooltipAppliedValues, selectTooltipAxis, selectTooltipAxisType], combineCategoricalDomain);\nvar combineTicksOfTooltipAxis = (layout, axis, realScaleType, scale, range, duplicateDomain, categoricalDomain, axisType) => {\n  if (!axis) {\n    return undefined;\n  }\n  var {\n    type\n  } = axis;\n  var isCategorical = isCategoricalAxis(layout, axisType);\n  if (!scale) {\n    return undefined;\n  }\n  var offsetForBand = realScaleType === 'scaleBand' && scale.bandwidth ? scale.bandwidth() / 2 : 2;\n  var offset = type === 'category' && scale.bandwidth ? scale.bandwidth() / offsetForBand : 0;\n  offset = axisType === 'angleAxis' && range != null && (range === null || range === void 0 ? void 0 : range.length) >= 2 ? mathSign(range[0] - range[1]) * 2 * offset : offset;\n\n  // When axis is a categorical axis, but the type of axis is number or the scale of axis is not \"auto\"\n  if (isCategorical && categoricalDomain) {\n    return categoricalDomain.map((entry, index) => ({\n      coordinate: scale(entry) + offset,\n      value: entry,\n      index,\n      offset\n    }));\n  }\n\n  // When axis has duplicated text, serial numbers are used to generate scale\n  return scale.domain().map((entry, index) => ({\n    coordinate: scale(entry) + offset,\n    value: duplicateDomain ? duplicateDomain[entry] : entry,\n    index,\n    offset\n  }));\n};\nexport var selectTooltipAxisTicks = createSelector([selectChartLayout, selectTooltipAxis, selectTooltipAxisRealScaleType, selectTooltipAxisScale, selectTooltipAxisRange, selectTooltipDuplicateDomain, selectTooltipCategoricalDomain, selectTooltipAxisType], combineTicksOfTooltipAxis);\nvar selectTooltipEventType = createSelector([selectDefaultTooltipEventType, selectValidateTooltipEventTypes, selectTooltipSettings], (defaultTooltipEventType, validateTooltipEventType, settings) => combineTooltipEventType(settings.shared, defaultTooltipEventType, validateTooltipEventType));\nvar selectTooltipTrigger = state => state.tooltip.settings.trigger;\nvar selectDefaultIndex = state => state.tooltip.settings.defaultIndex;\nvar selectTooltipInteractionState = createSelector([selectTooltipState, selectTooltipEventType, selectTooltipTrigger, selectDefaultIndex], combineTooltipInteractionState);\nexport var selectActiveTooltipIndex = createSelector([selectTooltipInteractionState, selectTooltipDisplayedData], combineActiveTooltipIndex);\nexport var selectActiveLabel = createSelector([selectTooltipAxisTicks, selectActiveTooltipIndex], combineActiveLabel);\nexport var selectActiveTooltipDataKey = createSelector([selectTooltipInteractionState], tooltipInteraction => {\n  if (!tooltipInteraction) {\n    return undefined;\n  }\n  return tooltipInteraction.dataKey;\n});\nvar selectTooltipPayloadConfigurations = createSelector([selectTooltipState, selectTooltipEventType, selectTooltipTrigger, selectDefaultIndex], combineTooltipPayloadConfigurations);\nvar selectTooltipCoordinateForDefaultIndex = createSelector([selectChartWidth, selectChartHeight, selectChartLayout, selectChartOffsetInternal, selectTooltipAxisTicks, selectDefaultIndex, selectTooltipPayloadConfigurations, selectTooltipPayloadSearcher], combineCoordinateForDefaultIndex);\nexport var selectActiveTooltipCoordinate = createSelector([selectTooltipInteractionState, selectTooltipCoordinateForDefaultIndex], (tooltipInteractionState, defaultIndexCoordinate) => {\n  if (tooltipInteractionState !== null && tooltipInteractionState !== void 0 && tooltipInteractionState.coordinate) {\n    return tooltipInteractionState.coordinate;\n  }\n  return defaultIndexCoordinate;\n});\nexport var selectIsTooltipActive = createSelector([selectTooltipInteractionState], tooltipInteractionState => tooltipInteractionState.active);\nexport var selectActiveTooltipPayload = createSelector([selectTooltipPayloadConfigurations, selectActiveTooltipIndex, selectChartDataWithIndexes, selectTooltipAxis, selectActiveLabel, selectTooltipPayloadSearcher, selectTooltipEventType], combineTooltipPayload);\nexport var selectActiveTooltipDataPoints = createSelector([selectActiveTooltipPayload], payload => {\n  if (payload == null) {\n    return undefined;\n  }\n  var dataPoints = payload.map(p => p.payload).filter(p => p != null);\n  return Array.from(new Set(dataPoints));\n});"], "mappings": "AAAA,SAASA,cAAc,QAAQ,UAAU;AACzC,SAASC,iDAAiD,EAAEC,oBAAoB,EAAEC,kBAAkB,EAAEC,iBAAiB,EAAEC,8BAA8B,EAAEC,wBAAwB,EAAEC,oBAAoB,EAAEC,0BAA0B,EAAEC,iBAAiB,EAAEC,sBAAsB,EAAEC,yBAAyB,EAAEC,6BAA6B,EAAEC,kBAAkB,EAAEC,gBAAgB,EAAEC,sBAAsB,EAAEC,oBAAoB,EAAEC,oBAAoB,EAAEC,kBAAkB,EAAEC,8BAA8B,EAAEC,uBAAuB,EAAEC,mBAAmB,EAAEC,iBAAiB,EAAEC,YAAY,EAAEC,eAAe,EAAEC,kBAAkB,EAAEC,YAAY,EAAEC,oBAAoB,EAAEC,mBAAmB,EAAEC,oBAAoB,QAAQ,iBAAiB;AAClsB,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD,SAASC,0BAA0B,QAAQ,iBAAiB;AAC5D,SAASC,eAAe,EAAEC,qBAAqB,QAAQ,sBAAsB;AAC7E,SAASC,QAAQ,QAAQ,sBAAsB;AAC/C,SAASC,2BAA2B,QAAQ,yCAAyC;AACrF,SAASC,uBAAuB,EAAEC,6BAA6B,EAAEC,+BAA+B,QAAQ,0BAA0B;AAClI,SAASC,kBAAkB,QAAQ,gCAAgC;AACnE,SAASC,qBAAqB,QAAQ,yBAAyB;AAC/D,SAASC,8BAA8B,QAAQ,4CAA4C;AAC3F,SAASC,yBAAyB,QAAQ,uCAAuC;AACjF,SAASC,gCAAgC,QAAQ,8CAA8C;AAC/F,SAASC,iBAAiB,EAAEC,gBAAgB,QAAQ,sBAAsB;AAC1E,SAASC,yBAAyB,QAAQ,6BAA6B;AACvE,SAASC,mCAAmC,QAAQ,iDAAiD;AACrG,SAASC,4BAA4B,QAAQ,gCAAgC;AAC7E,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,qBAAqB,QAAQ,mCAAmC;AACzE,OAAO,IAAIC,qBAAqB,GAAGC,KAAK,IAAI;EAC1C,IAAIC,MAAM,GAAGxB,iBAAiB,CAACuB,KAAK,CAAC;EACrC,IAAIC,MAAM,KAAK,YAAY,EAAE;IAC3B,OAAO,OAAO;EAChB;EACA,IAAIA,MAAM,KAAK,UAAU,EAAE;IACzB,OAAO,OAAO;EAChB;EACA,IAAIA,MAAM,KAAK,SAAS,EAAE;IACxB,OAAO,WAAW;EACpB;EACA,OAAO,YAAY;AACrB,CAAC;AACD,OAAO,IAAIC,mBAAmB,GAAGF,KAAK,IAAIA,KAAK,CAACG,OAAO,CAACC,QAAQ,CAACC,MAAM;AACvE,OAAO,IAAIC,iBAAiB,GAAGN,KAAK,IAAI;EACtC,IAAIO,QAAQ,GAAGR,qBAAqB,CAACC,KAAK,CAAC;EAC3C,IAAIK,MAAM,GAAGH,mBAAmB,CAACF,KAAK,CAAC;EACvC,OAAO5B,kBAAkB,CAAC4B,KAAK,EAAEO,QAAQ,EAAEF,MAAM,CAAC;AACpD,CAAC;AACD,OAAO,IAAIG,8BAA8B,GAAG7D,cAAc,CAAC,CAAC2D,iBAAiB,EAAE7B,iBAAiB,EAAEJ,YAAY,EAAEO,eAAe,EAAEmB,qBAAqB,CAAC,EAAEpC,oBAAoB,CAAC;AAC9K,OAAO,IAAI8C,iCAAiC,GAAG9D,cAAc,CAAC,CAACqD,KAAK,IAAIA,KAAK,CAACU,cAAc,CAACC,cAAc,EAAEX,KAAK,IAAIA,KAAK,CAACU,cAAc,CAACE,UAAU,CAAC,EAAE,CAACD,cAAc,EAAEC,UAAU,KAAK,CAAC,GAAGD,cAAc,EAAE,GAAGC,UAAU,CAAC,CAAC;AAC3N,IAAIC,0BAA0B,GAAGlE,cAAc,CAAC,CAACoD,qBAAqB,EAAEG,mBAAmB,CAAC,EAAEjC,iBAAiB,CAAC;AAChH,OAAO,IAAI6C,+BAA+B,GAAGnE,cAAc,CAAC,CAAC8D,iCAAiC,EAAEH,iBAAiB,EAAEO,0BAA0B,CAAC,EAAEtD,6BAA6B,CAAC;AAC9K,OAAO,IAAIwD,+BAA+B,GAAGpE,cAAc,CAAC,CAACmE,+BAA+B,CAAC,EAAExD,yBAAyB,CAAC;;AAEzH;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAI0D,0BAA0B,GAAGrE,cAAc,CAAC,CAACoE,+BAA+B,EAAEpC,0BAA0B,CAAC,EAAEzB,oBAAoB,CAAC;AAC3I,IAAI+D,6BAA6B,GAAGtE,cAAc,CAAC,CAACqE,0BAA0B,EAAEV,iBAAiB,EAAEQ,+BAA+B,CAAC,EAAEjE,oBAAoB,CAAC;AAC1J,IAAIqE,iCAAiC,GAAGvE,cAAc,CAAC,CAAC2D,iBAAiB,CAAC,EAAEtC,mBAAmB,CAAC;AAChG,IAAImD,wBAAwB,GAAGxE,cAAc,CAAC,CAACqE,0BAA0B,EAAEF,+BAA+B,EAAEjC,qBAAqB,CAAC,EAAEhB,kBAAkB,CAAC;AACvJ,IAAIuD,gCAAgC,GAAGzE,cAAc,CAAC,CAACwE,wBAAwB,EAAExC,0BAA0B,EAAEoB,qBAAqB,CAAC,EAAE5C,0BAA0B,CAAC;AAChK,IAAIkE,uCAAuC,GAAG1E,cAAc,CAAC,CAACmE,+BAA+B,CAAC,EAAEhD,8BAA8B,CAAC;AAC/H,IAAIwD,0DAA0D,GAAG3E,cAAc,CAAC,CAACqE,0BAA0B,EAAEV,iBAAiB,EAAEe,uCAAuC,EAAEtB,qBAAqB,CAAC,EAAEnD,iDAAiD,CAAC;AACnP,IAAI2E,gCAAgC,GAAG5E,cAAc,CAAC,CAAC4B,mBAAmB,EAAEwB,qBAAqB,EAAEG,mBAAmB,CAAC,EAAEnC,uBAAuB,CAAC;AACjJ,IAAIyD,gCAAgC,GAAG7E,cAAc,CAAC,CAAC4E,gCAAgC,EAAExB,qBAAqB,CAAC,EAAE3C,iBAAiB,CAAC;AACnI,IAAIqE,iCAAiC,GAAG9E,cAAc,CAAC,CAAC2B,oBAAoB,EAAEyB,qBAAqB,EAAEG,mBAAmB,CAAC,EAAEnC,uBAAuB,CAAC;AACnJ,IAAI2D,iCAAiC,GAAG/E,cAAc,CAAC,CAAC8E,iCAAiC,EAAE1B,qBAAqB,CAAC,EAAEjD,kBAAkB,CAAC;AACtI,IAAI6E,iCAAiC,GAAGhF,cAAc,CAAC,CAAC6B,oBAAoB,EAAEuB,qBAAqB,EAAEG,mBAAmB,CAAC,EAAEnC,uBAAuB,CAAC;AACnJ,IAAI6D,iCAAiC,GAAGjF,cAAc,CAAC,CAACgF,iCAAiC,EAAE5B,qBAAqB,CAAC,EAAEvC,kBAAkB,CAAC;AACtI,IAAIqE,oCAAoC,GAAGlF,cAAc,CAAC,CAAC6E,gCAAgC,EAAEI,iCAAiC,EAAEF,iCAAiC,CAAC,EAAExD,YAAY,CAAC;AACjL,IAAI4D,4BAA4B,GAAGnF,cAAc,CAAC,CAAC2D,iBAAiB,EAAEY,iCAAiC,EAAEE,gCAAgC,EAAEE,0DAA0D,EAAEO,oCAAoC,CAAC,EAAEnE,sBAAsB,CAAC;AACrQ,OAAO,IAAIqE,uBAAuB,GAAGpF,cAAc,CAAC,CAAC2D,iBAAiB,EAAE7B,iBAAiB,EAAEuC,0BAA0B,EAAEC,6BAA6B,EAAEpC,qBAAqB,EAAEkB,qBAAqB,EAAE+B,4BAA4B,CAAC,EAAE/E,iBAAiB,CAAC;AACrP,IAAIiF,sBAAsB,GAAGrF,cAAc,CAAC,CAACoF,uBAAuB,EAAEzB,iBAAiB,EAAEE,8BAA8B,CAAC,EAAE/C,gBAAgB,CAAC;AAC3I,OAAO,IAAIwE,yCAAyC,GAAGtF,cAAc,CAAC,CAAC2D,iBAAiB,EAAEyB,uBAAuB,EAAEC,sBAAsB,EAAEjC,qBAAqB,CAAC,EAAE/C,8BAA8B,CAAC;AAClM,IAAIkF,sBAAsB,GAAGlC,KAAK,IAAI;EACpC,IAAIO,QAAQ,GAAGR,qBAAqB,CAACC,KAAK,CAAC;EAC3C,IAAIK,MAAM,GAAGH,mBAAmB,CAACF,KAAK,CAAC;EACvC,IAAImC,UAAU,GAAG,KAAK,CAAC,CAAC;EACxB,OAAOhE,eAAe,CAAC6B,KAAK,EAAEO,QAAQ,EAAEF,MAAM,EAAE8B,UAAU,CAAC;AAC7D,CAAC;AACD,OAAO,IAAIC,iCAAiC,GAAGzF,cAAc,CAAC,CAAC2D,iBAAiB,EAAE4B,sBAAsB,CAAC,EAAEnD,2BAA2B,CAAC;AACvI,OAAO,IAAIsD,sBAAsB,GAAG1F,cAAc,CAAC,CAAC2D,iBAAiB,EAAEE,8BAA8B,EAAEyB,yCAAyC,EAAEG,iCAAiC,CAAC,EAAExE,oBAAoB,CAAC;AAC3M,IAAI0E,4BAA4B,GAAG3F,cAAc,CAAC,CAAC8B,iBAAiB,EAAEwC,6BAA6B,EAAEX,iBAAiB,EAAEP,qBAAqB,CAAC,EAAE1C,sBAAsB,CAAC;AACvK,OAAO,IAAIkF,8BAA8B,GAAG5F,cAAc,CAAC,CAAC8B,iBAAiB,EAAEwC,6BAA6B,EAAEX,iBAAiB,EAAEP,qBAAqB,CAAC,EAAE9C,wBAAwB,CAAC;AAClL,IAAIuF,yBAAyB,GAAGA,CAACvC,MAAM,EAAEwC,IAAI,EAAEC,aAAa,EAAEC,KAAK,EAAEC,KAAK,EAAEC,eAAe,EAAEC,iBAAiB,EAAEvC,QAAQ,KAAK;EAC3H,IAAI,CAACkC,IAAI,EAAE;IACT,OAAOM,SAAS;EAClB;EACA,IAAI;IACFC;EACF,CAAC,GAAGP,IAAI;EACR,IAAIQ,aAAa,GAAGvE,iBAAiB,CAACuB,MAAM,EAAEM,QAAQ,CAAC;EACvD,IAAI,CAACoC,KAAK,EAAE;IACV,OAAOI,SAAS;EAClB;EACA,IAAIG,aAAa,GAAGR,aAAa,KAAK,WAAW,IAAIC,KAAK,CAACQ,SAAS,GAAGR,KAAK,CAACQ,SAAS,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;EAChG,IAAIC,MAAM,GAAGJ,IAAI,KAAK,UAAU,IAAIL,KAAK,CAACQ,SAAS,GAAGR,KAAK,CAACQ,SAAS,CAAC,CAAC,GAAGD,aAAa,GAAG,CAAC;EAC3FE,MAAM,GAAG7C,QAAQ,KAAK,WAAW,IAAIqC,KAAK,IAAI,IAAI,IAAI,CAACA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACS,MAAM,KAAK,CAAC,GAAGvE,QAAQ,CAAC8D,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAGQ,MAAM,GAAGA,MAAM;;EAE7K;EACA,IAAIH,aAAa,IAAIH,iBAAiB,EAAE;IACtC,OAAOA,iBAAiB,CAACQ,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,MAAM;MAC9CC,UAAU,EAAEd,KAAK,CAACY,KAAK,CAAC,GAAGH,MAAM;MACjCM,KAAK,EAAEH,KAAK;MACZC,KAAK;MACLJ;IACF,CAAC,CAAC,CAAC;EACL;;EAEA;EACA,OAAOT,KAAK,CAACgB,MAAM,CAAC,CAAC,CAACL,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,MAAM;IAC3CC,UAAU,EAAEd,KAAK,CAACY,KAAK,CAAC,GAAGH,MAAM;IACjCM,KAAK,EAAEb,eAAe,GAAGA,eAAe,CAACU,KAAK,CAAC,GAAGA,KAAK;IACvDC,KAAK;IACLJ;EACF,CAAC,CAAC,CAAC;AACL,CAAC;AACD,OAAO,IAAIQ,sBAAsB,GAAGjH,cAAc,CAAC,CAAC8B,iBAAiB,EAAE6B,iBAAiB,EAAEE,8BAA8B,EAAE6B,sBAAsB,EAAEH,sBAAsB,EAAEI,4BAA4B,EAAEC,8BAA8B,EAAExC,qBAAqB,CAAC,EAAEyC,yBAAyB,CAAC;AAC1R,IAAIqB,sBAAsB,GAAGlH,cAAc,CAAC,CAACsC,6BAA6B,EAAEC,+BAA+B,EAAEE,qBAAqB,CAAC,EAAE,CAAC0E,uBAAuB,EAAEC,wBAAwB,EAAE3D,QAAQ,KAAKpB,uBAAuB,CAACoB,QAAQ,CAAC4D,MAAM,EAAEF,uBAAuB,EAAEC,wBAAwB,CAAC,CAAC;AAClS,IAAIE,oBAAoB,GAAGjE,KAAK,IAAIA,KAAK,CAACG,OAAO,CAACC,QAAQ,CAAC8D,OAAO;AAClE,IAAIC,kBAAkB,GAAGnE,KAAK,IAAIA,KAAK,CAACG,OAAO,CAACC,QAAQ,CAACgE,YAAY;AACrE,IAAIC,6BAA6B,GAAG1H,cAAc,CAAC,CAACkD,kBAAkB,EAAEgE,sBAAsB,EAAEI,oBAAoB,EAAEE,kBAAkB,CAAC,EAAE9E,8BAA8B,CAAC;AAC1K,OAAO,IAAIiF,wBAAwB,GAAG3H,cAAc,CAAC,CAAC0H,6BAA6B,EAAErD,0BAA0B,CAAC,EAAE1B,yBAAyB,CAAC;AAC5I,OAAO,IAAIiF,iBAAiB,GAAG5H,cAAc,CAAC,CAACiH,sBAAsB,EAAEU,wBAAwB,CAAC,EAAEnF,kBAAkB,CAAC;AACrH,OAAO,IAAIqF,0BAA0B,GAAG7H,cAAc,CAAC,CAAC0H,6BAA6B,CAAC,EAAEI,kBAAkB,IAAI;EAC5G,IAAI,CAACA,kBAAkB,EAAE;IACvB,OAAO1B,SAAS;EAClB;EACA,OAAO0B,kBAAkB,CAACC,OAAO;AACnC,CAAC,CAAC;AACF,IAAIC,kCAAkC,GAAGhI,cAAc,CAAC,CAACkD,kBAAkB,EAAEgE,sBAAsB,EAAEI,oBAAoB,EAAEE,kBAAkB,CAAC,EAAExE,mCAAmC,CAAC;AACpL,IAAIiF,sCAAsC,GAAGjI,cAAc,CAAC,CAAC8C,gBAAgB,EAAED,iBAAiB,EAAEf,iBAAiB,EAAEiB,yBAAyB,EAAEkE,sBAAsB,EAAEO,kBAAkB,EAAEQ,kCAAkC,EAAE/E,4BAA4B,CAAC,EAAEL,gCAAgC,CAAC;AAChS,OAAO,IAAIsF,6BAA6B,GAAGlI,cAAc,CAAC,CAAC0H,6BAA6B,EAAEO,sCAAsC,CAAC,EAAE,CAACE,uBAAuB,EAAEC,sBAAsB,KAAK;EACtL,IAAID,uBAAuB,KAAK,IAAI,IAAIA,uBAAuB,KAAK,KAAK,CAAC,IAAIA,uBAAuB,CAACrB,UAAU,EAAE;IAChH,OAAOqB,uBAAuB,CAACrB,UAAU;EAC3C;EACA,OAAOsB,sBAAsB;AAC/B,CAAC,CAAC;AACF,OAAO,IAAIC,qBAAqB,GAAGrI,cAAc,CAAC,CAAC0H,6BAA6B,CAAC,EAAES,uBAAuB,IAAIA,uBAAuB,CAACG,MAAM,CAAC;AAC7I,OAAO,IAAIC,0BAA0B,GAAGvI,cAAc,CAAC,CAACgI,kCAAkC,EAAEL,wBAAwB,EAAE3F,0BAA0B,EAAE2B,iBAAiB,EAAEiE,iBAAiB,EAAE3E,4BAA4B,EAAEiE,sBAAsB,CAAC,EAAE/D,qBAAqB,CAAC;AACrQ,OAAO,IAAIqF,6BAA6B,GAAGxI,cAAc,CAAC,CAACuI,0BAA0B,CAAC,EAAEE,OAAO,IAAI;EACjG,IAAIA,OAAO,IAAI,IAAI,EAAE;IACnB,OAAOrC,SAAS;EAClB;EACA,IAAIsC,UAAU,GAAGD,OAAO,CAAC9B,GAAG,CAACgC,CAAC,IAAIA,CAAC,CAACF,OAAO,CAAC,CAACG,MAAM,CAACD,CAAC,IAAIA,CAAC,IAAI,IAAI,CAAC;EACnE,OAAOE,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAACL,UAAU,CAAC,CAAC;AACxC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}