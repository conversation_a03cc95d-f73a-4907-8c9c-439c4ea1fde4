import React from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { 
  FiUsers, 
  FiFile, 
  FiDownload, 
  FiTrendingUp,
  FiEye,
  FiStar,
  FiCalendar,
  FiActivity
} from 'react-icons/fi';
import { Card } from '../ui';

const StatsContainer = styled.div`
  padding: ${({ theme }) => theme.spacing[6]};
`;

const PageHeader = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing[8]};
`;

const PageTitle = styled.h1`
  font-size: ${({ theme }) => theme.fontSizes['3xl']};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  color: ${({ theme }) => theme.colors.gray[900]};
  margin-bottom: ${({ theme }) => theme.spacing[2]};
`;

const PageSubtitle = styled.p`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  color: ${({ theme }) => theme.colors.gray[600]};
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: ${({ theme }) => theme.spacing[6]};
  margin-bottom: ${({ theme }) => theme.spacing[8]};
`;

const StatCard = styled(Card)`
  position: relative;
  overflow: hidden;
  transition: all ${({ theme }) => theme.transitions.normal};
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: ${({ theme }) => theme.shadows.xl};
  }
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: ${({ color }) => color};
  }
`;

const StatContent = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

const StatInfo = styled.div`
  flex: 1;
`;

const StatNumber = styled.div`
  font-size: ${({ theme }) => theme.fontSizes['3xl']};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  color: ${({ theme }) => theme.colors.gray[900]};
  margin-bottom: ${({ theme }) => theme.spacing[1]};
`;

const StatLabel = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.base};
  color: ${({ theme }) => theme.colors.gray[600]};
  margin-bottom: ${({ theme }) => theme.spacing[2]};
`;

const StatChange = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[1]};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  
  ${({ positive, theme }) =>
    positive
      ? `color: ${theme.colors.success[600]};`
      : `color: ${theme.colors.error[600]};`
  }
`;

const StatIcon = styled.div`
  width: 60px;
  height: 60px;
  border-radius: ${({ theme }) => theme.borderRadius.xl};
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: ${({ theme }) => theme.fontSizes['2xl']};
  color: white;
  background: ${({ color }) => color};
  box-shadow: ${({ theme }) => theme.shadows.md};
`;

const ChartsSection = styled.div`
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: ${({ theme }) => theme.spacing[6]};
  margin-bottom: ${({ theme }) => theme.spacing[8]};
  
  @media (max-width: ${({ theme }) => theme.breakpoints.lg}) {
    grid-template-columns: 1fr;
  }
`;

const ChartCard = styled(Card)`
  min-height: 400px;
`;

const ChartHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: ${({ theme }) => theme.spacing[6]};
`;

const ChartTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes.xl};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  color: ${({ theme }) => theme.colors.gray[900]};
`;

const ChartPlaceholder = styled.div`
  height: 300px;
  background: linear-gradient(135deg, 
    ${({ theme }) => theme.colors.gray[100]} 0%, 
    ${({ theme }) => theme.colors.gray[50]} 100%);
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${({ theme }) => theme.colors.gray[500]};
  font-size: ${({ theme }) => theme.fontSizes.lg};
  border: 2px dashed ${({ theme }) => theme.colors.gray[300]};
`;

const RecentActivity = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: ${({ theme }) => theme.spacing[6]};
`;

const ActivityCard = styled(Card)`
  max-height: 500px;
  overflow: hidden;
`;

const ActivityList = styled.div`
  max-height: 350px;
  overflow-y: auto;
`;

const ActivityItem = styled(motion.div)`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[3]};
  padding: ${({ theme }) => theme.spacing[3]} 0;
  border-bottom: 1px solid ${({ theme }) => theme.colors.gray[100]};
  
  &:last-child {
    border-bottom: none;
  }
`;

const ActivityIcon = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: ${({ color, theme }) => color || theme.colors.gray[100]};
  color: white;
  font-size: ${({ theme }) => theme.fontSizes.base};
`;

const ActivityContent = styled.div`
  flex: 1;
`;

const ActivityText = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.gray[700]};
  margin-bottom: ${({ theme }) => theme.spacing[1]};
`;

const ActivityTime = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.xs};
  color: ${({ theme }) => theme.colors.gray[500]};
`;

const Statistics = () => {
  const stats = [
    {
      icon: <FiUsers />,
      number: '1,234',
      label: 'إجمالي المستخدمين',
      change: '+12%',
      positive: true,
      color: 'linear-gradient(135deg, #3B82F6, #1D4ED8)'
    },
    {
      icon: <FiFile />,
      number: '567',
      label: 'إجمالي الملفات',
      change: '+8%',
      positive: true,
      color: 'linear-gradient(135deg, #10B981, #047857)'
    },
    {
      icon: <FiDownload />,
      number: '12,345',
      label: 'إجمالي التحميلات',
      change: '+25%',
      positive: true,
      color: 'linear-gradient(135deg, #F59E0B, #D97706)'
    },
    {
      icon: <FiStar />,
      number: '4.8',
      label: 'متوسط التقييم',
      change: '+0.2',
      positive: true,
      color: 'linear-gradient(135deg, #EF4444, #DC2626)'
    }
  ];

  const recentActivities = [
    {
      icon: <FiFile />,
      text: 'تم رفع ملف جديد: "مقدمة في البرمجة"',
      time: 'منذ 5 دقائق',
      color: '#3B82F6'
    },
    {
      icon: <FiUsers />,
      text: 'انضم مستخدم جديد: أحمد محمد',
      time: 'منذ 15 دقيقة',
      color: '#10B981'
    },
    {
      icon: <FiDownload />,
      text: 'تم تحميل ملف: "أساسيات الرياضيات"',
      time: 'منذ 30 دقيقة',
      color: '#F59E0B'
    },
    {
      icon: <FiStar />,
      text: 'تقييم جديد: 5 نجوم لملف "الفيزياء المتقدمة"',
      time: 'منذ ساعة',
      color: '#EF4444'
    },
    {
      icon: <FiEye />,
      text: 'تم عرض ملف: "تاريخ الحضارات"',
      time: 'منذ ساعتين',
      color: '#8B5CF6'
    }
  ];

  return (
    <StatsContainer>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <PageHeader>
          <PageTitle>لوحة التحكم</PageTitle>
          <PageSubtitle>نظرة عامة على إحصائيات النظام والأنشطة الحديثة</PageSubtitle>
        </PageHeader>

        <StatsGrid>
          {stats.map((stat, index) => (
            <StatCard
              key={index}
              as={motion.div}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              color={stat.color}
              padding="lg"
            >
              <StatContent>
                <StatInfo>
                  <StatNumber>{stat.number}</StatNumber>
                  <StatLabel>{stat.label}</StatLabel>
                  <StatChange positive={stat.positive}>
                    <FiTrendingUp size={16} />
                    {stat.change} من الشهر الماضي
                  </StatChange>
                </StatInfo>
                <StatIcon color={stat.color}>
                  {stat.icon}
                </StatIcon>
              </StatContent>
            </StatCard>
          ))}
        </StatsGrid>

        <ChartsSection>
          <ChartCard padding="lg">
            <ChartHeader>
              <ChartTitle>إحصائيات التحميلات الشهرية</ChartTitle>
            </ChartHeader>
            <ChartPlaceholder>
              <div style={{ textAlign: 'center' }}>
                <FiActivity size={48} style={{ marginBottom: '16px', opacity: 0.5 }} />
                <div>الرسم البياني سيتم إضافته قريباً</div>
              </div>
            </ChartPlaceholder>
          </ChartCard>

          <ChartCard padding="lg">
            <ChartHeader>
              <ChartTitle>توزيع أنواع الملفات</ChartTitle>
            </ChartHeader>
            <ChartPlaceholder>
              <div style={{ textAlign: 'center' }}>
                <FiActivity size={48} style={{ marginBottom: '16px', opacity: 0.5 }} />
                <div>الرسم البياني سيتم إضافته قريباً</div>
              </div>
            </ChartPlaceholder>
          </ChartCard>
        </ChartsSection>

        <RecentActivity>
          <ActivityCard padding="lg">
            <ChartHeader>
              <ChartTitle>الأنشطة الحديثة</ChartTitle>
            </ChartHeader>
            <ActivityList>
              {recentActivities.map((activity, index) => (
                <ActivityItem
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <ActivityIcon color={activity.color}>
                    {activity.icon}
                  </ActivityIcon>
                  <ActivityContent>
                    <ActivityText>{activity.text}</ActivityText>
                    <ActivityTime>{activity.time}</ActivityTime>
                  </ActivityContent>
                </ActivityItem>
              ))}
            </ActivityList>
          </ActivityCard>

          <ActivityCard padding="lg">
            <ChartHeader>
              <ChartTitle>الملفات الأكثر تحميلاً</ChartTitle>
            </ChartHeader>
            <ActivityList>
              {[
                { name: 'مقدمة في البرمجة', downloads: '1,234' },
                { name: 'أساسيات الرياضيات', downloads: '987' },
                { name: 'الفيزياء المتقدمة', downloads: '756' },
                { name: 'تاريخ الحضارات', downloads: '543' },
                { name: 'اللغة العربية', downloads: '432' }
              ].map((file, index) => (
                <ActivityItem
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <ActivityIcon color="#3B82F6">
                    <FiFile />
                  </ActivityIcon>
                  <ActivityContent>
                    <ActivityText>{file.name}</ActivityText>
                    <ActivityTime>{file.downloads} تحميل</ActivityTime>
                  </ActivityContent>
                </ActivityItem>
              ))}
            </ActivityList>
          </ActivityCard>
        </RecentActivity>
      </motion.div>
    </StatsContainer>
  );
};

export default Statistics;
