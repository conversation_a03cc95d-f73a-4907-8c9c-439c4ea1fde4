import React, { useState } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { 
  FiDatabase, 
  FiDownload, 
  FiUpload,
  FiClock,
  FiCheck,
  FiX,
  FiAlertTriangle,
  FiRefreshCw,
  FiSettings,
  FiHardDrive,
  FiCloud
} from 'react-icons/fi';
import { Card, Button } from '../ui';
import { backup } from '../../services/api';
import { formatDistanceToNow } from 'date-fns';
import { ar } from 'date-fns/locale';
import { toast } from 'react-toastify';

const BackupContainer = styled.div`
  padding: ${({ theme }) => theme.spacing[6]};
`;

const BackupHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing[6]};
`;

const BackupTitle = styled.h2`
  font-size: ${({ theme }) => theme.fontSizes['2xl']};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  color: ${({ theme }) => theme.colors.gray[900]};
  margin: 0;
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[3]};
`;

const BackupGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: ${({ theme }) => theme.spacing[6]};
  margin-bottom: ${({ theme }) => theme.spacing[8]};
`;

const BackupCard = styled(Card)`
  padding: ${({ theme }) => theme.spacing[6]};
`;

const CardHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: ${({ theme }) => theme.spacing[4]};
`;

const CardTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  color: ${({ theme }) => theme.colors.gray[900]};
  margin: 0;
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
`;

const StatusBadge = styled.span`
  padding: ${({ theme }) => theme.spacing[1]} ${({ theme }) => theme.spacing[2]};
  border-radius: ${({ theme }) => theme.borderRadius.full};
  font-size: ${({ theme }) => theme.fontSizes.xs};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  background: ${({ status, theme }) => {
    switch (status) {
      case 'success': return theme.colors.green[100];
      case 'error': return theme.colors.red[100];
      case 'running': return theme.colors.blue[100];
      default: return theme.colors.gray[100];
    }
  }};
  color: ${({ status, theme }) => {
    switch (status) {
      case 'success': return theme.colors.green[700];
      case 'error': return theme.colors.red[700];
      case 'running': return theme.colors.blue[700];
      default: return theme.colors.gray[700];
    }
  }};
`;

const BackupInfo = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing[4]};
`;

const InfoRow = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing[2]};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  
  &:last-child {
    margin-bottom: 0;
  }
`;

const InfoLabel = styled.span`
  color: ${({ theme }) => theme.colors.gray[600]};
`;

const InfoValue = styled.span`
  color: ${({ theme }) => theme.colors.gray[900]};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
`;

const BackupActions = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing[2]};
  margin-top: ${({ theme }) => theme.spacing[4]};
`;

const BackupList = styled.div`
  background: white;
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  overflow: hidden;
  box-shadow: ${({ theme }) => theme.shadows.sm};
  border: 1px solid ${({ theme }) => theme.colors.gray[200]};
`;

const BackupListHeader = styled.div`
  background: ${({ theme }) => theme.colors.gray[50]};
  padding: ${({ theme }) => theme.spacing[4]};
  border-bottom: 1px solid ${({ theme }) => theme.colors.gray[200]};
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const BackupListTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  color: ${({ theme }) => theme.colors.gray[900]};
  margin: 0;
`;

const BackupItem = styled(motion.div)`
  padding: ${({ theme }) => theme.spacing[4]};
  border-bottom: 1px solid ${({ theme }) => theme.colors.gray[100]};
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  &:last-child {
    border-bottom: none;
  }
  
  &:hover {
    background: ${({ theme }) => theme.colors.gray[50]};
  }
`;

const BackupItemInfo = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[3]};
`;

const BackupItemIcon = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: ${({ status, theme }) => {
    switch (status) {
      case 'success': return theme.colors.green[100];
      case 'error': return theme.colors.red[100];
      case 'running': return theme.colors.blue[100];
      default: return theme.colors.gray[100];
    }
  }};
  color: ${({ status, theme }) => {
    switch (status) {
      case 'success': return theme.colors.green[600];
      case 'error': return theme.colors.red[600];
      case 'running': return theme.colors.blue[600];
      default: return theme.colors.gray[600];
    }
  }};
  display: flex;
  align-items: center;
  justify-content: center;
`;

const BackupItemDetails = styled.div``;

const BackupItemName = styled.div`
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  color: ${({ theme }) => theme.colors.gray[900]};
  margin-bottom: ${({ theme }) => theme.spacing[1]};
`;

const BackupItemMeta = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.gray[600]};
`;

const BackupItemActions = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing[2]};
`;

const ProgressBar = styled.div`
  width: 100%;
  height: 8px;
  background: ${({ theme }) => theme.colors.gray[200]};
  border-radius: 4px;
  overflow: hidden;
  margin-top: ${({ theme }) => theme.spacing[2]};
`;

const ProgressFill = styled(motion.div)`
  height: 100%;
  background: ${({ theme }) => theme.colors.primary[500]};
  border-radius: 4px;
`;

const BackupCenter = () => {
  const [isCreatingBackup, setIsCreatingBackup] = useState(false);
  const queryClient = useQueryClient();

  // جلب معلومات النسخ الاحتياطي
  const { data: backupInfo } = useQuery({
    queryKey: ['backup-info'],
    queryFn: backup.getInfo,
    select: (data) => data.data,
    refetchInterval: 30000, // تحديث كل 30 ثانية
  });

  // جلب قائمة النسخ الاحتياطية
  const { data: backupList } = useQuery({
    queryKey: ['backup-list'],
    queryFn: backup.getList,
    select: (data) => data.data,
  });

  const handleCreateBackup = async () => {
    setIsCreatingBackup(true);
    try {
      await backup.create();
      toast.success('تم بدء إنشاء النسخة الاحتياطية');
      queryClient.invalidateQueries(['backup-info']);
      queryClient.invalidateQueries(['backup-list']);
    } catch (error) {
      toast.error('فشل في إنشاء النسخة الاحتياطية');
      console.error('Backup creation error:', error);
    } finally {
      setIsCreatingBackup(false);
    }
  };

  const handleRestoreBackup = async (backupId) => {
    if (window.confirm('هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟ سيتم استبدال البيانات الحالية.')) {
      try {
        await backup.restore(backupId);
        toast.success('تم بدء استعادة النسخة الاحتياطية');
        queryClient.invalidateQueries(['backup-info']);
      } catch (error) {
        toast.error('فشل في استعادة النسخة الاحتياطية');
        console.error('Backup restore error:', error);
      }
    }
  };

  const handleDownloadBackup = async (backupId) => {
    try {
      const response = await backup.download(backupId);
      // إنشاء رابط تحميل
      const url = window.URL.createObjectURL(response.data);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `backup-${backupId}.zip`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
      
      toast.success('تم تحميل النسخة الاحتياطية');
    } catch (error) {
      toast.error('فشل في تحميل النسخة الاحتياطية');
      console.error('Backup download error:', error);
    }
  };

  const handleDeleteBackup = async (backupId) => {
    if (window.confirm('هل أنت متأكد من حذف هذه النسخة الاحتياطية؟')) {
      try {
        await backup.delete(backupId);
        toast.success('تم حذف النسخة الاحتياطية');
        queryClient.invalidateQueries(['backup-list']);
      } catch (error) {
        toast.error('فشل في حذف النسخة الاحتياطية');
        console.error('Backup delete error:', error);
      }
    }
  };

  const formatTime = (dateString) => {
    return formatDistanceToNow(new Date(dateString), {
      addSuffix: true,
      locale: ar
    });
  };

  const formatSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'success': return <FiCheck size={20} />;
      case 'error': return <FiX size={20} />;
      case 'running': return <FiRefreshCw size={20} />;
      default: return <FiDatabase size={20} />;
    }
  };

  return (
    <BackupContainer>
      <BackupHeader>
        <BackupTitle>
          <FiDatabase size={28} />
          النسخ الاحتياطي
        </BackupTitle>
        <Button
          variant="primary"
          leftIcon={<FiUpload size={16} />}
          onClick={handleCreateBackup}
          loading={isCreatingBackup}
        >
          إنشاء نسخة احتياطية
        </Button>
      </BackupHeader>

      {/* معلومات النسخ الاحتياطي */}
      <BackupGrid>
        <BackupCard>
          <CardHeader>
            <CardTitle>
              <FiHardDrive size={20} />
              النسخة الاحتياطية الأخيرة
            </CardTitle>
            <StatusBadge status={backupInfo?.lastBackup?.status || 'none'}>
              {backupInfo?.lastBackup?.status === 'success' ? 'مكتملة' :
               backupInfo?.lastBackup?.status === 'error' ? 'فشلت' :
               backupInfo?.lastBackup?.status === 'running' ? 'جارية' : 'لا توجد'}
            </StatusBadge>
          </CardHeader>
          
          <BackupInfo>
            <InfoRow>
              <InfoLabel>التاريخ:</InfoLabel>
              <InfoValue>
                {backupInfo?.lastBackup?.created_at 
                  ? formatTime(backupInfo.lastBackup.created_at)
                  : 'لم يتم إنشاء نسخة بعد'}
              </InfoValue>
            </InfoRow>
            <InfoRow>
              <InfoLabel>الحجم:</InfoLabel>
              <InfoValue>
                {backupInfo?.lastBackup?.size 
                  ? formatSize(backupInfo.lastBackup.size)
                  : '-'}
              </InfoValue>
            </InfoRow>
            <InfoRow>
              <InfoLabel>عدد الملفات:</InfoLabel>
              <InfoValue>{backupInfo?.lastBackup?.file_count || 0}</InfoValue>
            </InfoRow>
          </BackupInfo>

          {backupInfo?.lastBackup?.status === 'running' && (
            <ProgressBar>
              <ProgressFill
                initial={{ width: 0 }}
                animate={{ width: `${backupInfo.lastBackup.progress || 0}%` }}
                transition={{ duration: 0.5 }}
              />
            </ProgressBar>
          )}
        </BackupCard>

        <BackupCard>
          <CardHeader>
            <CardTitle>
              <FiCloud size={20} />
              إعدادات النسخ الاحتياطي
            </CardTitle>
          </CardHeader>
          
          <BackupInfo>
            <InfoRow>
              <InfoLabel>النسخ التلقائي:</InfoLabel>
              <InfoValue>
                {backupInfo?.autoBackup ? 'مفعل' : 'معطل'}
              </InfoValue>
            </InfoRow>
            <InfoRow>
              <InfoLabel>التكرار:</InfoLabel>
              <InfoValue>
                {backupInfo?.frequency === 'daily' ? 'يومي' :
                 backupInfo?.frequency === 'weekly' ? 'أسبوعي' :
                 backupInfo?.frequency === 'monthly' ? 'شهري' : 'يدوي'}
              </InfoValue>
            </InfoRow>
            <InfoRow>
              <InfoLabel>الاحتفاظ بـ:</InfoLabel>
              <InfoValue>{backupInfo?.retention || 10} نسخة</InfoValue>
            </InfoRow>
          </BackupInfo>

          <BackupActions>
            <Button variant="outline" size="sm" leftIcon={<FiSettings size={14} />}>
              الإعدادات
            </Button>
          </BackupActions>
        </BackupCard>
      </BackupGrid>

      {/* قائمة النسخ الاحتياطية */}
      <BackupList>
        <BackupListHeader>
          <BackupListTitle>النسخ الاحتياطية المتاحة</BackupListTitle>
          <Button variant="ghost" size="sm" leftIcon={<FiRefreshCw size={14} />}>
            تحديث
          </Button>
        </BackupListHeader>

        {backupList?.length === 0 ? (
          <div style={{ padding: '40px', textAlign: 'center', color: '#6b7280' }}>
            <FiDatabase size={48} style={{ marginBottom: '16px', opacity: 0.3 }} />
            <div>لا توجد نسخ احتياطية</div>
          </div>
        ) : (
          backupList?.map((backup, index) => (
            <BackupItem
              key={backup.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <BackupItemInfo>
                <BackupItemIcon status={backup.status}>
                  {getStatusIcon(backup.status)}
                </BackupItemIcon>
                <BackupItemDetails>
                  <BackupItemName>
                    نسخة احتياطية - {formatTime(backup.created_at)}
                  </BackupItemName>
                  <BackupItemMeta>
                    {formatSize(backup.size)} • {backup.file_count} ملف
                  </BackupItemMeta>
                </BackupItemDetails>
              </BackupItemInfo>

              <BackupItemActions>
                <Button
                  variant="ghost"
                  size="sm"
                  leftIcon={<FiDownload size={14} />}
                  onClick={() => handleDownloadBackup(backup.id)}
                >
                  تحميل
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  leftIcon={<FiRefreshCw size={14} />}
                  onClick={() => handleRestoreBackup(backup.id)}
                >
                  استعادة
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  leftIcon={<FiX size={14} />}
                  onClick={() => handleDeleteBackup(backup.id)}
                  style={{ color: '#ef4444' }}
                >
                  حذف
                </Button>
              </BackupItemActions>
            </BackupItem>
          ))
        )}
      </BackupList>
    </BackupContainer>
  );
};

export default BackupCenter;
