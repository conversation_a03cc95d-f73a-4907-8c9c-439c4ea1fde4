import { supabase, handleSupabaseError, uploadFile as supabaseUploadFile, downloadFile as supabaseDownloadFile } from '../lib/supabase';

// Auth services
export const auth = {
  // تسجيل الدخول
  login: async (credentials) => {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email: credentials.email,
        password: credentials.password,
      });

      if (error) throw error;

      // الحصول على بيانات المستخدم الإضافية
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', data.user.id)
        .single();

      if (profileError) throw profileError;

      return {
        data: {
          user: { ...data.user, ...profile },
          session: data.session
        }
      };
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  // تسجيل حساب جديد
  register: async (userData) => {
    try {
      const { data, error } = await supabase.auth.signUp({
        email: userData.email,
        password: userData.password,
        options: {
          data: {
            full_name: userData.full_name,
            role: userData.role || 'student'
          }
        }
      });

      if (error) throw error;

      return { data };
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  // الحصول على بيانات المستخدم الحالي
  getProfile: async () => {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError) throw userError;
      if (!user) throw new Error('لم يتم العثور على المستخدم');

      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      if (profileError) throw profileError;

      return {
        data: { ...user, ...profile }
      };
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  // تسجيل الخروج
  logout: async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  // تحديث الملف الشخصي
  updateProfile: async (updates) => {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError) throw userError;

      const { data, error } = await supabase
        .from('profiles')
        .update(updates)
        .eq('id', user.id)
        .select()
        .single();

      if (error) throw error;
      return { data };
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  }
};

// Files services
export const files = {
  // الحصول على جميع الملفات مع الفلترة والبحث
  getAll: async (params = {}) => {
    try {
      let query = supabase
        .from('files_with_uploader')
        .select('*')
        .eq('is_active', true);

      // البحث في العنوان والوصف
      if (params.search) {
        query = query.or(`title.ilike.%${params.search}%,description.ilike.%${params.search}%`);
      }

      // فلترة حسب المادة
      if (params.subject) {
        query = query.eq('subject', params.subject);
      }

      // فلترة حسب الفصل الدراسي
      if (params.semester) {
        query = query.eq('semester', params.semester);
      }

      // فلترة حسب نوع الملف
      if (params.fileTypes && params.fileTypes.length > 0) {
        query = query.in('file_type', params.fileTypes);
      }

      // فلترة حسب التقييم
      if (params.minRating) {
        query = query.gte('average_rating', params.minRating);
      }
      if (params.maxRating) {
        query = query.lte('average_rating', params.maxRating);
      }

      // ترتيب النتائج
      const sortBy = params.sortBy || 'created_at';
      const sortOrder = params.sortOrder || 'desc';
      query = query.order(sortBy, { ascending: sortOrder === 'asc' });

      // تحديد عدد النتائج
      if (params.limit) {
        query = query.limit(params.limit);
      }

      const { data, error } = await query;
      if (error) throw error;

      return { data };
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  // الحصول على ملف واحد
  getById: async (id) => {
    try {
      const { data, error } = await supabase
        .from('files_with_uploader')
        .select('*')
        .eq('id', id)
        .eq('is_active', true)
        .single();

      if (error) throw error;
      return { data };
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  // رفع ملف جديد
  upload: async (fileData, onProgress) => {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError) throw userError;

      // إنشاء مسار فريد للملف
      const fileExt = fileData.file.name.split('.').pop();
      const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;
      const filePath = `${user.id}/${fileName}`;

      // رفع الملف إلى Storage
      const { data: uploadData, error: uploadError } = await supabaseUploadFile(fileData.file, filePath);
      if (uploadError) throw uploadError;

      // حفظ معلومات الملف في قاعدة البيانات
      const { data, error } = await supabase
        .from('files')
        .insert({
          title: fileData.title,
          description: fileData.description,
          file_name: fileData.file.name,
          file_path: filePath,
          file_type: fileExt.toLowerCase(),
          file_size: fileData.file.size,
          subject: fileData.subject,
          semester: fileData.semester,
          uploaded_by: user.id
        })
        .select()
        .single();

      if (error) throw error;
      return { data };
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  // تحميل ملف
  download: async (id) => {
    try {
      // الحصول على معلومات الملف
      const { data: fileInfo, error: fileError } = await supabase
        .from('files')
        .select('file_path, file_name')
        .eq('id', id)
        .single();

      if (fileError) throw fileError;

      // تسجيل التحميل
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        await supabase
          .from('downloads')
          .insert({
            file_id: id,
            user_id: user.id
          });
      }

      // تحميل الملف من Storage
      const fileBlob = await supabaseDownloadFile(fileInfo.file_path);
      return { data: fileBlob, fileName: fileInfo.file_name };
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  // تقييم ملف
  rate: async (fileId, rating) => {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError) throw userError;

      const { data, error } = await supabase
        .from('ratings')
        .upsert({
          file_id: fileId,
          user_id: user.id,
          rating: rating
        })
        .select()
        .single();

      if (error) throw error;
      return { data };
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  // إضافة/إزالة من المفضلة
  toggleFavorite: async (fileId) => {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError) throw userError;

      // التحقق من وجود الملف في المفضلة
      const { data: existing, error: checkError } = await supabase
        .from('favorites')
        .select('id')
        .eq('file_id', fileId)
        .eq('user_id', user.id)
        .single();

      if (checkError && checkError.code !== 'PGRST116') throw checkError;

      if (existing) {
        // إزالة من المفضلة
        const { error } = await supabase
          .from('favorites')
          .delete()
          .eq('id', existing.id);

        if (error) throw error;
        return { data: { isFavorite: false } };
      } else {
        // إضافة للمفضلة
        const { data, error } = await supabase
          .from('favorites')
          .insert({
            file_id: fileId,
            user_id: user.id
          })
          .select()
          .single();

        if (error) throw error;
        return { data: { isFavorite: true } };
      }
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  // الحصول على المفضلة للمستخدم
  getFavorites: async () => {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError) throw userError;

      const { data, error } = await supabase
        .from('favorites')
        .select(`
          *,
          files:file_id (
            *,
            profiles:uploaded_by (full_name, email)
          )
        `)
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return { data };
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  }
};

// Admin services
export const admin = {
  // الحصول على الإحصائيات العامة
  getStats: async () => {
    try {
      const { data, error } = await supabase
        .from('admin_stats')
        .select('*')
        .single();

      if (error) throw error;
      return { data };
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  // الحصول على جميع المستخدمين
  getUsers: async (params = {}) => {
    try {
      let query = supabase
        .from('profiles')
        .select('*')
        .order('created_at', { ascending: false });

      // فلترة حسب الدور
      if (params.role) {
        query = query.eq('role', params.role);
      }

      // البحث في الاسم والبريد الإلكتروني
      if (params.search) {
        query = query.or(`full_name.ilike.%${params.search}%,email.ilike.%${params.search}%`);
      }

      const { data, error } = await query;
      if (error) throw error;

      return { data };
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  // تحديث مستخدم
  updateUser: async (id, updates) => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return { data };
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  // حذف مستخدم
  deleteUser: async (id) => {
    try {
      const { error } = await supabase
        .from('profiles')
        .delete()
        .eq('id', id);

      if (error) throw error;
      return { success: true };
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  // الحصول على الملفات الشائعة
  getPopularFiles: async () => {
    try {
      const { data, error } = await supabase
        .from('popular_files')
        .select('*');

      if (error) throw error;
      return { data };
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  // الحصول على الملفات الحديثة
  getRecentFiles: async () => {
    try {
      const { data, error } = await supabase
        .from('recent_files')
        .select('*');

      if (error) throw error;
      return { data };
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  // الحصول على الأنشطة الحديثة
  getRecentActivities: async () => {
    try {
      const { data, error } = await supabase
        .from('recent_activities')
        .select('*');

      if (error) throw error;
      return { data };
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  // الحصول على إحصائيات المواد
  getSubjectStats: async () => {
    try {
      const { data, error } = await supabase
        .from('subject_stats')
        .select('*');

      if (error) throw error;
      return { data };
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  // حذف ملف
  deleteFile: async (id) => {
    try {
      // الحصول على معلومات الملف أولاً
      const { data: fileInfo, error: fileError } = await supabase
        .from('files')
        .select('file_path')
        .eq('id', id)
        .single();

      if (fileError) throw fileError;

      // حذف الملف من Storage
      const { error: storageError } = await supabase.storage
        .from('educational-files')
        .remove([fileInfo.file_path]);

      if (storageError) throw storageError;

      // حذف سجل الملف من قاعدة البيانات
      const { error } = await supabase
        .from('files')
        .delete()
        .eq('id', id);

      if (error) throw error;
      return { success: true };
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  // تحديث ملف
  updateFile: async (id, updates) => {
    try {
      const { data, error } = await supabase
        .from('files')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return { data };
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  }
};

// Legacy exports for backward compatibility
export const getFiles = files.getAll;
export const downloadFile = files.download;
export const uploadFile = files.upload;

export default { auth, files, admin };