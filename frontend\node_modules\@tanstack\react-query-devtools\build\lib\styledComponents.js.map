{"version": 3, "file": "styledComponents.js", "sources": ["../../src/styledComponents.ts"], "sourcesContent": ["import { styled } from './utils'\n\nexport const Panel = styled(\n  'div',\n  (_props, theme) => ({\n    fontSize: 'clamp(12px, 1.5vw, 14px)',\n    fontFamily: `sans-serif`,\n    display: 'flex',\n    backgroundColor: theme.background,\n    color: theme.foreground,\n  }),\n  {\n    '(max-width: 700px)': {\n      flexDirection: 'column',\n    },\n    '(max-width: 600px)': {\n      fontSize: '.9em',\n      // flexDirection: 'column',\n    },\n  },\n)\n\nexport const ActiveQueryPanel = styled(\n  'div',\n  () => ({\n    flex: '1 1 500px',\n    display: 'flex',\n    flexDirection: 'column',\n    overflow: 'auto',\n    height: '100%',\n  }),\n  {\n    '(max-width: 700px)': (_props, theme) => ({\n      borderTop: `2px solid ${theme.gray}`,\n    }),\n  },\n)\n\nexport const Button = styled('button', (props, theme) => ({\n  appearance: 'none',\n  fontSize: '.9em',\n  fontWeight: 'bold',\n  background: theme.gray,\n  border: '0',\n  borderRadius: '.3em',\n  color: 'white',\n  padding: '.5em',\n  opacity: props.disabled ? '.5' : undefined,\n  cursor: 'pointer',\n}))\n\nexport const QueryKeys = styled('span', {\n  display: 'flex',\n  flexWrap: 'wrap',\n  gap: '0.5em',\n  fontSize: '0.9em',\n})\n\nexport const QueryKey = styled('span', {\n  display: 'inline-flex',\n  alignItems: 'center',\n  padding: '.2em .4em',\n  fontWeight: 'bold',\n  textShadow: '0 0 10px black',\n  borderRadius: '.2em',\n})\n\nexport const Code = styled('code', {\n  fontSize: '.9em',\n  color: 'inherit',\n  background: 'inherit',\n})\n\nexport const Input = styled('input', (_props, theme) => ({\n  backgroundColor: theme.inputBackgroundColor,\n  border: 0,\n  borderRadius: '.2em',\n  color: theme.inputTextColor,\n  fontSize: '.9em',\n  lineHeight: `1.3`,\n  padding: '.3em .4em',\n}))\n\nexport const Select = styled(\n  'select',\n  (_props, theme) => ({\n    display: `inline-block`,\n    fontSize: `.9em`,\n    fontFamily: `sans-serif`,\n    fontWeight: 'normal',\n    lineHeight: `1.3`,\n    padding: `.3em 1.5em .3em .5em`,\n    height: 'auto',\n    border: 0,\n    borderRadius: `.2em`,\n    appearance: `none`,\n    WebkitAppearance: 'none',\n    backgroundColor: theme.inputBackgroundColor,\n    backgroundImage: `url(\"data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='100' height='100' fill='%23444444'><polygon points='0,25 100,25 50,75'/></svg>\")`,\n    backgroundRepeat: `no-repeat`,\n    backgroundPosition: `right .55em center`,\n    backgroundSize: `.65em auto, 100%`,\n    color: theme.inputTextColor,\n  }),\n  {\n    '(max-width: 500px)': {\n      display: 'none',\n    },\n  },\n)\n"], "names": ["Panel", "styled", "_props", "theme", "fontSize", "fontFamily", "display", "backgroundColor", "background", "color", "foreground", "flexDirection", "ActiveQueryPanel", "flex", "overflow", "height", "borderTop", "gray", "<PERSON><PERSON>", "props", "appearance", "fontWeight", "border", "borderRadius", "padding", "opacity", "disabled", "undefined", "cursor", "Query<PERSON><PERSON>s", "flexWrap", "gap", "Query<PERSON>ey", "alignItems", "textShadow", "Code", "Input", "inputBackgroundColor", "inputTextColor", "lineHeight", "Select", "WebkitAppearance", "backgroundImage", "backgroundRepeat", "backgroundPosition", "backgroundSize"], "mappings": ";;;;;;AAEO,MAAMA,KAAK,GAAGC,YAAM,CACzB,KADyB,EAEzB,CAACC,MAAD,EAASC,KAAT,MAAoB;AAClBC,EAAAA,QAAQ,EAAE,0BADQ;AAElBC,EAAAA,UAAU,EAFQ,YAAA;AAGlBC,EAAAA,OAAO,EAAE,MAHS;EAIlBC,eAAe,EAAEJ,KAAK,CAACK,UAJL;EAKlBC,KAAK,EAAEN,KAAK,CAACO,UAAAA;AALK,CAApB,CAFyB,EASzB;EACE,oBAAsB,EAAA;AACpBC,IAAAA,aAAa,EAAE,QAAA;GAFnB;EAIE,oBAAsB,EAAA;IACpBP,QAAQ,EAAE,MADU;;AAAA,GAAA;AAJxB,CATyB,EAApB;MAoBMQ,gBAAgB,GAAGX,YAAM,CACpC,KADoC,EAEpC,OAAO;AACLY,EAAAA,IAAI,EAAE,WADD;AAELP,EAAAA,OAAO,EAAE,MAFJ;AAGLK,EAAAA,aAAa,EAAE,QAHV;AAILG,EAAAA,QAAQ,EAAE,MAJL;AAKLC,EAAAA,MAAM,EAAE,MAAA;AALH,CAAP,CAFoC,EASpC;AACE,EAAA,oBAAA,EAAsB,CAACb,MAAD,EAASC,KAAT,MAAoB;IACxCa,SAAS,EAAA,YAAA,GAAeb,KAAK,CAACc,IAAAA;GADV,CAAA;AADxB,CAToC,EAA/B;AAgBA,MAAMC,MAAM,GAAGjB,YAAM,CAAC,QAAD,EAAW,CAACkB,KAAD,EAAQhB,KAAR,MAAmB;AACxDiB,EAAAA,UAAU,EAAE,MAD4C;AAExDhB,EAAAA,QAAQ,EAAE,MAF8C;AAGxDiB,EAAAA,UAAU,EAAE,MAH4C;EAIxDb,UAAU,EAAEL,KAAK,CAACc,IAJsC;AAKxDK,EAAAA,MAAM,EAAE,GALgD;AAMxDC,EAAAA,YAAY,EAAE,MAN0C;AAOxDd,EAAAA,KAAK,EAAE,OAPiD;AAQxDe,EAAAA,OAAO,EAAE,MAR+C;AASxDC,EAAAA,OAAO,EAAEN,KAAK,CAACO,QAAN,GAAiB,IAAjB,GAAwBC,SATuB;AAUxDC,EAAAA,MAAM,EAAE,SAAA;AAVgD,CAAnB,CAAX,EAArB;MAaMC,SAAS,GAAG5B,YAAM,CAAC,MAAD,EAAS;AACtCK,EAAAA,OAAO,EAAE,MAD6B;AAEtCwB,EAAAA,QAAQ,EAAE,MAF4B;AAGtCC,EAAAA,GAAG,EAAE,OAHiC;AAItC3B,EAAAA,QAAQ,EAAE,OAAA;AAJ4B,CAAT,EAAxB;MAOM4B,QAAQ,GAAG/B,YAAM,CAAC,MAAD,EAAS;AACrCK,EAAAA,OAAO,EAAE,aAD4B;AAErC2B,EAAAA,UAAU,EAAE,QAFyB;AAGrCT,EAAAA,OAAO,EAAE,WAH4B;AAIrCH,EAAAA,UAAU,EAAE,MAJyB;AAKrCa,EAAAA,UAAU,EAAE,gBALyB;AAMrCX,EAAAA,YAAY,EAAE,MAAA;AANuB,CAAT,EAAvB;MASMY,IAAI,GAAGlC,YAAM,CAAC,MAAD,EAAS;AACjCG,EAAAA,QAAQ,EAAE,MADuB;AAEjCK,EAAAA,KAAK,EAAE,SAF0B;AAGjCD,EAAAA,UAAU,EAAE,SAAA;AAHqB,CAAT,EAAnB;AAMA,MAAM4B,KAAK,GAAGnC,YAAM,CAAC,OAAD,EAAU,CAACC,MAAD,EAASC,KAAT,MAAoB;EACvDI,eAAe,EAAEJ,KAAK,CAACkC,oBADgC;AAEvDf,EAAAA,MAAM,EAAE,CAF+C;AAGvDC,EAAAA,YAAY,EAAE,MAHyC;EAIvDd,KAAK,EAAEN,KAAK,CAACmC,cAJ0C;AAKvDlC,EAAAA,QAAQ,EAAE,MAL6C;AAMvDmC,EAAAA,UAAU,EAN6C,KAAA;AAOvDf,EAAAA,OAAO,EAAE,WAAA;AAP8C,CAApB,CAAV,EAApB;AAUA,MAAMgB,MAAM,GAAGvC,YAAM,CAC1B,QAD0B,EAE1B,CAACC,MAAD,EAASC,KAAT,MAAoB;AAClBG,EAAAA,OAAO,EADW,cAAA;AAElBF,EAAAA,QAAQ,EAFU,MAAA;AAGlBC,EAAAA,UAAU,EAHQ,YAAA;AAIlBgB,EAAAA,UAAU,EAAE,QAJM;AAKlBkB,EAAAA,UAAU,EALQ,KAAA;AAMlBf,EAAAA,OAAO,EANW,sBAAA;AAOlBT,EAAAA,MAAM,EAAE,MAPU;AAQlBO,EAAAA,MAAM,EAAE,CARU;AASlBC,EAAAA,YAAY,EATM,MAAA;AAUlBH,EAAAA,UAAU,EAVQ,MAAA;AAWlBqB,EAAAA,gBAAgB,EAAE,MAXA;EAYlBlC,eAAe,EAAEJ,KAAK,CAACkC,oBAZL;AAalBK,EAAAA,eAAe,EAbG,gKAAA;AAclBC,EAAAA,gBAAgB,EAdE,WAAA;AAelBC,EAAAA,kBAAkB,EAfA,oBAAA;AAgBlBC,EAAAA,cAAc,EAhBI,kBAAA;EAiBlBpC,KAAK,EAAEN,KAAK,CAACmC,cAAAA;AAjBK,CAApB,CAF0B,EAqB1B;EACE,oBAAsB,EAAA;AACpBhC,IAAAA,OAAO,EAAE,MAAA;AADW,GAAA;AADxB,CArB0B;;;;;;;;;;;"}