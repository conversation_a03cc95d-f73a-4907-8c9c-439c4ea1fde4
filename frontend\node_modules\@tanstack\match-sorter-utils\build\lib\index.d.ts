/**
 * @name match-sorter
 * @license MIT license.
 * @copyright (c) 2099 Kent <PERSON><PERSON>
 * <AUTHOR> <PERSON> <<EMAIL>> (https://kentcdodds.com)
 */
export type AccessorAttributes = {
    threshold?: Ranking;
    maxRanking: Ranking;
    minRanking: Ranking;
};
export interface RankingInfo {
    rankedValue: any;
    rank: Ranking;
    accessorIndex: number;
    accessorThreshold: Ranking | undefined;
    passed: boolean;
}
export interface AccessorOptions<TItem> {
    accessor: AccessorFn<TItem>;
    threshold?: Ranking;
    maxRanking?: Ranking;
    minRanking?: Ranking;
}
export type AccessorFn<TItem> = (item: TItem) => string | Array<string>;
export type Accessor<TItem> = AccessorFn<TItem> | AccessorOptions<TItem>;
export interface RankItemOptions<TItem = unknown> {
    accessors?: ReadonlyArray<Accessor<TItem>>;
    threshold?: Ranking;
    keepDiacritics?: boolean;
}
export declare const rankings: {
    readonly CASE_SENSITIVE_EQUAL: 7;
    readonly EQUAL: 6;
    readonly STARTS_WITH: 5;
    readonly WORD_STARTS_WITH: 4;
    readonly CONTAINS: 3;
    readonly ACRONYM: 2;
    readonly MATCHES: 1;
    readonly NO_MATCH: 0;
};
export type Ranking = (typeof rankings)[keyof typeof rankings];
/**
 * Gets the highest ranking for value for the given item based on its values for the given keys
 * @param {*} item - the item to rank
 * @param {String} value - the value to rank against
 * @param {Object} options - options to control the ranking
 * @return {{rank: Number, accessorIndex: Number, accessorThreshold: Number}} - the highest ranking
 */
export declare function rankItem<TItem>(item: TItem, value: string, options?: RankItemOptions<TItem>): RankingInfo;
/**
 * Sorts items that have a rank, index, and accessorIndex
 * @param {Object} a - the first item to sort
 * @param {Object} b - the second item to sort
 * @return {Number} -1 if a should come first, 1 if b should come first, 0 if equal
 */
export declare function compareItems<TItem>(a: RankingInfo, b: RankingInfo): number;
