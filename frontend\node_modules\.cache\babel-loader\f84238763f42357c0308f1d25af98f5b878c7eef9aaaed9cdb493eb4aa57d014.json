{"ast": null, "code": "'use client';\n\nimport { extends as _extends } from './_virtual/_rollupPluginBabelHelpers.mjs';\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport { useQueryClient, onlineManager, notifyManager } from '@tanstack/react-query';\nimport { rankItem } from '@tanstack/match-sorter-utils';\nimport useLocalStorage from './useLocalStorage.mjs';\nimport { defaultPanelSize, useIsMounted, getSidedProp, getSidePanelStyle, sortFns, getResizeHandleStyle, displayValue, getQueryStatusColor, getQueryStatusLabel, isVerticalSide, minPanelSize } from './utils.mjs';\nimport { Panel, Select, Input, Button, ActiveQueryPanel, Code, QueryKeys, QueryKey } from './styledComponents.mjs';\nimport ScreenReader from './screenreader.mjs';\nimport { defaultTheme, ThemeProvider } from './theme.mjs';\nimport Explorer from './Explorer.mjs';\nimport Logo from './Logo.mjs';\nimport { useSyncExternalStore } from 'use-sync-external-store/shim/index.js';\nfunction ReactQueryDevtools({\n  initialIsOpen,\n  panelProps = {},\n  closeButtonProps = {},\n  toggleButtonProps = {},\n  position = 'bottom-left',\n  containerElement: Container = 'aside',\n  context,\n  styleNonce,\n  panelPosition: initialPanelPosition = 'bottom',\n  errorTypes = []\n}) {\n  const rootRef = React.useRef(null);\n  const panelRef = React.useRef(null);\n  const [isOpen, setIsOpen] = useLocalStorage('reactQueryDevtoolsOpen', initialIsOpen);\n  const [devtoolsHeight, setDevtoolsHeight] = useLocalStorage('reactQueryDevtoolsHeight', defaultPanelSize);\n  const [devtoolsWidth, setDevtoolsWidth] = useLocalStorage('reactQueryDevtoolsWidth', defaultPanelSize);\n  const [panelPosition = 'bottom', setPanelPosition] = useLocalStorage('reactQueryDevtoolsPanelPosition', initialPanelPosition);\n  const [isResolvedOpen, setIsResolvedOpen] = React.useState(false);\n  const [isResizing, setIsResizing] = React.useState(false);\n  const isMounted = useIsMounted();\n  const handleDragStart = (panelElement, startEvent) => {\n    if (!panelElement) return;\n    if (startEvent.button !== 0) return; // Only allow left click for drag\n\n    const isVertical = isVerticalSide(panelPosition);\n    setIsResizing(true);\n    const {\n      height,\n      width\n    } = panelElement.getBoundingClientRect();\n    const startX = startEvent.clientX;\n    const startY = startEvent.clientY;\n    let newSize = 0;\n    const run = moveEvent => {\n      // prevent mouse selecting stuff with mouse drag\n      moveEvent.preventDefault(); // calculate the correct size based on mouse position and current panel position\n      // hint: it is different formula for the opposite sides\n\n      if (isVertical) {\n        newSize = width + (panelPosition === 'right' ? startX - moveEvent.clientX : moveEvent.clientX - startX);\n        setDevtoolsWidth(newSize);\n      } else {\n        newSize = height + (panelPosition === 'bottom' ? startY - moveEvent.clientY : moveEvent.clientY - startY);\n        setDevtoolsHeight(newSize);\n      }\n      if (newSize < minPanelSize) {\n        setIsOpen(false);\n      } else {\n        setIsOpen(true);\n      }\n    };\n    const unsub = () => {\n      if (isResizing) {\n        setIsResizing(false);\n      }\n      document.removeEventListener('mousemove', run, false);\n      document.removeEventListener('mouseUp', unsub, false);\n    };\n    document.addEventListener('mousemove', run, false);\n    document.addEventListener('mouseup', unsub, false);\n  };\n  React.useEffect(() => {\n    setIsResolvedOpen(isOpen != null ? isOpen : false);\n  }, [isOpen, isResolvedOpen, setIsResolvedOpen]); // Toggle panel visibility before/after transition (depending on direction).\n  // Prevents focusing in a closed panel.\n\n  React.useEffect(() => {\n    const ref = panelRef.current;\n    if (ref) {\n      const handlePanelTransitionStart = () => {\n        if (isResolvedOpen) {\n          ref.style.visibility = 'visible';\n        }\n      };\n      const handlePanelTransitionEnd = () => {\n        if (!isResolvedOpen) {\n          ref.style.visibility = 'hidden';\n        }\n      };\n      ref.addEventListener('transitionstart', handlePanelTransitionStart);\n      ref.addEventListener('transitionend', handlePanelTransitionEnd);\n      return () => {\n        ref.removeEventListener('transitionstart', handlePanelTransitionStart);\n        ref.removeEventListener('transitionend', handlePanelTransitionEnd);\n      };\n    }\n    return;\n  }, [isResolvedOpen]);\n  React.useEffect(() => {\n    var _rootRef$current;\n    if (isResolvedOpen && (_rootRef$current = rootRef.current) != null && _rootRef$current.parentElement) {\n      const {\n        parentElement\n      } = rootRef.current;\n      const styleProp = getSidedProp('padding', panelPosition);\n      const isVertical = isVerticalSide(panelPosition);\n      const previousPaddings = (({\n        padding,\n        paddingTop,\n        paddingBottom,\n        paddingLeft,\n        paddingRight\n      }) => ({\n        padding,\n        paddingTop,\n        paddingBottom,\n        paddingLeft,\n        paddingRight\n      }))(parentElement.style);\n      const run = () => {\n        // reset the padding\n        parentElement.style.padding = '0px';\n        parentElement.style.paddingTop = '0px';\n        parentElement.style.paddingBottom = '0px';\n        parentElement.style.paddingLeft = '0px';\n        parentElement.style.paddingRight = '0px'; // set the new padding based on the new panel position\n\n        parentElement.style[styleProp] = (isVertical ? devtoolsWidth : devtoolsHeight) + \"px\";\n      };\n      run();\n      if (typeof window !== 'undefined') {\n        window.addEventListener('resize', run);\n        return () => {\n          window.removeEventListener('resize', run);\n          Object.entries(previousPaddings).forEach(([property, previousValue]) => {\n            parentElement.style[property] = previousValue;\n          });\n        };\n      }\n    }\n    return;\n  }, [isResolvedOpen, panelPosition, devtoolsHeight, devtoolsWidth]);\n  const {\n    style: panelStyle = {},\n    ...otherPanelProps\n  } = panelProps;\n  const {\n    style: toggleButtonStyle = {},\n    onClick: onToggleClick,\n    ...otherToggleButtonProps\n  } = toggleButtonProps; // get computed style based on panel position\n\n  const style = getSidePanelStyle({\n    position: panelPosition,\n    devtoolsTheme: defaultTheme,\n    isOpen: isResolvedOpen,\n    height: devtoolsHeight,\n    width: devtoolsWidth,\n    isResizing,\n    panelStyle\n  }); // Do not render on the server\n\n  if (!isMounted()) return null;\n  return /*#__PURE__*/React.createElement(Container, {\n    ref: rootRef,\n    className: \"ReactQueryDevtools\",\n    \"aria-label\": \"React Query Devtools\"\n  }, /*#__PURE__*/React.createElement(ThemeProvider, {\n    theme: defaultTheme\n  }, /*#__PURE__*/React.createElement(ReactQueryDevtoolsPanel, _extends({\n    ref: panelRef,\n    context: context,\n    styleNonce: styleNonce,\n    position: panelPosition,\n    onPositionChange: setPanelPosition,\n    showCloseButton: true,\n    closeButtonProps: closeButtonProps\n  }, otherPanelProps, {\n    style: style,\n    isOpen: isResolvedOpen,\n    setIsOpen: setIsOpen,\n    onDragStart: e => handleDragStart(panelRef.current, e),\n    errorTypes: errorTypes\n  }))), !isResolvedOpen ? /*#__PURE__*/React.createElement(\"button\", _extends({\n    type: \"button\"\n  }, otherToggleButtonProps, {\n    \"aria-label\": \"Open React Query Devtools\",\n    \"aria-controls\": \"ReactQueryDevtoolsPanel\",\n    \"aria-haspopup\": \"true\",\n    \"aria-expanded\": \"false\",\n    onClick: e => {\n      setIsOpen(true);\n      onToggleClick == null ? void 0 : onToggleClick(e);\n    },\n    style: {\n      background: 'none',\n      border: 0,\n      padding: 0,\n      position: 'fixed',\n      zIndex: 99999,\n      display: 'inline-flex',\n      fontSize: '1.5em',\n      margin: '.5em',\n      cursor: 'pointer',\n      width: 'fit-content',\n      ...(position === 'top-right' ? {\n        top: '0',\n        right: '0'\n      } : position === 'top-left' ? {\n        top: '0',\n        left: '0'\n      } : position === 'bottom-right' ? {\n        bottom: '0',\n        right: '0'\n      } : {\n        bottom: '0',\n        left: '0'\n      }),\n      ...toggleButtonStyle\n    }\n  }), /*#__PURE__*/React.createElement(Logo, {\n    \"aria-hidden\": true\n  }), /*#__PURE__*/React.createElement(ScreenReader, {\n    text: \"Open React Query Devtools\"\n  })) : null);\n}\nconst useSubscribeToQueryCache = (queryCache, getSnapshot, skip = false) => {\n  return useSyncExternalStore(React.useCallback(onStoreChange => {\n    if (!skip) return queryCache.subscribe(notifyManager.batchCalls(onStoreChange));\n    return () => {\n      return;\n    };\n  }, [queryCache, skip]), getSnapshot, getSnapshot);\n};\nconst ReactQueryDevtoolsPanel = /*#__PURE__*/React.forwardRef(function ReactQueryDevtoolsPanel(props, ref) {\n  const {\n    isOpen = true,\n    styleNonce,\n    setIsOpen,\n    context,\n    onDragStart,\n    onPositionChange,\n    showCloseButton,\n    position,\n    closeButtonProps = {},\n    errorTypes = [],\n    ...panelProps\n  } = props;\n  const {\n    onClick: onCloseClick,\n    ...otherCloseButtonProps\n  } = closeButtonProps;\n  const queryClient = useQueryClient({\n    context\n  });\n  const queryCache = queryClient.getQueryCache();\n  const [sort, setSort] = useLocalStorage('reactQueryDevtoolsSortFn', Object.keys(sortFns)[0]);\n  const [filter, setFilter] = useLocalStorage('reactQueryDevtoolsFilter', '');\n  const [baseSort, setBaseSort] = useLocalStorage('reactQueryDevtoolsBaseSort', 1);\n  const sortFn = React.useMemo(() => sortFns[sort], [sort]);\n  const queriesCount = useSubscribeToQueryCache(queryCache, () => queryCache.getAll().length, !isOpen);\n  const [activeQueryHash, setActiveQueryHash] = useLocalStorage('reactQueryDevtoolsActiveQueryHash', '');\n  const queries = React.useMemo(() => {\n    const unsortedQueries = queryCache.getAll();\n    if (queriesCount === 0) {\n      return [];\n    }\n    const filtered = filter ? unsortedQueries.filter(item => rankItem(item.queryHash, filter).passed) : [...unsortedQueries];\n    const sorted = sortFn ? filtered.sort((a, b) => sortFn(a, b) * baseSort) : filtered;\n    return sorted;\n  }, [baseSort, sortFn, filter, queriesCount, queryCache]);\n  const [isMockOffline, setMockOffline] = React.useState(false);\n  return /*#__PURE__*/React.createElement(ThemeProvider, {\n    theme: defaultTheme\n  }, /*#__PURE__*/React.createElement(Panel, _extends({\n    ref: ref,\n    className: \"ReactQueryDevtoolsPanel\",\n    \"aria-label\": \"React Query Devtools Panel\",\n    id: \"ReactQueryDevtoolsPanel\"\n  }, panelProps, {\n    style: {\n      height: defaultPanelSize,\n      position: 'relative',\n      ...panelProps.style\n    }\n  }), /*#__PURE__*/React.createElement(\"style\", {\n    nonce: styleNonce,\n    dangerouslySetInnerHTML: {\n      __html: \"\\n            .ReactQueryDevtoolsPanel * {\\n              scrollbar-color: \" + defaultTheme.backgroundAlt + \" \" + defaultTheme.gray + \";\\n            }\\n\\n            .ReactQueryDevtoolsPanel *::-webkit-scrollbar, .ReactQueryDevtoolsPanel scrollbar {\\n              width: 1em;\\n              height: 1em;\\n            }\\n\\n            .ReactQueryDevtoolsPanel *::-webkit-scrollbar-track, .ReactQueryDevtoolsPanel scrollbar-track {\\n              background: \" + defaultTheme.backgroundAlt + \";\\n            }\\n\\n            .ReactQueryDevtoolsPanel *::-webkit-scrollbar-thumb, .ReactQueryDevtoolsPanel scrollbar-thumb {\\n              background: \" + defaultTheme.gray + \";\\n              border-radius: .5em;\\n              border: 3px solid \" + defaultTheme.backgroundAlt + \";\\n            }\\n          \"\n    }\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    style: getResizeHandleStyle(position),\n    onMouseDown: onDragStart\n  }), isOpen && /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      flex: '1 1 500px',\n      minHeight: '40%',\n      maxHeight: '100%',\n      overflow: 'auto',\n      borderRight: \"1px solid \" + defaultTheme.grayAlt,\n      display: 'flex',\n      flexDirection: 'column'\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      padding: '.5em',\n      background: defaultTheme.backgroundAlt,\n      display: 'flex',\n      justifyContent: 'space-between',\n      alignItems: 'center'\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    type: \"button\",\n    \"aria-label\": \"Close React Query Devtools\",\n    \"aria-controls\": \"ReactQueryDevtoolsPanel\",\n    \"aria-haspopup\": \"true\",\n    \"aria-expanded\": \"true\",\n    onClick: () => setIsOpen(false),\n    style: {\n      display: 'inline-flex',\n      background: 'none',\n      border: 0,\n      padding: 0,\n      marginRight: '.5em',\n      cursor: 'pointer'\n    }\n  }, /*#__PURE__*/React.createElement(Logo, {\n    \"aria-hidden\": true\n  }), /*#__PURE__*/React.createElement(ScreenReader, {\n    text: \"Close React Query Devtools\"\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'flex',\n      flexDirection: 'column'\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'flex',\n      justifyContent: 'space-between',\n      alignItems: 'center',\n      marginBottom: '.5em'\n    }\n  }, /*#__PURE__*/React.createElement(QueryStatusCount, {\n    queryCache: queryCache\n  }), position && onPositionChange ? /*#__PURE__*/React.createElement(Select, {\n    \"aria-label\": \"Panel position\",\n    value: position,\n    style: {\n      marginInlineStart: '.5em'\n    },\n    onChange: e => onPositionChange(e.target.value)\n  }, /*#__PURE__*/React.createElement(\"option\", {\n    value: \"left\"\n  }, \"Left\"), /*#__PURE__*/React.createElement(\"option\", {\n    value: \"right\"\n  }, \"Right\"), /*#__PURE__*/React.createElement(\"option\", {\n    value: \"top\"\n  }, \"Top\"), /*#__PURE__*/React.createElement(\"option\", {\n    value: \"bottom\"\n  }, \"Bottom\")) : null), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'flex',\n      alignItems: 'center',\n      flexWrap: 'wrap',\n      gap: '0.5em'\n    }\n  }, /*#__PURE__*/React.createElement(Input, {\n    placeholder: \"Filter\",\n    \"aria-label\": \"Filter by queryhash\",\n    value: filter != null ? filter : '',\n    onChange: e => setFilter(e.target.value),\n    onKeyDown: e => {\n      if (e.key === 'Escape') setFilter('');\n    },\n    style: {\n      flex: '1',\n      width: '100%'\n    }\n  }), /*#__PURE__*/React.createElement(Select, {\n    \"aria-label\": \"Sort queries\",\n    value: sort,\n    onChange: e => setSort(e.target.value),\n    style: {\n      flex: '1',\n      minWidth: 75,\n      marginRight: '.5em'\n    }\n  }, Object.keys(sortFns).map(key => /*#__PURE__*/React.createElement(\"option\", {\n    key: key,\n    value: key\n  }, \"Sort by \", key))), /*#__PURE__*/React.createElement(Button, {\n    type: \"button\",\n    onClick: () => setBaseSort(old => old * -1),\n    style: {\n      padding: '.3em .4em',\n      marginRight: '.5em'\n    }\n  }, baseSort === 1 ? '⬆ Asc' : '⬇ Desc'), /*#__PURE__*/React.createElement(Button, {\n    title: \"Clear cache\",\n    \"aria-label\": \"Clear cache\",\n    type: \"button\",\n    onClick: () => queryCache.clear(),\n    style: {\n      padding: '.3em .4em',\n      marginRight: '.5em'\n    }\n  }, \"Clear\"), /*#__PURE__*/React.createElement(Button, {\n    type: \"button\",\n    onClick: () => {\n      if (isMockOffline) {\n        onlineManager.setOnline(undefined);\n        setMockOffline(false);\n        window.dispatchEvent(new Event('online'));\n      } else {\n        onlineManager.setOnline(false);\n        setMockOffline(true);\n      }\n    },\n    \"aria-label\": isMockOffline ? 'Restore offline mock' : 'Mock offline behavior',\n    title: isMockOffline ? 'Restore offline mock' : 'Mock offline behavior',\n    style: {\n      padding: '0',\n      height: '2em'\n    }\n  }, /*#__PURE__*/React.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: \"2em\",\n    height: \"2em\",\n    viewBox: \"0 0 24 24\",\n    stroke: isMockOffline ? defaultTheme.danger : 'currentColor',\n    fill: \"none\"\n  }, isMockOffline ? /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"path\", {\n    stroke: \"none\",\n    d: \"M0 0h24v24H0z\",\n    fill: \"none\"\n  }), /*#__PURE__*/React.createElement(\"line\", {\n    x1: \"12\",\n    y1: \"18\",\n    x2: \"12.01\",\n    y2: \"18\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M9.172 15.172a4 4 0 0 1 5.656 0\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M6.343 12.343a7.963 7.963 0 0 1 3.864 -2.14m4.163 .155a7.965 7.965 0 0 1 3.287 2\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M3.515 9.515a12 12 0 0 1 3.544 -2.455m3.101 -.92a12 12 0 0 1 10.325 3.374\"\n  }), /*#__PURE__*/React.createElement(\"line\", {\n    x1: \"3\",\n    y1: \"3\",\n    x2: \"21\",\n    y2: \"21\"\n  })) : /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"path\", {\n    stroke: \"none\",\n    d: \"M0 0h24v24H0z\",\n    fill: \"none\"\n  }), /*#__PURE__*/React.createElement(\"line\", {\n    x1: \"12\",\n    y1: \"18\",\n    x2: \"12.01\",\n    y2: \"18\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M9.172 15.172a4 4 0 0 1 5.656 0\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M6.343 12.343a8 8 0 0 1 11.314 0\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M3.515 9.515c4.686 -4.687 12.284 -4.687 17 0\"\n  }))), /*#__PURE__*/React.createElement(ScreenReader, {\n    text: isMockOffline ? 'Restore offline mock' : 'Mock offline behavior'\n  }))))), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      overflowY: 'auto',\n      flex: '1'\n    }\n  }, queries.map(query => {\n    return /*#__PURE__*/React.createElement(QueryRow, {\n      queryKey: query.queryKey,\n      activeQueryHash: activeQueryHash,\n      setActiveQueryHash: setActiveQueryHash,\n      key: query.queryHash,\n      queryCache: queryCache\n    });\n  }))), activeQueryHash && isOpen ? /*#__PURE__*/React.createElement(ActiveQuery, {\n    activeQueryHash: activeQueryHash,\n    queryCache: queryCache,\n    queryClient: queryClient,\n    errorTypes: errorTypes\n  }) : null, showCloseButton ? /*#__PURE__*/React.createElement(Button, _extends({\n    type: \"button\",\n    \"aria-controls\": \"ReactQueryDevtoolsPanel\",\n    \"aria-haspopup\": \"true\",\n    \"aria-expanded\": \"true\"\n  }, otherCloseButtonProps, {\n    style: {\n      position: 'absolute',\n      zIndex: 99999,\n      margin: '.5em',\n      bottom: 0,\n      left: 0,\n      ...otherCloseButtonProps.style\n    },\n    onClick: e => {\n      setIsOpen(false);\n      onCloseClick == null ? void 0 : onCloseClick(e);\n    }\n  }), \"Close\") : null));\n});\nconst ActiveQuery = ({\n  queryCache,\n  activeQueryHash,\n  queryClient,\n  errorTypes\n}) => {\n  var _useSubscribeToQueryC, _useSubscribeToQueryC2;\n  const activeQuery = useSubscribeToQueryCache(queryCache, () => queryCache.getAll().find(query => query.queryHash === activeQueryHash));\n  const activeQueryState = useSubscribeToQueryCache(queryCache, () => {\n    var _queryCache$getAll$fi;\n    return (_queryCache$getAll$fi = queryCache.getAll().find(query => query.queryHash === activeQueryHash)) == null ? void 0 : _queryCache$getAll$fi.state;\n  });\n  const isStale = (_useSubscribeToQueryC = useSubscribeToQueryCache(queryCache, () => {\n    var _queryCache$getAll$fi2;\n    return (_queryCache$getAll$fi2 = queryCache.getAll().find(query => query.queryHash === activeQueryHash)) == null ? void 0 : _queryCache$getAll$fi2.isStale();\n  })) != null ? _useSubscribeToQueryC : false;\n  const observerCount = (_useSubscribeToQueryC2 = useSubscribeToQueryCache(queryCache, () => {\n    var _queryCache$getAll$fi3;\n    return (_queryCache$getAll$fi3 = queryCache.getAll().find(query => query.queryHash === activeQueryHash)) == null ? void 0 : _queryCache$getAll$fi3.getObserversCount();\n  })) != null ? _useSubscribeToQueryC2 : 0;\n  const handleRefetch = () => {\n    const promise = activeQuery == null ? void 0 : activeQuery.fetch();\n    promise == null ? void 0 : promise.catch(noop);\n  };\n  const currentErrorTypeName = useMemo(() => {\n    if (activeQuery && activeQueryState != null && activeQueryState.error) {\n      const errorType = errorTypes.find(type => {\n        var _activeQueryState$err;\n        return type.initializer(activeQuery).toString() === ((_activeQueryState$err = activeQueryState.error) == null ? void 0 : _activeQueryState$err.toString());\n      });\n      return errorType == null ? void 0 : errorType.name;\n    }\n    return undefined;\n  }, [activeQuery, activeQueryState == null ? void 0 : activeQueryState.error, errorTypes]);\n  if (!activeQuery || !activeQueryState) {\n    return null;\n  }\n  const triggerError = errorType => {\n    var _errorType$initialize;\n    const error = (_errorType$initialize = errorType == null ? void 0 : errorType.initializer(activeQuery)) != null ? _errorType$initialize : new Error('Unknown error from devtools');\n    const __previousQueryOptions = activeQuery.options;\n    activeQuery.setState({\n      status: 'error',\n      error,\n      fetchMeta: {\n        ...activeQuery.state.fetchMeta,\n        __previousQueryOptions\n      }\n    });\n  };\n  const restoreQueryAfterLoadingOrError = () => {\n    activeQuery.fetch(activeQuery.state.fetchMeta.__previousQueryOptions, {\n      // Make sure this fetch will cancel the previous one\n      cancelRefetch: true\n    });\n  };\n  return /*#__PURE__*/React.createElement(ActiveQueryPanel, null, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      padding: '.5em',\n      background: defaultTheme.backgroundAlt,\n      position: 'sticky',\n      top: 0,\n      zIndex: 1\n    }\n  }, \"Query Details\"), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      padding: '.5em'\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      marginBottom: '.5em',\n      display: 'flex',\n      alignItems: 'flex-start',\n      justifyContent: 'space-between'\n    }\n  }, /*#__PURE__*/React.createElement(Code, {\n    style: {\n      lineHeight: '1.8em'\n    }\n  }, /*#__PURE__*/React.createElement(\"pre\", {\n    style: {\n      margin: 0,\n      padding: 0,\n      overflow: 'auto'\n    }\n  }, displayValue(activeQuery.queryKey, true))), /*#__PURE__*/React.createElement(\"span\", {\n    style: {\n      padding: '0.3em .6em',\n      borderRadius: '0.4em',\n      fontWeight: 'bold',\n      textShadow: '0 2px 10px black',\n      background: getQueryStatusColor({\n        queryState: activeQueryState,\n        isStale: isStale,\n        observerCount: observerCount,\n        theme: defaultTheme\n      }),\n      flexShrink: 0\n    }\n  }, getQueryStatusLabel(activeQuery))), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      marginBottom: '.5em',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'space-between'\n    }\n  }, \"Observers: \", /*#__PURE__*/React.createElement(Code, null, observerCount)), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'space-between'\n    }\n  }, \"Last Updated:\", ' ', /*#__PURE__*/React.createElement(Code, null, new Date(activeQueryState.dataUpdatedAt).toLocaleTimeString()))), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      background: defaultTheme.backgroundAlt,\n      padding: '.5em',\n      position: 'sticky',\n      top: 0,\n      zIndex: 1\n    }\n  }, \"Actions\"), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      padding: '0.5em',\n      display: 'flex',\n      flexWrap: 'wrap',\n      gap: '0.5em',\n      alignItems: 'flex-end'\n    }\n  }, /*#__PURE__*/React.createElement(Button, {\n    type: \"button\",\n    onClick: handleRefetch,\n    disabled: activeQueryState.fetchStatus === 'fetching',\n    style: {\n      background: defaultTheme.active\n    }\n  }, \"Refetch\"), ' ', /*#__PURE__*/React.createElement(Button, {\n    type: \"button\",\n    onClick: () => queryClient.invalidateQueries(activeQuery),\n    style: {\n      background: defaultTheme.warning,\n      color: defaultTheme.inputTextColor\n    }\n  }, \"Invalidate\"), ' ', /*#__PURE__*/React.createElement(Button, {\n    type: \"button\",\n    onClick: () => queryClient.resetQueries(activeQuery),\n    style: {\n      background: defaultTheme.gray\n    }\n  }, \"Reset\"), ' ', /*#__PURE__*/React.createElement(Button, {\n    type: \"button\",\n    onClick: () => queryClient.removeQueries(activeQuery),\n    style: {\n      background: defaultTheme.danger\n    }\n  }, \"Remove\"), ' ', /*#__PURE__*/React.createElement(Button, {\n    type: \"button\",\n    onClick: () => {\n      var _activeQuery$state$fe;\n\n      // Return early if the query is already restoring\n      if (activeQuery.state.fetchStatus === 'fetching' && typeof ((_activeQuery$state$fe = activeQuery.state.fetchMeta) == null ? void 0 : _activeQuery$state$fe.__previousQueryOptions) === 'undefined') {\n        return;\n      }\n      if (activeQuery.state.data === undefined) {\n        restoreQueryAfterLoadingOrError();\n      } else {\n        const __previousQueryOptions = activeQuery.options; // Trigger a fetch in order to trigger suspense as well.\n\n        activeQuery.fetch({\n          ...__previousQueryOptions,\n          queryFn: () => {\n            return new Promise(() => {// Never resolve\n            });\n          },\n          cacheTime: -1\n        });\n        activeQuery.setState({\n          data: undefined,\n          status: 'loading',\n          fetchMeta: {\n            ...activeQuery.state.fetchMeta,\n            __previousQueryOptions\n          }\n        });\n      }\n    },\n    style: {\n      background: defaultTheme.paused\n    }\n  }, activeQuery.state.status === 'loading' ? 'Restore' : 'Trigger', ' ', \"loading\"), ' ', errorTypes.length === 0 || activeQuery.state.status === 'error' ? /*#__PURE__*/React.createElement(Button, {\n    type: \"button\",\n    onClick: () => {\n      if (!activeQuery.state.error) {\n        triggerError();\n      } else {\n        queryClient.resetQueries(activeQuery);\n      }\n    },\n    style: {\n      background: defaultTheme.danger\n    }\n  }, activeQuery.state.status === 'error' ? 'Restore' : 'Trigger', \" error\") : /*#__PURE__*/React.createElement(\"label\", null, \"Trigger error:\", /*#__PURE__*/React.createElement(Select, {\n    value: currentErrorTypeName != null ? currentErrorTypeName : '',\n    style: {\n      marginInlineStart: '.5em'\n    },\n    onChange: e => {\n      const errorType = errorTypes.find(t => t.name === e.target.value);\n      triggerError(errorType);\n    }\n  }, /*#__PURE__*/React.createElement(\"option\", {\n    key: \"\",\n    value: \"\"\n  }), errorTypes.map(errorType => /*#__PURE__*/React.createElement(\"option\", {\n    key: errorType.name,\n    value: errorType.name\n  }, errorType.name))))), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      background: defaultTheme.backgroundAlt,\n      padding: '.5em',\n      position: 'sticky',\n      top: 0,\n      zIndex: 1\n    }\n  }, \"Data Explorer\"), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      padding: '.5em'\n    }\n  }, /*#__PURE__*/React.createElement(Explorer, {\n    label: \"Data\",\n    value: activeQueryState.data,\n    defaultExpanded: {},\n    copyable: true\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      background: defaultTheme.backgroundAlt,\n      padding: '.5em',\n      position: 'sticky',\n      top: 0,\n      zIndex: 1\n    }\n  }, \"Query Explorer\"), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      padding: '.5em'\n    }\n  }, /*#__PURE__*/React.createElement(Explorer, {\n    label: \"Query\",\n    value: activeQuery,\n    defaultExpanded: {\n      queryKey: true\n    }\n  })));\n};\nconst QueryStatusCount = ({\n  queryCache\n}) => {\n  const hasFresh = useSubscribeToQueryCache(queryCache, () => queryCache.getAll().filter(q => getQueryStatusLabel(q) === 'fresh').length);\n  const hasFetching = useSubscribeToQueryCache(queryCache, () => queryCache.getAll().filter(q => getQueryStatusLabel(q) === 'fetching').length);\n  const hasPaused = useSubscribeToQueryCache(queryCache, () => queryCache.getAll().filter(q => getQueryStatusLabel(q) === 'paused').length);\n  const hasStale = useSubscribeToQueryCache(queryCache, () => queryCache.getAll().filter(q => getQueryStatusLabel(q) === 'stale').length);\n  const hasInactive = useSubscribeToQueryCache(queryCache, () => queryCache.getAll().filter(q => getQueryStatusLabel(q) === 'inactive').length);\n  return /*#__PURE__*/React.createElement(QueryKeys, null, /*#__PURE__*/React.createElement(QueryKey, {\n    style: {\n      background: defaultTheme.success,\n      opacity: hasFresh ? 1 : 0.3\n    }\n  }, \"fresh \", /*#__PURE__*/React.createElement(Code, null, \"(\", hasFresh, \")\")), ' ', /*#__PURE__*/React.createElement(QueryKey, {\n    style: {\n      background: defaultTheme.active,\n      opacity: hasFetching ? 1 : 0.3\n    }\n  }, \"fetching \", /*#__PURE__*/React.createElement(Code, null, \"(\", hasFetching, \")\")), ' ', /*#__PURE__*/React.createElement(QueryKey, {\n    style: {\n      background: defaultTheme.paused,\n      opacity: hasPaused ? 1 : 0.3\n    }\n  }, \"paused \", /*#__PURE__*/React.createElement(Code, null, \"(\", hasPaused, \")\")), ' ', /*#__PURE__*/React.createElement(QueryKey, {\n    style: {\n      background: defaultTheme.warning,\n      color: 'black',\n      textShadow: '0',\n      opacity: hasStale ? 1 : 0.3\n    }\n  }, \"stale \", /*#__PURE__*/React.createElement(Code, null, \"(\", hasStale, \")\")), ' ', /*#__PURE__*/React.createElement(QueryKey, {\n    style: {\n      background: defaultTheme.gray,\n      opacity: hasInactive ? 1 : 0.3\n    }\n  }, \"inactive \", /*#__PURE__*/React.createElement(Code, null, \"(\", hasInactive, \")\")));\n};\nconst QueryRow = /*#__PURE__*/React.memo(({\n  queryKey,\n  setActiveQueryHash,\n  activeQueryHash,\n  queryCache\n}) => {\n  var _useSubscribeToQueryC3, _useSubscribeToQueryC4, _useSubscribeToQueryC5, _useSubscribeToQueryC6;\n  const queryHash = (_useSubscribeToQueryC3 = useSubscribeToQueryCache(queryCache, () => {\n    var _queryCache$find;\n    return (_queryCache$find = queryCache.find(queryKey)) == null ? void 0 : _queryCache$find.queryHash;\n  })) != null ? _useSubscribeToQueryC3 : '';\n  const queryState = useSubscribeToQueryCache(queryCache, () => {\n    var _queryCache$find2;\n    return (_queryCache$find2 = queryCache.find(queryKey)) == null ? void 0 : _queryCache$find2.state;\n  });\n  const isStale = (_useSubscribeToQueryC4 = useSubscribeToQueryCache(queryCache, () => {\n    var _queryCache$find3;\n    return (_queryCache$find3 = queryCache.find(queryKey)) == null ? void 0 : _queryCache$find3.isStale();\n  })) != null ? _useSubscribeToQueryC4 : false;\n  const isDisabled = (_useSubscribeToQueryC5 = useSubscribeToQueryCache(queryCache, () => {\n    var _queryCache$find4;\n    return (_queryCache$find4 = queryCache.find(queryKey)) == null ? void 0 : _queryCache$find4.isDisabled();\n  })) != null ? _useSubscribeToQueryC5 : false;\n  const observerCount = (_useSubscribeToQueryC6 = useSubscribeToQueryCache(queryCache, () => {\n    var _queryCache$find5;\n    return (_queryCache$find5 = queryCache.find(queryKey)) == null ? void 0 : _queryCache$find5.getObserversCount();\n  })) != null ? _useSubscribeToQueryC6 : 0;\n  if (!queryState) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    role: \"button\",\n    \"aria-label\": \"Open query details for \" + queryHash,\n    onClick: () => setActiveQueryHash(activeQueryHash === queryHash ? '' : queryHash),\n    style: {\n      display: 'flex',\n      borderBottom: \"solid 1px \" + defaultTheme.grayAlt,\n      cursor: 'pointer',\n      background: queryHash === activeQueryHash ? 'rgba(255,255,255,.1)' : undefined\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      flex: '0 0 auto',\n      width: '2em',\n      height: '2em',\n      background: getQueryStatusColor({\n        queryState,\n        isStale,\n        observerCount,\n        theme: defaultTheme\n      }),\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      fontWeight: 'bold',\n      textShadow: isStale ? '0' : '0 0 10px black',\n      color: isStale ? 'black' : 'white'\n    }\n  }, observerCount), isDisabled ? /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      flex: '0 0 auto',\n      height: '2em',\n      background: defaultTheme.gray,\n      display: 'flex',\n      alignItems: 'center',\n      fontWeight: 'bold',\n      padding: '0 0.5em'\n    }\n  }, \"disabled\") : null, /*#__PURE__*/React.createElement(Code, {\n    style: {\n      padding: '.5em'\n    }\n  }, \"\" + queryHash));\n});\nQueryRow.displayName = 'QueryRow'; // eslint-disable-next-line @typescript-eslint/no-empty-function\n\nfunction noop() {}\nexport { ReactQueryDevtools, ReactQueryDevtoolsPanel };", "map": {"version": 3, "names": ["ReactQueryDevtools", "panelProps", "closeButtonProps", "toggleButtonProps", "position", "errorTypes", "rootRef", "React", "useRef", "panelRef", "panelPosition", "setPanelPosition", "useLocalStorage", "initialPanelPosition", "handleDragStart", "panelElement", "startEvent", "button", "isVertical", "isVerticalSide", "width", "startX", "clientX", "startY", "clientY", "newSize", "moveEvent", "height", "isResizing", "document", "removeEventListener", "run", "unsub", "addEventListener", "setIsResolvedOpen", "isOpen", "ref", "current", "isResolvedOpen", "style", "visibility", "handlePanelTransitionStart", "handlePanelTransitionEnd", "_rootRef$current", "parentElement", "styleProp", "getSidedProp", "paddingRight", "padding", "paddingTop", "paddingBottom", "paddingLeft", "window", "Object", "entries", "previousPaddings", "for<PERSON>ach", "property", "previousValue", "onClick", "onToggleClick", "devtoolsTheme", "defaultTheme", "devtoolsHeight", "dev<PERSON><PERSON><PERSON><PERSON><PERSON>", "panelStyle", "isMounted", "createElement", "Container", "className", "ThemeProvider", "theme", "ReactQueryDevtoolsPanel", "_extends", "context", "styleNonce", "onPositionChange", "showCloseButton", "otherPanelProps", "setIsOpen", "type", "otherToggleButtonProps", "e", "background", "border", "zIndex", "display", "fontSize", "margin", "cursor", "top", "right", "left", "bottom", "Logo", "text", "useSubscribeToQueryCache", "queryCache", "getSnapshot", "skip", "useSyncExternalStore", "useCallback", "onStoreChange", "subscribe", "notify<PERSON><PERSON>ger", "batchCalls", "forwardRef", "props", "onCloseClick", "queryClient", "get<PERSON><PERSON><PERSON><PERSON>ache", "sort", "setSort", "keys", "sortFns", "sortFn", "useMemo", "queriesCount", "getAll", "length", "queries", "unsortedQueries", "sorted", "baseSort", "filter", "Panel", "id", "defaultPanelSize", "nonce", "dangerouslySetInnerHTML", "__html", "backgroundAlt", "gray", "getResizeHandleStyle", "onMouseDown", "onDragStart", "flex", "minHeight", "maxHeight", "overflow", "flexDirection", "justifyContent", "alignItems", "marginRight", "marginBottom", "QueryStatusCount", "Select", "value", "marginInlineStart", "flexWrap", "gap", "Input", "placeholder", "min<PERSON><PERSON><PERSON>", "key", "<PERSON><PERSON>", "title", "clear", "isMockOffline", "dispatchEvent", "Event", "xmlns", "viewBox", "stroke", "danger", "fill", "d", "x1", "y1", "x2", "y2", "ScreenReader", "overflowY", "map", "query", "QueryRow", "activeQueryHash", "setActiveQueryHash", "otherCloseButtonProps", "ActiveQuery", "_useSubscribeToQueryC", "_useSubscribeToQueryC2", "activeQueryState", "_queryCache$getAll$fi", "find", "queryHash", "state", "isStale", "_queryCache$getAll$fi2", "observerCount", "_queryCache$getAll$fi3", "getObserversCount", "promise", "activeQuery", "fetch", "catch", "noop", "currentErrorTypeName", "error", "errorType", "_activeQueryState$err", "initializer", "toString", "name", "undefined", "_errorType$initialize", "Error", "__previousQueryOptions", "options", "status", "fetchMeta", "cancelRefetch", "Code", "lineHeight", "borderRadius", "fontWeight", "textShadow", "queryState", "flexShrink", "getQueryStatusLabel", "Date", "dataUpdatedAt", "toLocaleTimeString", "handleRefetch", "disabled", "fetchStatus", "invalidateQueries", "resetQueries", "removeQueries", "_activeQuery$state$fe", "data", "queryFn", "Promise", "cacheTime", "t", "target", "Explorer", "label", "defaultExpanded", "query<PERSON><PERSON>", "Query<PERSON><PERSON>s", "Query<PERSON>ey", "opacity", "hasFresh", "hasFetching", "hasPaused", "color", "hasStale", "hasInactive", "memo", "_useSubscribeToQueryC3", "_useSubscribeToQueryC4", "_useSubscribeToQueryC5", "_useSubscribeToQueryC6", "_queryCache$find", "_queryCache$find2", "_queryCache$find3", "isDisabled", "_queryCache$find4", "_queryCache$find5", "role", "displayName"], "sources": ["D:\\menasa\\frontend\\node_modules\\@tanstack\\react-query-devtools\\src\\devtools.tsx"], "sourcesContent": ["'use client'\nimport * as React from 'react'\nimport {\n  notify<PERSON>ana<PERSON>,\n  onlineManager,\n  useQueryClient,\n} from '@tanstack/react-query'\nimport { rankItem } from '@tanstack/match-sorter-utils'\nimport { useMemo } from 'react'\nimport { useSyncExternalStore } from './useSyncExternalStore'\nimport useLocalStorage from './useLocalStorage'\nimport {\n  defaultPanelSize,\n  displayValue,\n  getResizeHandleStyle,\n  getSidePanelStyle,\n  getSidedProp,\n  isVerticalSide,\n  minPanelSize,\n  sortFns,\n  useIsMounted,\n} from './utils'\nimport {\n  ActiveQueryPanel,\n  Button,\n  Code,\n  Input,\n  Panel,\n  QueryKey,\n  QueryKeys,\n  Select,\n} from './styledComponents'\nimport ScreenReader from './screenreader'\nimport { ThemeProvider, defaultTheme as theme } from './theme'\nimport { getQueryStatusColor, getQueryStatusLabel } from './utils'\nimport Explorer from './Explorer'\nimport Logo from './Logo'\nimport type { Corner, Side } from './utils'\nimport type {\n  ContextOptions,\n  Query,\n  QueryCache,\n  QueryClient,\n  QueryKey as QueryKeyType,\n} from '@tanstack/react-query'\n\nexport interface DevToolsErrorType {\n  /**\n   * The name of the error.\n   */\n  name: string\n  /**\n   * How the error is initialized. Whatever it returns MUST implement toString() so\n   * we can check against the current error.\n   */\n  initializer: (query: Query) => { toString(): string }\n}\n\nexport interface DevtoolsOptions extends ContextOptions {\n  /**\n   * Set this true if you want the dev tools to default to being open\n   */\n  initialIsOpen?: boolean\n  /**\n   * Use this to add props to the panel. For example, you can add className, style (merge and override default style), etc.\n   */\n  panelProps?: React.ComponentPropsWithoutRef<'div'>\n  /**\n   * Use this to add props to the close button. For example, you can add className, style (merge and override default style), onClick (extend default handler), etc.\n   */\n  closeButtonProps?: React.ComponentPropsWithoutRef<'button'>\n  /**\n   * Use this to add props to the toggle button. For example, you can add className, style (merge and override default style), onClick (extend default handler), etc.\n   */\n  toggleButtonProps?: React.ComponentPropsWithoutRef<'button'>\n  /**\n   * The position of the React Query logo to open and close the devtools panel.\n   * Defaults to 'bottom-left'.\n   */\n  position?: Corner\n  /**\n   * The position of the React Query devtools panel.\n   * Defaults to 'bottom'.\n   */\n  panelPosition?: Side\n  /**\n   * Use this to render the devtools inside a different type of container element for a11y purposes.\n   * Any string which corresponds to a valid intrinsic JSX element is allowed.\n   * Defaults to 'aside'.\n   */\n  containerElement?: string | any\n  /**\n   * nonce for style element for CSP\n   */\n  styleNonce?: string\n  /**\n   * Use this so you can define custom errors that can be shown in the devtools.\n   */\n  errorTypes?: DevToolsErrorType[]\n}\n\ninterface DevtoolsPanelOptions extends ContextOptions {\n  /**\n   * The standard React style object used to style a component with inline styles\n   */\n  style?: React.CSSProperties\n  /**\n   * The standard React className property used to style a component with classes\n   */\n  className?: string\n  /**\n   * A boolean variable indicating whether the panel is open or closed\n   */\n  isOpen?: boolean\n  /**\n   * nonce for style element for CSP\n   */\n  styleNonce?: string\n  /**\n   * A function that toggles the open and close state of the panel\n   */\n  setIsOpen: (isOpen: boolean) => void\n  /**\n   * Handles the opening and closing the devtools panel\n   */\n  onDragStart: (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => void\n  /**\n   * The position of the React Query devtools panel.\n   * Defaults to 'bottom'.\n   */\n  position?: Side\n  /**\n   * Handles the panel position select change\n   */\n  onPositionChange?: (side: Side) => void\n  /**\n   * Show a close button inside the panel\n   */\n  showCloseButton?: boolean\n  /**\n   * Use this to add props to the close button. For example, you can add className, style (merge and override default style), onClick (extend default handler), etc.\n   */\n  closeButtonProps?: React.ComponentPropsWithoutRef<'button'>\n  /**\n   * Use this so you can define custom errors that can be shown in the devtools.\n   */\n  errorTypes?: DevToolsErrorType[]\n}\n\nexport function ReactQueryDevtools({\n  initialIsOpen,\n  panelProps = {},\n  closeButtonProps = {},\n  toggleButtonProps = {},\n  position = 'bottom-left',\n  containerElement: Container = 'aside',\n  context,\n  styleNonce,\n  panelPosition: initialPanelPosition = 'bottom',\n  errorTypes = [],\n}: DevtoolsOptions): React.ReactElement | null {\n  const rootRef = React.useRef<HTMLDivElement>(null)\n  const panelRef = React.useRef<HTMLDivElement>(null)\n  const [isOpen, setIsOpen] = useLocalStorage(\n    'reactQueryDevtoolsOpen',\n    initialIsOpen,\n  )\n  const [devtoolsHeight, setDevtoolsHeight] = useLocalStorage<number>(\n    'reactQueryDevtoolsHeight',\n    defaultPanelSize,\n  )\n  const [devtoolsWidth, setDevtoolsWidth] = useLocalStorage<number>(\n    'reactQueryDevtoolsWidth',\n    defaultPanelSize,\n  )\n\n  const [panelPosition = 'bottom', setPanelPosition] = useLocalStorage<Side>(\n    'reactQueryDevtoolsPanelPosition',\n    initialPanelPosition,\n  )\n\n  const [isResolvedOpen, setIsResolvedOpen] = React.useState(false)\n  const [isResizing, setIsResizing] = React.useState(false)\n  const isMounted = useIsMounted()\n\n  const handleDragStart = (\n    panelElement: HTMLDivElement | null,\n    startEvent: React.MouseEvent<HTMLDivElement, MouseEvent>,\n  ) => {\n    if (!panelElement) return\n    if (startEvent.button !== 0) return // Only allow left click for drag\n    const isVertical = isVerticalSide(panelPosition)\n    setIsResizing(true)\n\n    const { height, width } = panelElement.getBoundingClientRect()\n    const startX = startEvent.clientX\n    const startY = startEvent.clientY\n    let newSize = 0\n\n    const run = (moveEvent: MouseEvent) => {\n      // prevent mouse selecting stuff with mouse drag\n      moveEvent.preventDefault()\n\n      // calculate the correct size based on mouse position and current panel position\n      // hint: it is different formula for the opposite sides\n      if (isVertical) {\n        newSize =\n          width +\n          (panelPosition === 'right'\n            ? startX - moveEvent.clientX\n            : moveEvent.clientX - startX)\n        setDevtoolsWidth(newSize)\n      } else {\n        newSize =\n          height +\n          (panelPosition === 'bottom'\n            ? startY - moveEvent.clientY\n            : moveEvent.clientY - startY)\n        setDevtoolsHeight(newSize)\n      }\n\n      if (newSize < minPanelSize) {\n        setIsOpen(false)\n      } else {\n        setIsOpen(true)\n      }\n    }\n\n    const unsub = () => {\n      if (isResizing) {\n        setIsResizing(false)\n      }\n\n      document.removeEventListener('mousemove', run, false)\n      document.removeEventListener('mouseUp', unsub, false)\n    }\n\n    document.addEventListener('mousemove', run, false)\n    document.addEventListener('mouseup', unsub, false)\n  }\n\n  React.useEffect(() => {\n    setIsResolvedOpen(isOpen ?? false)\n  }, [isOpen, isResolvedOpen, setIsResolvedOpen])\n\n  // Toggle panel visibility before/after transition (depending on direction).\n  // Prevents focusing in a closed panel.\n  React.useEffect(() => {\n    const ref = panelRef.current\n    if (ref) {\n      const handlePanelTransitionStart = () => {\n        if (isResolvedOpen) {\n          ref.style.visibility = 'visible'\n        }\n      }\n\n      const handlePanelTransitionEnd = () => {\n        if (!isResolvedOpen) {\n          ref.style.visibility = 'hidden'\n        }\n      }\n\n      ref.addEventListener('transitionstart', handlePanelTransitionStart)\n      ref.addEventListener('transitionend', handlePanelTransitionEnd)\n\n      return () => {\n        ref.removeEventListener('transitionstart', handlePanelTransitionStart)\n        ref.removeEventListener('transitionend', handlePanelTransitionEnd)\n      }\n    }\n    return\n  }, [isResolvedOpen])\n\n  React.useEffect(() => {\n    if (isResolvedOpen && rootRef.current?.parentElement) {\n      const { parentElement } = rootRef.current\n      const styleProp = getSidedProp('padding', panelPosition)\n      const isVertical = isVerticalSide(panelPosition)\n\n      const previousPaddings = (({\n        padding,\n        paddingTop,\n        paddingBottom,\n        paddingLeft,\n        paddingRight,\n      }) => ({\n        padding,\n        paddingTop,\n        paddingBottom,\n        paddingLeft,\n        paddingRight,\n      }))(parentElement.style)\n\n      const run = () => {\n        // reset the padding\n        parentElement.style.padding = '0px'\n        parentElement.style.paddingTop = '0px'\n        parentElement.style.paddingBottom = '0px'\n        parentElement.style.paddingLeft = '0px'\n        parentElement.style.paddingRight = '0px'\n        // set the new padding based on the new panel position\n\n        parentElement.style[styleProp] = `${\n          isVertical ? devtoolsWidth : devtoolsHeight\n        }px`\n      }\n\n      run()\n\n      if (typeof window !== 'undefined') {\n        window.addEventListener('resize', run)\n\n        return () => {\n          window.removeEventListener('resize', run)\n          Object.entries(previousPaddings).forEach(\n            ([property, previousValue]) => {\n              parentElement.style[property as keyof typeof previousPaddings] =\n                previousValue\n            },\n          )\n        }\n      }\n    }\n    return\n  }, [isResolvedOpen, panelPosition, devtoolsHeight, devtoolsWidth])\n\n  const { style: panelStyle = {}, ...otherPanelProps } = panelProps\n\n  const {\n    style: toggleButtonStyle = {},\n    onClick: onToggleClick,\n    ...otherToggleButtonProps\n  } = toggleButtonProps\n\n  // get computed style based on panel position\n  const style = getSidePanelStyle({\n    position: panelPosition,\n    devtoolsTheme: theme,\n    isOpen: isResolvedOpen,\n    height: devtoolsHeight,\n    width: devtoolsWidth,\n    isResizing,\n    panelStyle,\n  })\n\n  // Do not render on the server\n  if (!isMounted()) return null\n\n  return (\n    <Container\n      ref={rootRef}\n      className=\"ReactQueryDevtools\"\n      aria-label=\"React Query Devtools\"\n    >\n      <ThemeProvider theme={theme}>\n        <ReactQueryDevtoolsPanel\n          ref={panelRef as any}\n          context={context}\n          styleNonce={styleNonce}\n          position={panelPosition}\n          onPositionChange={setPanelPosition}\n          showCloseButton\n          closeButtonProps={closeButtonProps}\n          {...otherPanelProps}\n          style={style}\n          isOpen={isResolvedOpen}\n          setIsOpen={setIsOpen}\n          onDragStart={(e) => handleDragStart(panelRef.current, e)}\n          errorTypes={errorTypes}\n        />\n      </ThemeProvider>\n      {!isResolvedOpen ? (\n        <button\n          type=\"button\"\n          {...otherToggleButtonProps}\n          aria-label=\"Open React Query Devtools\"\n          aria-controls=\"ReactQueryDevtoolsPanel\"\n          aria-haspopup=\"true\"\n          aria-expanded=\"false\"\n          onClick={(e) => {\n            setIsOpen(true)\n            onToggleClick?.(e)\n          }}\n          style={{\n            background: 'none',\n            border: 0,\n            padding: 0,\n            position: 'fixed',\n            zIndex: 99999,\n            display: 'inline-flex',\n            fontSize: '1.5em',\n            margin: '.5em',\n            cursor: 'pointer',\n            width: 'fit-content',\n            ...(position === 'top-right'\n              ? {\n                  top: '0',\n                  right: '0',\n                }\n              : position === 'top-left'\n              ? {\n                  top: '0',\n                  left: '0',\n                }\n              : position === 'bottom-right'\n              ? {\n                  bottom: '0',\n                  right: '0',\n                }\n              : {\n                  bottom: '0',\n                  left: '0',\n                }),\n            ...toggleButtonStyle,\n          }}\n        >\n          <Logo aria-hidden />\n          <ScreenReader text=\"Open React Query Devtools\" />\n        </button>\n      ) : null}\n    </Container>\n  )\n}\n\nconst useSubscribeToQueryCache = <T,>(\n  queryCache: QueryCache,\n  getSnapshot: () => T,\n  skip: boolean = false,\n): T => {\n  return useSyncExternalStore(\n    React.useCallback(\n      (onStoreChange) => {\n        if (!skip)\n          return queryCache.subscribe(notifyManager.batchCalls(onStoreChange))\n        return () => {\n          return\n        }\n      },\n      [queryCache, skip],\n    ),\n    getSnapshot,\n    getSnapshot,\n  )\n}\n\nexport const ReactQueryDevtoolsPanel = React.forwardRef<\n  HTMLDivElement,\n  DevtoolsPanelOptions\n>(function ReactQueryDevtoolsPanel(props, ref): React.ReactElement {\n  const {\n    isOpen = true,\n    styleNonce,\n    setIsOpen,\n    context,\n    onDragStart,\n    onPositionChange,\n    showCloseButton,\n    position,\n    closeButtonProps = {},\n    errorTypes = [],\n    ...panelProps\n  } = props\n\n  const { onClick: onCloseClick, ...otherCloseButtonProps } = closeButtonProps\n\n  const queryClient = useQueryClient({ context })\n  const queryCache = queryClient.getQueryCache()\n\n  const [sort, setSort] = useLocalStorage(\n    'reactQueryDevtoolsSortFn',\n    Object.keys(sortFns)[0],\n  )\n\n  const [filter, setFilter] = useLocalStorage('reactQueryDevtoolsFilter', '')\n\n  const [baseSort, setBaseSort] = useLocalStorage(\n    'reactQueryDevtoolsBaseSort',\n    1,\n  )\n\n  const sortFn = React.useMemo(() => sortFns[sort as string], [sort])\n\n  const queriesCount = useSubscribeToQueryCache(\n    queryCache,\n    () => queryCache.getAll().length,\n    !isOpen,\n  )\n\n  const [activeQueryHash, setActiveQueryHash] = useLocalStorage(\n    'reactQueryDevtoolsActiveQueryHash',\n    '',\n  )\n\n  const queries = React.useMemo(() => {\n    const unsortedQueries = queryCache.getAll()\n\n    if (queriesCount === 0) {\n      return []\n    }\n\n    const filtered = filter\n      ? unsortedQueries.filter(\n          (item) => rankItem(item.queryHash, filter).passed,\n        )\n      : [...unsortedQueries]\n\n    const sorted = sortFn\n      ? filtered.sort((a, b) => sortFn(a, b) * (baseSort as number))\n      : filtered\n\n    return sorted\n  }, [baseSort, sortFn, filter, queriesCount, queryCache])\n\n  const [isMockOffline, setMockOffline] = React.useState(false)\n\n  return (\n    <ThemeProvider theme={theme}>\n      <Panel\n        ref={ref}\n        className=\"ReactQueryDevtoolsPanel\"\n        aria-label=\"React Query Devtools Panel\"\n        id=\"ReactQueryDevtoolsPanel\"\n        {...panelProps}\n        style={{\n          height: defaultPanelSize,\n          position: 'relative',\n          ...panelProps.style,\n        }}\n      >\n        <style\n          nonce={styleNonce}\n          dangerouslySetInnerHTML={{\n            __html: `\n            .ReactQueryDevtoolsPanel * {\n              scrollbar-color: ${theme.backgroundAlt} ${theme.gray};\n            }\n\n            .ReactQueryDevtoolsPanel *::-webkit-scrollbar, .ReactQueryDevtoolsPanel scrollbar {\n              width: 1em;\n              height: 1em;\n            }\n\n            .ReactQueryDevtoolsPanel *::-webkit-scrollbar-track, .ReactQueryDevtoolsPanel scrollbar-track {\n              background: ${theme.backgroundAlt};\n            }\n\n            .ReactQueryDevtoolsPanel *::-webkit-scrollbar-thumb, .ReactQueryDevtoolsPanel scrollbar-thumb {\n              background: ${theme.gray};\n              border-radius: .5em;\n              border: 3px solid ${theme.backgroundAlt};\n            }\n          `,\n          }}\n        />\n        <div\n          style={getResizeHandleStyle(position)}\n          onMouseDown={onDragStart}\n        ></div>\n\n        {isOpen && (\n          <div\n            style={{\n              flex: '1 1 500px',\n              minHeight: '40%',\n              maxHeight: '100%',\n              overflow: 'auto',\n              borderRight: `1px solid ${theme.grayAlt}`,\n              display: 'flex',\n              flexDirection: 'column',\n            }}\n          >\n            <div\n              style={{\n                padding: '.5em',\n                background: theme.backgroundAlt,\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center',\n              }}\n            >\n              <button\n                type=\"button\"\n                aria-label=\"Close React Query Devtools\"\n                aria-controls=\"ReactQueryDevtoolsPanel\"\n                aria-haspopup=\"true\"\n                aria-expanded=\"true\"\n                onClick={() => setIsOpen(false)}\n                style={{\n                  display: 'inline-flex',\n                  background: 'none',\n                  border: 0,\n                  padding: 0,\n                  marginRight: '.5em',\n                  cursor: 'pointer',\n                }}\n              >\n                <Logo aria-hidden />\n                <ScreenReader text=\"Close React Query Devtools\" />\n              </button>\n\n              <div\n                style={{\n                  display: 'flex',\n                  flexDirection: 'column',\n                }}\n              >\n                <div\n                  style={{\n                    display: 'flex',\n                    justifyContent: 'space-between',\n                    alignItems: 'center',\n                    marginBottom: '.5em',\n                  }}\n                >\n                  <QueryStatusCount queryCache={queryCache} />\n                  {position && onPositionChange ? (\n                    <Select\n                      aria-label=\"Panel position\"\n                      value={position}\n                      style={{ marginInlineStart: '.5em' }}\n                      onChange={(e) => onPositionChange(e.target.value as Side)}\n                    >\n                      <option value=\"left\">Left</option>\n                      <option value=\"right\">Right</option>\n                      <option value=\"top\">Top</option>\n                      <option value=\"bottom\">Bottom</option>\n                    </Select>\n                  ) : null}\n                </div>\n                <div\n                  style={{\n                    display: 'flex',\n                    alignItems: 'center',\n                    flexWrap: 'wrap',\n                    gap: '0.5em',\n                  }}\n                >\n                  <Input\n                    placeholder=\"Filter\"\n                    aria-label=\"Filter by queryhash\"\n                    value={filter ?? ''}\n                    onChange={(e) => setFilter(e.target.value)}\n                    onKeyDown={(e) => {\n                      if (e.key === 'Escape') setFilter('')\n                    }}\n                    style={{\n                      flex: '1',\n                      width: '100%',\n                    }}\n                  />\n                  <Select\n                    aria-label=\"Sort queries\"\n                    value={sort}\n                    onChange={(e) => setSort(e.target.value)}\n                    style={{\n                      flex: '1',\n                      minWidth: 75,\n                      marginRight: '.5em',\n                    }}\n                  >\n                    {Object.keys(sortFns).map((key) => (\n                      <option key={key} value={key}>\n                        Sort by {key}\n                      </option>\n                    ))}\n                  </Select>\n                  <Button\n                    type=\"button\"\n                    onClick={() => setBaseSort((old) => old * -1)}\n                    style={{\n                      padding: '.3em .4em',\n                      marginRight: '.5em',\n                    }}\n                  >\n                    {baseSort === 1 ? '⬆ Asc' : '⬇ Desc'}\n                  </Button>\n                  <Button\n                    title=\"Clear cache\"\n                    aria-label=\"Clear cache\"\n                    type=\"button\"\n                    onClick={() => queryCache.clear()}\n                    style={{\n                      padding: '.3em .4em',\n                      marginRight: '.5em',\n                    }}\n                  >\n                    Clear\n                  </Button>\n                  <Button\n                    type=\"button\"\n                    onClick={() => {\n                      if (isMockOffline) {\n                        onlineManager.setOnline(undefined)\n                        setMockOffline(false)\n                        window.dispatchEvent(new Event('online'))\n                      } else {\n                        onlineManager.setOnline(false)\n                        setMockOffline(true)\n                      }\n                    }}\n                    aria-label={\n                      isMockOffline\n                        ? 'Restore offline mock'\n                        : 'Mock offline behavior'\n                    }\n                    title={\n                      isMockOffline\n                        ? 'Restore offline mock'\n                        : 'Mock offline behavior'\n                    }\n                    style={{\n                      padding: '0',\n                      height: '2em',\n                    }}\n                  >\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      width=\"2em\"\n                      height=\"2em\"\n                      viewBox=\"0 0 24 24\"\n                      stroke={isMockOffline ? theme.danger : 'currentColor'}\n                      fill=\"none\"\n                    >\n                      {isMockOffline ? (\n                        <>\n                          <path stroke=\"none\" d=\"M0 0h24v24H0z\" fill=\"none\" />\n                          <line x1=\"12\" y1=\"18\" x2=\"12.01\" y2=\"18\" />\n                          <path d=\"M9.172 15.172a4 4 0 0 1 5.656 0\" />\n                          <path d=\"M6.343 12.343a7.963 7.963 0 0 1 3.864 -2.14m4.163 .155a7.965 7.965 0 0 1 3.287 2\" />\n                          <path d=\"M3.515 9.515a12 12 0 0 1 3.544 -2.455m3.101 -.92a12 12 0 0 1 10.325 3.374\" />\n                          <line x1=\"3\" y1=\"3\" x2=\"21\" y2=\"21\" />\n                        </>\n                      ) : (\n                        <>\n                          <path stroke=\"none\" d=\"M0 0h24v24H0z\" fill=\"none\" />\n                          <line x1=\"12\" y1=\"18\" x2=\"12.01\" y2=\"18\" />\n                          <path d=\"M9.172 15.172a4 4 0 0 1 5.656 0\" />\n                          <path d=\"M6.343 12.343a8 8 0 0 1 11.314 0\" />\n                          <path d=\"M3.515 9.515c4.686 -4.687 12.284 -4.687 17 0\" />\n                        </>\n                      )}\n                    </svg>\n                    <ScreenReader\n                      text={\n                        isMockOffline\n                          ? 'Restore offline mock'\n                          : 'Mock offline behavior'\n                      }\n                    />\n                  </Button>\n                </div>\n              </div>\n            </div>\n            <div\n              style={{\n                overflowY: 'auto',\n                flex: '1',\n              }}\n            >\n              {queries.map((query) => {\n                return (\n                  <QueryRow\n                    queryKey={query.queryKey}\n                    activeQueryHash={activeQueryHash}\n                    setActiveQueryHash={setActiveQueryHash}\n                    key={query.queryHash}\n                    queryCache={queryCache}\n                  />\n                )\n              })}\n            </div>\n          </div>\n        )}\n\n        {activeQueryHash && isOpen ? (\n          <ActiveQuery\n            activeQueryHash={activeQueryHash}\n            queryCache={queryCache}\n            queryClient={queryClient}\n            errorTypes={errorTypes}\n          />\n        ) : null}\n\n        {showCloseButton ? (\n          <Button\n            type=\"button\"\n            aria-controls=\"ReactQueryDevtoolsPanel\"\n            aria-haspopup=\"true\"\n            aria-expanded=\"true\"\n            {...(otherCloseButtonProps as Record<string, unknown>)}\n            style={{\n              position: 'absolute',\n              zIndex: 99999,\n              margin: '.5em',\n              bottom: 0,\n              left: 0,\n              ...otherCloseButtonProps.style,\n            }}\n            onClick={(e) => {\n              setIsOpen(false)\n              onCloseClick?.(e)\n            }}\n          >\n            Close\n          </Button>\n        ) : null}\n      </Panel>\n    </ThemeProvider>\n  )\n})\n\nconst ActiveQuery = ({\n  queryCache,\n  activeQueryHash,\n  queryClient,\n  errorTypes,\n}: {\n  queryCache: QueryCache\n  activeQueryHash: string\n  queryClient: QueryClient\n  errorTypes: DevToolsErrorType[]\n}) => {\n  const activeQuery = useSubscribeToQueryCache(queryCache, () =>\n    queryCache.getAll().find((query) => query.queryHash === activeQueryHash),\n  )\n\n  const activeQueryState = useSubscribeToQueryCache(\n    queryCache,\n    () =>\n      queryCache.getAll().find((query) => query.queryHash === activeQueryHash)\n        ?.state,\n  )\n\n  const isStale =\n    useSubscribeToQueryCache(queryCache, () =>\n      queryCache\n        .getAll()\n        .find((query) => query.queryHash === activeQueryHash)\n        ?.isStale(),\n    ) ?? false\n\n  const observerCount =\n    useSubscribeToQueryCache(queryCache, () =>\n      queryCache\n        .getAll()\n        .find((query) => query.queryHash === activeQueryHash)\n        ?.getObserversCount(),\n    ) ?? 0\n\n  const handleRefetch = () => {\n    const promise = activeQuery?.fetch()\n    promise?.catch(noop)\n  }\n\n  const currentErrorTypeName = useMemo(() => {\n    if (activeQuery && activeQueryState?.error) {\n      const errorType = errorTypes.find(\n        (type) =>\n          type.initializer(activeQuery).toString() ===\n          activeQueryState.error?.toString(),\n      )\n      return errorType?.name\n    }\n    return undefined\n  }, [activeQuery, activeQueryState?.error, errorTypes])\n\n  if (!activeQuery || !activeQueryState) {\n    return null\n  }\n\n  const triggerError = (errorType?: DevToolsErrorType) => {\n    const error =\n      errorType?.initializer(activeQuery) ??\n      new Error('Unknown error from devtools')\n\n    const __previousQueryOptions = activeQuery.options\n\n    activeQuery.setState({\n      status: 'error',\n      error,\n      fetchMeta: {\n        ...activeQuery.state.fetchMeta,\n        __previousQueryOptions,\n      },\n    })\n  }\n\n  const restoreQueryAfterLoadingOrError = () => {\n    activeQuery.fetch(activeQuery.state.fetchMeta.__previousQueryOptions, {\n      // Make sure this fetch will cancel the previous one\n      cancelRefetch: true,\n    })\n  }\n\n  return (\n    <ActiveQueryPanel>\n      <div\n        style={{\n          padding: '.5em',\n          background: theme.backgroundAlt,\n          position: 'sticky',\n          top: 0,\n          zIndex: 1,\n        }}\n      >\n        Query Details\n      </div>\n      <div\n        style={{\n          padding: '.5em',\n        }}\n      >\n        <div\n          style={{\n            marginBottom: '.5em',\n            display: 'flex',\n            alignItems: 'flex-start',\n            justifyContent: 'space-between',\n          }}\n        >\n          <Code\n            style={{\n              lineHeight: '1.8em',\n            }}\n          >\n            <pre\n              style={{\n                margin: 0,\n                padding: 0,\n                overflow: 'auto',\n              }}\n            >\n              {displayValue(activeQuery.queryKey, true)}\n            </pre>\n          </Code>\n          <span\n            style={{\n              padding: '0.3em .6em',\n              borderRadius: '0.4em',\n              fontWeight: 'bold',\n              textShadow: '0 2px 10px black',\n              background: getQueryStatusColor({\n                queryState: activeQueryState,\n                isStale: isStale,\n                observerCount: observerCount,\n                theme,\n              }),\n              flexShrink: 0,\n            }}\n          >\n            {getQueryStatusLabel(activeQuery)}\n          </span>\n        </div>\n        <div\n          style={{\n            marginBottom: '.5em',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between',\n          }}\n        >\n          Observers: <Code>{observerCount}</Code>\n        </div>\n        <div\n          style={{\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between',\n          }}\n        >\n          Last Updated:{' '}\n          <Code>\n            {new Date(activeQueryState.dataUpdatedAt).toLocaleTimeString()}\n          </Code>\n        </div>\n      </div>\n      <div\n        style={{\n          background: theme.backgroundAlt,\n          padding: '.5em',\n          position: 'sticky',\n          top: 0,\n          zIndex: 1,\n        }}\n      >\n        Actions\n      </div>\n      <div\n        style={{\n          padding: '0.5em',\n          display: 'flex',\n          flexWrap: 'wrap',\n          gap: '0.5em',\n          alignItems: 'flex-end',\n        }}\n      >\n        <Button\n          type=\"button\"\n          onClick={handleRefetch}\n          disabled={activeQueryState.fetchStatus === 'fetching'}\n          style={{\n            background: theme.active,\n          }}\n        >\n          Refetch\n        </Button>{' '}\n        <Button\n          type=\"button\"\n          onClick={() => queryClient.invalidateQueries(activeQuery)}\n          style={{\n            background: theme.warning,\n            color: theme.inputTextColor,\n          }}\n        >\n          Invalidate\n        </Button>{' '}\n        <Button\n          type=\"button\"\n          onClick={() => queryClient.resetQueries(activeQuery)}\n          style={{\n            background: theme.gray,\n          }}\n        >\n          Reset\n        </Button>{' '}\n        <Button\n          type=\"button\"\n          onClick={() => queryClient.removeQueries(activeQuery)}\n          style={{\n            background: theme.danger,\n          }}\n        >\n          Remove\n        </Button>{' '}\n        <Button\n          type=\"button\"\n          onClick={() => {\n            // Return early if the query is already restoring\n            if (\n              activeQuery.state.fetchStatus === 'fetching' &&\n              typeof activeQuery.state.fetchMeta?.__previousQueryOptions ===\n                'undefined'\n            ) {\n              return\n            }\n\n            if (activeQuery.state.data === undefined) {\n              restoreQueryAfterLoadingOrError()\n            } else {\n              const __previousQueryOptions = activeQuery.options\n              // Trigger a fetch in order to trigger suspense as well.\n              activeQuery.fetch({\n                ...__previousQueryOptions,\n                queryFn: () => {\n                  return new Promise(() => {\n                    // Never resolve\n                  })\n                },\n                cacheTime: -1,\n              })\n              activeQuery.setState({\n                data: undefined,\n                status: 'loading',\n                fetchMeta: {\n                  ...activeQuery.state.fetchMeta,\n                  __previousQueryOptions,\n                },\n              })\n            }\n          }}\n          style={{\n            background: theme.paused,\n          }}\n        >\n          {activeQuery.state.status === 'loading' ? 'Restore' : 'Trigger'}{' '}\n          loading\n        </Button>{' '}\n        {errorTypes.length === 0 || activeQuery.state.status === 'error' ? (\n          <Button\n            type=\"button\"\n            onClick={() => {\n              if (!activeQuery.state.error) {\n                triggerError()\n              } else {\n                queryClient.resetQueries(activeQuery)\n              }\n            }}\n            style={{\n              background: theme.danger,\n            }}\n          >\n            {activeQuery.state.status === 'error' ? 'Restore' : 'Trigger'} error\n          </Button>\n        ) : (\n          <label>\n            Trigger error:\n            <Select\n              value={currentErrorTypeName ?? ''}\n              style={{ marginInlineStart: '.5em' }}\n              onChange={(e) => {\n                const errorType = errorTypes.find(\n                  (t) => t.name === e.target.value,\n                )\n\n                triggerError(errorType)\n              }}\n            >\n              <option key=\"\" value=\"\" />\n              {errorTypes.map((errorType) => (\n                <option key={errorType.name} value={errorType.name}>\n                  {errorType.name}\n                </option>\n              ))}\n            </Select>\n          </label>\n        )}\n      </div>\n      <div\n        style={{\n          background: theme.backgroundAlt,\n          padding: '.5em',\n          position: 'sticky',\n          top: 0,\n          zIndex: 1,\n        }}\n      >\n        Data Explorer\n      </div>\n      <div\n        style={{\n          padding: '.5em',\n        }}\n      >\n        <Explorer\n          label=\"Data\"\n          value={activeQueryState.data}\n          defaultExpanded={{}}\n          copyable\n        />\n      </div>\n      <div\n        style={{\n          background: theme.backgroundAlt,\n          padding: '.5em',\n          position: 'sticky',\n          top: 0,\n          zIndex: 1,\n        }}\n      >\n        Query Explorer\n      </div>\n      <div\n        style={{\n          padding: '.5em',\n        }}\n      >\n        <Explorer\n          label=\"Query\"\n          value={activeQuery}\n          defaultExpanded={{\n            queryKey: true,\n          }}\n        />\n      </div>\n    </ActiveQueryPanel>\n  )\n}\n\nconst QueryStatusCount = ({ queryCache }: { queryCache: QueryCache }) => {\n  const hasFresh = useSubscribeToQueryCache(\n    queryCache,\n    () =>\n      queryCache.getAll().filter((q) => getQueryStatusLabel(q) === 'fresh')\n        .length,\n  )\n  const hasFetching = useSubscribeToQueryCache(\n    queryCache,\n    () =>\n      queryCache.getAll().filter((q) => getQueryStatusLabel(q) === 'fetching')\n        .length,\n  )\n  const hasPaused = useSubscribeToQueryCache(\n    queryCache,\n    () =>\n      queryCache.getAll().filter((q) => getQueryStatusLabel(q) === 'paused')\n        .length,\n  )\n  const hasStale = useSubscribeToQueryCache(\n    queryCache,\n    () =>\n      queryCache.getAll().filter((q) => getQueryStatusLabel(q) === 'stale')\n        .length,\n  )\n  const hasInactive = useSubscribeToQueryCache(\n    queryCache,\n    () =>\n      queryCache.getAll().filter((q) => getQueryStatusLabel(q) === 'inactive')\n        .length,\n  )\n  return (\n    <QueryKeys>\n      <QueryKey\n        style={{\n          background: theme.success,\n          opacity: hasFresh ? 1 : 0.3,\n        }}\n      >\n        fresh <Code>({hasFresh})</Code>\n      </QueryKey>{' '}\n      <QueryKey\n        style={{\n          background: theme.active,\n          opacity: hasFetching ? 1 : 0.3,\n        }}\n      >\n        fetching <Code>({hasFetching})</Code>\n      </QueryKey>{' '}\n      <QueryKey\n        style={{\n          background: theme.paused,\n          opacity: hasPaused ? 1 : 0.3,\n        }}\n      >\n        paused <Code>({hasPaused})</Code>\n      </QueryKey>{' '}\n      <QueryKey\n        style={{\n          background: theme.warning,\n          color: 'black',\n          textShadow: '0',\n          opacity: hasStale ? 1 : 0.3,\n        }}\n      >\n        stale <Code>({hasStale})</Code>\n      </QueryKey>{' '}\n      <QueryKey\n        style={{\n          background: theme.gray,\n          opacity: hasInactive ? 1 : 0.3,\n        }}\n      >\n        inactive <Code>({hasInactive})</Code>\n      </QueryKey>\n    </QueryKeys>\n  )\n}\n\ninterface QueryRowProps {\n  queryKey: QueryKeyType\n  setActiveQueryHash: (hash: string) => void\n  activeQueryHash?: string\n  queryCache: QueryCache\n}\n\nconst QueryRow = React.memo(\n  ({\n    queryKey,\n    setActiveQueryHash,\n    activeQueryHash,\n    queryCache,\n  }: QueryRowProps) => {\n    const queryHash =\n      useSubscribeToQueryCache(\n        queryCache,\n        () => queryCache.find(queryKey)?.queryHash,\n      ) ?? ''\n\n    const queryState = useSubscribeToQueryCache(\n      queryCache,\n      () => queryCache.find(queryKey)?.state,\n    )\n\n    const isStale =\n      useSubscribeToQueryCache(queryCache, () =>\n        queryCache.find(queryKey)?.isStale(),\n      ) ?? false\n\n    const isDisabled =\n      useSubscribeToQueryCache(queryCache, () =>\n        queryCache.find(queryKey)?.isDisabled(),\n      ) ?? false\n\n    const observerCount =\n      useSubscribeToQueryCache(queryCache, () =>\n        queryCache.find(queryKey)?.getObserversCount(),\n      ) ?? 0\n\n    if (!queryState) {\n      return null\n    }\n\n    return (\n      <div\n        role=\"button\"\n        aria-label={`Open query details for ${queryHash}`}\n        onClick={() =>\n          setActiveQueryHash(activeQueryHash === queryHash ? '' : queryHash)\n        }\n        style={{\n          display: 'flex',\n          borderBottom: `solid 1px ${theme.grayAlt}`,\n          cursor: 'pointer',\n          background:\n            queryHash === activeQueryHash ? 'rgba(255,255,255,.1)' : undefined,\n        }}\n      >\n        <div\n          style={{\n            flex: '0 0 auto',\n            width: '2em',\n            height: '2em',\n            background: getQueryStatusColor({\n              queryState,\n              isStale,\n              observerCount,\n              theme,\n            }),\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            fontWeight: 'bold',\n            textShadow: isStale ? '0' : '0 0 10px black',\n            color: isStale ? 'black' : 'white',\n          }}\n        >\n          {observerCount}\n        </div>\n        {isDisabled ? (\n          <div\n            style={{\n              flex: '0 0 auto',\n              height: '2em',\n              background: theme.gray,\n              display: 'flex',\n              alignItems: 'center',\n              fontWeight: 'bold',\n              padding: '0 0.5em',\n            }}\n          >\n            disabled\n          </div>\n        ) : null}\n        <Code\n          style={{\n            padding: '.5em',\n          }}\n        >\n          {`${queryHash}`}\n        </Code>\n      </div>\n    )\n  },\n)\n\nQueryRow.displayName = 'QueryRow'\n\n// eslint-disable-next-line @typescript-eslint/no-empty-function\nfunction noop() {}\n"], "mappings": ";;;;;;;;;;;;;;;AAqJO,SAAAA,mBAAA;;EAELC,UAAA;EACAC,gBAAA;EACAC,iBAAA;EACAC,QAAA;;;;;EAKAC,UAAA;AAViC;EAYjC,MAAAC,OAAA,GAAAC,KAAA,CAAAC,MAAA;EACA,MAAAC,QAAA,GAAAF,KAAA,CAAAC,MAAA;;;;EAcA,OAAAE,aAAA,aAAAC,gBAAA,IAAAC,eAAA,oCAAAC,oBAAA;;;;EASA,MAAAC,eAAA,GAAAA,CAAAC,YAAA,EAAAC,UAAA;;IAKE,IAAAA,UAAA,CAAAC,MAAA;;IACA,MAAAC,UAAA,GAAAC,cAAA,CAAAT,aAAA;;;;MAGgBU;;IAChB,MAAAC,MAAA,GAAAL,UAAA,CAAAM,OAAA;IACA,MAAAC,MAAA,GAAAP,UAAA,CAAAQ,OAAA;;;MAIE;;MAIA;;MACA,IAAAN,UAAA;QACEO,OAAA,GAAAL,KAAA,IAAAV,aAAA,eAAAW,MAAA,GAAAK,SAAA,CAAAJ,OAAA,GAAAI,SAAA,CAAAJ,OAAA,GAAAD,MAAA;;MAMD;QACCI,OAAA,GAAAE,MAAA,IAAAjB,aAAA,gBAAAa,MAAA,GAAAG,SAAA,CAAAF,OAAA,GAAAE,SAAA,CAAAF,OAAA,GAAAD,MAAA;;MAMD;;;MAIA;;MAEA;;;MAID,IAAAK,UAAA;;MAEC;MAEDC,QAAA,CAAAC,mBAAA,cAAAC,GAAA;MACAF,QAAA,CAAAC,mBAAA,YAAAE,KAAA;;IAGFH,QAAA,CAAAI,gBAAA,cAAAF,GAAA;IACAF,QAAA,CAAAI,gBAAA,YAAAD,KAAA;;;IAIAE,iBAAA,CAAAC,MAAA,WAAAA,MAAA;;EAIF;;;IAEE,MAAAC,GAAA,GAAA3B,QAAA,CAAA4B,OAAA;IACA,IAAAD,GAAA;;QAEI,IAAAE,cAAA;UACEF,GAAA,CAAAG,KAAA,CAAAC,UAAA;QACD;;;;UAKCJ,GAAA,CAAAG,KAAA,CAAAC,UAAA;QACD;;MAGHJ,GAAA,CAAAH,gBAAA,oBAAAQ,0BAAA;MACAL,GAAA,CAAAH,gBAAA,kBAAAS,wBAAA;MAEA;QACEN,GAAA,CAAAN,mBAAA,oBAAAW,0BAAA;QACAL,GAAA,CAAAN,mBAAA,kBAAAY,wBAAA;;IAEH;IACD;;;IAGoB,IAAAC,gBAAA;;;QAEVC;;MACR,MAAAC,SAAA,GAAAC,YAAA,YAAApC,aAAA;MACA,MAAAQ,UAAA,GAAAC,cAAA,CAAAT,aAAA;;;;;;QAOEqC;MALyB;;;;;QAWzBA;MALK,IAAAH,aAAA,CAAAL,KAAA;;QASL;QACAK,aAAA,CAAAL,KAAA,CAAAS,OAAA;QACAJ,aAAA,CAAAL,KAAA,CAAAU,UAAA;QACAL,aAAA,CAAAL,KAAA,CAAAW,aAAA;QACAN,aAAA,CAAAL,KAAA,CAAAY,WAAA;QACAP,aAAA,CAAAL,KAAA,CAAAQ,YAAA;;;;;MAUF,WAAAK,MAAA;QACEA,MAAA,CAAAnB,gBAAA,WAAAF,GAAA;QAEA;UACEqB,MAAA,CAAAtB,mBAAA,WAAAC,GAAA;UACAsB,MAAA,CAAAC,OAAA,CAAAC,gBAAA,EAAAC,OAAA,GAAAC,QAAA,EAAAC,aAAA;YAEId,aAAA,CAAAL,KAAA,CAAAkB,QAAA,IAAAC,aAAA;;;MAKP;IACF;IACD;;;;;EAGI,IAAAzD,UAAA;;;IAIJ0D,OAAA,EAAAC,aAAA;;;;;IAMAxD,QAAA,EAAAM,aAAA;IACAmD,aAAA,EAAAC,YAAA;IACA3B,MAAA,EAAAG,cAAA;IACAX,MAAA,EAAAoC,cAAA;IACA3C,KAAA,EAAA4C,aAAA;;IAEAC;;;EAIF,KAAAC,SAAA;EAEA,oBAAA3D,KAAA,CAAA4D,aAAA,CAAAC,SAAA;IAEIhC,GAAA,EAAA9B,OAAA;IACA+D,SAAA;;EAFF,gBAAA9D,KAAA,CAAA4D,aAAA,CAAAG,aAAA;IAKiBC,KAAA,EAAAT;EAAf,gBAAAvD,KAAA,CAAA4D,aAAA,CAAAK,uBAAA,EAAAC,QAAA;IAEIrC,GAAA,EAAA3B,QAAA;IACAiE,OAAA,EAAAA,OAAA;IACAC,UAAA,EAAAA,UAAA;IACAvE,QAAA,EAAAM,aAAA;IACAkE,gBAAA,EAAAjE,gBAAA;IACAkE,eAAA;IACA3E,gBAAA,EAAAA;EAPF,GAAA4E,eAAA;IASEvC,KAAA,EAAAA,KAAA;IACAJ,MAAA,EAAAG,cAAA;IACAyC,SAAA,EAAAA,SAAA;;IAEA1E,UAAA,EAAAA;EAbF,OAAAiC,cAAA,gBAAA/B,KAAA,CAAA4D,aAAA,WAAAM,QAAA;IAkBEO,IAAA;EADF,GAAAC,sBAAA;IAGE;IACA;IACA;IACA;;;MAGErB,aAAA,oBAAAA,aAAA,CAAAsB,CAAA;;IAEF3C,KAAA;MACE4C,UAAA;MACAC,MAAA;MACApC,OAAA;MACA5C,QAAA;MACAiF,MAAA;MACAC,OAAA;MACAC,QAAA;MACAC,MAAA;MACAC,MAAA;MACArE,KAAA;;QAGMsE,GAAA;QACAC,KAAA;MAFF,IAAAvF,QAAA;QAMEsF,GAAA;QACAE,IAAA;MAFF,IAAAxF,QAAA;QAMEyF,MAAA;QACAF,KAAA;MAFF;QAKEE,MAAA;QACAD,IAAA;MAFF;;IA1BC;EAXT,iBAAArF,KAAA,CAAA4D,aAAA,CAAA2B,IAAA;IA4CQ;;IACQC,IAAA;;AAKvB;AAED,MAAAC,wBAAA,GAAAA,CAAAC,UAAA,EAAAC,WAAA,EAAAC,IAAA;EAKE,OAAAC,oBAAA,CAAA7F,KAAA,CAAA8F,WAAA,CAAAC,aAAA;IAGM,KAAAH,IAAA,SAAAF,UAAA,CAAAM,SAAA,CAAAC,aAAA,CAAAC,UAAA,CAAAH,aAAA;IAEA;MACE;;;AAQT;AAEY,MAAA9B,uBAAA,gBAAAjE,KAAA,CAAAmG,UAAA,UAAAlC,wBAAAmC,KAAA,EAAAvE,GAAA;;IAKTD,MAAA;;;;;;;;IAQAjC,gBAAA;IACAG,UAAA;;EAVI,IAAAsG,KAAA;;IAcEhD,OAAA,EAAAiD,YAAA;;EAAF,IAAA1G,gBAAA;;IAE+BwE;EAAF;EACnC,MAAAuB,UAAA,GAAAY,WAAA,CAAAC,aAAA;EAEA,OAAAC,IAAA,EAAAC,OAAA,IAAApG,eAAA,6BAAAyC,MAAA,CAAA4D,IAAA,CAAAC,OAAA;;;EAYA,MAAAC,MAAA,GAAA5G,KAAA,CAAA6G,OAAA,OAAAF,OAAA,CAAAH,IAAA,IAAAA,IAAA;EAEA,MAAAM,YAAA,GAAArB,wBAAA,CAAAC,UAAA,QAAAA,UAAA,CAAAqB,MAAA,GAAAC,MAAA,GAAApF,MAAA;;EAWA,MAAAqF,OAAA,GAAAjH,KAAA,CAAA6G,OAAA;IACE,MAAAK,eAAA,GAAAxB,UAAA,CAAAqB,MAAA;;MAGE;IACD;;;IAYD,OAAAI,MAAA;EACD,IAAAC,QAAA,EAAAR,MAAA,EAAAS,MAAA,EAAAP,YAAA,EAAApB,UAAA;;EAID,oBAAA1F,KAAA,CAAA4D,aAAA,CAAAG,aAAA;IACiBC,KAAA,EAAAT;EAAf,gBAAAvD,KAAA,CAAA4D,aAAA,CAAA0D,KAAA,EAAApD,QAAA;IAEIrC,GAAA,EAAAA,GAAA;IACAiC,SAAA;IACA;IACAyD,EAAA;EAJF,GAAA7H,UAAA;IAMEsC,KAAA;MACEZ,MAAA,EAAAoG,gBAAA;MACA3H,QAAA;MACA,GAAAH,UAAA,CAAAsC;IAHK;;IAOLyF,KAAA,EAAArD,UAAA;IACAsD,uBAAA;MACEC,MAAA,kFAAApE,YAAA,CAAAqE,aAAA,SAAArE,YAAA,CAAAsE,IAAA,4UAAAtE,YAAA,CAAAqE,aAAA,mKAAArE,YAAA,CAAAsE,IAAA,+EAAAtE,YAAA,CAAAqE,aAAA;IADuB;EAF3B,iBAAA5H,KAAA,CAAA4D,aAAA;IA0BE5B,KAAA,EAAA8F,oBAAA,CAAAjI,QAAA;IACAkI,WAAA,EAAAC;;IAKEhG,KAAA;MACEiG,IAAA;MACAC,SAAA;MACAC,SAAA;MACAC,QAAA;;MAEArD,OAAA;MACAsD,aAAA;IAPK;;IAWLrG,KAAA;MACES,OAAA;;MAEAsC,OAAA;MACAuD,cAAA;MACAC,UAAA;IALK;;IASL9D,IAAA;IACA;IACA;IACA;IACA;IACArB,OAAA,EAAAA,CAAA,KAAAoB,SAAA;IACAxC,KAAA;MACE+C,OAAA;MACAH,UAAA;MACAC,MAAA;MACApC,OAAA;MACA+F,WAAA;MACAtD,MAAA;IANK;EAPT,gBAAAlF,KAAA,CAAA4D,aAAA,CAAA2B,IAAA;IAgBQ;;IACQC,IAAA;EAAd,kBAAAxF,KAAA,CAAA4D,aAAA;IAIA5B,KAAA;MACE+C,OAAA;MACAsD,aAAA;IAFK;;IAMLrG,KAAA;MACE+C,OAAA;MACAuD,cAAA;MACAC,UAAA;MACAE,YAAA;IAJK;EADT,gBAAAzI,KAAA,CAAA4D,aAAA,CAAA8E,gBAAA;IAQoBhD,UAAA,EAAAA;EAAlB,IAAA7F,QAAA,IAAAwE,gBAAA,gBAAArE,KAAA,CAAA4D,aAAA,CAAA+E,MAAA;IAGI;IACAC,KAAA,EAAA/I,QAAA;IACAmC,KAAA;MAAS6G,iBAAA;;;;IAGDD,KAAA;EAAR,yBAAA5I,KAAA,CAAA4D,aAAA;IACQgF,KAAA;EAAR,0BAAA5I,KAAA,CAAA4D,aAAA;IACQgF,KAAA;EAAR,wBAAA5I,KAAA,CAAA4D,aAAA;IACQgF,KAAA;EAAR,oCAAA5I,KAAA,CAAA4D,aAAA;IAKJ5B,KAAA;MACE+C,OAAA;MACAwD,UAAA;MACAO,QAAA;MACAC,GAAA;IAJK;EADT,gBAAA/I,KAAA,CAAA4D,aAAA,CAAAoF,KAAA;IASIC,WAAA;IACA;IACAL,KAAA,EAAAvB,MAAA,WAAAA,MAAA;;;;;IAKArF,KAAA;MACEiG,IAAA;MACApH,KAAA;IAFK;;IAMP;IACA+H,KAAA,EAAApC,IAAA;;IAEAxE,KAAA;MACEiG,IAAA;MACAiB,QAAA;MACAV,WAAA;IAHK;;IAOGW,GAAA,EAAAA,GAAA;IAAUP,KAAA,EAAAO;EAAlB,eAAAA,GAAA,kBAAAnJ,KAAA,CAAA4D,aAAA,CAAAwF,MAAA;IAMF3E,IAAA;;IAEAzC,KAAA;MACES,OAAA;MACA+F,WAAA;IAFK;;IAQPa,KAAA;IACA;IACA5E,IAAA;IACArB,OAAA,EAAAA,CAAA,KAAAsC,UAAA,CAAA4D,KAAA;IACAtH,KAAA;MACES,OAAA;MACA+F,WAAA;IAFK;;IAQP/D,IAAA;IACArB,OAAA,EAAAA,CAAA;MACE,IAAAmG,aAAA;;;QAGE1G,MAAA,CAAA2G,aAAA,KAAAC,KAAA;MACD;;;MAGA;;IAEH,cAAAF,aAAA;IAKAF,KAAA,EAAAE,aAAA;IAKAvH,KAAA;MACES,OAAA;MACArB,MAAA;IAFK;;IAMLsI,KAAA;IACA7I,KAAA;IACAO,MAAA;IACAuI,OAAA;IACAC,MAAA,EAAAL,aAAA,GAAAhG,YAAA,CAAAsG,MAAA;IACAC,IAAA;;IAIUF,MAAA;IAAcG,CAAA;IAAkBD,IAAA;EAAtC,iBAAA9J,KAAA,CAAA4D,aAAA;IACMoG,EAAA;IAAQC,EAAA;IAAQC,EAAA;IAAWC,EAAA;EAAjC,iBAAAnK,KAAA,CAAA4D,aAAA;IACMmG,CAAA;EAAN,iBAAA/J,KAAA,CAAA4D,aAAA;IACMmG,CAAA;EAAN,iBAAA/J,KAAA,CAAA4D,aAAA;IACMmG,CAAA;EAAN,iBAAA/J,KAAA,CAAA4D,aAAA;IACMoG,EAAA;IAAOC,EAAA;IAAOC,EAAA;IAAQC,EAAA;;IAItBP,MAAA;IAAcG,CAAA;IAAkBD,IAAA;EAAtC,iBAAA9J,KAAA,CAAA4D,aAAA;IACMoG,EAAA;IAAQC,EAAA;IAAQC,EAAA;IAAWC,EAAA;EAAjC,iBAAAnK,KAAA,CAAA4D,aAAA;IACMmG,CAAA;EAAN,iBAAA/J,KAAA,CAAA4D,aAAA;IACMmG,CAAA;EAAN,iBAAA/J,KAAA,CAAA4D,aAAA;IACMmG,CAAA;EAAN,mBAAA/J,KAAA,CAAA4D,aAAA,CAAAwG,YAAA;IAKJ5E,IAAA,EAAA+D,aAAA;EADF,qBAAAvJ,KAAA,CAAA4D,aAAA;IAYN5B,KAAA;MACEqI,SAAA;MACApC,IAAA;IAFK;EADT,GAAAhB,OAAA,CAAAqD,GAAA,CAAAC,KAAA;IAOI,oBAAAvK,KAAA,CAAA4D,aAAA,CAAA4G,QAAA;;MAGIC,eAAA,EAAAA,eAAA;MACAC,kBAAA,EAAAA,kBAAA;;MAEAhF,UAAA,EAAAA;;;IAUR+E,eAAA,EAAAA,eAAA;IACA/E,UAAA,EAAAA,UAAA;IACAY,WAAA,EAAAA,WAAA;IACAxG,UAAA,EAAAA;EAJF,WAAAwE,eAAA,gBAAAtE,KAAA,CAAA4D,aAAA,CAAAwF,MAAA,EAAAlF,QAAA;IAUEO,IAAA;IACA;IACA;;EAHF,GAAAkG,qBAAA;IAME3I,KAAA;MACEnC,QAAA;MACAiF,MAAA;MACAG,MAAA;MACAK,MAAA;MACAD,IAAA;MACA,GAAAsF,qBAAA,CAAA3I;;;;MAIAqE,YAAA,oBAAAA,YAAA,CAAA1B,CAAA;IACD;;AAQZ;AAED,MAAAiG,WAAA,GAAAA,CAAA;;;;EAIE9K;AAJmB;EAUf,IAAA+K,qBAAA,EAAAC,sBAAA;;EAKJ,MAAAC,gBAAA,GAAAtF,wBAAA,CAAAC,UAAA;IAEE,IAAAsF,qBAAA;IAAA,QAAAA,qBAAA,GAAAtF,UAAA,CAAAqB,MAAA,GAAAkE,IAAA,CAAAV,KAAA,IAAAA,KAAA,CAAAW,SAAA,KAAAT,eAAA,sBAAAO,qBAAA,CAAAG,KAAA;EAAA;EAKF,MAAAC,OAAA,IAAAP,qBAAA,GAAApF,wBAAA,CAAAC,UAAA;IACuC,IAAA2F,sBAAA;IAAA,QAAAA,sBAAA,GAAA3F,UAAA,CAAAqB,MAAA,GAAAkE,IAAA,CAAAV,KAAA,IAAAA,KAAA,CAAAW,SAAA,KAAAT,eAAA,sBAAAY,sBAAA,CAAAD,OAAA;;EAOvC,MAAAE,aAAA,IAAAR,sBAAA,GAAArF,wBAAA,CAAAC,UAAA;IACuC,IAAA6F,sBAAA;IAAA,QAAAA,sBAAA,GAAA7F,UAAA,CAAAqB,MAAA,GAAAkE,IAAA,CAAAV,KAAA,IAAAA,KAAA,CAAAW,SAAA,KAAAT,eAAA,sBAAAc,sBAAA,CAAAC,iBAAA;;;IAQrC,MAAAC,OAAA,GAAAC,WAAA,oBAAAA,WAAA,CAAAC,KAAA;IACAF,OAAA,oBAAAA,OAAA,CAAAG,KAAA,CAAAC,IAAA;;EAGF,MAAAC,oBAAA,GAAAjF,OAAA;IACE,IAAA6E,WAAA,IAAAX,gBAAA,YAAAA,gBAAA,CAAAgB,KAAA;MACE,MAAAC,SAAA,GAAAlM,UAAA,CAAAmL,IAAA,CAAAxG,IAAA;QACE,IAAAwH,qBAAA;QAAA,OAAAxH,IAAA,CAAAyH,WAAA,CAAAR,WAAA,EAAAS,QAAA,SAAAF,qBAAA,GAAAlB,gBAAA,CAAAgB,KAAA,qBAAAE,qBAAA,CAAAE,QAAA;MAAA;MAIF,OAAAH,SAAA,oBAAAA,SAAA,CAAAI,IAAA;IACD;IACD,OAAAC,SAAA;EACD,IAAAX,WAAA,EAAAX,gBAAA,oBAAAA,gBAAA,CAAAgB,KAAA,EAAAjM,UAAA;EAED,KAAA4L,WAAA,KAAAX,gBAAA;IACE;EACD;;IAEuD,IAAAuB,qBAAA;IACtD,MAAAP,KAAA,IAAAO,qBAAA,GAAAN,SAAA,oBAAAA,SAAA,CAAAE,WAAA,CAAAR,WAAA,aAAAY,qBAAA,OAAAC,KAAA;IAIA,MAAAC,sBAAA,GAAAd,WAAA,CAAAe,OAAA;;MAGEC,MAAA;;MAEAC,SAAA;QAAA,GAAAjB,WAAA,CAAAP,KAAA,CAAAwB,SAAA;QAEEH;MAFS;;;;;MASX;MACAI,aAAA;;;;IAOE5K,KAAA;MACES,OAAA;;MAEA5C,QAAA;MACAsF,GAAA;MACAL,MAAA;IALK;EADT,kCAAA9E,KAAA,CAAA4D,aAAA;IAYE5B,KAAA;MACES,OAAA;IADK;;IAKLT,KAAA;MACEyG,YAAA;MACA1D,OAAA;MACAwD,UAAA;MACAD,cAAA;IAJK;EADT,gBAAAtI,KAAA,CAAA4D,aAAA,CAAAiJ,IAAA;IASI7K,KAAA;MACE8K,UAAA;IADK;;IAKL9K,KAAA;MACEiD,MAAA;MACAxC,OAAA;MACA2F,QAAA;IAHK;;IAUTpG,KAAA;MACES,OAAA;MACAsK,YAAA;MACAC,UAAA;MACAC,UAAA;;QAEEC,UAAA,EAAAnC,gBAAA;QACAK,OAAA,EAAAA,OAAA;QACAE,aAAA,EAAAA,aAAA;QACAtH,KAAA,EAAAT;MAJ8B;MAMhC4J,UAAA;IAXK;EADT,GAAAC,mBAAA,CAAA1B,WAAA,kBAAA1L,KAAA,CAAA4D,aAAA;IAmBA5B,KAAA;MACEyG,YAAA;MACA1D,OAAA;MACAwD,UAAA;MACAD,cAAA;IAJK;EADT,+BAAAtI,KAAA,CAAA4D,aAAA,CAAAiJ,IAAA,QAAAvB,aAAA,iBAAAtL,KAAA,CAAA4D,aAAA;IAWE5B,KAAA;MACE+C,OAAA;MACAwD,UAAA;MACAD,cAAA;IAHK;EADT,sCAAAtI,KAAA,CAAA4D,aAAA,CAAAiJ,IAAA,YAAAQ,IAAA,CAAAtC,gBAAA,CAAAuC,aAAA,EAAAC,kBAAA,oBAAAvN,KAAA,CAAA4D,aAAA;IAcA5B,KAAA;;MAEES,OAAA;MACA5C,QAAA;MACAsF,GAAA;MACAL,MAAA;IALK;EADT,4BAAA9E,KAAA,CAAA4D,aAAA;IAYE5B,KAAA;MACES,OAAA;MACAsC,OAAA;MACA+D,QAAA;MACAC,GAAA;MACAR,UAAA;IALK;EADT,gBAAAvI,KAAA,CAAA4D,aAAA,CAAAwF,MAAA;IAUI3E,IAAA;IACArB,OAAA,EAAAoK,aAAA;IACAC,QAAA,EAAA1C,gBAAA,CAAA2C,WAAA;IACA1L,KAAA;;IAAO;EAJT,iCAAAhC,KAAA,CAAA4D,aAAA,CAAAwF,MAAA;IAWE3E,IAAA;IACArB,OAAA,EAAAA,CAAA,KAAAkD,WAAA,CAAAqH,iBAAA,CAAAjC,WAAA;IACA1J,KAAA;;;IAAO;EAHT,oCAAAhC,KAAA,CAAA4D,aAAA,CAAAwF,MAAA;IAWE3E,IAAA;IACArB,OAAA,EAAAA,CAAA,KAAAkD,WAAA,CAAAsH,YAAA,CAAAlC,WAAA;IACA1J,KAAA;;IAAO;EAHT,+BAAAhC,KAAA,CAAA4D,aAAA,CAAAwF,MAAA;IAUE3E,IAAA;IACArB,OAAA,EAAAA,CAAA,KAAAkD,WAAA,CAAAuH,aAAA,CAAAnC,WAAA;IACA1J,KAAA;;IAAO;EAHT,gCAAAhC,KAAA,CAAA4D,aAAA,CAAAwF,MAAA;IAUE3E,IAAA;IACArB,OAAA,EAAAA,CAAA;MAAe,IAAA0K,qBAAA;;MACb;MACA,IAAApC,WAAA,CAAAP,KAAA,CAAAuC,WAAA,4BAAAI,qBAAA,GAAApC,WAAA,CAAAP,KAAA,CAAAwB,SAAA,qBAAAmB,qBAAA,CAAAtB,sBAAA;QAKE;MACD;MAED,IAAAd,WAAA,CAAAP,KAAA,CAAA4C,IAAA,KAAA1B,SAAA;;MAEC;QACC,MAAAG,sBAAA,GAAAd,WAAA,CAAAe,OAAA;;QAEAf,WAAA,CAAAC,KAAA;UAAA,GAAAa,sBAAA;UAEEwB,OAAA,EAAAA,CAAA;YACE,WAAAC,OAAA;YAAA,CAEC;;UAEHC,SAAA;;;UAGAH,IAAA,EAAA1B,SAAA;UACAK,MAAA;UACAC,SAAA;YAAA,GAAAjB,WAAA,CAAAP,KAAA,CAAAwB,SAAA;YAEEH;UAFS;;MAKd;;IAEHxK,KAAA;;IAAO;EApCT,GAAA0J,WAAA,CAAAP,KAAA,CAAAuB,MAAA,8DAAA5M,UAAA,CAAAkH,MAAA,UAAA0E,WAAA,CAAAP,KAAA,CAAAuB,MAAA,4BAAA1M,KAAA,CAAA4D,aAAA,CAAAwF,MAAA;IA6CI3E,IAAA;IACArB,OAAA,EAAAA,CAAA;MACE,KAAAsI,WAAA,CAAAP,KAAA,CAAAY,KAAA;;MAEC;;MAEA;;IAEH/J,KAAA;;IAAO;EATT,GAAA0J,WAAA,CAAAP,KAAA,CAAAuB,MAAA,+DAAA1M,KAAA,CAAA4D,aAAA,+CAAA5D,KAAA,CAAA4D,aAAA,CAAA+E,MAAA;IAmBIC,KAAA,EAAAkD,oBAAA,WAAAA,oBAAA;IACA9J,KAAA;MAAS6G,iBAAA;;;MAEP,MAAAmD,SAAA,GAAAlM,UAAA,CAAAmL,IAAA,CAAAkD,CAAA,IAAAA,CAAA,CAAA/B,IAAA,KAAAzH,CAAA,CAAAyJ,MAAA,CAAAxF,KAAA;;IAKD;;IAEOO,GAAA;IAAOP,KAAA;EAAf,IAAA9I,UAAA,CAAAwK,GAAA,CAAA0B,SAAA,iBAAAhM,KAAA,CAAA4D,aAAA;;;;IAWN5B,KAAA;;MAEES,OAAA;MACA5C,QAAA;MACAsF,GAAA;MACAL,MAAA;IALK;EADT,kCAAA9E,KAAA,CAAA4D,aAAA;IAYE5B,KAAA;MACES,OAAA;IADK;EADT,gBAAAzC,KAAA,CAAA4D,aAAA,CAAAyK,QAAA;IAMIC,KAAA;;IAEAC,eAAA;;EAHF,kBAAAvO,KAAA,CAAA4D,aAAA;IAQA5B,KAAA;;MAEES,OAAA;MACA5C,QAAA;MACAsF,GAAA;MACAL,MAAA;IALK;EADT,mCAAA9E,KAAA,CAAA4D,aAAA;IAYE5B,KAAA;MACES,OAAA;IADK;EADT,gBAAAzC,KAAA,CAAA4D,aAAA,CAAAyK,QAAA;IAMIC,KAAA;IACA1F,KAAA,EAAA8C,WAAA;IACA6C,eAAA;MACEC,QAAA;IADe;EAHnB;AAUP;AAED,MAAA9F,gBAAA,GAAAA,CAAA;EAA4BhD;AAAF;;;;;;EA+BxB,oBAAA1F,KAAA,CAAA4D,aAAA,CAAA6K,SAAA,qBAAAzO,KAAA,CAAA4D,aAAA,CAAA8K,QAAA;IAGM1M,KAAA;;MAEE2M,OAAA,EAAAC,QAAA;IAFK;;IAQP5M,KAAA;;MAEE2M,OAAA,EAAAE,WAAA;IAFK;;IAQP7M,KAAA;;MAEE2M,OAAA,EAAAG,SAAA;IAFK;;IAQP9M,KAAA;;MAEE+M,KAAA;MACA9B,UAAA;MACA0B,OAAA,EAAAK,QAAA;IAJK;;IAUPhN,KAAA;;MAEE2M,OAAA,EAAAM,WAAA;IAFK;EADT,6BAAAjP,KAAA,CAAA4D,aAAA,CAAAiJ,IAAA,aAAAoC,WAAA;AAUL;AASD,MAAAzE,QAAA,gBAAAxK,KAAA,CAAAkP,IAAA;;;;EAKIxJ;AAJD;EAKoB,IAAAyJ,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACnB,MAAApE,SAAA,IAAAiE,sBAAA,GAAA1J,wBAAA,CAAAC,UAAA;IAGI,IAAA6J,gBAAA;;;EAGJ,MAAArC,UAAA,GAAAzH,wBAAA,CAAAC,UAAA;IAEE,IAAA8J,iBAAA;;EAAA;EAGF,MAAApE,OAAA,IAAAgE,sBAAA,GAAA3J,wBAAA,CAAAC,UAAA;IACuC,IAAA+J,iBAAA;;;EAIvC,MAAAC,UAAA,IAAAL,sBAAA,GAAA5J,wBAAA,CAAAC,UAAA;IACuC,IAAAiK,iBAAA;;;EAIvC,MAAArE,aAAA,IAAAgE,sBAAA,GAAA7J,wBAAA,CAAAC,UAAA;IACuC,IAAAkK,iBAAA;;;;IAKrC;EACD;;IAIGC,IAAA;IACA,0CAAA3E,SAAA;;IAIAlJ,KAAA;MACE+C,OAAA;;MAEAG,MAAA;MACAN,UAAA,EAAAsG,SAAA,KAAAT,eAAA,4BAAA4B;IAJK;;IASLrK,KAAA;MACEiG,IAAA;MACApH,KAAA;MACAO,MAAA;;;;;QAKE4C,KAAA,EAAAT;MAJ8B;MAMhCwB,OAAA;MACAwD,UAAA;MACAD,cAAA;MACA0E,UAAA;MACAC,UAAA,EAAA7B,OAAA;MACA2D,KAAA,EAAA3D,OAAA;IAfK;EADT,GAAAE,aAAA,GAAAoE,UAAA,gBAAA1P,KAAA,CAAA4D,aAAA;IAuBI5B,KAAA;MACEiG,IAAA;MACA7G,MAAA;;MAEA2D,OAAA;MACAwD,UAAA;MACAyE,UAAA;MACAvK,OAAA;IAPK;EADT,oCAAAzC,KAAA,CAAA4D,aAAA,CAAAiJ,IAAA;IAeA7K,KAAA;MACES,OAAA;IADK;;AAQd;AAGH+H,QAAA,CAAAsF,WAAA;;AAGA,SAAAjE,KAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}