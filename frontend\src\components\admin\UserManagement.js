import React, { useState } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import {
  FiUsers,
  FiSearch,
  FiFilter,
  FiEdit,
  FiTrash2,
  FiPlus,
  FiMail,
  FiCalendar,
  FiShield,
  FiUser,
  FiMoreVertical
} from 'react-icons/fi';
import { Card, Button, Input } from '../ui';
import { admin } from '../../services/api';
import { toast } from 'react-toastify';

const UserManagementContainer = styled.div`
  padding: ${({ theme }) => theme.spacing[6]};
`;

const PageHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: ${({ theme }) => theme.spacing[8]};
  
  @media (max-width: ${({ theme }) => theme.breakpoints.md}) {
    flex-direction: column;
    gap: ${({ theme }) => theme.spacing[4]};
    align-items: stretch;
  }
`;

const HeaderLeft = styled.div`
  display: flex;
  flex-direction: column;
`;

const PageTitle = styled.h1`
  font-size: ${({ theme }) => theme.fontSizes['3xl']};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  color: ${({ theme }) => theme.colors.gray[900]};
  margin-bottom: ${({ theme }) => theme.spacing[2]};
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[3]};
`;

const PageSubtitle = styled.p`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  color: ${({ theme }) => theme.colors.gray[600]};
`;

const HeaderActions = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing[3]};
  
  @media (max-width: ${({ theme }) => theme.breakpoints.md}) {
    flex-direction: column;
  }
`;

const SearchAndFilter = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing[4]};
  margin-bottom: ${({ theme }) => theme.spacing[6]};
  
  @media (max-width: ${({ theme }) => theme.breakpoints.md}) {
    flex-direction: column;
  }
`;

const SearchContainer = styled.div`
  flex: 1;
  max-width: 400px;
`;

const UsersTable = styled(Card)`
  overflow: hidden;
`;

const TableHeader = styled.div`
  display: grid;
  grid-template-columns: 1fr 200px 150px 120px 100px 60px;
  gap: ${({ theme }) => theme.spacing[4]};
  padding: ${({ theme }) => theme.spacing[4]} ${({ theme }) => theme.spacing[6]};
  background: ${({ theme }) => theme.colors.gray[50]};
  border-bottom: 1px solid ${({ theme }) => theme.colors.gray[200]};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  color: ${({ theme }) => theme.colors.gray[700]};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  
  @media (max-width: ${({ theme }) => theme.breakpoints.lg}) {
    grid-template-columns: 1fr 150px 100px 60px;
    
    .hide-mobile {
      display: none;
    }
  }
`;

const TableBody = styled.div`
  max-height: 600px;
  overflow-y: auto;
`;

const UserRow = styled(motion.div)`
  display: grid;
  grid-template-columns: 1fr 200px 150px 120px 100px 60px;
  gap: ${({ theme }) => theme.spacing[4]};
  padding: ${({ theme }) => theme.spacing[4]} ${({ theme }) => theme.spacing[6]};
  border-bottom: 1px solid ${({ theme }) => theme.colors.gray[100]};
  align-items: center;
  transition: all ${({ theme }) => theme.transitions.fast};
  
  &:hover {
    background: ${({ theme }) => theme.colors.gray[25]};
  }
  
  &:last-child {
    border-bottom: none;
  }
  
  @media (max-width: ${({ theme }) => theme.breakpoints.lg}) {
    grid-template-columns: 1fr 150px 100px 60px;
    
    .hide-mobile {
      display: none;
    }
  }
`;

const UserInfo = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[3]};
`;

const UserAvatar = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, ${({ theme }) => theme.colors.primary[500]}, ${({ theme }) => theme.colors.primary[600]});
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  font-size: ${({ theme }) => theme.fontSizes.sm};
`;

const UserDetails = styled.div`
  flex: 1;
  min-width: 0;
`;

const UserName = styled.div`
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  color: ${({ theme }) => theme.colors.gray[900]};
  margin-bottom: 2px;
`;

const UserEmail = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.gray[500]};
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

const StatusBadge = styled.div`
  display: inline-flex;
  align-items: center;
  padding: ${({ theme }) => theme.spacing[1]} ${({ theme }) => theme.spacing[2]};
  border-radius: ${({ theme }) => theme.borderRadius.full};
  font-size: ${({ theme }) => theme.fontSizes.xs};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  
  ${({ status, theme }) => {
    switch (status) {
      case 'active':
        return `
          background: ${theme.colors.success[100]};
          color: ${theme.colors.success[700]};
        `;
      case 'inactive':
        return `
          background: ${theme.colors.gray[100]};
          color: ${theme.colors.gray[700]};
        `;
      case 'suspended':
        return `
          background: ${theme.colors.error[100]};
          color: ${theme.colors.error[700]};
        `;
      default:
        return `
          background: ${theme.colors.gray[100]};
          color: ${theme.colors.gray[700]};
        `;
    }
  }}
`;

const RoleBadge = styled.div`
  display: inline-flex;
  align-items: center;
  padding: ${({ theme }) => theme.spacing[1]} ${({ theme }) => theme.spacing[2]};
  border-radius: ${({ theme }) => theme.borderRadius.base};
  font-size: ${({ theme }) => theme.fontSizes.xs};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  
  ${({ role, theme }) => {
    switch (role) {
      case 'admin':
        return `
          background: ${theme.colors.primary[100]};
          color: ${theme.colors.primary[700]};
        `;
      case 'student':
        return `
          background: ${theme.colors.secondary[100]};
          color: ${theme.colors.secondary[700]};
        `;
      default:
        return `
          background: ${theme.colors.gray[100]};
          color: ${theme.colors.gray[700]};
        `;
    }
  }}
`;

const ActionButton = styled(motion.button)`
  width: 32px;
  height: 32px;
  border: none;
  border-radius: ${({ theme }) => theme.borderRadius.base};
  background: ${({ theme }) => theme.colors.gray[100]};
  color: ${({ theme }) => theme.colors.gray[600]};
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all ${({ theme }) => theme.transitions.fast};
  
  &:hover {
    background: ${({ theme }) => theme.colors.gray[200]};
    color: ${({ theme }) => theme.colors.gray[800]};
  }
`;

const EmptyState = styled.div`
  text-align: center;
  padding: ${({ theme }) => theme.spacing[12]} ${({ theme }) => theme.spacing[4]};
  color: ${({ theme }) => theme.colors.gray[500]};
`;

const UserManagement = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterRole, setFilterRole] = useState('all');
  const queryClient = useQueryClient();

  // جلب المستخدمين من قاعدة البيانات
  const { data: users, isLoading, error } = useQuery({
    queryKey: ['users', searchTerm, filterRole],
    queryFn: () => admin.getUsers({
      search: searchTerm,
      role: filterRole === 'all' ? undefined : filterRole
    }),
    select: (data) => data.data,
    onError: (error) => {
      toast.error('فشل في تحميل المستخدمين');
      console.error('Users error:', error);
    },
    refetchOnWindowFocus: false,
  });

  const handleUpdateUser = async (userId, updates) => {
    try {
      await admin.updateUser(userId, updates);
      toast.success('تم تحديث المستخدم بنجاح');
      queryClient.invalidateQueries(['users']);
    } catch (error) {
      toast.error('فشل في تحديث المستخدم');
      console.error('Update user error:', error);
    }
  };

  const handleDeleteUser = async (userId) => {
    if (window.confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
      try {
        await admin.deleteUser(userId);
        toast.success('تم حذف المستخدم بنجاح');
        queryClient.invalidateQueries(['users']);
      } catch (error) {
        toast.error('فشل في حذف المستخدم');
        console.error('Delete user error:', error);
      }
    }
  };

  const filteredUsers = users || [];

  const getStatusText = (status) => {
    switch (status) {
      case 'active': return 'نشط';
      case 'inactive': return 'غير نشط';
      case 'suspended': return 'معلق';
      default: return status;
    }
  };

  const getRoleText = (role) => {
    switch (role) {
      case 'admin': return 'مدير';
      case 'student': return 'طالب';
      default: return role;
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA');
  };

  return (
    <UserManagementContainer>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <PageHeader>
          <HeaderLeft>
            <PageTitle>
              <FiUsers size={32} />
              إدارة المستخدمين
            </PageTitle>
            <PageSubtitle>إدارة حسابات المستخدمين وصلاحياتهم</PageSubtitle>
          </HeaderLeft>
          
          <HeaderActions>
            <Button
              variant="primary"
              leftIcon={<FiPlus size={18} />}
            >
              إضافة مستخدم جديد
            </Button>
          </HeaderActions>
        </PageHeader>

        <SearchAndFilter>
          <SearchContainer>
            <Input
              placeholder="البحث عن المستخدمين..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              leftIcon={<FiSearch size={18} />}
            />
          </SearchContainer>
          
          <select
            value={filterRole}
            onChange={(e) => setFilterRole(e.target.value)}
            style={{
              padding: '12px 16px',
              border: '2px solid #e5e7eb',
              borderRadius: '8px',
              background: 'white',
              minWidth: '120px'
            }}
          >
            <option value="all">جميع الأدوار</option>
            <option value="admin">مدير</option>
            <option value="student">طالب</option>
          </select>
          

        </SearchAndFilter>

        <UsersTable>
          <TableHeader>
            <div>المستخدم</div>
            <div className="hide-mobile">تاريخ الانضمام</div>
            <div className="hide-mobile">آخر دخول</div>
            <div>الدور</div>
            <div>الحالة</div>
            <div>إجراءات</div>
          </TableHeader>
          
          <TableBody>
            <AnimatePresence>
              {isLoading ? (
                <div style={{ textAlign: 'center', padding: '40px', color: '#6b7280' }}>
                  جاري تحميل المستخدمين...
                </div>
              ) : error ? (
                <EmptyState>
                  <FiUsers size={48} style={{ marginBottom: '16px', opacity: 0.5 }} />
                  <div>حدث خطأ في تحميل المستخدمين</div>
                </EmptyState>
              ) : filteredUsers.length === 0 ? (
                <EmptyState>
                  <FiUsers size={48} style={{ marginBottom: '16px', opacity: 0.5 }} />
                  <div>لا توجد مستخدمين</div>
                </EmptyState>
              ) : (
                filteredUsers.map((user, index) => (
                  <UserRow
                    key={user.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ delay: index * 0.05 }}
                  >
                    <UserInfo>
                      <UserAvatar>
                        {user.full_name?.charAt(0) || user.email?.charAt(0) || '?'}
                      </UserAvatar>
                      <UserDetails>
                        <UserName>{user.full_name || 'غير محدد'}</UserName>
                        <UserEmail>{user.email}</UserEmail>
                      </UserDetails>
                    </UserInfo>

                    <div className="hide-mobile">{formatDate(user.created_at)}</div>
                    <div className="hide-mobile">{formatDate(user.updated_at)}</div>

                    <RoleBadge role={user.role}>
                      {user.role === 'admin' ? <FiShield size={12} /> : <FiUser size={12} />}
                      <span style={{ marginRight: '4px' }}>{getRoleText(user.role)}</span>
                    </RoleBadge>

                    <div style={{ display: 'flex', gap: '8px' }}>
                      <ActionButton
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        onClick={() => handleUpdateUser(user.id, {
                          role: user.role === 'admin' ? 'student' : 'admin'
                        })}
                        title="تغيير الدور"
                      >
                        <FiEdit size={16} />
                      </ActionButton>

                      <ActionButton
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        onClick={() => handleDeleteUser(user.id)}
                        title="حذف المستخدم"
                        style={{ color: '#ef4444' }}
                      >
                        <FiTrash2 size={16} />
                      </ActionButton>
                    </div>
                  </UserRow>
                ))
              )}
            </AnimatePresence>
          </TableBody>
        </UsersTable>
      </motion.div>
    </UserManagementContainer>
  );
};

export default UserManagement;
