{"ast": null, "code": "function getType(payload) {\n  return Object.prototype.toString.call(payload).slice(8, -1);\n}\nfunction isAnyObject(payload) {\n  return getType(payload) === \"Object\";\n}\nfunction isArray(payload) {\n  return getType(payload) === \"Array\";\n}\nfunction isBlob(payload) {\n  return getType(payload) === \"Blob\";\n}\nfunction isBoolean(payload) {\n  return getType(payload) === \"Boolean\";\n}\nfunction isDate(payload) {\n  return getType(payload) === \"Date\" && !isNaN(payload);\n}\nfunction isEmptyArray(payload) {\n  return isArray(payload) && payload.length === 0;\n}\nfunction isPlainObject(payload) {\n  if (getType(payload) !== \"Object\") return false;\n  const prototype = Object.getPrototypeOf(payload);\n  return !!prototype && prototype.constructor === Object && prototype === Object.prototype;\n}\nfunction isEmptyObject(payload) {\n  return isPlainObject(payload) && Object.keys(payload).length === 0;\n}\nfunction isEmptyString(payload) {\n  return payload === \"\";\n}\nfunction isError(payload) {\n  return getType(payload) === \"Error\" || payload instanceof Error;\n}\nfunction isFile(payload) {\n  return getType(payload) === \"File\";\n}\nfunction isFullArray(payload) {\n  return isArray(payload) && payload.length > 0;\n}\nfunction isFullObject(payload) {\n  return isPlainObject(payload) && Object.keys(payload).length > 0;\n}\nfunction isString(payload) {\n  return getType(payload) === \"String\";\n}\nfunction isFullString(payload) {\n  return isString(payload) && payload !== \"\";\n}\nfunction isFunction(payload) {\n  return typeof payload === \"function\";\n}\nfunction isType(payload, type) {\n  if (!(type instanceof Function)) {\n    throw new TypeError(\"Type must be a function\");\n  }\n  if (!Object.prototype.hasOwnProperty.call(type, \"prototype\")) {\n    throw new TypeError(\"Type is not a class\");\n  }\n  const name = type.name;\n  return getType(payload) === name || Boolean(payload && payload.constructor === type);\n}\nfunction isInstanceOf(value, classOrClassName) {\n  if (typeof classOrClassName === \"function\") {\n    for (let p = value; p; p = Object.getPrototypeOf(p)) {\n      if (isType(p, classOrClassName)) {\n        return true;\n      }\n    }\n    return false;\n  } else {\n    for (let p = value; p; p = Object.getPrototypeOf(p)) {\n      if (getType(p) === classOrClassName) {\n        return true;\n      }\n    }\n    return false;\n  }\n}\nfunction isMap(payload) {\n  return getType(payload) === \"Map\";\n}\nfunction isNaNValue(payload) {\n  return getType(payload) === \"Number\" && isNaN(payload);\n}\nfunction isNumber(payload) {\n  return getType(payload) === \"Number\" && !isNaN(payload);\n}\nfunction isNegativeNumber(payload) {\n  return isNumber(payload) && payload < 0;\n}\nfunction isNull(payload) {\n  return getType(payload) === \"Null\";\n}\nfunction isOneOf(a, b, c, d, e) {\n  return value => a(value) || b(value) || !!c && c(value) || !!d && d(value) || !!e && e(value);\n}\nfunction isUndefined(payload) {\n  return getType(payload) === \"Undefined\";\n}\nconst isNullOrUndefined = isOneOf(isNull, isUndefined);\nfunction isObject(payload) {\n  return isPlainObject(payload);\n}\nfunction isObjectLike(payload) {\n  return isAnyObject(payload);\n}\nfunction isPositiveNumber(payload) {\n  return isNumber(payload) && payload > 0;\n}\nfunction isSymbol(payload) {\n  return getType(payload) === \"Symbol\";\n}\nfunction isPrimitive(payload) {\n  return isBoolean(payload) || isNull(payload) || isUndefined(payload) || isNumber(payload) || isString(payload) || isSymbol(payload);\n}\nfunction isPromise(payload) {\n  return getType(payload) === \"Promise\";\n}\nfunction isRegExp(payload) {\n  return getType(payload) === \"RegExp\";\n}\nfunction isSet(payload) {\n  return getType(payload) === \"Set\";\n}\nfunction isWeakMap(payload) {\n  return getType(payload) === \"WeakMap\";\n}\nfunction isWeakSet(payload) {\n  return getType(payload) === \"WeakSet\";\n}\nexport { getType, isAnyObject, isArray, isBlob, isBoolean, isDate, isEmptyArray, isEmptyObject, isEmptyString, isError, isFile, isFullArray, isFullObject, isFullString, isFunction, isInstanceOf, isMap, isNaNValue, isNegativeNumber, isNull, isNullOrUndefined, isNumber, isObject, isObjectLike, isOneOf, isPlainObject, isPositiveNumber, isPrimitive, isPromise, isRegExp, isSet, isString, isSymbol, isType, isUndefined, isWeakMap, isWeakSet };", "map": {"version": 3, "names": ["getType", "payload", "Object", "prototype", "toString", "call", "slice", "isAnyObject", "isArray", "isBlob", "isBoolean", "isDate", "isNaN", "isEmptyArray", "length", "isPlainObject", "getPrototypeOf", "constructor", "isEmptyObject", "keys", "isEmptyString", "isError", "Error", "isFile", "isFullArray", "isFullObject", "isString", "isFullString", "isFunction", "isType", "type", "Function", "TypeError", "hasOwnProperty", "name", "Boolean", "isInstanceOf", "value", "classOrClassName", "p", "isMap", "isNaNValue", "isNumber", "isNegativeNumber", "isNull", "isOneOf", "a", "b", "c", "d", "e", "isUndefined", "isNullOrUndefined", "isObject", "isObjectLike", "isPositiveNumber", "isSymbol", "isPrimitive", "isPromise", "isRegExp", "isSet", "isWeakMap", "isWeakSet"], "sources": ["D:/menasa/frontend/node_modules/is-what/dist/index.js"], "sourcesContent": ["function getType(payload) {\n  return Object.prototype.toString.call(payload).slice(8, -1);\n}\n\nfunction isAnyObject(payload) {\n  return getType(payload) === \"Object\";\n}\n\nfunction isArray(payload) {\n  return getType(payload) === \"Array\";\n}\n\nfunction isBlob(payload) {\n  return getType(payload) === \"Blob\";\n}\n\nfunction isBoolean(payload) {\n  return getType(payload) === \"Boolean\";\n}\n\nfunction isDate(payload) {\n  return getType(payload) === \"Date\" && !isNaN(payload);\n}\n\nfunction isEmptyArray(payload) {\n  return isArray(payload) && payload.length === 0;\n}\n\nfunction isPlainObject(payload) {\n  if (getType(payload) !== \"Object\")\n    return false;\n  const prototype = Object.getPrototypeOf(payload);\n  return !!prototype && prototype.constructor === Object && prototype === Object.prototype;\n}\n\nfunction isEmptyObject(payload) {\n  return isPlainObject(payload) && Object.keys(payload).length === 0;\n}\n\nfunction isEmptyString(payload) {\n  return payload === \"\";\n}\n\nfunction isError(payload) {\n  return getType(payload) === \"Error\" || payload instanceof Error;\n}\n\nfunction isFile(payload) {\n  return getType(payload) === \"File\";\n}\n\nfunction isFullArray(payload) {\n  return isArray(payload) && payload.length > 0;\n}\n\nfunction isFullObject(payload) {\n  return isPlainObject(payload) && Object.keys(payload).length > 0;\n}\n\nfunction isString(payload) {\n  return getType(payload) === \"String\";\n}\n\nfunction isFullString(payload) {\n  return isString(payload) && payload !== \"\";\n}\n\nfunction isFunction(payload) {\n  return typeof payload === \"function\";\n}\n\nfunction isType(payload, type) {\n  if (!(type instanceof Function)) {\n    throw new TypeError(\"Type must be a function\");\n  }\n  if (!Object.prototype.hasOwnProperty.call(type, \"prototype\")) {\n    throw new TypeError(\"Type is not a class\");\n  }\n  const name = type.name;\n  return getType(payload) === name || Boolean(payload && payload.constructor === type);\n}\n\nfunction isInstanceOf(value, classOrClassName) {\n  if (typeof classOrClassName === \"function\") {\n    for (let p = value; p; p = Object.getPrototypeOf(p)) {\n      if (isType(p, classOrClassName)) {\n        return true;\n      }\n    }\n    return false;\n  } else {\n    for (let p = value; p; p = Object.getPrototypeOf(p)) {\n      if (getType(p) === classOrClassName) {\n        return true;\n      }\n    }\n    return false;\n  }\n}\n\nfunction isMap(payload) {\n  return getType(payload) === \"Map\";\n}\n\nfunction isNaNValue(payload) {\n  return getType(payload) === \"Number\" && isNaN(payload);\n}\n\nfunction isNumber(payload) {\n  return getType(payload) === \"Number\" && !isNaN(payload);\n}\n\nfunction isNegativeNumber(payload) {\n  return isNumber(payload) && payload < 0;\n}\n\nfunction isNull(payload) {\n  return getType(payload) === \"Null\";\n}\n\nfunction isOneOf(a, b, c, d, e) {\n  return (value) => a(value) || b(value) || !!c && c(value) || !!d && d(value) || !!e && e(value);\n}\n\nfunction isUndefined(payload) {\n  return getType(payload) === \"Undefined\";\n}\n\nconst isNullOrUndefined = isOneOf(isNull, isUndefined);\n\nfunction isObject(payload) {\n  return isPlainObject(payload);\n}\n\nfunction isObjectLike(payload) {\n  return isAnyObject(payload);\n}\n\nfunction isPositiveNumber(payload) {\n  return isNumber(payload) && payload > 0;\n}\n\nfunction isSymbol(payload) {\n  return getType(payload) === \"Symbol\";\n}\n\nfunction isPrimitive(payload) {\n  return isBoolean(payload) || isNull(payload) || isUndefined(payload) || isNumber(payload) || isString(payload) || isSymbol(payload);\n}\n\nfunction isPromise(payload) {\n  return getType(payload) === \"Promise\";\n}\n\nfunction isRegExp(payload) {\n  return getType(payload) === \"RegExp\";\n}\n\nfunction isSet(payload) {\n  return getType(payload) === \"Set\";\n}\n\nfunction isWeakMap(payload) {\n  return getType(payload) === \"WeakMap\";\n}\n\nfunction isWeakSet(payload) {\n  return getType(payload) === \"WeakSet\";\n}\n\nexport { getType, isAnyObject, isArray, isBlob, isBoolean, isDate, isEmptyArray, isEmptyObject, isEmptyString, isError, isFile, isFullArray, isFullObject, isFullString, isFunction, isInstanceOf, isMap, isNaNValue, isNegativeNumber, isNull, isNullOrUndefined, isNumber, isObject, isObjectLike, isOneOf, isPlainObject, isPositiveNumber, isPrimitive, isPromise, isRegExp, isSet, isString, isSymbol, isType, isUndefined, isWeakMap, isWeakSet };\n"], "mappings": "AAAA,SAASA,OAAOA,CAACC,OAAO,EAAE;EACxB,OAAOC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACJ,OAAO,CAAC,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC7D;AAEA,SAASC,WAAWA,CAACN,OAAO,EAAE;EAC5B,OAAOD,OAAO,CAACC,OAAO,CAAC,KAAK,QAAQ;AACtC;AAEA,SAASO,OAAOA,CAACP,OAAO,EAAE;EACxB,OAAOD,OAAO,CAACC,OAAO,CAAC,KAAK,OAAO;AACrC;AAEA,SAASQ,MAAMA,CAACR,OAAO,EAAE;EACvB,OAAOD,OAAO,CAACC,OAAO,CAAC,KAAK,MAAM;AACpC;AAEA,SAASS,SAASA,CAACT,OAAO,EAAE;EAC1B,OAAOD,OAAO,CAACC,OAAO,CAAC,KAAK,SAAS;AACvC;AAEA,SAASU,MAAMA,CAACV,OAAO,EAAE;EACvB,OAAOD,OAAO,CAACC,OAAO,CAAC,KAAK,MAAM,IAAI,CAACW,KAAK,CAACX,OAAO,CAAC;AACvD;AAEA,SAASY,YAAYA,CAACZ,OAAO,EAAE;EAC7B,OAAOO,OAAO,CAACP,OAAO,CAAC,IAAIA,OAAO,CAACa,MAAM,KAAK,CAAC;AACjD;AAEA,SAASC,aAAaA,CAACd,OAAO,EAAE;EAC9B,IAAID,OAAO,CAACC,OAAO,CAAC,KAAK,QAAQ,EAC/B,OAAO,KAAK;EACd,MAAME,SAAS,GAAGD,MAAM,CAACc,cAAc,CAACf,OAAO,CAAC;EAChD,OAAO,CAAC,CAACE,SAAS,IAAIA,SAAS,CAACc,WAAW,KAAKf,MAAM,IAAIC,SAAS,KAAKD,MAAM,CAACC,SAAS;AAC1F;AAEA,SAASe,aAAaA,CAACjB,OAAO,EAAE;EAC9B,OAAOc,aAAa,CAACd,OAAO,CAAC,IAAIC,MAAM,CAACiB,IAAI,CAAClB,OAAO,CAAC,CAACa,MAAM,KAAK,CAAC;AACpE;AAEA,SAASM,aAAaA,CAACnB,OAAO,EAAE;EAC9B,OAAOA,OAAO,KAAK,EAAE;AACvB;AAEA,SAASoB,OAAOA,CAACpB,OAAO,EAAE;EACxB,OAAOD,OAAO,CAACC,OAAO,CAAC,KAAK,OAAO,IAAIA,OAAO,YAAYqB,KAAK;AACjE;AAEA,SAASC,MAAMA,CAACtB,OAAO,EAAE;EACvB,OAAOD,OAAO,CAACC,OAAO,CAAC,KAAK,MAAM;AACpC;AAEA,SAASuB,WAAWA,CAACvB,OAAO,EAAE;EAC5B,OAAOO,OAAO,CAACP,OAAO,CAAC,IAAIA,OAAO,CAACa,MAAM,GAAG,CAAC;AAC/C;AAEA,SAASW,YAAYA,CAACxB,OAAO,EAAE;EAC7B,OAAOc,aAAa,CAACd,OAAO,CAAC,IAAIC,MAAM,CAACiB,IAAI,CAAClB,OAAO,CAAC,CAACa,MAAM,GAAG,CAAC;AAClE;AAEA,SAASY,QAAQA,CAACzB,OAAO,EAAE;EACzB,OAAOD,OAAO,CAACC,OAAO,CAAC,KAAK,QAAQ;AACtC;AAEA,SAAS0B,YAAYA,CAAC1B,OAAO,EAAE;EAC7B,OAAOyB,QAAQ,CAACzB,OAAO,CAAC,IAAIA,OAAO,KAAK,EAAE;AAC5C;AAEA,SAAS2B,UAAUA,CAAC3B,OAAO,EAAE;EAC3B,OAAO,OAAOA,OAAO,KAAK,UAAU;AACtC;AAEA,SAAS4B,MAAMA,CAAC5B,OAAO,EAAE6B,IAAI,EAAE;EAC7B,IAAI,EAAEA,IAAI,YAAYC,QAAQ,CAAC,EAAE;IAC/B,MAAM,IAAIC,SAAS,CAAC,yBAAyB,CAAC;EAChD;EACA,IAAI,CAAC9B,MAAM,CAACC,SAAS,CAAC8B,cAAc,CAAC5B,IAAI,CAACyB,IAAI,EAAE,WAAW,CAAC,EAAE;IAC5D,MAAM,IAAIE,SAAS,CAAC,qBAAqB,CAAC;EAC5C;EACA,MAAME,IAAI,GAAGJ,IAAI,CAACI,IAAI;EACtB,OAAOlC,OAAO,CAACC,OAAO,CAAC,KAAKiC,IAAI,IAAIC,OAAO,CAAClC,OAAO,IAAIA,OAAO,CAACgB,WAAW,KAAKa,IAAI,CAAC;AACtF;AAEA,SAASM,YAAYA,CAACC,KAAK,EAAEC,gBAAgB,EAAE;EAC7C,IAAI,OAAOA,gBAAgB,KAAK,UAAU,EAAE;IAC1C,KAAK,IAAIC,CAAC,GAAGF,KAAK,EAAEE,CAAC,EAAEA,CAAC,GAAGrC,MAAM,CAACc,cAAc,CAACuB,CAAC,CAAC,EAAE;MACnD,IAAIV,MAAM,CAACU,CAAC,EAAED,gBAAgB,CAAC,EAAE;QAC/B,OAAO,IAAI;MACb;IACF;IACA,OAAO,KAAK;EACd,CAAC,MAAM;IACL,KAAK,IAAIC,CAAC,GAAGF,KAAK,EAAEE,CAAC,EAAEA,CAAC,GAAGrC,MAAM,CAACc,cAAc,CAACuB,CAAC,CAAC,EAAE;MACnD,IAAIvC,OAAO,CAACuC,CAAC,CAAC,KAAKD,gBAAgB,EAAE;QACnC,OAAO,IAAI;MACb;IACF;IACA,OAAO,KAAK;EACd;AACF;AAEA,SAASE,KAAKA,CAACvC,OAAO,EAAE;EACtB,OAAOD,OAAO,CAACC,OAAO,CAAC,KAAK,KAAK;AACnC;AAEA,SAASwC,UAAUA,CAACxC,OAAO,EAAE;EAC3B,OAAOD,OAAO,CAACC,OAAO,CAAC,KAAK,QAAQ,IAAIW,KAAK,CAACX,OAAO,CAAC;AACxD;AAEA,SAASyC,QAAQA,CAACzC,OAAO,EAAE;EACzB,OAAOD,OAAO,CAACC,OAAO,CAAC,KAAK,QAAQ,IAAI,CAACW,KAAK,CAACX,OAAO,CAAC;AACzD;AAEA,SAAS0C,gBAAgBA,CAAC1C,OAAO,EAAE;EACjC,OAAOyC,QAAQ,CAACzC,OAAO,CAAC,IAAIA,OAAO,GAAG,CAAC;AACzC;AAEA,SAAS2C,MAAMA,CAAC3C,OAAO,EAAE;EACvB,OAAOD,OAAO,CAACC,OAAO,CAAC,KAAK,MAAM;AACpC;AAEA,SAAS4C,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAC9B,OAAQb,KAAK,IAAKS,CAAC,CAACT,KAAK,CAAC,IAAIU,CAAC,CAACV,KAAK,CAAC,IAAI,CAAC,CAACW,CAAC,IAAIA,CAAC,CAACX,KAAK,CAAC,IAAI,CAAC,CAACY,CAAC,IAAIA,CAAC,CAACZ,KAAK,CAAC,IAAI,CAAC,CAACa,CAAC,IAAIA,CAAC,CAACb,KAAK,CAAC;AACjG;AAEA,SAASc,WAAWA,CAAClD,OAAO,EAAE;EAC5B,OAAOD,OAAO,CAACC,OAAO,CAAC,KAAK,WAAW;AACzC;AAEA,MAAMmD,iBAAiB,GAAGP,OAAO,CAACD,MAAM,EAAEO,WAAW,CAAC;AAEtD,SAASE,QAAQA,CAACpD,OAAO,EAAE;EACzB,OAAOc,aAAa,CAACd,OAAO,CAAC;AAC/B;AAEA,SAASqD,YAAYA,CAACrD,OAAO,EAAE;EAC7B,OAAOM,WAAW,CAACN,OAAO,CAAC;AAC7B;AAEA,SAASsD,gBAAgBA,CAACtD,OAAO,EAAE;EACjC,OAAOyC,QAAQ,CAACzC,OAAO,CAAC,IAAIA,OAAO,GAAG,CAAC;AACzC;AAEA,SAASuD,QAAQA,CAACvD,OAAO,EAAE;EACzB,OAAOD,OAAO,CAACC,OAAO,CAAC,KAAK,QAAQ;AACtC;AAEA,SAASwD,WAAWA,CAACxD,OAAO,EAAE;EAC5B,OAAOS,SAAS,CAACT,OAAO,CAAC,IAAI2C,MAAM,CAAC3C,OAAO,CAAC,IAAIkD,WAAW,CAAClD,OAAO,CAAC,IAAIyC,QAAQ,CAACzC,OAAO,CAAC,IAAIyB,QAAQ,CAACzB,OAAO,CAAC,IAAIuD,QAAQ,CAACvD,OAAO,CAAC;AACrI;AAEA,SAASyD,SAASA,CAACzD,OAAO,EAAE;EAC1B,OAAOD,OAAO,CAACC,OAAO,CAAC,KAAK,SAAS;AACvC;AAEA,SAAS0D,QAAQA,CAAC1D,OAAO,EAAE;EACzB,OAAOD,OAAO,CAACC,OAAO,CAAC,KAAK,QAAQ;AACtC;AAEA,SAAS2D,KAAKA,CAAC3D,OAAO,EAAE;EACtB,OAAOD,OAAO,CAACC,OAAO,CAAC,KAAK,KAAK;AACnC;AAEA,SAAS4D,SAASA,CAAC5D,OAAO,EAAE;EAC1B,OAAOD,OAAO,CAACC,OAAO,CAAC,KAAK,SAAS;AACvC;AAEA,SAAS6D,SAASA,CAAC7D,OAAO,EAAE;EAC1B,OAAOD,OAAO,CAACC,OAAO,CAAC,KAAK,SAAS;AACvC;AAEA,SAASD,OAAO,EAAEO,WAAW,EAAEC,OAAO,EAAEC,MAAM,EAAEC,SAAS,EAAEC,MAAM,EAAEE,YAAY,EAAEK,aAAa,EAAEE,aAAa,EAAEC,OAAO,EAAEE,MAAM,EAAEC,WAAW,EAAEC,YAAY,EAAEE,YAAY,EAAEC,UAAU,EAAEQ,YAAY,EAAEI,KAAK,EAAEC,UAAU,EAAEE,gBAAgB,EAAEC,MAAM,EAAEQ,iBAAiB,EAAEV,QAAQ,EAAEW,QAAQ,EAAEC,YAAY,EAAET,OAAO,EAAE9B,aAAa,EAAEwC,gBAAgB,EAAEE,WAAW,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,KAAK,EAAElC,QAAQ,EAAE8B,QAAQ,EAAE3B,MAAM,EAAEsB,WAAW,EAAEU,SAAS,EAAEC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}