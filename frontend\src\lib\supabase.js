import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.REACT_APP_SUPABASE_URL;
const supabaseAnonKey = process.env.REACT_APP_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    flowType: 'pkce'
  },
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  }
});

// Helper functions for common operations
export const getCurrentUser = async () => {
  const { data: { user }, error } = await supabase.auth.getUser();
  if (error) throw error;
  return user;
};

export const getCurrentSession = async () => {
  const { data: { session }, error } = await supabase.auth.getSession();
  if (error) throw error;
  return session;
};

export const signOut = async () => {
  const { error } = await supabase.auth.signOut();
  if (error) throw error;
};

// Database helpers
export const getProfile = async (userId) => {
  const { data, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', userId)
    .single();
  
  if (error) throw error;
  return data;
};

export const updateProfile = async (userId, updates) => {
  const { data, error } = await supabase
    .from('profiles')
    .update(updates)
    .eq('id', userId)
    .select()
    .single();
  
  if (error) throw error;
  return data;
};

// File operations
export const uploadFile = async (file, filePath) => {
  const { data, error } = await supabase.storage
    .from('educational-files')
    .upload(filePath, file, {
      cacheControl: '3600',
      upsert: false
    });
  
  if (error) throw error;
  return data;
};

export const getFileUrl = (filePath) => {
  const { data } = supabase.storage
    .from('educational-files')
    .getPublicUrl(filePath);
  
  return data.publicUrl;
};

export const downloadFile = async (filePath) => {
  const { data, error } = await supabase.storage
    .from('educational-files')
    .download(filePath);
  
  if (error) throw error;
  return data;
};

export const deleteFile = async (filePath) => {
  const { error } = await supabase.storage
    .from('educational-files')
    .remove([filePath]);
  
  if (error) throw error;
};

// Real-time subscriptions
export const subscribeToFiles = (callback) => {
  return supabase
    .channel('files-changes')
    .on('postgres_changes', 
      { 
        event: '*', 
        schema: 'public', 
        table: 'files' 
      }, 
      callback
    )
    .subscribe();
};

export const subscribeToRatings = (callback) => {
  return supabase
    .channel('ratings-changes')
    .on('postgres_changes', 
      { 
        event: '*', 
        schema: 'public', 
        table: 'ratings' 
      }, 
      callback
    )
    .subscribe();
};

export const subscribeToDownloads = (callback) => {
  return supabase
    .channel('downloads-changes')
    .on('postgres_changes', 
      { 
        event: '*', 
        schema: 'public', 
        table: 'downloads' 
      }, 
      callback
    )
    .subscribe();
};

// Utility function to handle Supabase errors
export const handleSupabaseError = (error) => {
  console.error('Supabase error:', error);
  
  if (error.message) {
    return error.message;
  }
  
  if (error.error_description) {
    return error.error_description;
  }
  
  return 'حدث خطأ غير متوقع';
};

export default supabase;
