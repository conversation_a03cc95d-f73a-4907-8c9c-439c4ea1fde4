{"version": 3, "file": "index.prod.mjs", "sources": ["../../src/useLocalStorage.ts", "../../src/theme.tsx", "../../src/useMediaQuery.ts", "../../src/utils.ts", "../../src/styledComponents.ts", "../../src/screenreader.tsx", "../../src/Explorer.tsx", "../../src/Logo.tsx", "../../src/devtools.tsx", "../../src/index.ts"], "sourcesContent": ["import * as React from 'react'\n\nconst getItem = (key: string): unknown => {\n  try {\n    const itemValue = localStorage.getItem(key)\n    if (typeof itemValue === 'string') {\n      return JSON.parse(itemValue)\n    }\n    return undefined\n  } catch {\n    return undefined\n  }\n}\n\nexport default function useLocalStorage<T>(\n  key: string,\n  defaultValue: T | undefined,\n): [T | undefined, (newVal: T | ((prevVal: T) => T)) => void] {\n  const [value, setValue] = React.useState<T>()\n\n  React.useEffect(() => {\n    const initialValue = getItem(key) as T | undefined\n\n    if (typeof initialValue === 'undefined' || initialValue === null) {\n      setValue(\n        typeof defaultValue === 'function' ? defaultValue() : defaultValue,\n      )\n    } else {\n      setValue(initialValue)\n    }\n  }, [defaultValue, key])\n\n  const setter = React.useCallback(\n    (updater: any) => {\n      setValue((old) => {\n        let newVal = updater\n\n        if (typeof updater == 'function') {\n          newVal = updater(old)\n        }\n        try {\n          localStorage.setItem(key, JSON.stringify(newVal))\n        } catch {}\n\n        return newVal\n      })\n    },\n    [key],\n  )\n\n  return [value, setter]\n}\n", "'use client'\nimport * as React from 'react'\n\nexport const defaultTheme = {\n  background: '#0b1521',\n  backgroundAlt: '#132337',\n  foreground: 'white',\n  gray: '#3f4e60',\n  grayAlt: '#222e3e',\n  inputBackgroundColor: '#fff',\n  inputTextColor: '#000',\n  success: '#00ab52',\n  danger: '#ff0085',\n  active: '#006bff',\n  paused: '#8c49eb',\n  warning: '#ffb200',\n} as const\n\nexport type Theme = typeof defaultTheme\ninterface ProviderProps {\n  theme: Theme\n  children?: React.ReactNode\n}\n\nconst ThemeContext = React.createContext(defaultTheme)\n\nexport function ThemeProvider({ theme, ...rest }: ProviderProps) {\n  return <ThemeContext.Provider value={theme} {...rest} />\n}\n\nexport function useTheme() {\n  return React.useContext(ThemeContext)\n}\n", "import * as React from 'react'\n\nexport default function useMediaQuery(query: string): boolean | undefined {\n  // Keep track of the preference in state, start with the current match\n  const [isMatch, setIsMatch] = React.useState(() => {\n    if (typeof window !== 'undefined') {\n      return window.matchMedia(query).matches\n    }\n    return\n  })\n\n  // Watch for changes\n  React.useEffect(() => {\n    if (typeof window !== 'undefined') {\n      // Create a matcher\n      const matcher = window.matchMedia(query)\n\n      // Create our handler\n      const onChange = ({ matches }: { matches: boolean }) =>\n        setIsMatch(matches)\n\n      // Listen for changes\n      matcher.addListener(onChange)\n\n      return () => {\n        // Stop listening for changes\n        matcher.removeListener(onChange)\n      }\n    }\n    return\n  }, [isMatch, query, setIsMatch])\n\n  return isMatch\n}\n", "import * as React from 'react'\nimport SuperJSON from 'superjson'\n\nimport { useTheme } from './theme'\nimport useMediaQuery from './useMediaQuery'\nimport type { Theme } from './theme'\nimport type { Query } from '@tanstack/react-query'\n\ntype StyledComponent<T> = T extends 'button'\n  ? React.DetailedHTMLProps<\n      React.ButtonHTMLAttributes<HTMLButtonElement>,\n      HTMLButtonElement\n    >\n  : T extends 'input'\n  ? React.DetailedHTMLProps<\n      React.InputHTMLAttributes<HTMLInputElement>,\n      HTMLInputElement\n    >\n  : T extends 'select'\n  ? React.DetailedHTMLProps<\n      React.SelectHTMLAttributes<HTMLSelectElement>,\n      HTMLSelectElement\n    >\n  : T extends keyof HTMLElementTagNameMap\n  ? React.HTMLAttributes<HTMLElementTagNameMap[T]>\n  : never\n\nexport function getQueryStatusColor({\n  queryState,\n  observerCount,\n  isStale,\n  theme,\n}: {\n  queryState: Query['state']\n  observerCount: number\n  isStale: boolean\n  theme: Theme\n}) {\n  return queryState.fetchStatus === 'fetching'\n    ? theme.active\n    : !observerCount\n    ? theme.gray\n    : queryState.fetchStatus === 'paused'\n    ? theme.paused\n    : isStale\n    ? theme.warning\n    : theme.success\n}\n\nexport function getQueryStatusLabel(query: Query) {\n  return query.state.fetchStatus === 'fetching'\n    ? 'fetching'\n    : !query.getObserversCount()\n    ? 'inactive'\n    : query.state.fetchStatus === 'paused'\n    ? 'paused'\n    : query.isStale()\n    ? 'stale'\n    : 'fresh'\n}\n\ntype Styles =\n  | React.CSSProperties\n  | ((props: Record<string, any>, theme: Theme) => React.CSSProperties)\n\nexport function styled<T extends keyof HTMLElementTagNameMap>(\n  type: T,\n  newStyles: Styles,\n  queries: Record<string, Styles> = {},\n) {\n  return React.forwardRef<HTMLElementTagNameMap[T], StyledComponent<T>>(\n    ({ style, ...rest }, ref) => {\n      const theme = useTheme()\n\n      const mediaStyles = Object.entries(queries).reduce(\n        (current, [key, value]) => {\n          // eslint-disable-next-line react-hooks/rules-of-hooks\n          return useMediaQuery(key)\n            ? {\n                ...current,\n                ...(typeof value === 'function' ? value(rest, theme) : value),\n              }\n            : current\n        },\n        {},\n      )\n\n      return React.createElement(type, {\n        ...rest,\n        style: {\n          ...(typeof newStyles === 'function'\n            ? newStyles(rest, theme)\n            : newStyles),\n          ...style,\n          ...mediaStyles,\n        },\n        ref,\n      })\n    },\n  )\n}\n\nexport function useIsMounted() {\n  const mountedRef = React.useRef(false)\n  const isMounted = React.useCallback(() => mountedRef.current, [])\n\n  React.useEffect(() => {\n    mountedRef.current = true\n    return () => {\n      mountedRef.current = false\n    }\n  }, [])\n\n  return isMounted\n}\n\n/**\n * Displays a string regardless the type of the data\n * @param {unknown} value Value to be stringified\n * @param {boolean} beautify Formats json to multiline\n */\nexport const displayValue = (value: unknown, beautify: boolean = false) => {\n  const { json } = SuperJSON.serialize(value)\n\n  return JSON.stringify(json, null, beautify ? 2 : undefined)\n}\n\n// Sorting functions\ntype SortFn = (a: Query, b: Query) => number\n\nconst getStatusRank = (q: Query) =>\n  q.state.fetchStatus !== 'idle'\n    ? 0\n    : !q.getObserversCount()\n    ? 3\n    : q.isStale()\n    ? 2\n    : 1\n\nconst queryHashSort: SortFn = (a, b) => a.queryHash.localeCompare(b.queryHash)\n\nconst dateSort: SortFn = (a, b) =>\n  a.state.dataUpdatedAt < b.state.dataUpdatedAt ? 1 : -1\n\nconst statusAndDateSort: SortFn = (a, b) => {\n  if (getStatusRank(a) === getStatusRank(b)) {\n    return dateSort(a, b)\n  }\n\n  return getStatusRank(a) > getStatusRank(b) ? 1 : -1\n}\n\nexport const sortFns: Record<string, SortFn> = {\n  'Status > Last Updated': statusAndDateSort,\n  'Query Hash': queryHashSort,\n  'Last Updated': dateSort,\n}\n\nexport const minPanelSize = 70\nexport const defaultPanelSize = 500\nexport const sides: Record<Side, Side> = {\n  top: 'bottom',\n  bottom: 'top',\n  left: 'right',\n  right: 'left',\n}\n\nexport type Corner = 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'\nexport type Side = 'left' | 'right' | 'top' | 'bottom'\n/**\n * Check if the given side is vertical (left/right)\n */\nexport function isVerticalSide(side: Side) {\n  return ['left', 'right'].includes(side)\n}\n/**\n * Get the opposite side, eg 'left' => 'right'. 'top' => 'bottom', etc\n */\nexport function getOppositeSide(side: Side): Side {\n  return sides[side]\n}\n/**\n * Given as css prop it will return a sided css prop based on a given side\n * Example given `border` and `right` it return `borderRight`\n */\nexport function getSidedProp<T extends string>(prop: T, side: Side) {\n  return `${prop}${\n    side.charAt(0).toUpperCase() + side.slice(1)\n  }` as `${T}${Capitalize<Side>}`\n}\n\nexport interface SidePanelStyleOptions {\n  /**\n   * Position of the panel\n   * Defaults to 'bottom'\n   */\n  position?: Side\n  /**\n   * Staring height for the panel, it is set if the position is horizontal eg 'top' or 'bottom'\n   * Defaults to 500\n   */\n  height?: number\n  /**\n   * Staring width for the panel, it is set if the position is vertical eg 'left' or 'right'\n   * Defaults to 500\n   */\n  width?: number\n  /**\n   * RQ devtools theme\n   */\n  devtoolsTheme: Theme\n  /**\n   * Sets the correct transition and visibility styles\n   */\n  isOpen?: boolean\n  /**\n   * If the panel is resizing set to true to apply the correct transition styles\n   */\n  isResizing?: boolean\n  /**\n   * Extra panel style passed by the user\n   */\n  panelStyle?: React.CSSProperties\n}\n\nexport function getSidePanelStyle({\n  position = 'bottom',\n  height,\n  width,\n  devtoolsTheme,\n  isOpen,\n  isResizing,\n  panelStyle,\n}: SidePanelStyleOptions): React.CSSProperties {\n  const oppositeSide = getOppositeSide(position)\n  const borderSide = getSidedProp('border', oppositeSide)\n  const isVertical = isVerticalSide(position)\n\n  return {\n    ...panelStyle,\n    direction: 'ltr',\n    position: 'fixed',\n    [position]: 0,\n    [borderSide]: `1px solid ${devtoolsTheme.gray}`,\n    transformOrigin: oppositeSide,\n    boxShadow: '0 0 20px rgba(0,0,0,.3)',\n    zIndex: 99999,\n    // visibility will be toggled after transitions, but set initial state here\n    visibility: isOpen ? 'visible' : 'hidden',\n    ...(isResizing\n      ? {\n          transition: `none`,\n        }\n      : { transition: `all .2s ease` }),\n    ...(isOpen\n      ? {\n          opacity: 1,\n          pointerEvents: 'all',\n          transform: isVertical\n            ? `translateX(0) scale(1)`\n            : `translateY(0) scale(1)`,\n        }\n      : {\n          opacity: 0,\n          pointerEvents: 'none',\n          transform: isVertical\n            ? `translateX(15px) scale(1.02)`\n            : `translateY(15px) scale(1.02)`,\n        }),\n    ...(isVertical\n      ? {\n          top: 0,\n          height: '100vh',\n          maxWidth: '90%',\n          width:\n            typeof width === 'number' && width >= minPanelSize\n              ? width\n              : defaultPanelSize,\n        }\n      : {\n          left: 0,\n          width: '100%',\n          maxHeight: '90%',\n          height:\n            typeof height === 'number' && height >= minPanelSize\n              ? height\n              : defaultPanelSize,\n        }),\n  }\n}\n\n/**\n * Get resize handle style based on a given side\n */\nexport function getResizeHandleStyle(\n  position: Side = 'bottom',\n): React.CSSProperties {\n  const isVertical = isVerticalSide(position)\n  const oppositeSide = getOppositeSide(position)\n  const marginSide = getSidedProp('margin', oppositeSide)\n\n  return {\n    position: 'absolute',\n    cursor: isVertical ? 'col-resize' : 'row-resize',\n    zIndex: 100000,\n    [oppositeSide]: 0,\n    [marginSide]: `-4px`,\n    ...(isVertical\n      ? {\n          top: 0,\n          height: '100%',\n          width: '4px',\n        }\n      : {\n          width: '100%',\n          height: '4px',\n        }),\n  }\n}\n", "import { styled } from './utils'\n\nexport const Panel = styled(\n  'div',\n  (_props, theme) => ({\n    fontSize: 'clamp(12px, 1.5vw, 14px)',\n    fontFamily: `sans-serif`,\n    display: 'flex',\n    backgroundColor: theme.background,\n    color: theme.foreground,\n  }),\n  {\n    '(max-width: 700px)': {\n      flexDirection: 'column',\n    },\n    '(max-width: 600px)': {\n      fontSize: '.9em',\n      // flexDirection: 'column',\n    },\n  },\n)\n\nexport const ActiveQueryPanel = styled(\n  'div',\n  () => ({\n    flex: '1 1 500px',\n    display: 'flex',\n    flexDirection: 'column',\n    overflow: 'auto',\n    height: '100%',\n  }),\n  {\n    '(max-width: 700px)': (_props, theme) => ({\n      borderTop: `2px solid ${theme.gray}`,\n    }),\n  },\n)\n\nexport const Button = styled('button', (props, theme) => ({\n  appearance: 'none',\n  fontSize: '.9em',\n  fontWeight: 'bold',\n  background: theme.gray,\n  border: '0',\n  borderRadius: '.3em',\n  color: 'white',\n  padding: '.5em',\n  opacity: props.disabled ? '.5' : undefined,\n  cursor: 'pointer',\n}))\n\nexport const QueryKeys = styled('span', {\n  display: 'flex',\n  flexWrap: 'wrap',\n  gap: '0.5em',\n  fontSize: '0.9em',\n})\n\nexport const QueryKey = styled('span', {\n  display: 'inline-flex',\n  alignItems: 'center',\n  padding: '.2em .4em',\n  fontWeight: 'bold',\n  textShadow: '0 0 10px black',\n  borderRadius: '.2em',\n})\n\nexport const Code = styled('code', {\n  fontSize: '.9em',\n  color: 'inherit',\n  background: 'inherit',\n})\n\nexport const Input = styled('input', (_props, theme) => ({\n  backgroundColor: theme.inputBackgroundColor,\n  border: 0,\n  borderRadius: '.2em',\n  color: theme.inputTextColor,\n  fontSize: '.9em',\n  lineHeight: `1.3`,\n  padding: '.3em .4em',\n}))\n\nexport const Select = styled(\n  'select',\n  (_props, theme) => ({\n    display: `inline-block`,\n    fontSize: `.9em`,\n    fontFamily: `sans-serif`,\n    fontWeight: 'normal',\n    lineHeight: `1.3`,\n    padding: `.3em 1.5em .3em .5em`,\n    height: 'auto',\n    border: 0,\n    borderRadius: `.2em`,\n    appearance: `none`,\n    WebkitAppearance: 'none',\n    backgroundColor: theme.inputBackgroundColor,\n    backgroundImage: `url(\"data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='100' height='100' fill='%23444444'><polygon points='0,25 100,25 50,75'/></svg>\")`,\n    backgroundRepeat: `no-repeat`,\n    backgroundPosition: `right .55em center`,\n    backgroundSize: `.65em auto, 100%`,\n    color: theme.inputTextColor,\n  }),\n  {\n    '(max-width: 500px)': {\n      display: 'none',\n    },\n  },\n)\n", "import * as React from 'react'\n\nexport default function ScreenReader({ text }: { text: string }) {\n  return (\n    <span\n      style={{\n        position: 'absolute',\n        width: '0.1px',\n        height: '0.1px',\n        overflow: 'hidden',\n      }}\n    >\n      {text}\n    </span>\n  )\n}\n", "'use client'\nimport * as React from 'react'\n\nimport superjson from 'superjson'\nimport { displayValue, styled } from './utils'\n\nexport const Entry = styled('div', {\n  fontFamily: 'Menlo, monospace',\n  fontSize: '1em',\n  lineHeight: '1.7',\n  outline: 'none',\n  wordBreak: 'break-word',\n})\n\nexport const Label = styled('span', {\n  color: 'white',\n})\n\nexport const LabelButton = styled('button', {\n  cursor: 'pointer',\n  color: 'white',\n})\n\nexport const ExpandButton = styled('button', {\n  cursor: 'pointer',\n  color: 'inherit',\n  font: 'inherit',\n  outline: 'inherit',\n  background: 'transparent',\n  border: 'none',\n  padding: 0,\n})\n\ntype CopyState = 'NoCopy' | 'SuccessCopy' | 'ErrorCopy'\n\nexport const CopyButton = ({ value }: { value: unknown }) => {\n  const [copyState, setCopyState] = React.useState<CopyState>('NoCopy')\n\n  return (\n    <button\n      onClick={\n        copyState === 'NoCopy'\n          ? () => {\n              navigator.clipboard.writeText(superjson.stringify(value)).then(\n                () => {\n                  setCopyState('SuccessCopy')\n                  setTimeout(() => {\n                    setCopyState('NoCopy')\n                  }, 1500)\n                },\n                (err) => {\n                  console.error('Failed to copy: ', err)\n                  setCopyState('ErrorCopy')\n                  setTimeout(() => {\n                    setCopyState('NoCopy')\n                  }, 1500)\n                },\n              )\n            }\n          : undefined\n      }\n      style={{\n        cursor: 'pointer',\n        color: 'inherit',\n        font: 'inherit',\n        outline: 'inherit',\n        background: 'transparent',\n        border: 'none',\n        padding: 0,\n      }}\n    >\n      {copyState === 'NoCopy' ? (\n        <Copier />\n      ) : copyState === 'SuccessCopy' ? (\n        <CopiedCopier />\n      ) : (\n        <ErrorCopier />\n      )}\n    </button>\n  )\n}\n\nexport const Value = styled('span', (_props, theme) => ({\n  color: theme.danger,\n}))\n\nexport const SubEntries = styled('div', {\n  marginLeft: '.1em',\n  paddingLeft: '1em',\n  borderLeft: '2px solid rgba(0,0,0,.15)',\n})\n\nexport const Info = styled('span', {\n  color: 'grey',\n  fontSize: '.7em',\n})\n\ntype ExpanderProps = {\n  expanded: boolean\n  style?: React.CSSProperties\n}\n\nexport const Expander = ({ expanded, style = {} }: ExpanderProps) => (\n  <span\n    style={{\n      display: 'inline-block',\n      transition: 'all .1s ease',\n      transform: `rotate(${expanded ? 90 : 0}deg) ${style.transform || ''}`,\n      ...style,\n    }}\n  >\n    ▶\n  </span>\n)\n\nconst Copier = () => (\n  <span\n    aria-label=\"Copy object to clipboard\"\n    title=\"Copy object to clipboard\"\n    style={{\n      paddingLeft: '1em',\n    }}\n  >\n    <svg height=\"12\" viewBox=\"0 0 16 12\" width=\"10\">\n      <path\n        fill=\"currentColor\"\n        d=\"M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 010 1.5h-1.5a.25.25 0 00-.25.25v7.5c0 .*************.25h7.5a.25.25 0 00.25-.25v-1.5a.75.75 0 011.5 0v1.5A1.75 1.75 0 019.25 16h-7.5A1.75 1.75 0 010 14.25v-7.5z\"\n      ></path>\n      <path\n        fill=\"currentColor\"\n        d=\"M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0114.25 11h-7.5A1.75 1.75 0 015 9.25v-7.5zm1.75-.25a.25.25 0 00-.25.25v7.5c0 .*************.25h7.5a.25.25 0 00.25-.25v-7.5a.25.25 0 00-.25-.25h-7.5z\"\n      ></path>\n    </svg>\n  </span>\n)\n\nconst ErrorCopier = () => (\n  <span\n    aria-label=\"Failed copying to clipboard\"\n    title=\"Failed copying to clipboard\"\n    style={{\n      paddingLeft: '1em',\n      display: 'flex',\n      alignItems: 'center',\n    }}\n  >\n    <svg height=\"12\" viewBox=\"0 0 16 12\" width=\"10\" display=\"block\">\n      <path\n        fill=\"red\"\n        d=\"M3.72 3.72a.75.75 0 011.06 0L8 6.94l3.22-3.22a.75.75 0 111.06 1.06L9.06 8l3.22 3.22a.75.75 0 11-1.06 1.06L8 9.06l-3.22 3.22a.75.75 0 01-1.06-1.06L6.94 8 3.72 4.78a.75.75 0 010-1.06z\"\n      ></path>\n    </svg>\n    <span\n      style={{\n        color: 'red',\n        fontSize: '12px',\n        paddingLeft: '4px',\n        position: 'relative',\n        top: '2px',\n      }}\n    >\n      See console\n    </span>\n  </span>\n)\n\nconst CopiedCopier = () => (\n  <span\n    aria-label=\"Object copied to clipboard\"\n    title=\"Object copied to clipboard\"\n    style={{\n      paddingLeft: '1em',\n      display: 'inline-block',\n      verticalAlign: 'middle',\n    }}\n  >\n    <svg height=\"16\" viewBox=\"0 0 16 16\" width=\"16\" display=\"block\">\n      <path\n        fill=\"green\"\n        d=\"M13.78 4.22a.75.75 0 010 1.06l-7.25 7.25a.75.75 0 01-1.06 0L2.22 9.28a.75.75 0 011.06-1.06L6 10.94l6.72-6.72a.75.75 0 011.06 0z\"\n      ></path>\n    </svg>\n  </span>\n)\n\ntype Entry = {\n  label: string\n}\n\ntype RendererProps = {\n  handleEntry: (entry: Entry) => JSX.Element\n  label?: string\n  value: unknown\n  subEntries: Entry[]\n  subEntryPages: Entry[][]\n  type: string\n  expanded: boolean\n  copyable: boolean\n  toggleExpanded: () => void\n  pageSize: number\n}\n\n/**\n * Chunk elements in the array by size\n *\n * when the array cannot be chunked evenly by size, the last chunk will be\n * filled with the remaining elements\n *\n * @example\n * chunkArray(['a','b', 'c', 'd', 'e'], 2) // returns [['a','b'], ['c', 'd'], ['e']]\n */\nexport function chunkArray<T>(array: T[], size: number): T[][] {\n  if (size < 1) return []\n  let i = 0\n  const result: T[][] = []\n  while (i < array.length) {\n    result.push(array.slice(i, i + size))\n    i = i + size\n  }\n  return result\n}\n\ntype Renderer = (props: RendererProps) => JSX.Element\n\nexport const DefaultRenderer: Renderer = ({\n  handleEntry,\n  label,\n  value,\n  subEntries = [],\n  subEntryPages = [],\n  type,\n  expanded = false,\n  copyable = false,\n  toggleExpanded,\n  pageSize,\n}) => {\n  const [expandedPages, setExpandedPages] = React.useState<number[]>([])\n\n  return (\n    <Entry key={label}>\n      {subEntryPages.length ? (\n        <>\n          <ExpandButton onClick={() => toggleExpanded()}>\n            <Expander expanded={expanded} /> {label}{' '}\n            <Info>\n              {String(type).toLowerCase() === 'iterable' ? '(Iterable) ' : ''}\n              {subEntries.length} {subEntries.length > 1 ? `items` : `item`}\n            </Info>\n          </ExpandButton>\n          {copyable ? <CopyButton value={value} /> : null}\n          {expanded ? (\n            subEntryPages.length === 1 ? (\n              <SubEntries>{subEntries.map(handleEntry)}</SubEntries>\n            ) : (\n              <SubEntries>\n                {subEntryPages.map((entries, index) => (\n                  <div key={index}>\n                    <Entry>\n                      <LabelButton\n                        onClick={() =>\n                          setExpandedPages((old) =>\n                            old.includes(index)\n                              ? old.filter((d) => d !== index)\n                              : [...old, index],\n                          )\n                        }\n                      >\n                        <Expander expanded={expanded} /> [{index * pageSize} ...{' '}\n                        {index * pageSize + pageSize - 1}]\n                      </LabelButton>\n                      {expandedPages.includes(index) ? (\n                        <SubEntries>{entries.map(handleEntry)}</SubEntries>\n                      ) : null}\n                    </Entry>\n                  </div>\n                ))}\n              </SubEntries>\n            )\n          ) : null}\n        </>\n      ) : (\n        <>\n          <Label>{label}:</Label> <Value>{displayValue(value)}</Value>\n        </>\n      )}\n    </Entry>\n  )\n}\n\ntype ExplorerProps = Partial<RendererProps> & {\n  renderer?: Renderer\n  defaultExpanded?: true | Record<string, boolean>\n  copyable?: boolean\n}\n\ntype Property = {\n  defaultExpanded?: boolean | Record<string, boolean>\n  label: string\n  value: unknown\n}\n\nfunction isIterable(x: any): x is Iterable<unknown> {\n  return Symbol.iterator in x\n}\n\nexport default function Explorer({\n  value,\n  defaultExpanded,\n  renderer = DefaultRenderer,\n  pageSize = 100,\n  copyable = false,\n  ...rest\n}: ExplorerProps) {\n  const [expanded, setExpanded] = React.useState(Boolean(defaultExpanded))\n  const toggleExpanded = React.useCallback(() => setExpanded((old) => !old), [])\n\n  let type: string = typeof value\n  let subEntries: Property[] = []\n\n  const makeProperty = (sub: { label: string; value: unknown }): Property => {\n    const subDefaultExpanded =\n      defaultExpanded === true\n        ? { [sub.label]: true }\n        : defaultExpanded?.[sub.label]\n    return {\n      ...sub,\n      defaultExpanded: subDefaultExpanded,\n    }\n  }\n\n  if (Array.isArray(value)) {\n    type = 'array'\n    subEntries = value.map((d, i) =>\n      makeProperty({\n        label: i.toString(),\n        value: d,\n      }),\n    )\n  } else if (\n    value !== null &&\n    typeof value === 'object' &&\n    isIterable(value) &&\n    typeof value[Symbol.iterator] === 'function'\n  ) {\n    type = 'Iterable'\n    subEntries = Array.from(value, (val, i) =>\n      makeProperty({\n        label: i.toString(),\n        value: val,\n      }),\n    )\n  } else if (typeof value === 'object' && value !== null) {\n    type = 'object'\n    subEntries = Object.entries(value).map(([key, val]) =>\n      makeProperty({\n        label: key,\n        value: val,\n      }),\n    )\n  }\n\n  const subEntryPages = chunkArray(subEntries, pageSize)\n\n  return renderer({\n    handleEntry: (entry) => (\n      <Explorer\n        key={entry.label}\n        value={value}\n        renderer={renderer}\n        copyable={copyable}\n        {...rest}\n        {...entry}\n      />\n    ),\n    type,\n    subEntries,\n    subEntryPages,\n    value,\n    expanded,\n    copyable,\n    toggleExpanded,\n    pageSize,\n    ...rest,\n  })\n}\n", "import * as React from 'react'\n\nexport default function Logo(props: any) {\n  return (\n    <svg\n      width=\"40px\"\n      height=\"40px\"\n      viewBox=\"0 0 190 190\"\n      version=\"1.1\"\n      {...props}\n    >\n      <g stroke=\"none\" strokeWidth=\"1\" fill=\"none\" fillRule=\"evenodd\">\n        <g transform=\"translate(-33.000000, 0.000000)\">\n          <path\n            d=\"M72.7239712,61.3436237 C69.631224,46.362877 68.9675112,34.8727722 70.9666331,26.5293551 C72.1555965,21.5671678 74.3293088,17.5190846 77.6346064,14.5984631 C81.1241394,11.5150478 85.5360327,10.0020122 90.493257,10.0020122 C98.6712013,10.0020122 107.26826,13.7273214 116.455725,20.8044264 C120.20312,23.6910458 124.092437,27.170411 128.131651,31.2444746 C128.45314,30.8310265 128.816542,30.4410453 129.22143,30.0806152 C140.64098,19.9149716 150.255245,13.5989272 158.478408,11.1636507 C163.367899,9.715636 167.958526,9.57768202 172.138936,10.983031 C176.551631,12.4664684 180.06766,15.5329489 182.548314,19.8281091 C186.642288,26.9166735 187.721918,36.2310983 186.195595,47.7320243 C185.573451,52.4199112 184.50985,57.5263831 183.007094,63.0593153 C183.574045,63.1277086 184.142416,63.2532808 184.705041,63.4395297 C199.193932,68.2358678 209.453582,73.3937462 215.665021,79.2882839 C219.360669,82.7953831 221.773972,86.6998434 222.646365,91.0218204 C223.567176,95.5836746 222.669313,100.159332 220.191548,104.451297 C216.105211,111.529614 208.591643,117.11221 197.887587,121.534031 C193.589552,123.309539 188.726579,124.917559 183.293259,126.363748 C183.541176,126.92292 183.733521,127.516759 183.862138,128.139758 C186.954886,143.120505 187.618598,154.61061 185.619477,162.954027 C184.430513,167.916214 182.256801,171.964297 178.951503,174.884919 C175.46197,177.968334 171.050077,179.48137 166.092853,179.48137 C157.914908,179.48137 149.31785,175.756061 140.130385,168.678956 C136.343104,165.761613 132.410866,162.238839 128.325434,158.108619 C127.905075,158.765474 127.388968,159.376011 126.77857,159.919385 C115.35902,170.085028 105.744755,176.401073 97.5215915,178.836349 C92.6321009,180.284364 88.0414736,180.422318 83.8610636,179.016969 C79.4483686,177.533532 75.9323404,174.467051 73.4516862,170.171891 C69.3577116,163.083327 68.2780823,153.768902 69.8044053,142.267976 C70.449038,137.410634 71.56762,132.103898 73.1575891,126.339009 C72.5361041,126.276104 71.9120754,126.144816 71.2949591,125.940529 C56.8060684,121.144191 46.5464184,115.986312 40.3349789,110.091775 C36.6393312,106.584675 34.2260275,102.680215 33.3536352,98.3582381 C32.4328237,93.7963839 33.3306866,89.2207269 35.8084524,84.9287618 C39.8947886,77.8504443 47.4083565,72.2678481 58.1124133,67.8460273 C62.5385143,66.0176154 67.5637208,64.366822 73.1939394,62.8874674 C72.9933393,62.3969171 72.8349374,61.8811235 72.7239712,61.3436237 Z\"\n            fill=\"#002C4B\"\n            fillRule=\"nonzero\"\n            transform=\"translate(128.000000, 95.000000) scale(-1, 1) translate(-128.000000, -95.000000) \"\n          ></path>\n          <path\n            d=\"M113.396882,64 L142.608177,64 C144.399254,64 146.053521,64.958025 146.944933,66.5115174 L161.577138,92.0115174 C162.461464,93.5526583 162.461464,95.4473417 161.577138,96.9884826 L146.944933,122.488483 C146.053521,124.041975 144.399254,125 142.608177,125 L113.396882,125 C111.605806,125 109.951539,124.041975 109.060126,122.488483 L94.4279211,96.9884826 C93.543596,95.4473417 93.543596,93.5526583 94.4279211,92.0115174 L109.060126,66.5115174 C109.951539,64.958025 111.605806,64 113.396882,64 Z M138.987827,70.2765273 C140.779849,70.2765273 142.434839,71.2355558 143.325899,72.7903404 L154.343038,92.0138131 C155.225607,93.5537825 155.225607,95.4462175 154.343038,96.9861869 L143.325899,116.20966 C142.434839,117.764444 140.779849,118.723473 138.987827,118.723473 L117.017233,118.723473 C115.225211,118.723473 113.570221,117.764444 112.67916,116.20966 L101.662022,96.9861869 C100.779452,95.4462175 100.779452,93.5537825 101.662022,92.0138131 L112.67916,72.7903404 C113.570221,71.2355558 115.225211,70.2765273 117.017233,70.2765273 L138.987827,70.2765273 Z M135.080648,77.1414791 L120.924411,77.1414791 C119.134228,77.1414791 117.480644,78.0985567 116.5889,79.6508285 L116.5889,79.6508285 L109.489217,92.0093494 C108.603232,93.5515958 108.603232,95.4484042 109.489217,96.9906506 L109.489217,96.9906506 L116.5889,109.349172 C117.480644,110.901443 119.134228,111.858521 120.924411,111.858521 L120.924411,111.858521 L135.080648,111.858521 C136.870831,111.858521 138.524416,110.901443 139.41616,109.349172 L139.41616,109.349172 L146.515843,96.9906506 C147.401828,95.4484042 147.401828,93.5515958 146.515843,92.0093494 L146.515843,92.0093494 L139.41616,79.6508285 C138.524416,78.0985567 136.870831,77.1414791 135.080648,77.1414791 L135.080648,77.1414791 Z M131.319186,83.7122186 C133.108028,83.7122186 134.760587,84.6678753 135.652827,86.2183156 L138.983552,92.0060969 C139.87203,93.5500005 139.87203,95.4499995 138.983552,96.9939031 L135.652827,102.781684 C134.760587,104.332125 133.108028,105.287781 131.319186,105.287781 L124.685874,105.287781 C122.897032,105.287781 121.244473,104.332125 120.352233,102.781684 L117.021508,96.9939031 C116.13303,95.4499995 116.13303,93.5500005 117.021508,92.0060969 L120.352233,86.2183156 C121.244473,84.6678753 122.897032,83.7122186 124.685874,83.7122186 L131.319186,83.7122186 Z M128.003794,90.1848875 C126.459294,90.1848875 125.034382,91.0072828 124.263005,92.3424437 C123.491732,93.6774232 123.491732,95.3225768 124.263005,96.6575563 C125.034382,97.9927172 126.459294,98.8151125 128.001266,98.8151125 L128.001266,98.8151125 C129.545766,98.8151125 130.970678,97.9927172 131.742055,96.6575563 C132.513327,95.3225768 132.513327,93.6774232 131.742055,92.3424437 C130.970678,91.0072828 129.545766,90.1848875 128.003794,90.1848875 L128.003794,90.1848875 Z M93,94.5009646 L100.767764,94.5009646\"\n            fill=\"#FFD94C\"\n          ></path>\n          <path\n            d=\"M87.8601729,108.357758 C89.1715224,107.608286 90.8360246,108.074601 91.5779424,109.399303 L91.5779424,109.399303 L92.0525843,110.24352 C95.8563392,116.982993 99.8190116,123.380176 103.940602,129.435068 C108.807881,136.585427 114.28184,143.82411 120.362479,151.151115 C121.316878,152.30114 121.184944,154.011176 120.065686,154.997937 L120.065686,154.997937 L119.454208,155.534625 C99.3465389,173.103314 86.2778188,176.612552 80.2480482,166.062341 C74.3500652,155.742717 76.4844915,136.982888 86.6513274,109.782853 C86.876818,109.179582 87.3045861,108.675291 87.8601729,108.357758 Z M173.534177,129.041504 C174.986131,128.785177 176.375496,129.742138 176.65963,131.194242 L176.65963,131.194242 L176.812815,131.986376 C181.782365,157.995459 178.283348,171 166.315764,171 C154.609745,171 139.708724,159.909007 121.612702,137.727022 C121.211349,137.235047 120.994572,136.617371 121,135.981509 C121.013158,134.480686 122.235785,133.274651 123.730918,133.287756 L123.730918,133.287756 L124.684654,133.294531 C132.305698,133.335994 139.714387,133.071591 146.910723,132.501323 C155.409039,131.82788 164.283523,130.674607 173.534177,129.041504 Z M180.408726,73.8119663 C180.932139,72.4026903 182.508386,71.6634537 183.954581,72.149012 L183.954581,72.149012 L184.742552,72.4154854 C210.583763,81.217922 220.402356,90.8916805 214.198332,101.436761 C208.129904,111.751366 190.484347,119.260339 161.26166,123.963678 C160.613529,124.067994 159.948643,123.945969 159.382735,123.618843 C158.047025,122.846729 157.602046,121.158214 158.388848,119.847438 L158.388848,119.847438 L158.889328,119.0105 C162.877183,112.31633 166.481358,105.654262 169.701854,99.0242957 C173.50501,91.1948179 177.073967,82.7907081 180.408726,73.8119663 Z M94.7383398,66.0363218 C95.3864708,65.9320063 96.0513565,66.0540315 96.6172646,66.3811573 C97.9529754,67.153271 98.3979538,68.8417862 97.6111517,70.1525615 L97.6111517,70.1525615 L97.1106718,70.9895001 C93.1228168,77.6836699 89.5186416,84.3457379 86.2981462,90.9757043 C82.49499,98.8051821 78.9260328,107.209292 75.5912744,116.188034 C75.0678608,117.59731 73.4916142,118.336546 72.045419,117.850988 L72.045419,117.850988 L71.2574475,117.584515 C45.4162372,108.782078 35.597644,99.1083195 41.8016679,88.5632391 C47.8700957,78.2486335 65.515653,70.7396611 94.7383398,66.0363218 Z M136.545792,34.4653746 C156.653461,16.8966864 169.722181,13.3874478 175.751952,23.9376587 C181.649935,34.2572826 179.515508,53.0171122 169.348673,80.2171474 C169.123182,80.8204179 168.695414,81.324709 168.139827,81.6422422 C166.828478,82.3917144 165.163975,81.9253986 164.422058,80.6006966 L164.422058,80.6006966 L163.947416,79.7564798 C160.143661,73.0170065 156.180988,66.6198239 152.059398,60.564932 C147.192119,53.4145727 141.71816,46.1758903 135.637521,38.8488847 C134.683122,37.6988602 134.815056,35.9888243 135.934314,35.0020629 L135.934314,35.0020629 Z M90.6842361,18 C102.390255,18 117.291276,29.0909926 135.387298,51.2729777 C135.788651,51.7649527 136.005428,52.3826288 136,53.0184911 C135.986842,54.5193144 134.764215,55.7253489 133.269082,55.7122445 L133.269082,55.7122445 L132.315346,55.7054689 C124.694302,55.6640063 117.285613,55.9284091 110.089277,56.4986773 C101.590961,57.17212 92.7164767,58.325393 83.4658235,59.9584962 C82.0138691,60.2148231 80.6245044,59.2578618 80.3403697,57.805758 L80.3403697,57.805758 L80.1871846,57.0136235 C75.2176347,31.0045412 78.7166519,18 90.6842361,18 Z\"\n            fill=\"#FF4154\"\n          ></path>\n        </g>\n      </g>\n    </svg>\n  )\n}\n", "'use client'\nimport * as React from 'react'\nimport {\n  notify<PERSON>ana<PERSON>,\n  onlineManager,\n  useQueryClient,\n} from '@tanstack/react-query'\nimport { rankItem } from '@tanstack/match-sorter-utils'\nimport { useMemo } from 'react'\nimport { useSyncExternalStore } from './useSyncExternalStore'\nimport useLocalStorage from './useLocalStorage'\nimport {\n  defaultPanelSize,\n  displayValue,\n  getResizeHandleStyle,\n  getSidePanelStyle,\n  getSidedProp,\n  isVerticalSide,\n  minPanelSize,\n  sortFns,\n  useIsMounted,\n} from './utils'\nimport {\n  ActiveQueryPanel,\n  Button,\n  Code,\n  Input,\n  Panel,\n  QueryKey,\n  QueryKeys,\n  Select,\n} from './styledComponents'\nimport ScreenReader from './screenreader'\nimport { ThemeProvider, defaultTheme as theme } from './theme'\nimport { getQueryStatusColor, getQueryStatusLabel } from './utils'\nimport Explorer from './Explorer'\nimport Logo from './Logo'\nimport type { Corner, Side } from './utils'\nimport type {\n  ContextOptions,\n  Query,\n  QueryCache,\n  QueryClient,\n  QueryKey as QueryKeyType,\n} from '@tanstack/react-query'\n\nexport interface DevToolsErrorType {\n  /**\n   * The name of the error.\n   */\n  name: string\n  /**\n   * How the error is initialized. Whatever it returns MUST implement toString() so\n   * we can check against the current error.\n   */\n  initializer: (query: Query) => { toString(): string }\n}\n\nexport interface DevtoolsOptions extends ContextOptions {\n  /**\n   * Set this true if you want the dev tools to default to being open\n   */\n  initialIsOpen?: boolean\n  /**\n   * Use this to add props to the panel. For example, you can add className, style (merge and override default style), etc.\n   */\n  panelProps?: React.ComponentPropsWithoutRef<'div'>\n  /**\n   * Use this to add props to the close button. For example, you can add className, style (merge and override default style), onClick (extend default handler), etc.\n   */\n  closeButtonProps?: React.ComponentPropsWithoutRef<'button'>\n  /**\n   * Use this to add props to the toggle button. For example, you can add className, style (merge and override default style), onClick (extend default handler), etc.\n   */\n  toggleButtonProps?: React.ComponentPropsWithoutRef<'button'>\n  /**\n   * The position of the React Query logo to open and close the devtools panel.\n   * Defaults to 'bottom-left'.\n   */\n  position?: Corner\n  /**\n   * The position of the React Query devtools panel.\n   * Defaults to 'bottom'.\n   */\n  panelPosition?: Side\n  /**\n   * Use this to render the devtools inside a different type of container element for a11y purposes.\n   * Any string which corresponds to a valid intrinsic JSX element is allowed.\n   * Defaults to 'aside'.\n   */\n  containerElement?: string | any\n  /**\n   * nonce for style element for CSP\n   */\n  styleNonce?: string\n  /**\n   * Use this so you can define custom errors that can be shown in the devtools.\n   */\n  errorTypes?: DevToolsErrorType[]\n}\n\ninterface DevtoolsPanelOptions extends ContextOptions {\n  /**\n   * The standard React style object used to style a component with inline styles\n   */\n  style?: React.CSSProperties\n  /**\n   * The standard React className property used to style a component with classes\n   */\n  className?: string\n  /**\n   * A boolean variable indicating whether the panel is open or closed\n   */\n  isOpen?: boolean\n  /**\n   * nonce for style element for CSP\n   */\n  styleNonce?: string\n  /**\n   * A function that toggles the open and close state of the panel\n   */\n  setIsOpen: (isOpen: boolean) => void\n  /**\n   * Handles the opening and closing the devtools panel\n   */\n  onDragStart: (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => void\n  /**\n   * The position of the React Query devtools panel.\n   * Defaults to 'bottom'.\n   */\n  position?: Side\n  /**\n   * Handles the panel position select change\n   */\n  onPositionChange?: (side: Side) => void\n  /**\n   * Show a close button inside the panel\n   */\n  showCloseButton?: boolean\n  /**\n   * Use this to add props to the close button. For example, you can add className, style (merge and override default style), onClick (extend default handler), etc.\n   */\n  closeButtonProps?: React.ComponentPropsWithoutRef<'button'>\n  /**\n   * Use this so you can define custom errors that can be shown in the devtools.\n   */\n  errorTypes?: DevToolsErrorType[]\n}\n\nexport function ReactQueryDevtools({\n  initialIsOpen,\n  panelProps = {},\n  closeButtonProps = {},\n  toggleButtonProps = {},\n  position = 'bottom-left',\n  containerElement: Container = 'aside',\n  context,\n  styleNonce,\n  panelPosition: initialPanelPosition = 'bottom',\n  errorTypes = [],\n}: DevtoolsOptions): React.ReactElement | null {\n  const rootRef = React.useRef<HTMLDivElement>(null)\n  const panelRef = React.useRef<HTMLDivElement>(null)\n  const [isOpen, setIsOpen] = useLocalStorage(\n    'reactQueryDevtoolsOpen',\n    initialIsOpen,\n  )\n  const [devtoolsHeight, setDevtoolsHeight] = useLocalStorage<number>(\n    'reactQueryDevtoolsHeight',\n    defaultPanelSize,\n  )\n  const [devtoolsWidth, setDevtoolsWidth] = useLocalStorage<number>(\n    'reactQueryDevtoolsWidth',\n    defaultPanelSize,\n  )\n\n  const [panelPosition = 'bottom', setPanelPosition] = useLocalStorage<Side>(\n    'reactQueryDevtoolsPanelPosition',\n    initialPanelPosition,\n  )\n\n  const [isResolvedOpen, setIsResolvedOpen] = React.useState(false)\n  const [isResizing, setIsResizing] = React.useState(false)\n  const isMounted = useIsMounted()\n\n  const handleDragStart = (\n    panelElement: HTMLDivElement | null,\n    startEvent: React.MouseEvent<HTMLDivElement, MouseEvent>,\n  ) => {\n    if (!panelElement) return\n    if (startEvent.button !== 0) return // Only allow left click for drag\n    const isVertical = isVerticalSide(panelPosition)\n    setIsResizing(true)\n\n    const { height, width } = panelElement.getBoundingClientRect()\n    const startX = startEvent.clientX\n    const startY = startEvent.clientY\n    let newSize = 0\n\n    const run = (moveEvent: MouseEvent) => {\n      // prevent mouse selecting stuff with mouse drag\n      moveEvent.preventDefault()\n\n      // calculate the correct size based on mouse position and current panel position\n      // hint: it is different formula for the opposite sides\n      if (isVertical) {\n        newSize =\n          width +\n          (panelPosition === 'right'\n            ? startX - moveEvent.clientX\n            : moveEvent.clientX - startX)\n        setDevtoolsWidth(newSize)\n      } else {\n        newSize =\n          height +\n          (panelPosition === 'bottom'\n            ? startY - moveEvent.clientY\n            : moveEvent.clientY - startY)\n        setDevtoolsHeight(newSize)\n      }\n\n      if (newSize < minPanelSize) {\n        setIsOpen(false)\n      } else {\n        setIsOpen(true)\n      }\n    }\n\n    const unsub = () => {\n      if (isResizing) {\n        setIsResizing(false)\n      }\n\n      document.removeEventListener('mousemove', run, false)\n      document.removeEventListener('mouseUp', unsub, false)\n    }\n\n    document.addEventListener('mousemove', run, false)\n    document.addEventListener('mouseup', unsub, false)\n  }\n\n  React.useEffect(() => {\n    setIsResolvedOpen(isOpen ?? false)\n  }, [isOpen, isResolvedOpen, setIsResolvedOpen])\n\n  // Toggle panel visibility before/after transition (depending on direction).\n  // Prevents focusing in a closed panel.\n  React.useEffect(() => {\n    const ref = panelRef.current\n    if (ref) {\n      const handlePanelTransitionStart = () => {\n        if (isResolvedOpen) {\n          ref.style.visibility = 'visible'\n        }\n      }\n\n      const handlePanelTransitionEnd = () => {\n        if (!isResolvedOpen) {\n          ref.style.visibility = 'hidden'\n        }\n      }\n\n      ref.addEventListener('transitionstart', handlePanelTransitionStart)\n      ref.addEventListener('transitionend', handlePanelTransitionEnd)\n\n      return () => {\n        ref.removeEventListener('transitionstart', handlePanelTransitionStart)\n        ref.removeEventListener('transitionend', handlePanelTransitionEnd)\n      }\n    }\n    return\n  }, [isResolvedOpen])\n\n  React.useEffect(() => {\n    if (isResolvedOpen && rootRef.current?.parentElement) {\n      const { parentElement } = rootRef.current\n      const styleProp = getSidedProp('padding', panelPosition)\n      const isVertical = isVerticalSide(panelPosition)\n\n      const previousPaddings = (({\n        padding,\n        paddingTop,\n        paddingBottom,\n        paddingLeft,\n        paddingRight,\n      }) => ({\n        padding,\n        paddingTop,\n        paddingBottom,\n        paddingLeft,\n        paddingRight,\n      }))(parentElement.style)\n\n      const run = () => {\n        // reset the padding\n        parentElement.style.padding = '0px'\n        parentElement.style.paddingTop = '0px'\n        parentElement.style.paddingBottom = '0px'\n        parentElement.style.paddingLeft = '0px'\n        parentElement.style.paddingRight = '0px'\n        // set the new padding based on the new panel position\n\n        parentElement.style[styleProp] = `${\n          isVertical ? devtoolsWidth : devtoolsHeight\n        }px`\n      }\n\n      run()\n\n      if (typeof window !== 'undefined') {\n        window.addEventListener('resize', run)\n\n        return () => {\n          window.removeEventListener('resize', run)\n          Object.entries(previousPaddings).forEach(\n            ([property, previousValue]) => {\n              parentElement.style[property as keyof typeof previousPaddings] =\n                previousValue\n            },\n          )\n        }\n      }\n    }\n    return\n  }, [isResolvedOpen, panelPosition, devtoolsHeight, devtoolsWidth])\n\n  const { style: panelStyle = {}, ...otherPanelProps } = panelProps\n\n  const {\n    style: toggleButtonStyle = {},\n    onClick: onToggleClick,\n    ...otherToggleButtonProps\n  } = toggleButtonProps\n\n  // get computed style based on panel position\n  const style = getSidePanelStyle({\n    position: panelPosition,\n    devtoolsTheme: theme,\n    isOpen: isResolvedOpen,\n    height: devtoolsHeight,\n    width: devtoolsWidth,\n    isResizing,\n    panelStyle,\n  })\n\n  // Do not render on the server\n  if (!isMounted()) return null\n\n  return (\n    <Container\n      ref={rootRef}\n      className=\"ReactQueryDevtools\"\n      aria-label=\"React Query Devtools\"\n    >\n      <ThemeProvider theme={theme}>\n        <ReactQueryDevtoolsPanel\n          ref={panelRef as any}\n          context={context}\n          styleNonce={styleNonce}\n          position={panelPosition}\n          onPositionChange={setPanelPosition}\n          showCloseButton\n          closeButtonProps={closeButtonProps}\n          {...otherPanelProps}\n          style={style}\n          isOpen={isResolvedOpen}\n          setIsOpen={setIsOpen}\n          onDragStart={(e) => handleDragStart(panelRef.current, e)}\n          errorTypes={errorTypes}\n        />\n      </ThemeProvider>\n      {!isResolvedOpen ? (\n        <button\n          type=\"button\"\n          {...otherToggleButtonProps}\n          aria-label=\"Open React Query Devtools\"\n          aria-controls=\"ReactQueryDevtoolsPanel\"\n          aria-haspopup=\"true\"\n          aria-expanded=\"false\"\n          onClick={(e) => {\n            setIsOpen(true)\n            onToggleClick?.(e)\n          }}\n          style={{\n            background: 'none',\n            border: 0,\n            padding: 0,\n            position: 'fixed',\n            zIndex: 99999,\n            display: 'inline-flex',\n            fontSize: '1.5em',\n            margin: '.5em',\n            cursor: 'pointer',\n            width: 'fit-content',\n            ...(position === 'top-right'\n              ? {\n                  top: '0',\n                  right: '0',\n                }\n              : position === 'top-left'\n              ? {\n                  top: '0',\n                  left: '0',\n                }\n              : position === 'bottom-right'\n              ? {\n                  bottom: '0',\n                  right: '0',\n                }\n              : {\n                  bottom: '0',\n                  left: '0',\n                }),\n            ...toggleButtonStyle,\n          }}\n        >\n          <Logo aria-hidden />\n          <ScreenReader text=\"Open React Query Devtools\" />\n        </button>\n      ) : null}\n    </Container>\n  )\n}\n\nconst useSubscribeToQueryCache = <T,>(\n  queryCache: QueryCache,\n  getSnapshot: () => T,\n  skip: boolean = false,\n): T => {\n  return useSyncExternalStore(\n    React.useCallback(\n      (onStoreChange) => {\n        if (!skip)\n          return queryCache.subscribe(notifyManager.batchCalls(onStoreChange))\n        return () => {\n          return\n        }\n      },\n      [queryCache, skip],\n    ),\n    getSnapshot,\n    getSnapshot,\n  )\n}\n\nexport const ReactQueryDevtoolsPanel = React.forwardRef<\n  HTMLDivElement,\n  DevtoolsPanelOptions\n>(function ReactQueryDevtoolsPanel(props, ref): React.ReactElement {\n  const {\n    isOpen = true,\n    styleNonce,\n    setIsOpen,\n    context,\n    onDragStart,\n    onPositionChange,\n    showCloseButton,\n    position,\n    closeButtonProps = {},\n    errorTypes = [],\n    ...panelProps\n  } = props\n\n  const { onClick: onCloseClick, ...otherCloseButtonProps } = closeButtonProps\n\n  const queryClient = useQueryClient({ context })\n  const queryCache = queryClient.getQueryCache()\n\n  const [sort, setSort] = useLocalStorage(\n    'reactQueryDevtoolsSortFn',\n    Object.keys(sortFns)[0],\n  )\n\n  const [filter, setFilter] = useLocalStorage('reactQueryDevtoolsFilter', '')\n\n  const [baseSort, setBaseSort] = useLocalStorage(\n    'reactQueryDevtoolsBaseSort',\n    1,\n  )\n\n  const sortFn = React.useMemo(() => sortFns[sort as string], [sort])\n\n  const queriesCount = useSubscribeToQueryCache(\n    queryCache,\n    () => queryCache.getAll().length,\n    !isOpen,\n  )\n\n  const [activeQueryHash, setActiveQueryHash] = useLocalStorage(\n    'reactQueryDevtoolsActiveQueryHash',\n    '',\n  )\n\n  const queries = React.useMemo(() => {\n    const unsortedQueries = queryCache.getAll()\n\n    if (queriesCount === 0) {\n      return []\n    }\n\n    const filtered = filter\n      ? unsortedQueries.filter(\n          (item) => rankItem(item.queryHash, filter).passed,\n        )\n      : [...unsortedQueries]\n\n    const sorted = sortFn\n      ? filtered.sort((a, b) => sortFn(a, b) * (baseSort as number))\n      : filtered\n\n    return sorted\n  }, [baseSort, sortFn, filter, queriesCount, queryCache])\n\n  const [isMockOffline, setMockOffline] = React.useState(false)\n\n  return (\n    <ThemeProvider theme={theme}>\n      <Panel\n        ref={ref}\n        className=\"ReactQueryDevtoolsPanel\"\n        aria-label=\"React Query Devtools Panel\"\n        id=\"ReactQueryDevtoolsPanel\"\n        {...panelProps}\n        style={{\n          height: defaultPanelSize,\n          position: 'relative',\n          ...panelProps.style,\n        }}\n      >\n        <style\n          nonce={styleNonce}\n          dangerouslySetInnerHTML={{\n            __html: `\n            .ReactQueryDevtoolsPanel * {\n              scrollbar-color: ${theme.backgroundAlt} ${theme.gray};\n            }\n\n            .ReactQueryDevtoolsPanel *::-webkit-scrollbar, .ReactQueryDevtoolsPanel scrollbar {\n              width: 1em;\n              height: 1em;\n            }\n\n            .ReactQueryDevtoolsPanel *::-webkit-scrollbar-track, .ReactQueryDevtoolsPanel scrollbar-track {\n              background: ${theme.backgroundAlt};\n            }\n\n            .ReactQueryDevtoolsPanel *::-webkit-scrollbar-thumb, .ReactQueryDevtoolsPanel scrollbar-thumb {\n              background: ${theme.gray};\n              border-radius: .5em;\n              border: 3px solid ${theme.backgroundAlt};\n            }\n          `,\n          }}\n        />\n        <div\n          style={getResizeHandleStyle(position)}\n          onMouseDown={onDragStart}\n        ></div>\n\n        {isOpen && (\n          <div\n            style={{\n              flex: '1 1 500px',\n              minHeight: '40%',\n              maxHeight: '100%',\n              overflow: 'auto',\n              borderRight: `1px solid ${theme.grayAlt}`,\n              display: 'flex',\n              flexDirection: 'column',\n            }}\n          >\n            <div\n              style={{\n                padding: '.5em',\n                background: theme.backgroundAlt,\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center',\n              }}\n            >\n              <button\n                type=\"button\"\n                aria-label=\"Close React Query Devtools\"\n                aria-controls=\"ReactQueryDevtoolsPanel\"\n                aria-haspopup=\"true\"\n                aria-expanded=\"true\"\n                onClick={() => setIsOpen(false)}\n                style={{\n                  display: 'inline-flex',\n                  background: 'none',\n                  border: 0,\n                  padding: 0,\n                  marginRight: '.5em',\n                  cursor: 'pointer',\n                }}\n              >\n                <Logo aria-hidden />\n                <ScreenReader text=\"Close React Query Devtools\" />\n              </button>\n\n              <div\n                style={{\n                  display: 'flex',\n                  flexDirection: 'column',\n                }}\n              >\n                <div\n                  style={{\n                    display: 'flex',\n                    justifyContent: 'space-between',\n                    alignItems: 'center',\n                    marginBottom: '.5em',\n                  }}\n                >\n                  <QueryStatusCount queryCache={queryCache} />\n                  {position && onPositionChange ? (\n                    <Select\n                      aria-label=\"Panel position\"\n                      value={position}\n                      style={{ marginInlineStart: '.5em' }}\n                      onChange={(e) => onPositionChange(e.target.value as Side)}\n                    >\n                      <option value=\"left\">Left</option>\n                      <option value=\"right\">Right</option>\n                      <option value=\"top\">Top</option>\n                      <option value=\"bottom\">Bottom</option>\n                    </Select>\n                  ) : null}\n                </div>\n                <div\n                  style={{\n                    display: 'flex',\n                    alignItems: 'center',\n                    flexWrap: 'wrap',\n                    gap: '0.5em',\n                  }}\n                >\n                  <Input\n                    placeholder=\"Filter\"\n                    aria-label=\"Filter by queryhash\"\n                    value={filter ?? ''}\n                    onChange={(e) => setFilter(e.target.value)}\n                    onKeyDown={(e) => {\n                      if (e.key === 'Escape') setFilter('')\n                    }}\n                    style={{\n                      flex: '1',\n                      width: '100%',\n                    }}\n                  />\n                  <Select\n                    aria-label=\"Sort queries\"\n                    value={sort}\n                    onChange={(e) => setSort(e.target.value)}\n                    style={{\n                      flex: '1',\n                      minWidth: 75,\n                      marginRight: '.5em',\n                    }}\n                  >\n                    {Object.keys(sortFns).map((key) => (\n                      <option key={key} value={key}>\n                        Sort by {key}\n                      </option>\n                    ))}\n                  </Select>\n                  <Button\n                    type=\"button\"\n                    onClick={() => setBaseSort((old) => old * -1)}\n                    style={{\n                      padding: '.3em .4em',\n                      marginRight: '.5em',\n                    }}\n                  >\n                    {baseSort === 1 ? '⬆ Asc' : '⬇ Desc'}\n                  </Button>\n                  <Button\n                    title=\"Clear cache\"\n                    aria-label=\"Clear cache\"\n                    type=\"button\"\n                    onClick={() => queryCache.clear()}\n                    style={{\n                      padding: '.3em .4em',\n                      marginRight: '.5em',\n                    }}\n                  >\n                    Clear\n                  </Button>\n                  <Button\n                    type=\"button\"\n                    onClick={() => {\n                      if (isMockOffline) {\n                        onlineManager.setOnline(undefined)\n                        setMockOffline(false)\n                        window.dispatchEvent(new Event('online'))\n                      } else {\n                        onlineManager.setOnline(false)\n                        setMockOffline(true)\n                      }\n                    }}\n                    aria-label={\n                      isMockOffline\n                        ? 'Restore offline mock'\n                        : 'Mock offline behavior'\n                    }\n                    title={\n                      isMockOffline\n                        ? 'Restore offline mock'\n                        : 'Mock offline behavior'\n                    }\n                    style={{\n                      padding: '0',\n                      height: '2em',\n                    }}\n                  >\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      width=\"2em\"\n                      height=\"2em\"\n                      viewBox=\"0 0 24 24\"\n                      stroke={isMockOffline ? theme.danger : 'currentColor'}\n                      fill=\"none\"\n                    >\n                      {isMockOffline ? (\n                        <>\n                          <path stroke=\"none\" d=\"M0 0h24v24H0z\" fill=\"none\" />\n                          <line x1=\"12\" y1=\"18\" x2=\"12.01\" y2=\"18\" />\n                          <path d=\"M9.172 15.172a4 4 0 0 1 5.656 0\" />\n                          <path d=\"M6.343 12.343a7.963 7.963 0 0 1 3.864 -2.14m4.163 .155a7.965 7.965 0 0 1 3.287 2\" />\n                          <path d=\"M3.515 9.515a12 12 0 0 1 3.544 -2.455m3.101 -.92a12 12 0 0 1 10.325 3.374\" />\n                          <line x1=\"3\" y1=\"3\" x2=\"21\" y2=\"21\" />\n                        </>\n                      ) : (\n                        <>\n                          <path stroke=\"none\" d=\"M0 0h24v24H0z\" fill=\"none\" />\n                          <line x1=\"12\" y1=\"18\" x2=\"12.01\" y2=\"18\" />\n                          <path d=\"M9.172 15.172a4 4 0 0 1 5.656 0\" />\n                          <path d=\"M6.343 12.343a8 8 0 0 1 11.314 0\" />\n                          <path d=\"M3.515 9.515c4.686 -4.687 12.284 -4.687 17 0\" />\n                        </>\n                      )}\n                    </svg>\n                    <ScreenReader\n                      text={\n                        isMockOffline\n                          ? 'Restore offline mock'\n                          : 'Mock offline behavior'\n                      }\n                    />\n                  </Button>\n                </div>\n              </div>\n            </div>\n            <div\n              style={{\n                overflowY: 'auto',\n                flex: '1',\n              }}\n            >\n              {queries.map((query) => {\n                return (\n                  <QueryRow\n                    queryKey={query.queryKey}\n                    activeQueryHash={activeQueryHash}\n                    setActiveQueryHash={setActiveQueryHash}\n                    key={query.queryHash}\n                    queryCache={queryCache}\n                  />\n                )\n              })}\n            </div>\n          </div>\n        )}\n\n        {activeQueryHash && isOpen ? (\n          <ActiveQuery\n            activeQueryHash={activeQueryHash}\n            queryCache={queryCache}\n            queryClient={queryClient}\n            errorTypes={errorTypes}\n          />\n        ) : null}\n\n        {showCloseButton ? (\n          <Button\n            type=\"button\"\n            aria-controls=\"ReactQueryDevtoolsPanel\"\n            aria-haspopup=\"true\"\n            aria-expanded=\"true\"\n            {...(otherCloseButtonProps as Record<string, unknown>)}\n            style={{\n              position: 'absolute',\n              zIndex: 99999,\n              margin: '.5em',\n              bottom: 0,\n              left: 0,\n              ...otherCloseButtonProps.style,\n            }}\n            onClick={(e) => {\n              setIsOpen(false)\n              onCloseClick?.(e)\n            }}\n          >\n            Close\n          </Button>\n        ) : null}\n      </Panel>\n    </ThemeProvider>\n  )\n})\n\nconst ActiveQuery = ({\n  queryCache,\n  activeQueryHash,\n  queryClient,\n  errorTypes,\n}: {\n  queryCache: QueryCache\n  activeQueryHash: string\n  queryClient: QueryClient\n  errorTypes: DevToolsErrorType[]\n}) => {\n  const activeQuery = useSubscribeToQueryCache(queryCache, () =>\n    queryCache.getAll().find((query) => query.queryHash === activeQueryHash),\n  )\n\n  const activeQueryState = useSubscribeToQueryCache(\n    queryCache,\n    () =>\n      queryCache.getAll().find((query) => query.queryHash === activeQueryHash)\n        ?.state,\n  )\n\n  const isStale =\n    useSubscribeToQueryCache(queryCache, () =>\n      queryCache\n        .getAll()\n        .find((query) => query.queryHash === activeQueryHash)\n        ?.isStale(),\n    ) ?? false\n\n  const observerCount =\n    useSubscribeToQueryCache(queryCache, () =>\n      queryCache\n        .getAll()\n        .find((query) => query.queryHash === activeQueryHash)\n        ?.getObserversCount(),\n    ) ?? 0\n\n  const handleRefetch = () => {\n    const promise = activeQuery?.fetch()\n    promise?.catch(noop)\n  }\n\n  const currentErrorTypeName = useMemo(() => {\n    if (activeQuery && activeQueryState?.error) {\n      const errorType = errorTypes.find(\n        (type) =>\n          type.initializer(activeQuery).toString() ===\n          activeQueryState.error?.toString(),\n      )\n      return errorType?.name\n    }\n    return undefined\n  }, [activeQuery, activeQueryState?.error, errorTypes])\n\n  if (!activeQuery || !activeQueryState) {\n    return null\n  }\n\n  const triggerError = (errorType?: DevToolsErrorType) => {\n    const error =\n      errorType?.initializer(activeQuery) ??\n      new Error('Unknown error from devtools')\n\n    const __previousQueryOptions = activeQuery.options\n\n    activeQuery.setState({\n      status: 'error',\n      error,\n      fetchMeta: {\n        ...activeQuery.state.fetchMeta,\n        __previousQueryOptions,\n      },\n    })\n  }\n\n  const restoreQueryAfterLoadingOrError = () => {\n    activeQuery.fetch(activeQuery.state.fetchMeta.__previousQueryOptions, {\n      // Make sure this fetch will cancel the previous one\n      cancelRefetch: true,\n    })\n  }\n\n  return (\n    <ActiveQueryPanel>\n      <div\n        style={{\n          padding: '.5em',\n          background: theme.backgroundAlt,\n          position: 'sticky',\n          top: 0,\n          zIndex: 1,\n        }}\n      >\n        Query Details\n      </div>\n      <div\n        style={{\n          padding: '.5em',\n        }}\n      >\n        <div\n          style={{\n            marginBottom: '.5em',\n            display: 'flex',\n            alignItems: 'flex-start',\n            justifyContent: 'space-between',\n          }}\n        >\n          <Code\n            style={{\n              lineHeight: '1.8em',\n            }}\n          >\n            <pre\n              style={{\n                margin: 0,\n                padding: 0,\n                overflow: 'auto',\n              }}\n            >\n              {displayValue(activeQuery.queryKey, true)}\n            </pre>\n          </Code>\n          <span\n            style={{\n              padding: '0.3em .6em',\n              borderRadius: '0.4em',\n              fontWeight: 'bold',\n              textShadow: '0 2px 10px black',\n              background: getQueryStatusColor({\n                queryState: activeQueryState,\n                isStale: isStale,\n                observerCount: observerCount,\n                theme,\n              }),\n              flexShrink: 0,\n            }}\n          >\n            {getQueryStatusLabel(activeQuery)}\n          </span>\n        </div>\n        <div\n          style={{\n            marginBottom: '.5em',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between',\n          }}\n        >\n          Observers: <Code>{observerCount}</Code>\n        </div>\n        <div\n          style={{\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between',\n          }}\n        >\n          Last Updated:{' '}\n          <Code>\n            {new Date(activeQueryState.dataUpdatedAt).toLocaleTimeString()}\n          </Code>\n        </div>\n      </div>\n      <div\n        style={{\n          background: theme.backgroundAlt,\n          padding: '.5em',\n          position: 'sticky',\n          top: 0,\n          zIndex: 1,\n        }}\n      >\n        Actions\n      </div>\n      <div\n        style={{\n          padding: '0.5em',\n          display: 'flex',\n          flexWrap: 'wrap',\n          gap: '0.5em',\n          alignItems: 'flex-end',\n        }}\n      >\n        <Button\n          type=\"button\"\n          onClick={handleRefetch}\n          disabled={activeQueryState.fetchStatus === 'fetching'}\n          style={{\n            background: theme.active,\n          }}\n        >\n          Refetch\n        </Button>{' '}\n        <Button\n          type=\"button\"\n          onClick={() => queryClient.invalidateQueries(activeQuery)}\n          style={{\n            background: theme.warning,\n            color: theme.inputTextColor,\n          }}\n        >\n          Invalidate\n        </Button>{' '}\n        <Button\n          type=\"button\"\n          onClick={() => queryClient.resetQueries(activeQuery)}\n          style={{\n            background: theme.gray,\n          }}\n        >\n          Reset\n        </Button>{' '}\n        <Button\n          type=\"button\"\n          onClick={() => queryClient.removeQueries(activeQuery)}\n          style={{\n            background: theme.danger,\n          }}\n        >\n          Remove\n        </Button>{' '}\n        <Button\n          type=\"button\"\n          onClick={() => {\n            // Return early if the query is already restoring\n            if (\n              activeQuery.state.fetchStatus === 'fetching' &&\n              typeof activeQuery.state.fetchMeta?.__previousQueryOptions ===\n                'undefined'\n            ) {\n              return\n            }\n\n            if (activeQuery.state.data === undefined) {\n              restoreQueryAfterLoadingOrError()\n            } else {\n              const __previousQueryOptions = activeQuery.options\n              // Trigger a fetch in order to trigger suspense as well.\n              activeQuery.fetch({\n                ...__previousQueryOptions,\n                queryFn: () => {\n                  return new Promise(() => {\n                    // Never resolve\n                  })\n                },\n                cacheTime: -1,\n              })\n              activeQuery.setState({\n                data: undefined,\n                status: 'loading',\n                fetchMeta: {\n                  ...activeQuery.state.fetchMeta,\n                  __previousQueryOptions,\n                },\n              })\n            }\n          }}\n          style={{\n            background: theme.paused,\n          }}\n        >\n          {activeQuery.state.status === 'loading' ? 'Restore' : 'Trigger'}{' '}\n          loading\n        </Button>{' '}\n        {errorTypes.length === 0 || activeQuery.state.status === 'error' ? (\n          <Button\n            type=\"button\"\n            onClick={() => {\n              if (!activeQuery.state.error) {\n                triggerError()\n              } else {\n                queryClient.resetQueries(activeQuery)\n              }\n            }}\n            style={{\n              background: theme.danger,\n            }}\n          >\n            {activeQuery.state.status === 'error' ? 'Restore' : 'Trigger'} error\n          </Button>\n        ) : (\n          <label>\n            Trigger error:\n            <Select\n              value={currentErrorTypeName ?? ''}\n              style={{ marginInlineStart: '.5em' }}\n              onChange={(e) => {\n                const errorType = errorTypes.find(\n                  (t) => t.name === e.target.value,\n                )\n\n                triggerError(errorType)\n              }}\n            >\n              <option key=\"\" value=\"\" />\n              {errorTypes.map((errorType) => (\n                <option key={errorType.name} value={errorType.name}>\n                  {errorType.name}\n                </option>\n              ))}\n            </Select>\n          </label>\n        )}\n      </div>\n      <div\n        style={{\n          background: theme.backgroundAlt,\n          padding: '.5em',\n          position: 'sticky',\n          top: 0,\n          zIndex: 1,\n        }}\n      >\n        Data Explorer\n      </div>\n      <div\n        style={{\n          padding: '.5em',\n        }}\n      >\n        <Explorer\n          label=\"Data\"\n          value={activeQueryState.data}\n          defaultExpanded={{}}\n          copyable\n        />\n      </div>\n      <div\n        style={{\n          background: theme.backgroundAlt,\n          padding: '.5em',\n          position: 'sticky',\n          top: 0,\n          zIndex: 1,\n        }}\n      >\n        Query Explorer\n      </div>\n      <div\n        style={{\n          padding: '.5em',\n        }}\n      >\n        <Explorer\n          label=\"Query\"\n          value={activeQuery}\n          defaultExpanded={{\n            queryKey: true,\n          }}\n        />\n      </div>\n    </ActiveQueryPanel>\n  )\n}\n\nconst QueryStatusCount = ({ queryCache }: { queryCache: QueryCache }) => {\n  const hasFresh = useSubscribeToQueryCache(\n    queryCache,\n    () =>\n      queryCache.getAll().filter((q) => getQueryStatusLabel(q) === 'fresh')\n        .length,\n  )\n  const hasFetching = useSubscribeToQueryCache(\n    queryCache,\n    () =>\n      queryCache.getAll().filter((q) => getQueryStatusLabel(q) === 'fetching')\n        .length,\n  )\n  const hasPaused = useSubscribeToQueryCache(\n    queryCache,\n    () =>\n      queryCache.getAll().filter((q) => getQueryStatusLabel(q) === 'paused')\n        .length,\n  )\n  const hasStale = useSubscribeToQueryCache(\n    queryCache,\n    () =>\n      queryCache.getAll().filter((q) => getQueryStatusLabel(q) === 'stale')\n        .length,\n  )\n  const hasInactive = useSubscribeToQueryCache(\n    queryCache,\n    () =>\n      queryCache.getAll().filter((q) => getQueryStatusLabel(q) === 'inactive')\n        .length,\n  )\n  return (\n    <QueryKeys>\n      <QueryKey\n        style={{\n          background: theme.success,\n          opacity: hasFresh ? 1 : 0.3,\n        }}\n      >\n        fresh <Code>({hasFresh})</Code>\n      </QueryKey>{' '}\n      <QueryKey\n        style={{\n          background: theme.active,\n          opacity: hasFetching ? 1 : 0.3,\n        }}\n      >\n        fetching <Code>({hasFetching})</Code>\n      </QueryKey>{' '}\n      <QueryKey\n        style={{\n          background: theme.paused,\n          opacity: hasPaused ? 1 : 0.3,\n        }}\n      >\n        paused <Code>({hasPaused})</Code>\n      </QueryKey>{' '}\n      <QueryKey\n        style={{\n          background: theme.warning,\n          color: 'black',\n          textShadow: '0',\n          opacity: hasStale ? 1 : 0.3,\n        }}\n      >\n        stale <Code>({hasStale})</Code>\n      </QueryKey>{' '}\n      <QueryKey\n        style={{\n          background: theme.gray,\n          opacity: hasInactive ? 1 : 0.3,\n        }}\n      >\n        inactive <Code>({hasInactive})</Code>\n      </QueryKey>\n    </QueryKeys>\n  )\n}\n\ninterface QueryRowProps {\n  queryKey: QueryKeyType\n  setActiveQueryHash: (hash: string) => void\n  activeQueryHash?: string\n  queryCache: QueryCache\n}\n\nconst QueryRow = React.memo(\n  ({\n    queryKey,\n    setActiveQueryHash,\n    activeQueryHash,\n    queryCache,\n  }: QueryRowProps) => {\n    const queryHash =\n      useSubscribeToQueryCache(\n        queryCache,\n        () => queryCache.find(queryKey)?.queryHash,\n      ) ?? ''\n\n    const queryState = useSubscribeToQueryCache(\n      queryCache,\n      () => queryCache.find(queryKey)?.state,\n    )\n\n    const isStale =\n      useSubscribeToQueryCache(queryCache, () =>\n        queryCache.find(queryKey)?.isStale(),\n      ) ?? false\n\n    const isDisabled =\n      useSubscribeToQueryCache(queryCache, () =>\n        queryCache.find(queryKey)?.isDisabled(),\n      ) ?? false\n\n    const observerCount =\n      useSubscribeToQueryCache(queryCache, () =>\n        queryCache.find(queryKey)?.getObserversCount(),\n      ) ?? 0\n\n    if (!queryState) {\n      return null\n    }\n\n    return (\n      <div\n        role=\"button\"\n        aria-label={`Open query details for ${queryHash}`}\n        onClick={() =>\n          setActiveQueryHash(activeQueryHash === queryHash ? '' : queryHash)\n        }\n        style={{\n          display: 'flex',\n          borderBottom: `solid 1px ${theme.grayAlt}`,\n          cursor: 'pointer',\n          background:\n            queryHash === activeQueryHash ? 'rgba(255,255,255,.1)' : undefined,\n        }}\n      >\n        <div\n          style={{\n            flex: '0 0 auto',\n            width: '2em',\n            height: '2em',\n            background: getQueryStatusColor({\n              queryState,\n              isStale,\n              observerCount,\n              theme,\n            }),\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            fontWeight: 'bold',\n            textShadow: isStale ? '0' : '0 0 10px black',\n            color: isStale ? 'black' : 'white',\n          }}\n        >\n          {observerCount}\n        </div>\n        {isDisabled ? (\n          <div\n            style={{\n              flex: '0 0 auto',\n              height: '2em',\n              background: theme.gray,\n              display: 'flex',\n              alignItems: 'center',\n              fontWeight: 'bold',\n              padding: '0 0.5em',\n            }}\n          >\n            disabled\n          </div>\n        ) : null}\n        <Code\n          style={{\n            padding: '.5em',\n          }}\n        >\n          {`${queryHash}`}\n        </Code>\n      </div>\n    )\n  },\n)\n\nQueryRow.displayName = 'QueryRow'\n\n// eslint-disable-next-line @typescript-eslint/no-empty-function\nfunction noop() {}\n", "'use client'\n\nimport * as devtools from './devtools'\n\nexport const ReactQueryDevtools: typeof devtools['ReactQueryDevtools'] =\n  process.env.NODE_ENV !== 'development'\n    ? function () {\n        return null\n      }\n    : devtools.ReactQueryDevtools\n\nexport const ReactQueryDevtoolsPanel: typeof devtools['ReactQueryDevtoolsPanel'] =\n  process.env.NODE_ENV !== 'development'\n    ? (function () {\n        return null\n      } as any)\n    : devtools.ReactQueryDevtoolsPanel\n"], "names": ["getItem", "key", "itemValue", "localStorage", "JSON", "parse", "undefined", "useLocalStorage", "defaultValue", "value", "setValue", "React", "useState", "useEffect", "initialValue", "setter", "useCallback", "updater", "old", "newVal", "setItem", "stringify", "defaultTheme", "background", "backgroundAlt", "foreground", "gray", "grayAlt", "inputBackgroundColor", "inputTextColor", "success", "danger", "active", "paused", "warning", "ThemeContext", "createContext", "ThemeProvider", "theme", "rest", "useTheme", "useContext", "useMediaQuery", "query", "isMatch", "setIsMatch", "window", "matchMedia", "matches", "matcher", "onChange", "addListener", "removeListener", "getQueryStatusColor", "queryState", "observerCount", "isStale", "fetchStatus", "getQueryStatusLabel", "state", "getObserversCount", "styled", "type", "newStyles", "queries", "forwardRef", "style", "ref", "mediaStyles", "Object", "entries", "reduce", "current", "createElement", "useIsMounted", "mountedRef", "useRef", "isMounted", "displayValue", "beautify", "json", "SuperJSON", "serialize", "getStatusRank", "q", "queryHashSort", "a", "b", "queryHash", "localeCompare", "dateSort", "dataUpdatedAt", "statusAndDateSort", "sortFns", "minPanelSize", "defaultPanelSize", "sides", "top", "bottom", "left", "right", "isVerticalSide", "side", "includes", "getOppositeSide", "getSidedProp", "prop", "char<PERSON>t", "toUpperCase", "slice", "getSidePanelStyle", "position", "height", "width", "devtoolsTheme", "isOpen", "isResizing", "panelStyle", "oppositeSide", "borderSide", "isVertical", "direction", "transform<PERSON><PERSON>in", "boxShadow", "zIndex", "visibility", "transition", "opacity", "pointerEvents", "transform", "max<PERSON><PERSON><PERSON>", "maxHeight", "getResizeHandleStyle", "marginSide", "cursor", "Panel", "_props", "fontSize", "fontFamily", "display", "backgroundColor", "color", "flexDirection", "ActiveQueryPanel", "flex", "overflow", "borderTop", "<PERSON><PERSON>", "props", "appearance", "fontWeight", "border", "borderRadius", "padding", "disabled", "Query<PERSON><PERSON>s", "flexWrap", "gap", "Query<PERSON>ey", "alignItems", "textShadow", "Code", "Input", "lineHeight", "Select", "WebkitAppearance", "backgroundImage", "backgroundRepeat", "backgroundPosition", "backgroundSize", "ScreenReader", "text", "Entry", "outline", "wordBreak", "Label", "LabelButton", "ExpandButton", "font", "Copy<PERSON><PERSON><PERSON>", "copyState", "setCopyState", "navigator", "clipboard", "writeText", "<PERSON>j<PERSON>", "then", "setTimeout", "err", "console", "error", "Value", "SubEntries", "marginLeft", "paddingLeft", "borderLeft", "Info", "Expander", "expanded", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>r<PERSON><PERSON><PERSON>", "CopiedCopier", "verticalAlign", "chunkArray", "array", "size", "i", "result", "length", "push", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleEntry", "label", "subEntries", "subEntryPages", "copyable", "toggleExpanded", "pageSize", "expandedPages", "setExpandedPages", "String", "toLowerCase", "map", "index", "filter", "d", "isIterable", "x", "Symbol", "iterator", "Explorer", "defaultExpanded", "renderer", "setExpanded", "Boolean", "makeProperty", "sub", "subDefaultExpanded", "Array", "isArray", "toString", "from", "val", "entry", "Logo", "ReactQueryDevtools", "initialIsOpen", "panelProps", "closeButtonProps", "toggleButtonProps", "containerElement", "Container", "context", "styleNonce", "panelPosition", "initialPanelPosition", "errorTypes", "rootRef", "panelRef", "setIsOpen", "devtoolsHeight", "setDevtoolsHeight", "dev<PERSON><PERSON><PERSON><PERSON><PERSON>", "set<PERSON>ev<PERSON><PERSON><PERSON><PERSON><PERSON>", "setPanelPosition", "isResolvedOpen", "setIsResolvedOpen", "setIsResizing", "handleDragStart", "panelElement", "startEvent", "button", "getBoundingClientRect", "startX", "clientX", "startY", "clientY", "newSize", "run", "moveEvent", "preventDefault", "unsub", "document", "removeEventListener", "addEventListener", "handlePanelTransitionStart", "handlePanelTransitionEnd", "parentElement", "styleProp", "previousPaddings", "paddingTop", "paddingBottom", "paddingRight", "for<PERSON>ach", "property", "previousValue", "otherPanelProps", "toggleButtonStyle", "onClick", "onToggleClick", "otherToggleButtonProps", "ReactQueryDevtoolsPanel", "e", "margin", "useSubscribeToQueryCache", "queryCache", "getSnapshot", "skip", "useSyncExternalStore", "onStoreChange", "subscribe", "notify<PERSON><PERSON>ger", "batchCalls", "onDragStart", "onPositionChange", "showCloseButton", "onCloseClick", "otherCloseButtonProps", "queryClient", "useQueryClient", "get<PERSON><PERSON><PERSON><PERSON>ache", "sort", "setSort", "keys", "setFilter", "baseSort", "setBaseSort", "sortFn", "useMemo", "queriesCount", "getAll", "activeQueryHash", "setActiveQueryHash", "unsortedQueries", "filtered", "item", "rankItem", "passed", "sorted", "isMockOffline", "setMockOffline", "__html", "minHeight", "borderRight", "justifyContent", "marginRight", "marginBottom", "marginInlineStart", "target", "min<PERSON><PERSON><PERSON>", "clear", "onlineManager", "setOnline", "dispatchEvent", "Event", "overflowY", "query<PERSON><PERSON>", "ActiveQuery", "activeQuery", "find", "activeQueryState", "handleRefetch", "promise", "fetch", "catch", "noop", "currentErrorTypeName", "errorType", "initializer", "name", "triggerError", "Error", "__previousQueryOptions", "options", "setState", "status", "fetchMeta", "restoreQueryAfterLoadingOrError", "cancelRefetch", "flexShrink", "Date", "toLocaleTimeString", "invalidateQueries", "resetQueries", "removeQueries", "data", "queryFn", "Promise", "cacheTime", "t", "QueryStatusCount", "hasFresh", "hasFetching", "hasPaused", "hasStale", "hasInactive", "QueryRow", "memo", "isDisabled", "borderBottom", "displayName", "devtools"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAEA,MAAMA,OAAO,GAAIC,GAAD,IAA0B;EACxC,IAAI;AACF,IAAA,MAAMC,SAAS,GAAGC,YAAY,CAACH,OAAb,CAAqBC,GAArB,CAAlB,CAAA;;AACA,IAAA,IAAI,OAAOC,SAAP,KAAqB,QAAzB,EAAmC;AACjC,MAAA,OAAOE,IAAI,CAACC,KAAL,CAAWH,SAAX,CAAP,CAAA;AACD,KAAA;;AACD,IAAA,OAAOI,SAAP,CAAA;AACD,GAND,CAME,MAAM;AACN,IAAA,OAAOA,SAAP,CAAA;AACD,GAAA;AACF,CAVD,CAAA;;AAYe,SAASC,eAAT,CACbN,GADa,EAEbO,YAFa,EAG+C;EAC5D,MAAM,CAACC,KAAD,EAAQC,QAAR,IAAoBC,KAAK,CAACC,QAAN,EAA1B,CAAA;EAEAD,KAAK,CAACE,SAAN,CAAgB,MAAM;AACpB,IAAA,MAAMC,YAAY,GAAGd,OAAO,CAACC,GAAD,CAA5B,CAAA;;IAEA,IAAI,OAAOa,YAAP,KAAwB,WAAxB,IAAuCA,YAAY,KAAK,IAA5D,EAAkE;MAChEJ,QAAQ,CACN,OAAOF,YAAP,KAAwB,UAAxB,GAAqCA,YAAY,EAAjD,GAAsDA,YADhD,CAAR,CAAA;AAGD,KAJD,MAIO;MACLE,QAAQ,CAACI,YAAD,CAAR,CAAA;AACD,KAAA;AACF,GAVD,EAUG,CAACN,YAAD,EAAeP,GAAf,CAVH,CAAA,CAAA;AAYA,EAAA,MAAMc,MAAM,GAAGJ,KAAK,CAACK,WAAN,CACZC,OAAD,IAAkB;IAChBP,QAAQ,CAAEQ,GAAD,IAAS;MAChB,IAAIC,MAAM,GAAGF,OAAb,CAAA;;AAEA,MAAA,IAAI,OAAOA,OAAP,IAAkB,UAAtB,EAAkC;AAChCE,QAAAA,MAAM,GAAGF,OAAO,CAACC,GAAD,CAAhB,CAAA;AACD,OAAA;;MACD,IAAI;QACFf,YAAY,CAACiB,OAAb,CAAqBnB,GAArB,EAA0BG,IAAI,CAACiB,SAAL,CAAeF,MAAf,CAA1B,CAAA,CAAA;OADF,CAEE,MAAM,EAAE;;AAEV,MAAA,OAAOA,MAAP,CAAA;AACD,KAXO,CAAR,CAAA;AAYD,GAdY,EAeb,CAAClB,GAAD,CAfa,CAAf,CAAA;AAkBA,EAAA,OAAO,CAACQ,KAAD,EAAQM,MAAR,CAAP,CAAA;AACD;;AChDM,MAAMO,YAAY,GAAG;AAC1BC,EAAAA,UAAU,EAAE,SADc;AAE1BC,EAAAA,aAAa,EAAE,SAFW;AAG1BC,EAAAA,UAAU,EAAE,OAHc;AAI1BC,EAAAA,IAAI,EAAE,SAJoB;AAK1BC,EAAAA,OAAO,EAAE,SALiB;AAM1BC,EAAAA,oBAAoB,EAAE,MANI;AAO1BC,EAAAA,cAAc,EAAE,MAPU;AAQ1BC,EAAAA,OAAO,EAAE,SARiB;AAS1BC,EAAAA,MAAM,EAAE,SATkB;AAU1BC,EAAAA,MAAM,EAAE,SAVkB;AAW1BC,EAAAA,MAAM,EAAE,SAXkB;AAY1BC,EAAAA,OAAO,EAAE,SAAA;AAZiB,CAArB,CAAA;AAqBP,MAAMC,YAAY,gBAAGxB,KAAK,CAACyB,aAAN,CAAoBd,YAApB,CAArB,CAAA;AAEO,SAASe,aAAT,CAAuB;EAAEC,KAAF;EAAS,GAAGC,IAAAA;AAAZ,CAAvB,EAA0D;EAC/D,oBAAO,KAAA,CAAA,aAAA,CAAC,YAAD,CAAc,QAAd,EAAA,QAAA,CAAA;AAAuB,IAAA,KAAK,EAAED,KAAAA;AAA9B,GAAA,EAAyCC,IAAzC,CAAP,CAAA,CAAA;AACD,CAAA;AAEM,SAASC,QAAT,GAAoB;AACzB,EAAA,OAAO7B,KAAK,CAAC8B,UAAN,CAAiBN,YAAjB,CAAP,CAAA;AACD;;AC9Bc,SAASO,aAAT,CAAuBC,KAAvB,EAA2D;AACxE;EACA,MAAM,CAACC,OAAD,EAAUC,UAAV,IAAwBlC,KAAK,CAACC,QAAN,CAAe,MAAM;AACjD,IAAA,IAAI,OAAOkC,MAAP,KAAkB,WAAtB,EAAmC;AACjC,MAAA,OAAOA,MAAM,CAACC,UAAP,CAAkBJ,KAAlB,EAAyBK,OAAhC,CAAA;AACD,KAAA;;AACD,IAAA,OAAA;GAJ4B,CAA9B,CAFwE;;EAUxErC,KAAK,CAACE,SAAN,CAAgB,MAAM;AACpB,IAAA,IAAI,OAAOiC,MAAP,KAAkB,WAAtB,EAAmC;AACjC;MACA,MAAMG,OAAO,GAAGH,MAAM,CAACC,UAAP,CAAkBJ,KAAlB,CAAhB,CAFiC;;MAKjC,MAAMO,QAAQ,GAAG,CAAC;AAAEF,QAAAA,OAAAA;AAAF,OAAD,KACfH,UAAU,CAACG,OAAD,CADZ,CALiC;;;MASjCC,OAAO,CAACE,WAAR,CAAoBD,QAApB,CAAA,CAAA;AAEA,MAAA,OAAO,MAAM;AACX;QACAD,OAAO,CAACG,cAAR,CAAuBF,QAAvB,CAAA,CAAA;OAFF,CAAA;AAID,KAAA;;AACD,IAAA,OAAA;AACD,GAlBD,EAkBG,CAACN,OAAD,EAAUD,KAAV,EAAiBE,UAAjB,CAlBH,CAAA,CAAA;AAoBA,EAAA,OAAOD,OAAP,CAAA;AACD;;ACNM,SAASS,mBAAT,CAA6B;EAClCC,UADkC;EAElCC,aAFkC;EAGlCC,OAHkC;AAIlClB,EAAAA,KAAAA;AAJkC,CAA7B,EAUJ;AACD,EAAA,OAAOgB,UAAU,CAACG,WAAX,KAA2B,UAA3B,GACHnB,KAAK,CAACN,MADH,GAEH,CAACuB,aAAD,GACAjB,KAAK,CAACZ,IADN,GAEA4B,UAAU,CAACG,WAAX,KAA2B,QAA3B,GACAnB,KAAK,CAACL,MADN,GAEAuB,OAAO,GACPlB,KAAK,CAACJ,OADC,GAEPI,KAAK,CAACR,OARV,CAAA;AASD,CAAA;AAEM,SAAS4B,mBAAT,CAA6Bf,KAA7B,EAA2C;AAChD,EAAA,OAAOA,KAAK,CAACgB,KAAN,CAAYF,WAAZ,KAA4B,UAA5B,GACH,UADG,GAEH,CAACd,KAAK,CAACiB,iBAAN,EAAD,GACA,UADA,GAEAjB,KAAK,CAACgB,KAAN,CAAYF,WAAZ,KAA4B,QAA5B,GACA,QADA,GAEAd,KAAK,CAACa,OAAN,EACA,GAAA,OADA,GAEA,OARJ,CAAA;AASD,CAAA;AAMM,SAASK,MAAT,CACLC,IADK,EAELC,SAFK,EAGLC,OAA+B,GAAG,EAH7B,EAIL;AACA,EAAA,oBAAOrD,KAAK,CAACsD,UAAN,CACL,CAAC;IAAEC,KAAF;IAAS,GAAG3B,IAAAA;GAAb,EAAqB4B,GAArB,KAA6B;IAC3B,MAAM7B,KAAK,GAAGE,QAAQ,EAAtB,CAAA;AAEA,IAAA,MAAM4B,WAAW,GAAGC,MAAM,CAACC,OAAP,CAAeN,OAAf,CAAwBO,CAAAA,MAAxB,CAClB,CAACC,OAAD,EAAU,CAACvE,GAAD,EAAMQ,KAAN,CAAV,KAA2B;AACzB;AACA,MAAA,OAAOiC,aAAa,CAACzC,GAAD,CAAb,GACH,EACE,GAAGuE,OADL;AAEE,QAAA,IAAI,OAAO/D,KAAP,KAAiB,UAAjB,GAA8BA,KAAK,CAAC8B,IAAD,EAAOD,KAAP,CAAnC,GAAmD7B,KAAvD,CAAA;AAFF,OADG,GAKH+D,OALJ,CAAA;KAHgB,EAUlB,EAVkB,CAApB,CAAA;IAaA,oBAAO7D,KAAK,CAAC8D,aAAN,CAAoBX,IAApB,EAA0B,EAC/B,GAAGvB,IAD4B;AAE/B2B,MAAAA,KAAK,EAAE,EACL,IAAI,OAAOH,SAAP,KAAqB,UAArB,GACAA,SAAS,CAACxB,IAAD,EAAOD,KAAP,CADT,GAEAyB,SAFJ,CADK;AAIL,QAAA,GAAGG,KAJE;QAKL,GAAGE,WAAAA;OAP0B;AAS/BD,MAAAA,GAAAA;AAT+B,KAA1B,CAAP,CAAA;AAWD,GA5BI,CAAP,CAAA;AA8BD,CAAA;AAEM,SAASO,YAAT,GAAwB;AAC7B,EAAA,MAAMC,UAAU,GAAGhE,KAAK,CAACiE,MAAN,CAAa,KAAb,CAAnB,CAAA;AACA,EAAA,MAAMC,SAAS,GAAGlE,KAAK,CAACK,WAAN,CAAkB,MAAM2D,UAAU,CAACH,OAAnC,EAA4C,EAA5C,CAAlB,CAAA;EAEA7D,KAAK,CAACE,SAAN,CAAgB,MAAM;IACpB8D,UAAU,CAACH,OAAX,GAAqB,IAArB,CAAA;AACA,IAAA,OAAO,MAAM;MACXG,UAAU,CAACH,OAAX,GAAqB,KAArB,CAAA;KADF,CAAA;AAGD,GALD,EAKG,EALH,CAAA,CAAA;AAOA,EAAA,OAAOK,SAAP,CAAA;AACD,CAAA;AAED;AACA;AACA;AACA;AACA;;AACO,MAAMC,YAAY,GAAG,CAACrE,KAAD,EAAiBsE,QAAiB,GAAG,KAArC,KAA+C;EACzE,MAAM;AAAEC,IAAAA,IAAAA;AAAF,GAAA,GAAWC,SAAS,CAACC,SAAV,CAAoBzE,KAApB,CAAjB,CAAA;AAEA,EAAA,OAAOL,IAAI,CAACiB,SAAL,CAAe2D,IAAf,EAAqB,IAArB,EAA2BD,QAAQ,GAAG,CAAH,GAAOzE,SAA1C,CAAP,CAAA;AACD,CAJM;;AASP,MAAM6E,aAAa,GAAIC,CAAD,IACpBA,CAAC,CAACzB,KAAF,CAAQF,WAAR,KAAwB,MAAxB,GACI,CADJ,GAEI,CAAC2B,CAAC,CAACxB,iBAAF,EAAD,GACA,CADA,GAEAwB,CAAC,CAAC5B,OAAF,EAAA,GACA,CADA,GAEA,CAPN,CAAA;;AASA,MAAM6B,aAAqB,GAAG,CAACC,CAAD,EAAIC,CAAJ,KAAUD,CAAC,CAACE,SAAF,CAAYC,aAAZ,CAA0BF,CAAC,CAACC,SAA5B,CAAxC,CAAA;;AAEA,MAAME,QAAgB,GAAG,CAACJ,CAAD,EAAIC,CAAJ,KACvBD,CAAC,CAAC3B,KAAF,CAAQgC,aAAR,GAAwBJ,CAAC,CAAC5B,KAAF,CAAQgC,aAAhC,GAAgD,CAAhD,GAAoD,CAAC,CADvD,CAAA;;AAGA,MAAMC,iBAAyB,GAAG,CAACN,CAAD,EAAIC,CAAJ,KAAU;EAC1C,IAAIJ,aAAa,CAACG,CAAD,CAAb,KAAqBH,aAAa,CAACI,CAAD,CAAtC,EAA2C;AACzC,IAAA,OAAOG,QAAQ,CAACJ,CAAD,EAAIC,CAAJ,CAAf,CAAA;AACD,GAAA;;AAED,EAAA,OAAOJ,aAAa,CAACG,CAAD,CAAb,GAAmBH,aAAa,CAACI,CAAD,CAAhC,GAAsC,CAAtC,GAA0C,CAAC,CAAlD,CAAA;AACD,CAND,CAAA;;AAQO,MAAMM,OAA+B,GAAG;AAC7C,EAAA,uBAAA,EAAyBD,iBADoB;AAE7C,EAAA,YAAA,EAAcP,aAF+B;EAG7C,cAAgBK,EAAAA,QAAAA;AAH6B,CAAxC,CAAA;AAMA,MAAMI,YAAY,GAAG,EAArB,CAAA;AACA,MAAMC,gBAAgB,GAAG,GAAzB,CAAA;AACA,MAAMC,KAAyB,GAAG;AACvCC,EAAAA,GAAG,EAAE,QADkC;AAEvCC,EAAAA,MAAM,EAAE,KAF+B;AAGvCC,EAAAA,IAAI,EAAE,OAHiC;AAIvCC,EAAAA,KAAK,EAAE,MAAA;AAJgC,CAAlC,CAAA;;AASP;AACA;AACA;AACO,SAASC,cAAT,CAAwBC,IAAxB,EAAoC;EACzC,OAAO,CAAC,MAAD,EAAS,OAAT,EAAkBC,QAAlB,CAA2BD,IAA3B,CAAP,CAAA;AACD,CAAA;AACD;AACA;AACA;;AACO,SAASE,eAAT,CAAyBF,IAAzB,EAA2C;EAChD,OAAON,KAAK,CAACM,IAAD,CAAZ,CAAA;AACD,CAAA;AACD;AACA;AACA;AACA;;AACO,SAASG,YAAT,CAAwCC,IAAxC,EAAiDJ,IAAjD,EAA6D;AAClE,EAAA,OAAA,EAAA,GAAUI,IAAV,IACEJ,IAAI,CAACK,MAAL,CAAY,CAAZ,CAAeC,CAAAA,WAAf,KAA+BN,IAAI,CAACO,KAAL,CAAW,CAAX,CADjC,CAAA,CAAA;AAGD,CAAA;AAoCM,SAASC,iBAAT,CAA2B;AAChCC,EAAAA,QAAQ,GAAG,QADqB;EAEhCC,MAFgC;EAGhCC,KAHgC;EAIhCC,aAJgC;EAKhCC,MALgC;EAMhCC,UANgC;AAOhCC,EAAAA,UAAAA;AAPgC,CAA3B,EAQwC;AAC7C,EAAA,MAAMC,YAAY,GAAGd,eAAe,CAACO,QAAD,CAApC,CAAA;AACA,EAAA,MAAMQ,UAAU,GAAGd,YAAY,CAAC,QAAD,EAAWa,YAAX,CAA/B,CAAA;AACA,EAAA,MAAME,UAAU,GAAGnB,cAAc,CAACU,QAAD,CAAjC,CAAA;EAEA,OAAO,EACL,GAAGM,UADE;AAELI,IAAAA,SAAS,EAAE,KAFN;AAGLV,IAAAA,QAAQ,EAAE,OAHL;IAIL,CAACA,QAAD,GAAY,CAJP;AAKL,IAAA,CAACQ,UAAD,GAAA,YAAA,GAA2BL,aAAa,CAACxF,IALpC;AAMLgG,IAAAA,eAAe,EAAEJ,YANZ;AAOLK,IAAAA,SAAS,EAAE,yBAPN;AAQLC,IAAAA,MAAM,EAAE,KARH;AASL;AACAC,IAAAA,UAAU,EAAEV,MAAM,GAAG,SAAH,GAAe,QAV5B;AAWL,IAAA,IAAIC,UAAU,GACV;MACEU,UAAU,EAAA,MAAA;AADZ,KADU,GAIV;MAAEA,UAAU,EAAA,cAAA;AAAZ,KAJJ,CAXK;AAgBL,IAAA,IAAIX,MAAM,GACN;AACEY,MAAAA,OAAO,EAAE,CADX;AAEEC,MAAAA,aAAa,EAAE,KAFjB;AAGEC,MAAAA,SAAS,EAAET,UAAU,GAAA,wBAAA,GAAA,wBAAA;AAHvB,KADM,GAQN;AACEO,MAAAA,OAAO,EAAE,CADX;AAEEC,MAAAA,aAAa,EAAE,MAFjB;AAGEC,MAAAA,SAAS,EAAET,UAAU,GAAA,8BAAA,GAAA,8BAAA;AAHvB,KARJ,CAhBK;AA+BL,IAAA,IAAIA,UAAU,GACV;AACEvB,MAAAA,GAAG,EAAE,CADP;AAEEe,MAAAA,MAAM,EAAE,OAFV;AAGEkB,MAAAA,QAAQ,EAAE,KAHZ;MAIEjB,KAAK,EACH,OAAOA,KAAP,KAAiB,QAAjB,IAA6BA,KAAK,IAAInB,YAAtC,GACImB,KADJ,GAEIlB,gBAAAA;AAPR,KADU,GAUV;AACEI,MAAAA,IAAI,EAAE,CADR;AAEEc,MAAAA,KAAK,EAAE,MAFT;AAGEkB,MAAAA,SAAS,EAAE,KAHb;MAIEnB,MAAM,EACJ,OAAOA,MAAP,KAAkB,QAAlB,IAA8BA,MAAM,IAAIlB,YAAxC,GACIkB,MADJ,GAEIjB,gBAAAA;KAjBZ,CAAA;GA/BF,CAAA;AAmDD,CAAA;AAED;AACA;AACA;;AACO,SAASqC,oBAAT,CACLrB,QAAc,GAAG,QADZ,EAEgB;AACrB,EAAA,MAAMS,UAAU,GAAGnB,cAAc,CAACU,QAAD,CAAjC,CAAA;AACA,EAAA,MAAMO,YAAY,GAAGd,eAAe,CAACO,QAAD,CAApC,CAAA;AACA,EAAA,MAAMsB,UAAU,GAAG5B,YAAY,CAAC,QAAD,EAAWa,YAAX,CAA/B,CAAA;EAEA,OAAO;AACLP,IAAAA,QAAQ,EAAE,UADL;AAELuB,IAAAA,MAAM,EAAEd,UAAU,GAAG,YAAH,GAAkB,YAF/B;AAGLI,IAAAA,MAAM,EAAE,MAHH;IAIL,CAACN,YAAD,GAAgB,CAJX;AAKL,IAAA,CAACe,UAAD,GALK,MAAA;AAML,IAAA,IAAIb,UAAU,GACV;AACEvB,MAAAA,GAAG,EAAE,CADP;AAEEe,MAAAA,MAAM,EAAE,MAFV;AAGEC,MAAAA,KAAK,EAAE,KAAA;AAHT,KADU,GAMV;AACEA,MAAAA,KAAK,EAAE,MADT;AAEED,MAAAA,MAAM,EAAE,KAAA;KARd,CAAA;GANF,CAAA;AAiBD;;AC5TM,MAAMuB,KAAK,GAAG1E,MAAM,CACzB,KADyB,EAEzB,CAAC2E,MAAD,EAASlG,KAAT,MAAoB;AAClBmG,EAAAA,QAAQ,EAAE,0BADQ;AAElBC,EAAAA,UAAU,EAFQ,YAAA;AAGlBC,EAAAA,OAAO,EAAE,MAHS;EAIlBC,eAAe,EAAEtG,KAAK,CAACf,UAJL;EAKlBsH,KAAK,EAAEvG,KAAK,CAACb,UAAAA;AALK,CAApB,CAFyB,EASzB;EACE,oBAAsB,EAAA;AACpBqH,IAAAA,aAAa,EAAE,QAAA;GAFnB;EAIE,oBAAsB,EAAA;IACpBL,QAAQ,EAAE,MADU;;AAAA,GAAA;AAJxB,CATyB,CAApB,CAAA;AAoBA,MAAMM,gBAAgB,GAAGlF,MAAM,CACpC,KADoC,EAEpC,OAAO;AACLmF,EAAAA,IAAI,EAAE,WADD;AAELL,EAAAA,OAAO,EAAE,MAFJ;AAGLG,EAAAA,aAAa,EAAE,QAHV;AAILG,EAAAA,QAAQ,EAAE,MAJL;AAKLjC,EAAAA,MAAM,EAAE,MAAA;AALH,CAAP,CAFoC,EASpC;AACE,EAAA,oBAAA,EAAsB,CAACwB,MAAD,EAASlG,KAAT,MAAoB;IACxC4G,SAAS,EAAA,YAAA,GAAe5G,KAAK,CAACZ,IAAAA;GADV,CAAA;AADxB,CAToC,CAA/B,CAAA;AAgBA,MAAMyH,MAAM,GAAGtF,MAAM,CAAC,QAAD,EAAW,CAACuF,KAAD,EAAQ9G,KAAR,MAAmB;AACxD+G,EAAAA,UAAU,EAAE,MAD4C;AAExDZ,EAAAA,QAAQ,EAAE,MAF8C;AAGxDa,EAAAA,UAAU,EAAE,MAH4C;EAIxD/H,UAAU,EAAEe,KAAK,CAACZ,IAJsC;AAKxD6H,EAAAA,MAAM,EAAE,GALgD;AAMxDC,EAAAA,YAAY,EAAE,MAN0C;AAOxDX,EAAAA,KAAK,EAAE,OAPiD;AAQxDY,EAAAA,OAAO,EAAE,MAR+C;AASxD1B,EAAAA,OAAO,EAAEqB,KAAK,CAACM,QAAN,GAAiB,IAAjB,GAAwBpJ,SATuB;AAUxDgI,EAAAA,MAAM,EAAE,SAAA;AAVgD,CAAnB,CAAX,CAArB,CAAA;AAaA,MAAMqB,SAAS,GAAG9F,MAAM,CAAC,MAAD,EAAS;AACtC8E,EAAAA,OAAO,EAAE,MAD6B;AAEtCiB,EAAAA,QAAQ,EAAE,MAF4B;AAGtCC,EAAAA,GAAG,EAAE,OAHiC;AAItCpB,EAAAA,QAAQ,EAAE,OAAA;AAJ4B,CAAT,CAAxB,CAAA;AAOA,MAAMqB,QAAQ,GAAGjG,MAAM,CAAC,MAAD,EAAS;AACrC8E,EAAAA,OAAO,EAAE,aAD4B;AAErCoB,EAAAA,UAAU,EAAE,QAFyB;AAGrCN,EAAAA,OAAO,EAAE,WAH4B;AAIrCH,EAAAA,UAAU,EAAE,MAJyB;AAKrCU,EAAAA,UAAU,EAAE,gBALyB;AAMrCR,EAAAA,YAAY,EAAE,MAAA;AANuB,CAAT,CAAvB,CAAA;AASA,MAAMS,IAAI,GAAGpG,MAAM,CAAC,MAAD,EAAS;AACjC4E,EAAAA,QAAQ,EAAE,MADuB;AAEjCI,EAAAA,KAAK,EAAE,SAF0B;AAGjCtH,EAAAA,UAAU,EAAE,SAAA;AAHqB,CAAT,CAAnB,CAAA;AAMA,MAAM2I,KAAK,GAAGrG,MAAM,CAAC,OAAD,EAAU,CAAC2E,MAAD,EAASlG,KAAT,MAAoB;EACvDsG,eAAe,EAAEtG,KAAK,CAACV,oBADgC;AAEvD2H,EAAAA,MAAM,EAAE,CAF+C;AAGvDC,EAAAA,YAAY,EAAE,MAHyC;EAIvDX,KAAK,EAAEvG,KAAK,CAACT,cAJ0C;AAKvD4G,EAAAA,QAAQ,EAAE,MAL6C;AAMvD0B,EAAAA,UAAU,EAN6C,KAAA;AAOvDV,EAAAA,OAAO,EAAE,WAAA;AAP8C,CAApB,CAAV,CAApB,CAAA;AAUA,MAAMW,MAAM,GAAGvG,MAAM,CAC1B,QAD0B,EAE1B,CAAC2E,MAAD,EAASlG,KAAT,MAAoB;AAClBqG,EAAAA,OAAO,EADW,cAAA;AAElBF,EAAAA,QAAQ,EAFU,MAAA;AAGlBC,EAAAA,UAAU,EAHQ,YAAA;AAIlBY,EAAAA,UAAU,EAAE,QAJM;AAKlBa,EAAAA,UAAU,EALQ,KAAA;AAMlBV,EAAAA,OAAO,EANW,sBAAA;AAOlBzC,EAAAA,MAAM,EAAE,MAPU;AAQlBuC,EAAAA,MAAM,EAAE,CARU;AASlBC,EAAAA,YAAY,EATM,MAAA;AAUlBH,EAAAA,UAAU,EAVQ,MAAA;AAWlBgB,EAAAA,gBAAgB,EAAE,MAXA;EAYlBzB,eAAe,EAAEtG,KAAK,CAACV,oBAZL;AAalB0I,EAAAA,eAAe,EAbG,gKAAA;AAclBC,EAAAA,gBAAgB,EAdE,WAAA;AAelBC,EAAAA,kBAAkB,EAfA,oBAAA;AAgBlBC,EAAAA,cAAc,EAhBI,kBAAA;EAiBlB5B,KAAK,EAAEvG,KAAK,CAACT,cAAAA;AAjBK,CAApB,CAF0B,EAqB1B;EACE,oBAAsB,EAAA;AACpB8G,IAAAA,OAAO,EAAE,MAAA;AADW,GAAA;AADxB,CArB0B,CAArB;;ACjFQ,SAAS+B,YAAT,CAAsB;AAAEC,EAAAA,IAAAA;AAAF,CAAtB,EAAkD;EAC/D,oBACE,KAAA,CAAA,aAAA,CAAA,MAAA,EAAA;AACE,IAAA,KAAK,EAAE;AACL5D,MAAAA,QAAQ,EAAE,UADL;AAELE,MAAAA,KAAK,EAAE,OAFF;AAGLD,MAAAA,MAAM,EAAE,OAHH;AAILiC,MAAAA,QAAQ,EAAE,QAAA;AAJL,KAAA;AADT,GAAA,EAQG0B,IARH,CADF,CAAA;AAYD;;ACTM,MAAMC,KAAK,GAAG/G,MAAM,CAAC,KAAD,EAAQ;AACjC6E,EAAAA,UAAU,EAAE,kBADqB;AAEjCD,EAAAA,QAAQ,EAAE,KAFuB;AAGjC0B,EAAAA,UAAU,EAAE,KAHqB;AAIjCU,EAAAA,OAAO,EAAE,MAJwB;AAKjCC,EAAAA,SAAS,EAAE,YAAA;AALsB,CAAR,CAApB,CAAA;AAQA,MAAMC,KAAK,GAAGlH,MAAM,CAAC,MAAD,EAAS;AAClCgF,EAAAA,KAAK,EAAE,OAAA;AAD2B,CAAT,CAApB,CAAA;AAIA,MAAMmC,WAAW,GAAGnH,MAAM,CAAC,QAAD,EAAW;AAC1CyE,EAAAA,MAAM,EAAE,SADkC;AAE1CO,EAAAA,KAAK,EAAE,OAAA;AAFmC,CAAX,CAA1B,CAAA;AAKA,MAAMoC,YAAY,GAAGpH,MAAM,CAAC,QAAD,EAAW;AAC3CyE,EAAAA,MAAM,EAAE,SADmC;AAE3CO,EAAAA,KAAK,EAAE,SAFoC;AAG3CqC,EAAAA,IAAI,EAAE,SAHqC;AAI3CL,EAAAA,OAAO,EAAE,SAJkC;AAK3CtJ,EAAAA,UAAU,EAAE,aAL+B;AAM3CgI,EAAAA,MAAM,EAAE,MANmC;AAO3CE,EAAAA,OAAO,EAAE,CAAA;AAPkC,CAAX,CAA3B,CAAA;AAYA,MAAM0B,UAAU,GAAG,CAAC;AAAE1K,EAAAA,KAAAA;AAAF,CAAD,KAAmC;EAC3D,MAAM,CAAC2K,SAAD,EAAYC,YAAZ,CAAA,GAA4B1K,KAAK,CAACC,QAAN,CAA0B,QAA1B,CAAlC,CAAA;EAEA,oBACE,KAAA,CAAA,aAAA,CAAA,QAAA,EAAA;AACE,IAAA,OAAO,EACLwK,SAAS,KAAK,QAAd,GACI,MAAM;AACJE,MAAAA,SAAS,CAACC,SAAV,CAAoBC,SAApB,CAA8BC,SAAS,CAACpK,SAAV,CAAoBZ,KAApB,CAA9B,CAA0DiL,CAAAA,IAA1D,CACE,MAAM;QACJL,YAAY,CAAC,aAAD,CAAZ,CAAA;AACAM,QAAAA,UAAU,CAAC,MAAM;UACfN,YAAY,CAAC,QAAD,CAAZ,CAAA;SADQ,EAEP,IAFO,CAAV,CAAA;OAHJ,EAOGO,GAAD,IAAS;AACPC,QAAAA,OAAO,CAACC,KAAR,CAAc,kBAAd,EAAkCF,GAAlC,CAAA,CAAA;QACAP,YAAY,CAAC,WAAD,CAAZ,CAAA;AACAM,QAAAA,UAAU,CAAC,MAAM;UACfN,YAAY,CAAC,QAAD,CAAZ,CAAA;SADQ,EAEP,IAFO,CAAV,CAAA;OAVJ,CAAA,CAAA;AAeD,KAjBL,GAkBI/K,SApBR;AAsBE,IAAA,KAAK,EAAE;AACLgI,MAAAA,MAAM,EAAE,SADH;AAELO,MAAAA,KAAK,EAAE,SAFF;AAGLqC,MAAAA,IAAI,EAAE,SAHD;AAILL,MAAAA,OAAO,EAAE,SAJJ;AAKLtJ,MAAAA,UAAU,EAAE,aALP;AAMLgI,MAAAA,MAAM,EAAE,MANH;AAOLE,MAAAA,OAAO,EAAE,CAAA;AAPJ,KAAA;AAtBT,GAAA,EAgCG2B,SAAS,KAAK,QAAd,gBACC,KAAC,CAAA,aAAA,CAAA,MAAD,OADD,GAEGA,SAAS,KAAK,aAAd,gBACF,oBAAC,YAAD,EAAA,IAAA,CADE,gBAGF,KAAC,CAAA,aAAA,CAAA,WAAD,OArCJ,CADF,CAAA;AA0CD,CA7CM,CAAA;AA+CA,MAAMW,KAAK,GAAGlI,MAAM,CAAC,MAAD,EAAS,CAAC2E,MAAD,EAASlG,KAAT,MAAoB;EACtDuG,KAAK,EAAEvG,KAAK,CAACP,MAAAA;AADyC,CAApB,CAAT,CAApB,CAAA;AAIA,MAAMiK,UAAU,GAAGnI,MAAM,CAAC,KAAD,EAAQ;AACtCoI,EAAAA,UAAU,EAAE,MAD0B;AAEtCC,EAAAA,WAAW,EAAE,KAFyB;AAGtCC,EAAAA,UAAU,EAAE,2BAAA;AAH0B,CAAR,CAAzB,CAAA;AAMA,MAAMC,IAAI,GAAGvI,MAAM,CAAC,MAAD,EAAS;AACjCgF,EAAAA,KAAK,EAAE,MAD0B;AAEjCJ,EAAAA,QAAQ,EAAE,MAAA;AAFuB,CAAT,CAAnB,CAAA;AAUA,MAAM4D,QAAQ,GAAG,CAAC;EAAEC,QAAF;AAAYpI,EAAAA,KAAK,GAAG,EAAA;AAApB,CAAD,kBACtB,KAAA,CAAA,aAAA,CAAA,MAAA,EAAA;AACE,EAAA,KAAK,EAAE;AACLyE,IAAAA,OAAO,EAAE,cADJ;AAELb,IAAAA,UAAU,EAAE,cAFP;AAGLG,IAAAA,SAAS,EAAYqE,SAAAA,IAAAA,QAAQ,GAAG,EAAH,GAAQ,CAA5B,CAAqCpI,GAAAA,OAAAA,IAAAA,KAAK,CAAC+D,SAAN,IAAmB,EAAxD,CAHJ;IAIL,GAAG/D,KAAAA;AAJE,GAAA;AADT,CADK,EAAA,QAAA,CAAA,CAAA;;AAaP,MAAMqI,MAAM,GAAG,mBACb,KAAA,CAAA,aAAA,CAAA,MAAA,EAAA;AACE,EAAA,YAAA,EAAW,0BADb;AAEE,EAAA,KAAK,EAAC,0BAFR;AAGE,EAAA,KAAK,EAAE;AACLL,IAAAA,WAAW,EAAE,KAAA;AADR,GAAA;AAHT,CAOE,eAAA,KAAA,CAAA,aAAA,CAAA,KAAA,EAAA;AAAK,EAAA,MAAM,EAAC,IAAZ;AAAiB,EAAA,OAAO,EAAC,WAAzB;AAAqC,EAAA,KAAK,EAAC,IAAA;AAA3C,CACE,eAAA,KAAA,CAAA,aAAA,CAAA,MAAA,EAAA;AACE,EAAA,IAAI,EAAC,cADP;AAEE,EAAA,CAAC,EAAC,2MAAA;AAFJ,CAAA,CADF,eAKE,KAAA,CAAA,aAAA,CAAA,MAAA,EAAA;AACE,EAAA,IAAI,EAAC,cADP;AAEE,EAAA,CAAC,EAAC,iOAAA;AAFJ,CAAA,CALF,CAPF,CADF,CAAA;;AAqBA,MAAMM,WAAW,GAAG,mBAClB,KAAA,CAAA,aAAA,CAAA,MAAA,EAAA;AACE,EAAA,YAAA,EAAW,6BADb;AAEE,EAAA,KAAK,EAAC,6BAFR;AAGE,EAAA,KAAK,EAAE;AACLN,IAAAA,WAAW,EAAE,KADR;AAELvD,IAAAA,OAAO,EAAE,MAFJ;AAGLoB,IAAAA,UAAU,EAAE,QAAA;AAHP,GAAA;AAHT,CASE,eAAA,KAAA,CAAA,aAAA,CAAA,KAAA,EAAA;AAAK,EAAA,MAAM,EAAC,IAAZ;AAAiB,EAAA,OAAO,EAAC,WAAzB;AAAqC,EAAA,KAAK,EAAC,IAA3C;AAAgD,EAAA,OAAO,EAAC,OAAA;AAAxD,CACE,eAAA,KAAA,CAAA,aAAA,CAAA,MAAA,EAAA;AACE,EAAA,IAAI,EAAC,KADP;AAEE,EAAA,CAAC,EAAC,uLAAA;AAFJ,CAAA,CADF,CATF,eAeE,KAAA,CAAA,aAAA,CAAA,MAAA,EAAA;AACE,EAAA,KAAK,EAAE;AACLlB,IAAAA,KAAK,EAAE,KADF;AAELJ,IAAAA,QAAQ,EAAE,MAFL;AAGLyD,IAAAA,WAAW,EAAE,KAHR;AAILnF,IAAAA,QAAQ,EAAE,UAJL;AAKLd,IAAAA,GAAG,EAAE,KAAA;AALA,GAAA;AADT,CAAA,EAAA,aAAA,CAfF,CADF,CAAA;;AA8BA,MAAMwG,YAAY,GAAG,mBACnB,KAAA,CAAA,aAAA,CAAA,MAAA,EAAA;AACE,EAAA,YAAA,EAAW,4BADb;AAEE,EAAA,KAAK,EAAC,4BAFR;AAGE,EAAA,KAAK,EAAE;AACLP,IAAAA,WAAW,EAAE,KADR;AAELvD,IAAAA,OAAO,EAAE,cAFJ;AAGL+D,IAAAA,aAAa,EAAE,QAAA;AAHV,GAAA;AAHT,CASE,eAAA,KAAA,CAAA,aAAA,CAAA,KAAA,EAAA;AAAK,EAAA,MAAM,EAAC,IAAZ;AAAiB,EAAA,OAAO,EAAC,WAAzB;AAAqC,EAAA,KAAK,EAAC,IAA3C;AAAgD,EAAA,OAAO,EAAC,OAAA;AAAxD,CACE,eAAA,KAAA,CAAA,aAAA,CAAA,MAAA,EAAA;AACE,EAAA,IAAI,EAAC,OADP;AAEE,EAAA,CAAC,EAAC,iIAAA;AAFJ,CAAA,CADF,CATF,CADF,CAAA;;AAoCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,UAAT,CAAuBC,KAAvB,EAAmCC,IAAnC,EAAwD;AAC7D,EAAA,IAAIA,IAAI,GAAG,CAAX,EAAc,OAAO,EAAP,CAAA;EACd,IAAIC,CAAC,GAAG,CAAR,CAAA;EACA,MAAMC,MAAa,GAAG,EAAtB,CAAA;;AACA,EAAA,OAAOD,CAAC,GAAGF,KAAK,CAACI,MAAjB,EAAyB;AACvBD,IAAAA,MAAM,CAACE,IAAP,CAAYL,KAAK,CAAC/F,KAAN,CAAYiG,CAAZ,EAAeA,CAAC,GAAGD,IAAnB,CAAZ,CAAA,CAAA;IACAC,CAAC,GAAGA,CAAC,GAAGD,IAAR,CAAA;AACD,GAAA;;AACD,EAAA,OAAOE,MAAP,CAAA;AACD,CAAA;AAIM,MAAMG,eAAyB,GAAG,CAAC;EACxCC,WADwC;EAExCC,KAFwC;EAGxC3M,KAHwC;AAIxC4M,EAAAA,UAAU,GAAG,EAJ2B;AAKxCC,EAAAA,aAAa,GAAG,EALwB;EAMxCxJ,IANwC;AAOxCwI,EAAAA,QAAQ,GAAG,KAP6B;AAQxCiB,EAAAA,QAAQ,GAAG,KAR6B;EASxCC,cATwC;AAUxCC,EAAAA,QAAAA;AAVwC,CAAD,KAWnC;EACJ,MAAM,CAACC,aAAD,EAAgBC,gBAAhB,CAAA,GAAoChN,KAAK,CAACC,QAAN,CAAyB,EAAzB,CAA1C,CAAA;AAEA,EAAA,oBACE,oBAAC,KAAD,EAAA;AAAO,IAAA,GAAG,EAAEwM,KAAAA;AAAZ,GAAA,EACGE,aAAa,CAACN,MAAd,gBACC,KAAA,CAAA,aAAA,CAAA,KAAA,CAAA,QAAA,EAAA,IAAA,eACE,oBAAC,YAAD,EAAA;IAAc,OAAO,EAAE,MAAMQ,cAAc,EAAA;AAA3C,GAAA,eACE,oBAAC,QAAD,EAAA;AAAU,IAAA,QAAQ,EAAElB,QAAAA;AAApB,GAAA,CADF,OACoCc,KADpC,EAC2C,GAD3C,eAEE,oBAAC,IAAD,EAAA,IAAA,EACGQ,MAAM,CAAC9J,IAAD,CAAN,CAAa+J,WAAb,EAAA,KAA+B,UAA/B,GAA4C,aAA5C,GAA4D,EAD/D,EAEGR,UAAU,CAACL,MAFd,EAAA,GAAA,EAEuBK,UAAU,CAACL,MAAX,GAAoB,CAApB,mBAFvB,CAFF,CADF,EAQGO,QAAQ,gBAAG,oBAAC,UAAD,EAAA;AAAY,IAAA,KAAK,EAAE9M,KAAAA;AAAnB,GAAA,CAAH,GAAkC,IAR7C,EASG6L,QAAQ,GACPgB,aAAa,CAACN,MAAd,KAAyB,CAAzB,gBACE,KAAC,CAAA,aAAA,CAAA,UAAD,QAAaK,UAAU,CAACS,GAAX,CAAeX,WAAf,CAAb,CADF,gBAGE,oBAAC,UAAD,EAAA,IAAA,EACGG,aAAa,CAACQ,GAAd,CAAkB,CAACxJ,OAAD,EAAUyJ,KAAV,kBACjB,KAAA,CAAA,aAAA,CAAA,KAAA,EAAA;AAAK,IAAA,GAAG,EAAEA,KAAAA;AAAV,GAAA,eACE,KAAC,CAAA,aAAA,CAAA,KAAD,EACE,IAAA,eAAA,KAAA,CAAA,aAAA,CAAC,WAAD,EAAA;AACE,IAAA,OAAO,EAAE,MACPJ,gBAAgB,CAAEzM,GAAD,IACfA,GAAG,CAACqF,QAAJ,CAAawH,KAAb,CAAA,GACI7M,GAAG,CAAC8M,MAAJ,CAAYC,CAAD,IAAOA,CAAC,KAAKF,KAAxB,CADJ,GAEI,CAAC,GAAG7M,GAAJ,EAAS6M,KAAT,CAHU,CAAA;AAFpB,GAAA,eASE,oBAAC,QAAD,EAAA;AAAU,IAAA,QAAQ,EAAEzB,QAAAA;GATtB,CAAA,EAAA,IAAA,EASqCyB,KAAK,GAAGN,QAT7C,EAAA,MAAA,EAS2D,GAT3D,EAUGM,KAAK,GAAGN,QAAR,GAAmBA,QAAnB,GAA8B,CAVjC,EAAA,GAAA,CADF,EAaGC,aAAa,CAACnH,QAAd,CAAuBwH,KAAvB,CACC,gBAAA,KAAA,CAAA,aAAA,CAAC,UAAD,EAAA,IAAA,EAAazJ,OAAO,CAACwJ,GAAR,CAAYX,WAAZ,CAAb,CADD,GAEG,IAfN,CADF,CADD,CADH,CAJK,GA4BL,IArCN,CADD,gBAyCC,KACE,CAAA,aAAA,CAAA,KAAA,CAAA,QAAA,EAAA,IAAA,eAAA,KAAA,CAAA,aAAA,CAAC,KAAD,EAAA,IAAA,EAAQC,KAAR,EAAA,GAAA,CADF,EAC0B,GAAA,eAAA,KAAA,CAAA,aAAA,CAAC,KAAD,EAAA,IAAA,EAAQtI,YAAY,CAACrE,KAAD,CAApB,CAD1B,CA1CJ,CADF,CAAA;AAiDD,CA/DM,CAAA;;AA6EP,SAASyN,UAAT,CAAoBC,CAApB,EAAoD;AAClD,EAAA,OAAOC,MAAM,CAACC,QAAP,IAAmBF,CAA1B,CAAA;AACD,CAAA;;AAEc,SAASG,QAAT,CAAkB;EAC/B7N,KAD+B;EAE/B8N,eAF+B;AAG/BC,EAAAA,QAAQ,GAAGtB,eAHoB;AAI/BO,EAAAA,QAAQ,GAAG,GAJoB;AAK/BF,EAAAA,QAAQ,GAAG,KALoB;EAM/B,GAAGhL,IAAAA;AAN4B,CAAlB,EAOG;AAChB,EAAA,MAAM,CAAC+J,QAAD,EAAWmC,WAAX,CAA0B9N,GAAAA,KAAK,CAACC,QAAN,CAAe8N,OAAO,CAACH,eAAD,CAAtB,CAAhC,CAAA;AACA,EAAA,MAAMf,cAAc,GAAG7M,KAAK,CAACK,WAAN,CAAkB,MAAMyN,WAAW,CAAEvN,GAAD,IAAS,CAACA,GAAX,CAAnC,EAAoD,EAApD,CAAvB,CAAA;EAEA,IAAI4C,IAAY,GAAG,OAAOrD,KAA1B,CAAA;EACA,IAAI4M,UAAsB,GAAG,EAA7B,CAAA;;EAEA,MAAMsB,YAAY,GAAIC,GAAD,IAAsD;AACzE,IAAA,MAAMC,kBAAkB,GACtBN,eAAe,KAAK,IAApB,GACI;MAAE,CAACK,GAAG,CAACxB,KAAL,GAAa,IAAA;KADnB,GAEImB,eAFJ,IAEIA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,eAAe,CAAGK,GAAG,CAACxB,KAAP,CAHrB,CAAA;IAIA,OAAO,EACL,GAAGwB,GADE;AAELL,MAAAA,eAAe,EAAEM,kBAAAA;KAFnB,CAAA;GALF,CAAA;;AAWA,EAAA,IAAIC,KAAK,CAACC,OAAN,CAActO,KAAd,CAAJ,EAA0B;AACxBqD,IAAAA,IAAI,GAAG,OAAP,CAAA;IACAuJ,UAAU,GAAG5M,KAAK,CAACqN,GAAN,CAAU,CAACG,CAAD,EAAInB,CAAJ,KACrB6B,YAAY,CAAC;AACXvB,MAAAA,KAAK,EAAEN,CAAC,CAACkC,QAAF,EADI;AAEXvO,MAAAA,KAAK,EAAEwN,CAAAA;AAFI,KAAD,CADD,CAAb,CAAA;GAFF,MAQO,IACLxN,KAAK,KAAK,IAAV,IACA,OAAOA,KAAP,KAAiB,QADjB,IAEAyN,UAAU,CAACzN,KAAD,CAFV,IAGA,OAAOA,KAAK,CAAC2N,MAAM,CAACC,QAAR,CAAZ,KAAkC,UAJ7B,EAKL;AACAvK,IAAAA,IAAI,GAAG,UAAP,CAAA;AACAuJ,IAAAA,UAAU,GAAGyB,KAAK,CAACG,IAAN,CAAWxO,KAAX,EAAkB,CAACyO,GAAD,EAAMpC,CAAN,KAC7B6B,YAAY,CAAC;AACXvB,MAAAA,KAAK,EAAEN,CAAC,CAACkC,QAAF,EADI;AAEXvO,MAAAA,KAAK,EAAEyO,GAAAA;AAFI,KAAD,CADD,CAAb,CAAA;GAPK,MAaA,IAAI,OAAOzO,KAAP,KAAiB,QAAjB,IAA6BA,KAAK,KAAK,IAA3C,EAAiD;AACtDqD,IAAAA,IAAI,GAAG,QAAP,CAAA;AACAuJ,IAAAA,UAAU,GAAGhJ,MAAM,CAACC,OAAP,CAAe7D,KAAf,CAAsBqN,CAAAA,GAAtB,CAA0B,CAAC,CAAC7N,GAAD,EAAMiP,GAAN,CAAD,KACrCP,YAAY,CAAC;AACXvB,MAAAA,KAAK,EAAEnN,GADI;AAEXQ,MAAAA,KAAK,EAAEyO,GAAAA;AAFI,KAAD,CADD,CAAb,CAAA;AAMD,GAAA;;AAED,EAAA,MAAM5B,aAAa,GAAGX,UAAU,CAACU,UAAD,EAAaI,QAAb,CAAhC,CAAA;AAEA,EAAA,OAAOe,QAAQ,CAAC;AACdrB,IAAAA,WAAW,EAAGgC,KAAD,iBACX,KAAA,CAAA,aAAA,CAAC,QAAD,EAAA,QAAA,CAAA;MACE,GAAG,EAAEA,KAAK,CAAC/B,KADb;AAEE,MAAA,KAAK,EAAE3M,KAFT;AAGE,MAAA,QAAQ,EAAE+N,QAHZ;AAIE,MAAA,QAAQ,EAAEjB,QAAAA;KACNhL,EAAAA,IALN,EAMM4M,KANN,CAFY,CAAA;IAWdrL,IAXc;IAYduJ,UAZc;IAadC,aAbc;IAcd7M,KAdc;IAed6L,QAfc;IAgBdiB,QAhBc;IAiBdC,cAjBc;IAkBdC,QAlBc;IAmBd,GAAGlL,IAAAA;AAnBW,GAAD,CAAf,CAAA;AAqBD;;AC9Xc,SAAS6M,IAAT,CAAchG,KAAd,EAA0B;EACvC,oBACE,KAAA,CAAA,aAAA,CAAA,KAAA,EAAA,QAAA,CAAA;AACE,IAAA,KAAK,EAAC,MADR;AAEE,IAAA,MAAM,EAAC,MAFT;AAGE,IAAA,OAAO,EAAC,aAHV;AAIE,IAAA,OAAO,EAAC,KAAA;AAJV,GAAA,EAKMA,KALN,CAOE,eAAA,KAAA,CAAA,aAAA,CAAA,GAAA,EAAA;AAAG,IAAA,MAAM,EAAC,MAAV;AAAiB,IAAA,WAAW,EAAC,GAA7B;AAAiC,IAAA,IAAI,EAAC,MAAtC;AAA6C,IAAA,QAAQ,EAAC,SAAA;GACpD,eAAA,KAAA,CAAA,aAAA,CAAA,GAAA,EAAA;AAAG,IAAA,SAAS,EAAC,iCAAA;GACX,eAAA,KAAA,CAAA,aAAA,CAAA,MAAA,EAAA;AACE,IAAA,CAAC,EAAC,02EADJ;AAEE,IAAA,IAAI,EAAC,SAFP;AAGE,IAAA,QAAQ,EAAC,SAHX;AAIE,IAAA,SAAS,EAAC,mFAAA;AAJZ,GAAA,CADF,eAOE,KAAA,CAAA,aAAA,CAAA,MAAA,EAAA;AACE,IAAA,CAAC,EAAC,qwFADJ;AAEE,IAAA,IAAI,EAAC,SAAA;AAFP,GAAA,CAPF,eAWE,KAAA,CAAA,aAAA,CAAA,MAAA,EAAA;AACE,IAAA,CAAC,EAAC,m0GADJ;AAEE,IAAA,IAAI,EAAC,SAAA;GAbT,CAAA,CADF,CAPF,CADF,CAAA;AA4BD;;ACsHM,SAASiG,oBAAT,CAA4B;EACjCC,aADiC;AAEjCC,EAAAA,UAAU,GAAG,EAFoB;AAGjCC,EAAAA,gBAAgB,GAAG,EAHc;AAIjCC,EAAAA,iBAAiB,GAAG,EAJa;AAKjC1I,EAAAA,QAAQ,GAAG,aALsB;EAMjC2I,gBAAgB,EAAEC,SAAS,GAAG,OANG;EAOjCC,OAPiC;EAQjCC,UARiC;EASjCC,aAAa,EAAEC,oBAAoB,GAAG,QATL;AAUjCC,EAAAA,UAAU,GAAG,EAAA;AAVoB,CAA5B,EAWwC;AAC7C,EAAA,MAAMC,OAAO,GAAGtP,KAAK,CAACiE,MAAN,CAA6B,IAA7B,CAAhB,CAAA;AACA,EAAA,MAAMsL,QAAQ,GAAGvP,KAAK,CAACiE,MAAN,CAA6B,IAA7B,CAAjB,CAAA;EACA,MAAM,CAACuC,MAAD,EAASgJ,SAAT,CAAA,GAAsB5P,eAAe,CACzC,wBADyC,EAEzC+O,aAFyC,CAA3C,CAAA;EAIA,MAAM,CAACc,cAAD,EAAiBC,iBAAjB,CAAA,GAAsC9P,eAAe,CACzD,0BADyD,EAEzDwF,gBAFyD,CAA3D,CAAA;EAIA,MAAM,CAACuK,aAAD,EAAgBC,gBAAhB,CAAA,GAAoChQ,eAAe,CACvD,yBADuD,EAEvDwF,gBAFuD,CAAzD,CAAA;AAKA,EAAA,MAAM,CAAC+J,aAAa,GAAG,QAAjB,EAA2BU,gBAA3B,CAA+CjQ,GAAAA,eAAe,CAClE,iCADkE,EAElEwP,oBAFkE,CAApE,CAAA;EAKA,MAAM,CAACU,cAAD,EAAiBC,iBAAjB,CAAA,GAAsC/P,KAAK,CAACC,QAAN,CAAe,KAAf,CAA5C,CAAA;EACA,MAAM,CAACwG,UAAD,EAAauJ,aAAb,CAAA,GAA8BhQ,KAAK,CAACC,QAAN,CAAe,KAAf,CAApC,CAAA;EACA,MAAMiE,SAAS,GAAGH,YAAY,EAA9B,CAAA;;AAEA,EAAA,MAAMkM,eAAe,GAAG,CACtBC,YADsB,EAEtBC,UAFsB,KAGnB;IACH,IAAI,CAACD,YAAL,EAAmB,OAAA;AACnB,IAAA,IAAIC,UAAU,CAACC,MAAX,KAAsB,CAA1B,EAA6B,OAF1B;;AAGH,IAAA,MAAMvJ,UAAU,GAAGnB,cAAc,CAACyJ,aAAD,CAAjC,CAAA;IACAa,aAAa,CAAC,IAAD,CAAb,CAAA;IAEA,MAAM;MAAE3J,MAAF;AAAUC,MAAAA,KAAAA;KAAU4J,GAAAA,YAAY,CAACG,qBAAb,EAA1B,CAAA;AACA,IAAA,MAAMC,MAAM,GAAGH,UAAU,CAACI,OAA1B,CAAA;AACA,IAAA,MAAMC,MAAM,GAAGL,UAAU,CAACM,OAA1B,CAAA;IACA,IAAIC,OAAO,GAAG,CAAd,CAAA;;IAEA,MAAMC,GAAG,GAAIC,SAAD,IAA2B;AACrC;MACAA,SAAS,CAACC,cAAV,EAAA,CAFqC;AAKrC;;AACA,MAAA,IAAIhK,UAAJ,EAAgB;AACd6J,QAAAA,OAAO,GACLpK,KAAK,IACJ6I,aAAa,KAAK,OAAlB,GACGmB,MAAM,GAAGM,SAAS,CAACL,OADtB,GAEGK,SAAS,CAACL,OAAV,GAAoBD,MAHnB,CADP,CAAA;QAKAV,gBAAgB,CAACc,OAAD,CAAhB,CAAA;AACD,OAPD,MAOO;AACLA,QAAAA,OAAO,GACLrK,MAAM,IACL8I,aAAa,KAAK,QAAlB,GACGqB,MAAM,GAAGI,SAAS,CAACH,OADtB,GAEGG,SAAS,CAACH,OAAV,GAAoBD,MAHlB,CADR,CAAA;QAKAd,iBAAiB,CAACgB,OAAD,CAAjB,CAAA;AACD,OAAA;;MAED,IAAIA,OAAO,GAAGvL,YAAd,EAA4B;QAC1BqK,SAAS,CAAC,KAAD,CAAT,CAAA;AACD,OAFD,MAEO;QACLA,SAAS,CAAC,IAAD,CAAT,CAAA;AACD,OAAA;KA1BH,CAAA;;IA6BA,MAAMsB,KAAK,GAAG,MAAM;AAClB,MAAA,IAAIrK,UAAJ,EAAgB;QACduJ,aAAa,CAAC,KAAD,CAAb,CAAA;AACD,OAAA;;AAEDe,MAAAA,QAAQ,CAACC,mBAAT,CAA6B,WAA7B,EAA0CL,GAA1C,EAA+C,KAA/C,CAAA,CAAA;AACAI,MAAAA,QAAQ,CAACC,mBAAT,CAA6B,SAA7B,EAAwCF,KAAxC,EAA+C,KAA/C,CAAA,CAAA;KANF,CAAA;;AASAC,IAAAA,QAAQ,CAACE,gBAAT,CAA0B,WAA1B,EAAuCN,GAAvC,EAA4C,KAA5C,CAAA,CAAA;AACAI,IAAAA,QAAQ,CAACE,gBAAT,CAA0B,SAA1B,EAAqCH,KAArC,EAA4C,KAA5C,CAAA,CAAA;GArDF,CAAA;;EAwDA9Q,KAAK,CAACE,SAAN,CAAgB,MAAM;AACpB6P,IAAAA,iBAAiB,CAACvJ,MAAD,IAAA,IAAA,GAACA,MAAD,GAAW,KAAX,CAAjB,CAAA;GADF,EAEG,CAACA,MAAD,EAASsJ,cAAT,EAAyBC,iBAAzB,CAFH,CAAA,CAjF6C;AAsF7C;;EACA/P,KAAK,CAACE,SAAN,CAAgB,MAAM;AACpB,IAAA,MAAMsD,GAAG,GAAG+L,QAAQ,CAAC1L,OAArB,CAAA;;AACA,IAAA,IAAIL,GAAJ,EAAS;MACP,MAAM0N,0BAA0B,GAAG,MAAM;AACvC,QAAA,IAAIpB,cAAJ,EAAoB;AAClBtM,UAAAA,GAAG,CAACD,KAAJ,CAAU2D,UAAV,GAAuB,SAAvB,CAAA;AACD,SAAA;OAHH,CAAA;;MAMA,MAAMiK,wBAAwB,GAAG,MAAM;QACrC,IAAI,CAACrB,cAAL,EAAqB;AACnBtM,UAAAA,GAAG,CAACD,KAAJ,CAAU2D,UAAV,GAAuB,QAAvB,CAAA;AACD,SAAA;OAHH,CAAA;;AAMA1D,MAAAA,GAAG,CAACyN,gBAAJ,CAAqB,iBAArB,EAAwCC,0BAAxC,CAAA,CAAA;AACA1N,MAAAA,GAAG,CAACyN,gBAAJ,CAAqB,eAArB,EAAsCE,wBAAtC,CAAA,CAAA;AAEA,MAAA,OAAO,MAAM;AACX3N,QAAAA,GAAG,CAACwN,mBAAJ,CAAwB,iBAAxB,EAA2CE,0BAA3C,CAAA,CAAA;AACA1N,QAAAA,GAAG,CAACwN,mBAAJ,CAAwB,eAAxB,EAAyCG,wBAAzC,CAAA,CAAA;OAFF,CAAA;AAID,KAAA;;AACD,IAAA,OAAA;GAvBF,EAwBG,CAACrB,cAAD,CAxBH,CAAA,CAAA;EA0BA9P,KAAK,CAACE,SAAN,CAAgB,MAAM;AAAA,IAAA,IAAA,gBAAA,CAAA;;IACpB,IAAI4P,cAAc,wBAAIR,OAAO,CAACzL,OAAZ,KAAI,IAAA,IAAA,gBAAA,CAAiBuN,aAAvC,EAAsD;MACpD,MAAM;AAAEA,QAAAA,aAAAA;OAAkB9B,GAAAA,OAAO,CAACzL,OAAlC,CAAA;AACA,MAAA,MAAMwN,SAAS,GAAGvL,YAAY,CAAC,SAAD,EAAYqJ,aAAZ,CAA9B,CAAA;AACA,MAAA,MAAMtI,UAAU,GAAGnB,cAAc,CAACyJ,aAAD,CAAjC,CAAA;;MAEA,MAAMmC,gBAAgB,GAAG,CAAC,CAAC;QACzBxI,OADyB;QAEzByI,UAFyB;QAGzBC,aAHyB;QAIzBjG,WAJyB;AAKzBkG,QAAAA,YAAAA;AALyB,OAAD,MAMnB;QACL3I,OADK;QAELyI,UAFK;QAGLC,aAHK;QAILjG,WAJK;AAKLkG,QAAAA,YAAAA;AALK,OANmB,CAAD,EAYrBL,aAAa,CAAC7N,KAZO,CAAzB,CAAA;;MAcA,MAAMoN,GAAG,GAAG,MAAM;AAChB;AACAS,QAAAA,aAAa,CAAC7N,KAAd,CAAoBuF,OAApB,GAA8B,KAA9B,CAAA;AACAsI,QAAAA,aAAa,CAAC7N,KAAd,CAAoBgO,UAApB,GAAiC,KAAjC,CAAA;AACAH,QAAAA,aAAa,CAAC7N,KAAd,CAAoBiO,aAApB,GAAoC,KAApC,CAAA;AACAJ,QAAAA,aAAa,CAAC7N,KAAd,CAAoBgI,WAApB,GAAkC,KAAlC,CAAA;AACA6F,QAAAA,aAAa,CAAC7N,KAAd,CAAoBkO,YAApB,GAAmC,KAAnC,CANgB;;QAShBL,aAAa,CAAC7N,KAAd,CAAoB8N,SAApB,KACExK,UAAU,GAAG8I,aAAH,GAAmBF,cAD/B,IAAA,IAAA,CAAA;OATF,CAAA;;MAcAkB,GAAG,EAAA,CAAA;;AAEH,MAAA,IAAI,OAAOxO,MAAP,KAAkB,WAAtB,EAAmC;AACjCA,QAAAA,MAAM,CAAC8O,gBAAP,CAAwB,QAAxB,EAAkCN,GAAlC,CAAA,CAAA;AAEA,QAAA,OAAO,MAAM;AACXxO,UAAAA,MAAM,CAAC6O,mBAAP,CAA2B,QAA3B,EAAqCL,GAArC,CAAA,CAAA;AACAjN,UAAAA,MAAM,CAACC,OAAP,CAAe2N,gBAAf,CAAiCI,CAAAA,OAAjC,CACE,CAAC,CAACC,QAAD,EAAWC,aAAX,CAAD,KAA+B;AAC7BR,YAAAA,aAAa,CAAC7N,KAAd,CAAoBoO,QAApB,IACEC,aADF,CAAA;WAFJ,CAAA,CAAA;SAFF,CAAA;AASD,OAAA;AACF,KAAA;;AACD,IAAA,OAAA;GAlDF,EAmDG,CAAC9B,cAAD,EAAiBX,aAAjB,EAAgCM,cAAhC,EAAgDE,aAAhD,CAnDH,CAAA,CAAA;EAqDA,MAAM;IAAEpM,KAAK,EAAEmD,UAAU,GAAG,EAAtB;IAA0B,GAAGmL,eAAAA;AAA7B,GAAA,GAAiDjD,UAAvD,CAAA;EAEA,MAAM;IACJrL,KAAK,EAAEuO,iBAAiB,GAAG,EADvB;AAEJC,IAAAA,OAAO,EAAEC,aAFL;IAGJ,GAAGC,sBAAAA;GACDnD,GAAAA,iBAJJ,CAxK6C;;EA+K7C,MAAMvL,KAAK,GAAG4C,iBAAiB,CAAC;AAC9BC,IAAAA,QAAQ,EAAE+I,aADoB;AAE9B5I,IAAAA,aAAa,EAAE5E,YAFe;AAG9B6E,IAAAA,MAAM,EAAEsJ,cAHsB;AAI9BzJ,IAAAA,MAAM,EAAEoJ,cAJsB;AAK9BnJ,IAAAA,KAAK,EAAEqJ,aALuB;IAM9BlJ,UAN8B;AAO9BC,IAAAA,UAAAA;GAP6B,CAA/B,CA/K6C;;AA0L7C,EAAA,IAAI,CAACxC,SAAS,EAAd,EAAkB,OAAO,IAAP,CAAA;AAElB,EAAA,oBACE,oBAAC,SAAD,EAAA;AACE,IAAA,GAAG,EAAEoL,OADP;AAEE,IAAA,SAAS,EAAC,oBAFZ;IAGE,YAAW,EAAA,sBAAA;AAHb,GAAA,eAKE,oBAAC,aAAD,EAAA;AAAe,IAAA,KAAK,EAAE3N,YAAAA;AAAtB,GAAA,eACE,oBAACuQ,yBAAD,EAAA,QAAA,CAAA;AACE,IAAA,GAAG,EAAE3C,QADP;AAEE,IAAA,OAAO,EAAEN,OAFX;AAGE,IAAA,UAAU,EAAEC,UAHd;AAIE,IAAA,QAAQ,EAAEC,aAJZ;AAKE,IAAA,gBAAgB,EAAEU,gBALpB;AAME,IAAA,eAAe,EANjB,IAAA;AAOE,IAAA,gBAAgB,EAAEhB,gBAAAA;AAPpB,GAAA,EAQMgD,eARN,EAAA;AASE,IAAA,KAAK,EAAEtO,KATT;AAUE,IAAA,MAAM,EAAEuM,cAVV;AAWE,IAAA,SAAS,EAAEN,SAXb;IAYE,WAAW,EAAG2C,CAAD,IAAOlC,eAAe,CAACV,QAAQ,CAAC1L,OAAV,EAAmBsO,CAAnB,CAZrC;AAaE,IAAA,UAAU,EAAE9C,UAAAA;AAbd,GAAA,CAAA,CADF,CALF,EAsBG,CAACS,cAAD,gBACC,KAAA,CAAA,aAAA,CAAA,QAAA,EAAA,QAAA,CAAA;AACE,IAAA,IAAI,EAAC,QAAA;AADP,GAAA,EAEMmC,sBAFN,EAAA;AAGE,IAAA,YAAA,EAAW,2BAHb;AAIE,IAAA,eAAA,EAAc,yBAJhB;AAKE,IAAA,eAAA,EAAc,MALhB;AAME,IAAA,eAAA,EAAc,OANhB;IAOE,OAAO,EAAGE,CAAD,IAAO;MACd3C,SAAS,CAAC,IAAD,CAAT,CAAA;AACAwC,MAAAA,aAAa,IAAb,IAAA,GAAA,KAAA,CAAA,GAAAA,aAAa,CAAGG,CAAH,CAAb,CAAA;KATJ;AAWE,IAAA,KAAK,EAAE;AACLvR,MAAAA,UAAU,EAAE,MADP;AAELgI,MAAAA,MAAM,EAAE,CAFH;AAGLE,MAAAA,OAAO,EAAE,CAHJ;AAIL1C,MAAAA,QAAQ,EAAE,OAJL;AAKLa,MAAAA,MAAM,EAAE,KALH;AAMLe,MAAAA,OAAO,EAAE,aANJ;AAOLF,MAAAA,QAAQ,EAAE,OAPL;AAQLsK,MAAAA,MAAM,EAAE,MARH;AASLzK,MAAAA,MAAM,EAAE,SATH;AAULrB,MAAAA,KAAK,EAAE,aAVF;MAWL,IAAIF,QAAQ,KAAK,WAAb,GACA;AACEd,QAAAA,GAAG,EAAE,GADP;AAEEG,QAAAA,KAAK,EAAE,GAAA;AAFT,OADA,GAKAW,QAAQ,KAAK,UAAb,GACA;AACEd,QAAAA,GAAG,EAAE,GADP;AAEEE,QAAAA,IAAI,EAAE,GAAA;AAFR,OADA,GAKAY,QAAQ,KAAK,cAAb,GACA;AACEb,QAAAA,MAAM,EAAE,GADV;AAEEE,QAAAA,KAAK,EAAE,GAAA;AAFT,OADA,GAKA;AACEF,QAAAA,MAAM,EAAE,GADV;AAEEC,QAAAA,IAAI,EAAE,GAAA;AAFR,OAfJ,CAXK;MA8BL,GAAGsM,iBAAAA;AA9BE,KAAA;AAXT,GAAA,CAAA,eA4CE,oBAAC,IAAD,EAAA;AAAM,IAAA,aAAA,EAAA,IAAA;GA5CR,CAAA,eA6CE,oBAAC,YAAD,EAAA;AAAc,IAAA,IAAI,EAAC,2BAAA;GA7CrB,CAAA,CADD,GAgDG,IAtEN,CADF,CAAA;AA0ED,CAAA;;AAED,MAAMO,wBAAwB,GAAG,CAC/BC,UAD+B,EAE/BC,WAF+B,EAG/BC,IAAa,GAAG,KAHe,KAIzB;AACN,EAAA,OAAOC,oBAAoB,CACzBzS,KAAK,CAACK,WAAN,CACGqS,aAAD,IAAmB;AACjB,IAAA,IAAI,CAACF,IAAL,EACE,OAAOF,UAAU,CAACK,SAAX,CAAqBC,aAAa,CAACC,UAAd,CAAyBH,aAAzB,CAArB,CAAP,CAAA;AACF,IAAA,OAAO,MAAM;AACX,MAAA,OAAA;KADF,CAAA;GAJJ,EAQE,CAACJ,UAAD,EAAaE,IAAb,CARF,CADyB,EAWzBD,WAXyB,EAYzBA,WAZyB,CAA3B,CAAA;AAcD,CAnBD,CAAA;;AAqBO,MAAML,yBAAuB,gBAAGlS,KAAK,CAACsD,UAAN,CAGrC,SAAS4O,uBAAT,CAAiCzJ,KAAjC,EAAwCjF,GAAxC,EAAiE;EACjE,MAAM;AACJgD,IAAAA,MAAM,GAAG,IADL;IAEJ0I,UAFI;IAGJM,SAHI;IAIJP,OAJI;IAKJ6D,WALI;IAMJC,gBANI;IAOJC,eAPI;IAQJ5M,QARI;AASJyI,IAAAA,gBAAgB,GAAG,EATf;AAUJQ,IAAAA,UAAU,GAAG,EAVT;IAWJ,GAAGT,UAAAA;AAXC,GAAA,GAYFnG,KAZJ,CAAA;EAcA,MAAM;AAAEsJ,IAAAA,OAAO,EAAEkB,YAAX;IAAyB,GAAGC,qBAAAA;AAA5B,GAAA,GAAsDrE,gBAA5D,CAAA;EAEA,MAAMsE,WAAW,GAAGC,cAAc,CAAC;AAAEnE,IAAAA,OAAAA;AAAF,GAAD,CAAlC,CAAA;AACA,EAAA,MAAMqD,UAAU,GAAGa,WAAW,CAACE,aAAZ,EAAnB,CAAA;AAEA,EAAA,MAAM,CAACC,IAAD,EAAOC,OAAP,CAAA,GAAkB3T,eAAe,CACrC,0BADqC,EAErC8D,MAAM,CAAC8P,IAAP,CAAYtO,OAAZ,CAAqB,CAAA,CAArB,CAFqC,CAAvC,CAAA;EAKA,MAAM,CAACmI,MAAD,EAASoG,SAAT,CAAA,GAAsB7T,eAAe,CAAC,0BAAD,EAA6B,EAA7B,CAA3C,CAAA;EAEA,MAAM,CAAC8T,QAAD,EAAWC,WAAX,CAAA,GAA0B/T,eAAe,CAC7C,4BAD6C,EAE7C,CAF6C,CAA/C,CAAA;AAKA,EAAA,MAAMgU,MAAM,GAAG5T,KAAK,CAAC6T,OAAN,CAAc,MAAM3O,OAAO,CAACoO,IAAD,CAA3B,EAA6C,CAACA,IAAD,CAA7C,CAAf,CAAA;AAEA,EAAA,MAAMQ,YAAY,GAAGzB,wBAAwB,CAC3CC,UAD2C,EAE3C,MAAMA,UAAU,CAACyB,MAAX,EAAoB1H,CAAAA,MAFiB,EAG3C,CAAC7F,MAH0C,CAA7C,CAAA;EAMA,MAAM,CAACwN,eAAD,EAAkBC,kBAAlB,CAAA,GAAwCrU,eAAe,CAC3D,mCAD2D,EAE3D,EAF2D,CAA7D,CAAA;AAKA,EAAA,MAAMyD,OAAO,GAAGrD,KAAK,CAAC6T,OAAN,CAAc,MAAM;AAClC,IAAA,MAAMK,eAAe,GAAG5B,UAAU,CAACyB,MAAX,EAAxB,CAAA;;IAEA,IAAID,YAAY,KAAK,CAArB,EAAwB;AACtB,MAAA,OAAO,EAAP,CAAA;AACD,KAAA;;IAED,MAAMK,QAAQ,GAAG9G,MAAM,GACnB6G,eAAe,CAAC7G,MAAhB,CACG+G,IAAD,IAAUC,QAAQ,CAACD,IAAI,CAACvP,SAAN,EAAiBwI,MAAjB,CAAR,CAAiCiH,MAD7C,CADmB,GAInB,CAAC,GAAGJ,eAAJ,CAJJ,CAAA;IAMA,MAAMK,MAAM,GAAGX,MAAM,GACjBO,QAAQ,CAACb,IAAT,CAAc,CAAC3O,CAAD,EAAIC,CAAJ,KAAUgP,MAAM,CAACjP,CAAD,EAAIC,CAAJ,CAAN,GAAgB8O,QAAxC,CADiB,GAEjBS,QAFJ,CAAA;AAIA,IAAA,OAAOI,MAAP,CAAA;AACD,GAlBe,EAkBb,CAACb,QAAD,EAAWE,MAAX,EAAmBvG,MAAnB,EAA2ByG,YAA3B,EAAyCxB,UAAzC,CAlBa,CAAhB,CAAA;EAoBA,MAAM,CAACkC,aAAD,EAAgBC,cAAhB,CAAA,GAAkCzU,KAAK,CAACC,QAAN,CAAe,KAAf,CAAxC,CAAA;AAEA,EAAA,oBACE,oBAAC,aAAD,EAAA;AAAe,IAAA,KAAK,EAAE0B,YAAAA;AAAtB,GAAA,eACE,oBAAC,KAAD,EAAA,QAAA,CAAA;AACE,IAAA,GAAG,EAAE6B,GADP;AAEE,IAAA,SAAS,EAAC,yBAFZ;AAGE,IAAA,YAAA,EAAW,4BAHb;AAIE,IAAA,EAAE,EAAC,yBAAA;AAJL,GAAA,EAKMoL,UALN,EAAA;AAME,IAAA,KAAK,EAAE;AACLvI,MAAAA,MAAM,EAAEjB,gBADH;AAELgB,MAAAA,QAAQ,EAAE,UAFL;AAGL,MAAA,GAAGwI,UAAU,CAACrL,KAAAA;AAHT,KAAA;GAMP,CAAA,eAAA,KAAA,CAAA,aAAA,CAAA,OAAA,EAAA;AACE,IAAA,KAAK,EAAE2L,UADT;AAEE,IAAA,uBAAuB,EAAE;AACvBwF,MAAAA,MAAM,kFAEe/S,YAAK,CAACd,aAFrB,GAEsCc,GAAAA,GAAAA,YAAK,CAACZ,IAF5C,GAAA,sUAAA,GAWUY,YAAK,CAACd,aAXhB,mKAeUc,YAAK,CAACZ,IAfhB,GAiBgBY,yEAAAA,GAAAA,YAAK,CAACd,aAjBtB,GAAA,8BAAA;AADiB,KAAA;AAF3B,GAAA,CAZF,eAqCE,KAAA,CAAA,aAAA,CAAA,KAAA,EAAA;AACE,IAAA,KAAK,EAAE4G,oBAAoB,CAACrB,QAAD,CAD7B;AAEE,IAAA,WAAW,EAAE0M,WAAAA;GAvCjB,CAAA,EA0CGtM,MAAM,iBACL,KAAA,CAAA,aAAA,CAAA,KAAA,EAAA;AACE,IAAA,KAAK,EAAE;AACL6B,MAAAA,IAAI,EAAE,WADD;AAELsM,MAAAA,SAAS,EAAE,KAFN;AAGLnN,MAAAA,SAAS,EAAE,MAHN;AAILc,MAAAA,QAAQ,EAAE,MAJL;MAKLsM,WAAW,EAAA,YAAA,GAAejT,YAAK,CAACX,OAL3B;AAMLgH,MAAAA,OAAO,EAAE,MANJ;AAOLG,MAAAA,aAAa,EAAE,QAAA;AAPV,KAAA;GAUP,eAAA,KAAA,CAAA,aAAA,CAAA,KAAA,EAAA;AACE,IAAA,KAAK,EAAE;AACLW,MAAAA,OAAO,EAAE,MADJ;MAELlI,UAAU,EAAEe,YAAK,CAACd,aAFb;AAGLmH,MAAAA,OAAO,EAAE,MAHJ;AAIL6M,MAAAA,cAAc,EAAE,eAJX;AAKLzL,MAAAA,UAAU,EAAE,QAAA;AALP,KAAA;GAQP,eAAA,KAAA,CAAA,aAAA,CAAA,QAAA,EAAA;AACE,IAAA,IAAI,EAAC,QADP;AAEE,IAAA,YAAA,EAAW,4BAFb;AAGE,IAAA,eAAA,EAAc,yBAHhB;AAIE,IAAA,eAAA,EAAc,MAJhB;AAKE,IAAA,eAAA,EAAc,MALhB;AAME,IAAA,OAAO,EAAE,MAAMoG,SAAS,CAAC,KAAD,CAN1B;AAOE,IAAA,KAAK,EAAE;AACLxH,MAAAA,OAAO,EAAE,aADJ;AAELpH,MAAAA,UAAU,EAAE,MAFP;AAGLgI,MAAAA,MAAM,EAAE,CAHH;AAILE,MAAAA,OAAO,EAAE,CAJJ;AAKLgM,MAAAA,WAAW,EAAE,MALR;AAMLnN,MAAAA,MAAM,EAAE,SAAA;AANH,KAAA;AAPT,GAAA,eAgBE,oBAAC,IAAD,EAAA;AAAM,IAAA,aAAA,EAAA,IAAA;GAhBR,CAAA,eAiBE,oBAAC,YAAD,EAAA;AAAc,IAAA,IAAI,EAAC,4BAAA;AAAnB,GAAA,CAjBF,CATF,eA6BE,KAAA,CAAA,aAAA,CAAA,KAAA,EAAA;AACE,IAAA,KAAK,EAAE;AACLK,MAAAA,OAAO,EAAE,MADJ;AAELG,MAAAA,aAAa,EAAE,QAAA;AAFV,KAAA;GAKP,eAAA,KAAA,CAAA,aAAA,CAAA,KAAA,EAAA;AACE,IAAA,KAAK,EAAE;AACLH,MAAAA,OAAO,EAAE,MADJ;AAEL6M,MAAAA,cAAc,EAAE,eAFX;AAGLzL,MAAAA,UAAU,EAAE,QAHP;AAIL2L,MAAAA,YAAY,EAAE,MAAA;AAJT,KAAA;AADT,GAAA,eAQE,oBAAC,gBAAD,EAAA;AAAkB,IAAA,UAAU,EAAEzC,UAAAA;AAA9B,GAAA,CARF,EASGlM,QAAQ,IAAI2M,gBAAZ,gBACC,oBAAC,MAAD,EAAA;AACE,IAAA,YAAA,EAAW,gBADb;AAEE,IAAA,KAAK,EAAE3M,QAFT;AAGE,IAAA,KAAK,EAAE;AAAE4O,MAAAA,iBAAiB,EAAE,MAAA;KAH9B;IAIE,QAAQ,EAAG7C,CAAD,IAAOY,gBAAgB,CAACZ,CAAC,CAAC8C,MAAF,CAASnV,KAAV,CAAA;GAEjC,eAAA,KAAA,CAAA,aAAA,CAAA,QAAA,EAAA;AAAQ,IAAA,KAAK,EAAC,MAAA;AAAd,GAAA,EAAA,MAAA,CANF,eAOE,KAAA,CAAA,aAAA,CAAA,QAAA,EAAA;AAAQ,IAAA,KAAK,EAAC,OAAA;AAAd,GAAA,EAAA,OAAA,CAPF,eAQE,KAAA,CAAA,aAAA,CAAA,QAAA,EAAA;AAAQ,IAAA,KAAK,EAAC,KAAA;AAAd,GAAA,EAAA,KAAA,CARF,eASE,KAAA,CAAA,aAAA,CAAA,QAAA,EAAA;AAAQ,IAAA,KAAK,EAAC,QAAA;AAAd,GAAA,EAAA,QAAA,CATF,CADD,GAYG,IArBN,CANF,eA6BE,KAAA,CAAA,aAAA,CAAA,KAAA,EAAA;AACE,IAAA,KAAK,EAAE;AACLkI,MAAAA,OAAO,EAAE,MADJ;AAELoB,MAAAA,UAAU,EAAE,QAFP;AAGLH,MAAAA,QAAQ,EAAE,MAHL;AAILC,MAAAA,GAAG,EAAE,OAAA;AAJA,KAAA;AADT,GAAA,eAQE,oBAAC,KAAD,EAAA;AACE,IAAA,WAAW,EAAC,QADd;AAEE,IAAA,YAAA,EAAW,qBAFb;AAGE,IAAA,KAAK,EAAEmE,MAAF,IAAEA,IAAAA,GAAAA,MAAF,GAAY,EAHnB;IAIE,QAAQ,EAAG8E,CAAD,IAAOsB,SAAS,CAACtB,CAAC,CAAC8C,MAAF,CAASnV,KAAV,CAJ5B;IAKE,SAAS,EAAGqS,CAAD,IAAO;MAChB,IAAIA,CAAC,CAAC7S,GAAF,KAAU,QAAd,EAAwBmU,SAAS,CAAC,EAAD,CAAT,CAAA;KAN5B;AAQE,IAAA,KAAK,EAAE;AACLpL,MAAAA,IAAI,EAAE,GADD;AAEL/B,MAAAA,KAAK,EAAE,MAAA;AAFF,KAAA;GAhBX,CAAA,eAqBE,oBAAC,MAAD,EAAA;AACE,IAAA,YAAA,EAAW,cADb;AAEE,IAAA,KAAK,EAAEgN,IAFT;IAGE,QAAQ,EAAGnB,CAAD,IAAOoB,OAAO,CAACpB,CAAC,CAAC8C,MAAF,CAASnV,KAAV,CAH1B;AAIE,IAAA,KAAK,EAAE;AACLuI,MAAAA,IAAI,EAAE,GADD;AAEL6M,MAAAA,QAAQ,EAAE,EAFL;AAGLJ,MAAAA,WAAW,EAAE,MAAA;AAHR,KAAA;GAMNpR,EAAAA,MAAM,CAAC8P,IAAP,CAAYtO,OAAZ,CAAqBiI,CAAAA,GAArB,CAA0B7N,GAAD,iBACxB,KAAA,CAAA,aAAA,CAAA,QAAA,EAAA;AAAQ,IAAA,GAAG,EAAEA,GAAb;AAAkB,IAAA,KAAK,EAAEA,GAAAA;AAAzB,GAAA,EAAA,UAAA,EACWA,GADX,CADD,CAVH,CArBF,eAqCE,oBAAC,MAAD,EAAA;AACE,IAAA,IAAI,EAAC,QADP;IAEE,OAAO,EAAE,MAAMqU,WAAW,CAAEpT,GAAD,IAASA,GAAG,GAAG,CAAC,CAAjB,CAF5B;AAGE,IAAA,KAAK,EAAE;AACLuI,MAAAA,OAAO,EAAE,WADJ;AAELgM,MAAAA,WAAW,EAAE,MAAA;AAFR,KAAA;GAKNpB,EAAAA,QAAQ,KAAK,CAAb,GAAiB,OAAjB,GAA2B,QAR9B,CArCF,eA+CE,KAAA,CAAA,aAAA,CAAC,MAAD,EAAA;AACE,IAAA,KAAK,EAAC,aADR;AAEE,IAAA,YAAA,EAAW,aAFb;AAGE,IAAA,IAAI,EAAC,QAHP;AAIE,IAAA,OAAO,EAAE,MAAMpB,UAAU,CAAC6C,KAAX,EAJjB;AAKE,IAAA,KAAK,EAAE;AACLrM,MAAAA,OAAO,EAAE,WADJ;AAELgM,MAAAA,WAAW,EAAE,MAAA;AAFR,KAAA;GApDX,EAAA,OAAA,CAAA,eA2DE,oBAAC,MAAD,EAAA;AACE,IAAA,IAAI,EAAC,QADP;AAEE,IAAA,OAAO,EAAE,MAAM;AACb,MAAA,IAAIN,aAAJ,EAAmB;QACjBY,aAAa,CAACC,SAAd,CAAwB1V,SAAxB,CAAA,CAAA;QACA8U,cAAc,CAAC,KAAD,CAAd,CAAA;AACAtS,QAAAA,MAAM,CAACmT,aAAP,CAAqB,IAAIC,KAAJ,CAAU,QAAV,CAArB,CAAA,CAAA;AACD,OAJD,MAIO;QACLH,aAAa,CAACC,SAAd,CAAwB,KAAxB,CAAA,CAAA;QACAZ,cAAc,CAAC,IAAD,CAAd,CAAA;AACD,OAAA;KAVL;AAYE,IAAA,YAAA,EACED,aAAa,GACT,sBADS,GAET,uBAfR;AAiBE,IAAA,KAAK,EACHA,aAAa,GACT,sBADS,GAET,uBApBR;AAsBE,IAAA,KAAK,EAAE;AACL1L,MAAAA,OAAO,EAAE,GADJ;AAELzC,MAAAA,MAAM,EAAE,KAAA;AAFH,KAAA;GAKP,eAAA,KAAA,CAAA,aAAA,CAAA,KAAA,EAAA;AACE,IAAA,KAAK,EAAC,4BADR;AAEE,IAAA,KAAK,EAAC,KAFR;AAGE,IAAA,MAAM,EAAC,KAHT;AAIE,IAAA,OAAO,EAAC,WAJV;AAKE,IAAA,MAAM,EAAEmO,aAAa,GAAG7S,YAAK,CAACP,MAAT,GAAkB,cALzC;AAME,IAAA,IAAI,EAAC,MAAA;GAEJoT,EAAAA,aAAa,gBACZ,KACE,CAAA,aAAA,CAAA,KAAA,CAAA,QAAA,EAAA,IAAA,eAAA,KAAA,CAAA,aAAA,CAAA,MAAA,EAAA;AAAM,IAAA,MAAM,EAAC,MAAb;AAAoB,IAAA,CAAC,EAAC,eAAtB;AAAsC,IAAA,IAAI,EAAC,MAAA;AAA3C,GAAA,CADF,eAEE,KAAA,CAAA,aAAA,CAAA,MAAA,EAAA;AAAM,IAAA,EAAE,EAAC,IAAT;AAAc,IAAA,EAAE,EAAC,IAAjB;AAAsB,IAAA,EAAE,EAAC,OAAzB;AAAiC,IAAA,EAAE,EAAC,IAAA;AAApC,GAAA,CAFF,eAGE,KAAA,CAAA,aAAA,CAAA,MAAA,EAAA;AAAM,IAAA,CAAC,EAAC,iCAAA;AAAR,GAAA,CAHF,eAIE,KAAA,CAAA,aAAA,CAAA,MAAA,EAAA;AAAM,IAAA,CAAC,EAAC,kFAAA;AAAR,GAAA,CAJF,eAKE,KAAA,CAAA,aAAA,CAAA,MAAA,EAAA;AAAM,IAAA,CAAC,EAAC,2EAAA;AAAR,GAAA,CALF,eAME,KAAA,CAAA,aAAA,CAAA,MAAA,EAAA;AAAM,IAAA,EAAE,EAAC,GAAT;AAAa,IAAA,EAAE,EAAC,GAAhB;AAAoB,IAAA,EAAE,EAAC,IAAvB;AAA4B,IAAA,EAAE,EAAC,IAAA;GANjC,CAAA,CADY,gBAUZ,KACE,CAAA,aAAA,CAAA,KAAA,CAAA,QAAA,EAAA,IAAA,eAAA,KAAA,CAAA,aAAA,CAAA,MAAA,EAAA;AAAM,IAAA,MAAM,EAAC,MAAb;AAAoB,IAAA,CAAC,EAAC,eAAtB;AAAsC,IAAA,IAAI,EAAC,MAAA;AAA3C,GAAA,CADF,eAEE,KAAA,CAAA,aAAA,CAAA,MAAA,EAAA;AAAM,IAAA,EAAE,EAAC,IAAT;AAAc,IAAA,EAAE,EAAC,IAAjB;AAAsB,IAAA,EAAE,EAAC,OAAzB;AAAiC,IAAA,EAAE,EAAC,IAAA;AAApC,GAAA,CAFF,eAGE,KAAA,CAAA,aAAA,CAAA,MAAA,EAAA;AAAM,IAAA,CAAC,EAAC,iCAAA;AAAR,GAAA,CAHF,eAIE,KAAA,CAAA,aAAA,CAAA,MAAA,EAAA;AAAM,IAAA,CAAC,EAAC,kCAAA;AAAR,GAAA,CAJF,eAKE,KAAA,CAAA,aAAA,CAAA,MAAA,EAAA;AAAM,IAAA,CAAC,EAAC,8CAAA;AAAR,GAAA,CALF,CAlBJ,CA3BF,eAsDE,KAAA,CAAA,aAAA,CAAC,YAAD,EAAA;AACE,IAAA,IAAI,EACFA,aAAa,GACT,sBADS,GAET,uBAAA;AAJR,GAAA,CAtDF,CA3DF,CA7BF,CA7BF,CAXF,eAiME,KAAA,CAAA,aAAA,CAAA,KAAA,EAAA;AACE,IAAA,KAAK,EAAE;AACLgB,MAAAA,SAAS,EAAE,MADN;AAELnN,MAAAA,IAAI,EAAE,GAAA;AAFD,KAAA;AADT,GAAA,EAMGhF,OAAO,CAAC8J,GAAR,CAAanL,KAAD,IAAW;AACtB,IAAA,oBACE,oBAAC,QAAD,EAAA;MACE,QAAQ,EAAEA,KAAK,CAACyT,QADlB;AAEE,MAAA,eAAe,EAAEzB,eAFnB;AAGE,MAAA,kBAAkB,EAAEC,kBAHtB;MAIE,GAAG,EAAEjS,KAAK,CAAC6C,SAJb;AAKE,MAAA,UAAU,EAAEyN,UAAAA;KANhB,CAAA,CAAA;GADD,CANH,CAjMF,CA3CJ,EAiQG0B,eAAe,IAAIxN,MAAnB,gBACC,KAAA,CAAA,aAAA,CAAC,WAAD,EAAA;AACE,IAAA,eAAe,EAAEwN,eADnB;AAEE,IAAA,UAAU,EAAE1B,UAFd;AAGE,IAAA,WAAW,EAAEa,WAHf;AAIE,IAAA,UAAU,EAAE9D,UAAAA;AAJd,GAAA,CADD,GAOG,IAxQN,EA0QG2D,eAAe,gBACd,oBAAC,MAAD,EAAA,QAAA,CAAA;AACE,IAAA,IAAI,EAAC,QADP;AAEE,IAAA,eAAA,EAAc,yBAFhB;AAGE,IAAA,eAAA,EAAc,MAHhB;IAIE,eAAc,EAAA,MAAA;AAJhB,GAAA,EAKOE,qBALP,EAAA;AAME,IAAA,KAAK,EAAE;AACL9M,MAAAA,QAAQ,EAAE,UADL;AAELa,MAAAA,MAAM,EAAE,KAFH;AAGLmL,MAAAA,MAAM,EAAE,MAHH;AAIL7M,MAAAA,MAAM,EAAE,CAJH;AAKLC,MAAAA,IAAI,EAAE,CALD;AAML,MAAA,GAAG0N,qBAAqB,CAAC3P,KAAAA;KAZ7B;IAcE,OAAO,EAAG4O,CAAD,IAAO;MACd3C,SAAS,CAAC,KAAD,CAAT,CAAA;AACAyD,MAAAA,YAAY,IAAZ,IAAA,GAAA,KAAA,CAAA,GAAAA,YAAY,CAAGd,CAAH,CAAZ,CAAA;AACD,KAAA;GAlBW,CAAA,EAAA,OAAA,CAAA,GAsBZ,IAhSN,CADF,CADF,CAAA;AAsSD,CA5WsC,CAAhC,CAAA;;AA8WP,MAAMuD,WAAW,GAAG,CAAC;EACnBpD,UADmB;EAEnB0B,eAFmB;EAGnBb,WAHmB;AAInB9D,EAAAA,UAAAA;AAJmB,CAAD,KAUd;AAAA,EAAA,IAAA,qBAAA,EAAA,sBAAA,CAAA;;EACJ,MAAMsG,WAAW,GAAGtD,wBAAwB,CAACC,UAAD,EAAa,MACvDA,UAAU,CAACyB,MAAX,GAAoB6B,IAApB,CAA0B5T,KAAD,IAAWA,KAAK,CAAC6C,SAAN,KAAoBmP,eAAxD,CAD0C,CAA5C,CAAA;AAIA,EAAA,MAAM6B,gBAAgB,GAAGxD,wBAAwB,CAC/CC,UAD+C,EAE/C,MAAA;AAAA,IAAA,IAAA,qBAAA,CAAA;;AAAA,IAAA,OAAA,CAAA,qBAAA,GACEA,UAAU,CAACyB,MAAX,EAAoB6B,CAAAA,IAApB,CAA0B5T,KAAD,IAAWA,KAAK,CAAC6C,SAAN,KAAoBmP,eAAxD,CADF,KAAA,IAAA,GAAA,KAAA,CAAA,GACE,sBACIhR,KAFN,CAAA;AAAA,GAF+C,CAAjD,CAAA;AAOA,EAAA,MAAMH,OAAO,GAAA,CAAA,qBAAA,GACXwP,wBAAwB,CAACC,UAAD,EAAa,MAAA;AAAA,IAAA,IAAA,sBAAA,CAAA;;AAAA,IAAA,OAAA,CAAA,sBAAA,GACnCA,UAAU,CACPyB,MADH,EAEG6B,CAAAA,IAFH,CAES5T,KAAD,IAAWA,KAAK,CAAC6C,SAAN,KAAoBmP,eAFvC,CADmC,KACnC,IAAA,GAAA,KAAA,CAAA,GAAA,sBAAA,CAGInR,OAHJ,EADmC,CAAA;GAAb,CADb,oCAMN,KANP,CAAA;AAQA,EAAA,MAAMD,aAAa,GAAA,CAAA,sBAAA,GACjByP,wBAAwB,CAACC,UAAD,EAAa,MAAA;AAAA,IAAA,IAAA,sBAAA,CAAA;;AAAA,IAAA,OAAA,CAAA,sBAAA,GACnCA,UAAU,CACPyB,MADH,EAEG6B,CAAAA,IAFH,CAES5T,KAAD,IAAWA,KAAK,CAAC6C,SAAN,KAAoBmP,eAFvC,CADmC,KACnC,IAAA,GAAA,KAAA,CAAA,GAAA,sBAAA,CAGI/Q,iBAHJ,EADmC,CAAA;GAAb,CADP,qCAMZ,CANP,CAAA;;EAQA,MAAM6S,aAAa,GAAG,MAAM;AAC1B,IAAA,MAAMC,OAAO,GAAGJ,WAAH,oBAAGA,WAAW,CAAEK,KAAb,EAAhB,CAAA;AACAD,IAAAA,OAAO,QAAP,GAAAA,KAAAA,CAAAA,GAAAA,OAAO,CAAEE,KAAT,CAAeC,IAAf,CAAA,CAAA;GAFF,CAAA;;AAKA,EAAA,MAAMC,oBAAoB,GAAGtC,OAAO,CAAC,MAAM;AACzC,IAAA,IAAI8B,WAAW,IAAIE,gBAAJ,YAAIA,gBAAgB,CAAE1K,KAArC,EAA4C;AAC1C,MAAA,MAAMiL,SAAS,GAAG/G,UAAU,CAACuG,IAAX,CACfzS,IAAD,IAAA;AAAA,QAAA,IAAA,qBAAA,CAAA;;AAAA,QAAA,OACEA,IAAI,CAACkT,WAAL,CAAiBV,WAAjB,CAA8BtH,CAAAA,QAA9B,EACAwH,MAAAA,CAAAA,qBAAAA,GAAAA,gBAAgB,CAAC1K,KADjB,KAAA,IAAA,GAAA,KAAA,CAAA,GACA,qBAAwBkD,CAAAA,QAAxB,EADA,CADF,CAAA;AAAA,OADgB,CAAlB,CAAA;AAKA,MAAA,OAAO+H,SAAP,IAAA,IAAA,GAAA,KAAA,CAAA,GAAOA,SAAS,CAAEE,IAAlB,CAAA;AACD,KAAA;;AACD,IAAA,OAAO3W,SAAP,CAAA;AACD,GAVmC,EAUjC,CAACgW,WAAD,EAAcE,gBAAd,IAAA,IAAA,GAAA,KAAA,CAAA,GAAcA,gBAAgB,CAAE1K,KAAhC,EAAuCkE,UAAvC,CAViC,CAApC,CAAA;;AAYA,EAAA,IAAI,CAACsG,WAAD,IAAgB,CAACE,gBAArB,EAAuC;AACrC,IAAA,OAAO,IAAP,CAAA;AACD,GAAA;;EAED,MAAMU,YAAY,GAAIH,SAAD,IAAmC;AAAA,IAAA,IAAA,qBAAA,CAAA;;AACtD,IAAA,MAAMjL,KAAK,GAAA,CAAA,qBAAA,GACTiL,SADS,IAAA,IAAA,GAAA,KAAA,CAAA,GACTA,SAAS,CAAEC,WAAX,CAAuBV,WAAvB,CADS,KAET,IAAA,GAAA,qBAAA,GAAA,IAAIa,KAAJ,CAAU,6BAAV,CAFF,CAAA;AAIA,IAAA,MAAMC,sBAAsB,GAAGd,WAAW,CAACe,OAA3C,CAAA;IAEAf,WAAW,CAACgB,QAAZ,CAAqB;AACnBC,MAAAA,MAAM,EAAE,OADW;MAEnBzL,KAFmB;AAGnB0L,MAAAA,SAAS,EAAE,EACT,GAAGlB,WAAW,CAAC3S,KAAZ,CAAkB6T,SADZ;AAETJ,QAAAA,sBAAAA;AAFS,OAAA;KAHb,CAAA,CAAA;GAPF,CAAA;;EAiBA,MAAMK,+BAA+B,GAAG,MAAM;IAC5CnB,WAAW,CAACK,KAAZ,CAAkBL,WAAW,CAAC3S,KAAZ,CAAkB6T,SAAlB,CAA4BJ,sBAA9C,EAAsE;AACpE;AACAM,MAAAA,aAAa,EAAE,IAAA;KAFjB,CAAA,CAAA;GADF,CAAA;;EAOA,oBACE,KAAA,CAAA,aAAA,CAAC,gBAAD,EACE,IAAA,eAAA,KAAA,CAAA,aAAA,CAAA,KAAA,EAAA;AACE,IAAA,KAAK,EAAE;AACLjO,MAAAA,OAAO,EAAE,MADJ;MAELlI,UAAU,EAAEe,YAAK,CAACd,aAFb;AAGLuF,MAAAA,QAAQ,EAAE,QAHL;AAILd,MAAAA,GAAG,EAAE,CAJA;AAKL2B,MAAAA,MAAM,EAAE,CAAA;AALH,KAAA;AADT,GAAA,EAAA,eAAA,CADF,eAYE,KAAA,CAAA,aAAA,CAAA,KAAA,EAAA;AACE,IAAA,KAAK,EAAE;AACL6B,MAAAA,OAAO,EAAE,MAAA;AADJ,KAAA;GAIP,eAAA,KAAA,CAAA,aAAA,CAAA,KAAA,EAAA;AACE,IAAA,KAAK,EAAE;AACLiM,MAAAA,YAAY,EAAE,MADT;AAEL/M,MAAAA,OAAO,EAAE,MAFJ;AAGLoB,MAAAA,UAAU,EAAE,YAHP;AAILyL,MAAAA,cAAc,EAAE,eAAA;AAJX,KAAA;AADT,GAAA,eAQE,oBAAC,IAAD,EAAA;AACE,IAAA,KAAK,EAAE;AACLrL,MAAAA,UAAU,EAAE,OAAA;AADP,KAAA;GAIP,eAAA,KAAA,CAAA,aAAA,CAAA,KAAA,EAAA;AACE,IAAA,KAAK,EAAE;AACL4I,MAAAA,MAAM,EAAE,CADH;AAELtJ,MAAAA,OAAO,EAAE,CAFJ;AAGLR,MAAAA,QAAQ,EAAE,MAAA;AAHL,KAAA;GAMNnE,EAAAA,YAAY,CAACwR,WAAW,CAACF,QAAb,EAAuB,IAAvB,CAPf,CALF,CARF,eAuBE,KAAA,CAAA,aAAA,CAAA,MAAA,EAAA;AACE,IAAA,KAAK,EAAE;AACL3M,MAAAA,OAAO,EAAE,YADJ;AAELD,MAAAA,YAAY,EAAE,OAFT;AAGLF,MAAAA,UAAU,EAAE,MAHP;AAILU,MAAAA,UAAU,EAAE,kBAJP;MAKLzI,UAAU,EAAE8B,mBAAmB,CAAC;AAC9BC,QAAAA,UAAU,EAAEkT,gBADkB;AAE9BhT,QAAAA,OAAO,EAAEA,OAFqB;AAG9BD,QAAAA,aAAa,EAAEA,aAHe;AAI9BjB,eAAAA,YAAAA;AAJ8B,OAAD,CAL1B;AAWLqV,MAAAA,UAAU,EAAE,CAAA;AAXP,KAAA;AADT,GAAA,EAeGjU,mBAAmB,CAAC4S,WAAD,CAftB,CAvBF,CALF,eA8CE,KAAA,CAAA,aAAA,CAAA,KAAA,EAAA;AACE,IAAA,KAAK,EAAE;AACLZ,MAAAA,YAAY,EAAE,MADT;AAEL/M,MAAAA,OAAO,EAAE,MAFJ;AAGLoB,MAAAA,UAAU,EAAE,QAHP;AAILyL,MAAAA,cAAc,EAAE,eAAA;AAJX,KAAA;AADT,GAAA,EAAA,aAAA,eAQa,oBAAC,IAAD,EAAA,IAAA,EAAOjS,aAAP,CARb,CA9CF,eAwDE,KAAA,CAAA,aAAA,CAAA,KAAA,EAAA;AACE,IAAA,KAAK,EAAE;AACLoF,MAAAA,OAAO,EAAE,MADJ;AAELoB,MAAAA,UAAU,EAAE,QAFP;AAGLyL,MAAAA,cAAc,EAAE,eAAA;AAHX,KAAA;AADT,GAAA,EAAA,eAAA,EAOgB,GAPhB,eAQE,KAAA,CAAA,aAAA,CAAC,IAAD,EAAA,IAAA,EACG,IAAIoC,IAAJ,CAASpB,gBAAgB,CAAC7Q,aAA1B,CAAyCkS,CAAAA,kBAAzC,EADH,CARF,CAxDF,CAZF,eAiFE,KAAA,CAAA,aAAA,CAAA,KAAA,EAAA;AACE,IAAA,KAAK,EAAE;MACLtW,UAAU,EAAEe,YAAK,CAACd,aADb;AAELiI,MAAAA,OAAO,EAAE,MAFJ;AAGL1C,MAAAA,QAAQ,EAAE,QAHL;AAILd,MAAAA,GAAG,EAAE,CAJA;AAKL2B,MAAAA,MAAM,EAAE,CAAA;AALH,KAAA;AADT,GAAA,EAAA,SAAA,CAjFF,eA4FE,KAAA,CAAA,aAAA,CAAA,KAAA,EAAA;AACE,IAAA,KAAK,EAAE;AACL6B,MAAAA,OAAO,EAAE,OADJ;AAELd,MAAAA,OAAO,EAAE,MAFJ;AAGLiB,MAAAA,QAAQ,EAAE,MAHL;AAILC,MAAAA,GAAG,EAAE,OAJA;AAKLE,MAAAA,UAAU,EAAE,UAAA;AALP,KAAA;AADT,GAAA,eASE,oBAAC,MAAD,EAAA;AACE,IAAA,IAAI,EAAC,QADP;AAEE,IAAA,OAAO,EAAE0M,aAFX;AAGE,IAAA,QAAQ,EAAED,gBAAgB,CAAC/S,WAAjB,KAAiC,UAH7C;AAIE,IAAA,KAAK,EAAE;MACLlC,UAAU,EAAEe,YAAK,CAACN,MAAAA;AADb,KAAA;AAJT,GAAA,EAAA,SAAA,CATF,EAkBY,GAlBZ,eAmBE,KAAA,CAAA,aAAA,CAAC,MAAD,EAAA;AACE,IAAA,IAAI,EAAC,QADP;AAEE,IAAA,OAAO,EAAE,MAAM8R,WAAW,CAACgE,iBAAZ,CAA8BxB,WAA9B,CAFjB;AAGE,IAAA,KAAK,EAAE;MACL/U,UAAU,EAAEe,YAAK,CAACJ,OADb;MAEL2G,KAAK,EAAEvG,YAAK,CAACT,cAAAA;AAFR,KAAA;AAHT,GAAA,EAAA,YAAA,CAnBF,EA4BY,GA5BZ,eA6BE,KAAA,CAAA,aAAA,CAAC,MAAD,EAAA;AACE,IAAA,IAAI,EAAC,QADP;AAEE,IAAA,OAAO,EAAE,MAAMiS,WAAW,CAACiE,YAAZ,CAAyBzB,WAAzB,CAFjB;AAGE,IAAA,KAAK,EAAE;MACL/U,UAAU,EAAEe,YAAK,CAACZ,IAAAA;AADb,KAAA;AAHT,GAAA,EAAA,OAAA,CA7BF,EAqCY,GArCZ,eAsCE,KAAA,CAAA,aAAA,CAAC,MAAD,EAAA;AACE,IAAA,IAAI,EAAC,QADP;AAEE,IAAA,OAAO,EAAE,MAAMoS,WAAW,CAACkE,aAAZ,CAA0B1B,WAA1B,CAFjB;AAGE,IAAA,KAAK,EAAE;MACL/U,UAAU,EAAEe,YAAK,CAACP,MAAAA;AADb,KAAA;AAHT,GAAA,EAAA,QAAA,CAtCF,EA8CY,GA9CZ,eA+CE,KAAA,CAAA,aAAA,CAAC,MAAD,EAAA;AACE,IAAA,IAAI,EAAC,QADP;AAEE,IAAA,OAAO,EAAE,MAAM;AAAA,MAAA,IAAA,qBAAA,CAAA;;AACb;AACA,MAAA,IACEuU,WAAW,CAAC3S,KAAZ,CAAkBF,WAAlB,KAAkC,UAAlC,IACA,QAAA,CAAA,qBAAA,GAAO6S,WAAW,CAAC3S,KAAZ,CAAkB6T,SAAzB,KAAA,IAAA,GAAA,KAAA,CAAA,GAAO,sBAA6BJ,sBAApC,CAAA,KACE,WAHJ,EAIE;AACA,QAAA,OAAA;AACD,OAAA;;AAED,MAAA,IAAId,WAAW,CAAC3S,KAAZ,CAAkBsU,IAAlB,KAA2B3X,SAA/B,EAA0C;QACxCmX,+BAA+B,EAAA,CAAA;AAChC,OAFD,MAEO;AACL,QAAA,MAAML,sBAAsB,GAAGd,WAAW,CAACe,OAA3C,CADK;;AAGLf,QAAAA,WAAW,CAACK,KAAZ,CAAkB,EAChB,GAAGS,sBADa;AAEhBc,UAAAA,OAAO,EAAE,MAAM;AACb,YAAA,OAAO,IAAIC,OAAJ,CAAY,MAAM;AAExB,aAFM,CAAP,CAAA;WAHc;AAOhBC,UAAAA,SAAS,EAAE,CAAC,CAAA;SAPd,CAAA,CAAA;QASA9B,WAAW,CAACgB,QAAZ,CAAqB;AACnBW,UAAAA,IAAI,EAAE3X,SADa;AAEnBiX,UAAAA,MAAM,EAAE,SAFW;AAGnBC,UAAAA,SAAS,EAAE,EACT,GAAGlB,WAAW,CAAC3S,KAAZ,CAAkB6T,SADZ;AAETJ,YAAAA,sBAAAA;AAFS,WAAA;SAHb,CAAA,CAAA;AAQD,OAAA;KAlCL;AAoCE,IAAA,KAAK,EAAE;MACL7V,UAAU,EAAEe,YAAK,CAACL,MAAAA;AADb,KAAA;AApCT,GAAA,EAwCGqU,WAAW,CAAC3S,KAAZ,CAAkB4T,MAAlB,KAA6B,SAA7B,GAAyC,SAAzC,GAAqD,SAxCxD,EAwCmE,GAxCnE,YA/CF,EAyFY,GAzFZ,EA0FGvH,UAAU,CAAChD,MAAX,KAAsB,CAAtB,IAA2BsJ,WAAW,CAAC3S,KAAZ,CAAkB4T,MAAlB,KAA6B,OAAxD,gBACC,oBAAC,MAAD,EAAA;AACE,IAAA,IAAI,EAAC,QADP;AAEE,IAAA,OAAO,EAAE,MAAM;AACb,MAAA,IAAI,CAACjB,WAAW,CAAC3S,KAAZ,CAAkBmI,KAAvB,EAA8B;QAC5BoL,YAAY,EAAA,CAAA;AACb,OAFD,MAEO;QACLpD,WAAW,CAACiE,YAAZ,CAAyBzB,WAAzB,CAAA,CAAA;AACD,OAAA;KAPL;AASE,IAAA,KAAK,EAAE;MACL/U,UAAU,EAAEe,YAAK,CAACP,MAAAA;AADb,KAAA;AATT,GAAA,EAaGuU,WAAW,CAAC3S,KAAZ,CAAkB4T,MAAlB,KAA6B,OAA7B,GAAuC,SAAvC,GAAmD,SAbtD,EAAA,QAAA,CADD,gBAiBC,KAAA,CAAA,aAAA,CAAA,OAAA,EAAA,IAAA,EAAA,gBAAA,eAEE,oBAAC,MAAD,EAAA;AACE,IAAA,KAAK,EAAET,oBAAF,IAAEA,IAAAA,GAAAA,oBAAF,GAA0B,EADjC;AAEE,IAAA,KAAK,EAAE;AAAEnB,MAAAA,iBAAiB,EAAE,MAAA;KAF9B;IAGE,QAAQ,EAAG7C,CAAD,IAAO;AACf,MAAA,MAAMiE,SAAS,GAAG/G,UAAU,CAACuG,IAAX,CACf8B,CAAD,IAAOA,CAAC,CAACpB,IAAF,KAAWnE,CAAC,CAAC8C,MAAF,CAASnV,KADX,CAAlB,CAAA;MAIAyW,YAAY,CAACH,SAAD,CAAZ,CAAA;AACD,KAAA;GAED,eAAA,KAAA,CAAA,aAAA,CAAA,QAAA,EAAA;AAAQ,IAAA,GAAG,EAAC,EAAZ;AAAe,IAAA,KAAK,EAAC,EAAA;AAArB,GAAA,CAXF,EAYG/G,UAAU,CAAClC,GAAX,CAAgBiJ,SAAD,iBACd,KAAA,CAAA,aAAA,CAAA,QAAA,EAAA;IAAQ,GAAG,EAAEA,SAAS,CAACE,IAAvB;IAA6B,KAAK,EAAEF,SAAS,CAACE,IAAAA;GAC3CF,EAAAA,SAAS,CAACE,IADb,CADD,CAZH,CAFF,CA3GJ,CA5FF,eA8NE,KAAA,CAAA,aAAA,CAAA,KAAA,EAAA;AACE,IAAA,KAAK,EAAE;MACL1V,UAAU,EAAEe,YAAK,CAACd,aADb;AAELiI,MAAAA,OAAO,EAAE,MAFJ;AAGL1C,MAAAA,QAAQ,EAAE,QAHL;AAILd,MAAAA,GAAG,EAAE,CAJA;AAKL2B,MAAAA,MAAM,EAAE,CAAA;AALH,KAAA;AADT,GAAA,EAAA,eAAA,CA9NF,eAyOE,KAAA,CAAA,aAAA,CAAA,KAAA,EAAA;AACE,IAAA,KAAK,EAAE;AACL6B,MAAAA,OAAO,EAAE,MAAA;AADJ,KAAA;AADT,GAAA,eAKE,oBAAC,QAAD,EAAA;AACE,IAAA,KAAK,EAAC,MADR;IAEE,KAAK,EAAE+M,gBAAgB,CAACyB,IAF1B;AAGE,IAAA,eAAe,EAAE,EAHnB;IAIE,QAAQ,EAAA,IAAA;AAJV,GAAA,CALF,CAzOF,eAqPE,KAAA,CAAA,aAAA,CAAA,KAAA,EAAA;AACE,IAAA,KAAK,EAAE;MACL1W,UAAU,EAAEe,YAAK,CAACd,aADb;AAELiI,MAAAA,OAAO,EAAE,MAFJ;AAGL1C,MAAAA,QAAQ,EAAE,QAHL;AAILd,MAAAA,GAAG,EAAE,CAJA;AAKL2B,MAAAA,MAAM,EAAE,CAAA;AALH,KAAA;AADT,GAAA,EAAA,gBAAA,CArPF,eAgQE,KAAA,CAAA,aAAA,CAAA,KAAA,EAAA;AACE,IAAA,KAAK,EAAE;AACL6B,MAAAA,OAAO,EAAE,MAAA;AADJ,KAAA;AADT,GAAA,eAKE,oBAAC,QAAD,EAAA;AACE,IAAA,KAAK,EAAC,OADR;AAEE,IAAA,KAAK,EAAE6M,WAFT;AAGE,IAAA,eAAe,EAAE;AACfF,MAAAA,QAAQ,EAAE,IAAA;AADK,KAAA;AAHnB,GAAA,CALF,CAhQF,CADF,CAAA;AAgRD,CAnWD,CAAA;;AAqWA,MAAMkC,gBAAgB,GAAG,CAAC;AAAErF,EAAAA,UAAAA;AAAF,CAAD,KAAgD;EACvE,MAAMsF,QAAQ,GAAGvF,wBAAwB,CACvCC,UADuC,EAEvC,MACEA,UAAU,CAACyB,MAAX,EAAA,CAAoB1G,MAApB,CAA4B5I,CAAD,IAAO1B,mBAAmB,CAAC0B,CAAD,CAAnB,KAA2B,OAA7D,CACG4H,CAAAA,MAJkC,CAAzC,CAAA;EAMA,MAAMwL,WAAW,GAAGxF,wBAAwB,CAC1CC,UAD0C,EAE1C,MACEA,UAAU,CAACyB,MAAX,EAAA,CAAoB1G,MAApB,CAA4B5I,CAAD,IAAO1B,mBAAmB,CAAC0B,CAAD,CAAnB,KAA2B,UAA7D,CACG4H,CAAAA,MAJqC,CAA5C,CAAA;EAMA,MAAMyL,SAAS,GAAGzF,wBAAwB,CACxCC,UADwC,EAExC,MACEA,UAAU,CAACyB,MAAX,EAAA,CAAoB1G,MAApB,CAA4B5I,CAAD,IAAO1B,mBAAmB,CAAC0B,CAAD,CAAnB,KAA2B,QAA7D,CACG4H,CAAAA,MAJmC,CAA1C,CAAA;EAMA,MAAM0L,QAAQ,GAAG1F,wBAAwB,CACvCC,UADuC,EAEvC,MACEA,UAAU,CAACyB,MAAX,EAAA,CAAoB1G,MAApB,CAA4B5I,CAAD,IAAO1B,mBAAmB,CAAC0B,CAAD,CAAnB,KAA2B,OAA7D,CACG4H,CAAAA,MAJkC,CAAzC,CAAA;EAMA,MAAM2L,WAAW,GAAG3F,wBAAwB,CAC1CC,UAD0C,EAE1C,MACEA,UAAU,CAACyB,MAAX,EAAA,CAAoB1G,MAApB,CAA4B5I,CAAD,IAAO1B,mBAAmB,CAAC0B,CAAD,CAAnB,KAA2B,UAA7D,CACG4H,CAAAA,MAJqC,CAA5C,CAAA;AAMA,EAAA,oBACE,KAAC,CAAA,aAAA,CAAA,SAAD,EACE,IAAA,eAAA,KAAA,CAAA,aAAA,CAAC,QAAD,EAAA;AACE,IAAA,KAAK,EAAE;MACLzL,UAAU,EAAEe,YAAK,CAACR,OADb;AAELiG,MAAAA,OAAO,EAAEwQ,QAAQ,GAAG,CAAH,GAAO,GAAA;AAFnB,KAAA;GAKD,EAAA,QAAA,eAAA,KAAA,CAAA,aAAA,CAAC,IAAD,EAAA,IAAA,EAAA,GAAA,EAAQA,QAAR,EAAA,GAAA,CANR,CADF,EAQc,GARd,eASE,KAAA,CAAA,aAAA,CAAC,QAAD,EAAA;AACE,IAAA,KAAK,EAAE;MACLhX,UAAU,EAAEe,YAAK,CAACN,MADb;AAEL+F,MAAAA,OAAO,EAAEyQ,WAAW,GAAG,CAAH,GAAO,GAAA;AAFtB,KAAA;GAKE,EAAA,WAAA,eAAA,KAAA,CAAA,aAAA,CAAC,IAAD,EAAA,IAAA,EAAA,GAAA,EAAQA,WAAR,EAAA,GAAA,CANX,CATF,EAgBc,GAhBd,eAiBE,KAAA,CAAA,aAAA,CAAC,QAAD,EAAA;AACE,IAAA,KAAK,EAAE;MACLjX,UAAU,EAAEe,YAAK,CAACL,MADb;AAEL8F,MAAAA,OAAO,EAAE0Q,SAAS,GAAG,CAAH,GAAO,GAAA;AAFpB,KAAA;GAKA,EAAA,SAAA,eAAA,KAAA,CAAA,aAAA,CAAC,IAAD,EAAA,IAAA,EAAA,GAAA,EAAQA,SAAR,EAAA,GAAA,CANT,CAjBF,EAwBc,GAxBd,eAyBE,KAAA,CAAA,aAAA,CAAC,QAAD,EAAA;AACE,IAAA,KAAK,EAAE;MACLlX,UAAU,EAAEe,YAAK,CAACJ,OADb;AAEL2G,MAAAA,KAAK,EAAE,OAFF;AAGLmB,MAAAA,UAAU,EAAE,GAHP;AAILjC,MAAAA,OAAO,EAAE2Q,QAAQ,GAAG,CAAH,GAAO,GAAA;AAJnB,KAAA;GAOD,EAAA,QAAA,eAAA,KAAA,CAAA,aAAA,CAAC,IAAD,EAAA,IAAA,EAAA,GAAA,EAAQA,QAAR,EAAA,GAAA,CARR,CAzBF,EAkCc,GAlCd,eAmCE,KAAA,CAAA,aAAA,CAAC,QAAD,EAAA;AACE,IAAA,KAAK,EAAE;MACLnX,UAAU,EAAEe,YAAK,CAACZ,IADb;AAELqG,MAAAA,OAAO,EAAE4Q,WAAW,GAAG,CAAH,GAAO,GAAA;AAFtB,KAAA;AADT,GAAA,EAAA,WAAA,eAMW,oBAAC,IAAD,EAAA,IAAA,EAAA,GAAA,EAAQA,WAAR,EAAA,GAAA,CANX,CAnCF,CADF,CAAA;AA8CD,CA7ED,CAAA;;AAsFA,MAAMC,QAAQ,gBAAGjY,KAAK,CAACkY,IAAN,CACf,CAAC;EACCzC,QADD;EAECxB,kBAFD;EAGCD,eAHD;AAIC1B,EAAAA,UAAAA;AAJD,CAAD,KAKqB;AAAA,EAAA,IAAA,sBAAA,EAAA,sBAAA,EAAA,sBAAA,EAAA,sBAAA,CAAA;;AACnB,EAAA,MAAMzN,SAAS,GAAA,CAAA,sBAAA,GACbwN,wBAAwB,CACtBC,UADsB,EAEtB,MAAA;AAAA,IAAA,IAAA,gBAAA,CAAA;;IAAA,OAAMA,CAAAA,gBAAAA,GAAAA,UAAU,CAACsD,IAAX,CAAgBH,QAAhB,CAAN,KAAA,IAAA,GAAA,KAAA,CAAA,GAAM,iBAA2B5Q,SAAjC,CAAA;GAFsB,CADX,qCAIR,EAJP,CAAA;AAMA,EAAA,MAAMlC,UAAU,GAAG0P,wBAAwB,CACzCC,UADyC,EAEzC,MAAA;AAAA,IAAA,IAAA,iBAAA,CAAA;;IAAA,OAAMA,CAAAA,iBAAAA,GAAAA,UAAU,CAACsD,IAAX,CAAgBH,QAAhB,CAAN,KAAA,IAAA,GAAA,KAAA,CAAA,GAAM,kBAA2BzS,KAAjC,CAAA;AAAA,GAFyC,CAA3C,CAAA;AAKA,EAAA,MAAMH,OAAO,GAAA,CAAA,sBAAA,GACXwP,wBAAwB,CAACC,UAAD,EAAa,MAAA;AAAA,IAAA,IAAA,iBAAA,CAAA;;IAAA,OACnCA,CAAAA,iBAAAA,GAAAA,UAAU,CAACsD,IAAX,CAAgBH,QAAhB,CADmC,KAAA,IAAA,GAAA,KAAA,CAAA,GACnC,iBAA2B5S,CAAAA,OAA3B,EADmC,CAAA;GAAb,CADb,qCAGN,KAHP,CAAA;AAKA,EAAA,MAAMsV,UAAU,GAAA,CAAA,sBAAA,GACd9F,wBAAwB,CAACC,UAAD,EAAa,MAAA;AAAA,IAAA,IAAA,iBAAA,CAAA;;IAAA,OACnCA,CAAAA,iBAAAA,GAAAA,UAAU,CAACsD,IAAX,CAAgBH,QAAhB,CADmC,KAAA,IAAA,GAAA,KAAA,CAAA,GACnC,iBAA2B0C,CAAAA,UAA3B,EADmC,CAAA;GAAb,CADV,qCAGT,KAHP,CAAA;AAKA,EAAA,MAAMvV,aAAa,GAAA,CAAA,sBAAA,GACjByP,wBAAwB,CAACC,UAAD,EAAa,MAAA;AAAA,IAAA,IAAA,iBAAA,CAAA;;IAAA,OACnCA,CAAAA,iBAAAA,GAAAA,UAAU,CAACsD,IAAX,CAAgBH,QAAhB,CADmC,KAAA,IAAA,GAAA,KAAA,CAAA,GACnC,iBAA2BxS,CAAAA,iBAA3B,EADmC,CAAA;GAAb,CADP,qCAGZ,CAHP,CAAA;;EAKA,IAAI,CAACN,UAAL,EAAiB;AACf,IAAA,OAAO,IAAP,CAAA;AACD,GAAA;;EAED,oBACE,KAAA,CAAA,aAAA,CAAA,KAAA,EAAA;AACE,IAAA,IAAI,EAAC,QADP;AAEE,IAAA,YAAA,EAAA,yBAAA,GAAsCkC,SAFxC;IAGE,OAAO,EAAE,MACPoP,kBAAkB,CAACD,eAAe,KAAKnP,SAApB,GAAgC,EAAhC,GAAqCA,SAAtC,CAJtB;AAME,IAAA,KAAK,EAAE;AACLmD,MAAAA,OAAO,EAAE,MADJ;MAELoQ,YAAY,EAAA,YAAA,GAAezW,YAAK,CAACX,OAF5B;AAGL2G,MAAAA,MAAM,EAAE,SAHH;AAIL/G,MAAAA,UAAU,EACRiE,SAAS,KAAKmP,eAAd,GAAgC,sBAAhC,GAAyDrU,SAAAA;AALtD,KAAA;GAQP,eAAA,KAAA,CAAA,aAAA,CAAA,KAAA,EAAA;AACE,IAAA,KAAK,EAAE;AACL0I,MAAAA,IAAI,EAAE,UADD;AAEL/B,MAAAA,KAAK,EAAE,KAFF;AAGLD,MAAAA,MAAM,EAAE,KAHH;MAILzF,UAAU,EAAE8B,mBAAmB,CAAC;QAC9BC,UAD8B;QAE9BE,OAF8B;QAG9BD,aAH8B;AAI9BjB,eAAAA,YAAAA;AAJ8B,OAAD,CAJ1B;AAULqG,MAAAA,OAAO,EAAE,MAVJ;AAWLoB,MAAAA,UAAU,EAAE,QAXP;AAYLyL,MAAAA,cAAc,EAAE,QAZX;AAaLlM,MAAAA,UAAU,EAAE,MAbP;AAcLU,MAAAA,UAAU,EAAExG,OAAO,GAAG,GAAH,GAAS,gBAdvB;AAeLqF,MAAAA,KAAK,EAAErF,OAAO,GAAG,OAAH,GAAa,OAAA;AAftB,KAAA;AADT,GAAA,EAmBGD,aAnBH,CAdF,EAmCGuV,UAAU,gBACT,KAAA,CAAA,aAAA,CAAA,KAAA,EAAA;AACE,IAAA,KAAK,EAAE;AACL9P,MAAAA,IAAI,EAAE,UADD;AAELhC,MAAAA,MAAM,EAAE,KAFH;MAGLzF,UAAU,EAAEe,YAAK,CAACZ,IAHb;AAILiH,MAAAA,OAAO,EAAE,MAJJ;AAKLoB,MAAAA,UAAU,EAAE,QALP;AAMLT,MAAAA,UAAU,EAAE,MANP;AAOLG,MAAAA,OAAO,EAAE,SAAA;AAPJ,KAAA;AADT,GAAA,EAAA,UAAA,CADS,GAcP,IAjDN,eAkDE,KAAA,CAAA,aAAA,CAAC,IAAD,EAAA;AACE,IAAA,KAAK,EAAE;AACLA,MAAAA,OAAO,EAAE,MAAA;AADJ,KAAA;GAIHjE,EAAAA,EAAAA,GAAAA,SALN,CAlDF,CADF,CAAA;AA4DD,CAjGc,CAAjB,CAAA;AAoGAoT,QAAQ,CAACI,WAAT,GAAuB,UAAvB;;AAGA,SAASnC,IAAT,GAAgB;;ACz0CT,MAAMxH,kBAAyD,GAKhE4J,qBALC;AAOA,MAAMpG,uBAAmE,GAK1EoG;;;;"}