import React, { useState } from 'react';
import styled from 'styled-components';
import { useQuery } from 'react-query';
import { FiSearch, FiDownload, FiEye, FiHeart } from 'react-icons/fi';
import FileCard from '../components/FileCard';
import SearchBar from '../components/SearchBar';
import FilterPanel from '../components/FilterPanel';
import { getFiles, downloadFile } from '../services/api';

const Dashboard = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  direction: ${props => props.isArabic ? 'rtl' : 'ltr'};
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
`;

const FilesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
`;

function StudentDashboard() {
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({});
  
  const { data: files, isLoading } = useQuery(
    ['files', searchTerm, filters],
    () => getFiles({ search: searchTerm, ...filters })
  );

  return (
    <Dashboard>
      <Header>
        <h1>مكتبة الملفات التعليمية</h1>
        <SearchBar value={searchTerm} onChange={setSearchTerm} />
      </Header>
      
      <FilterPanel filters={filters} onChange={setFilters} />
      
      <FilesGrid>
        {files?.map(file => (
          <FileCard key={file._id} file={file} />
        ))}
      </FilesGrid>
    </Dashboard>
  );
}

export default StudentDashboard;