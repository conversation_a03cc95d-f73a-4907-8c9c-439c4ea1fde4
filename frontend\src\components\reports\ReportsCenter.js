import React, { useState } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { useQuery } from '@tanstack/react-query';
import { toast } from 'react-toastify';
import { 
  FiBarChart,
  FiDownload, 
  FiCalendar,
  FiUsers,
  FiFileText,
  FiTrendingUp,
  FiPieChart,
  FiActivity,
  FiFilter
} from 'react-icons/fi';
import { Card, Button } from '../ui';
import { admin } from '../../services/api';
import { 
  LineChart, 
  Line, 
  AreaChart, 
  Area, 
  BarChart, 
  Bar, 
  PieChart, 
  Pie, 
  Cell, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer 
} from 'recharts';

const ReportsContainer = styled.div`
  padding: ${({ theme }) => theme.spacing[6]};
`;

const ReportsHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing[6]};
`;

const ReportsTitle = styled.h2`
  font-size: ${({ theme }) => theme.fontSizes['2xl']};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  color: ${({ theme }) => theme.colors.gray[900]};
  margin: 0;
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[3]};
`;

const FilterContainer = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing[3]};
  align-items: center;
`;

const DateRangeSelect = styled.select`
  padding: ${({ theme }) => theme.spacing[2]} ${({ theme }) => theme.spacing[3]};
  border: 1px solid ${({ theme }) => theme.colors.gray[300]};
  border-radius: ${({ theme }) => theme.borderRadius.base};
  background: white;
  font-size: ${({ theme }) => theme.fontSizes.sm};
  cursor: pointer;
`;

const ReportsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: ${({ theme }) => theme.spacing[6]};
  margin-bottom: ${({ theme }) => theme.spacing[8]};
`;

const ChartCard = styled(Card)`
  padding: ${({ theme }) => theme.spacing[6]};
`;

const ChartTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  color: ${({ theme }) => theme.colors.gray[900]};
  margin: 0 0 ${({ theme }) => theme.spacing[4]} 0;
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: ${({ theme }) => theme.spacing[4]};
  margin-bottom: ${({ theme }) => theme.spacing[6]};
`;

const StatCard = styled(motion.div)`
  background: linear-gradient(135deg, ${({ color }) => color}, ${({ color }) => color}dd);
  color: white;
  padding: ${({ theme }) => theme.spacing[6]};
  border-radius: ${({ theme }) => theme.borderRadius.xl};
  box-shadow: ${({ theme }) => theme.shadows.lg};
`;

const StatValue = styled.div`
  font-size: ${({ theme }) => theme.fontSizes['3xl']};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  margin-bottom: ${({ theme }) => theme.spacing[2]};
`;

const StatLabel = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  opacity: 0.9;
  margin-bottom: ${({ theme }) => theme.spacing[1]};
`;

const StatChange = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.xs};
  opacity: 0.8;
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[1]};
`;

const TableContainer = styled.div`
  background: white;
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  overflow: hidden;
  box-shadow: ${({ theme }) => theme.shadows.sm};
  border: 1px solid ${({ theme }) => theme.colors.gray[200]};
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
`;

const TableHeader = styled.thead`
  background: ${({ theme }) => theme.colors.gray[50]};
`;

const TableRow = styled.tr`
  border-bottom: 1px solid ${({ theme }) => theme.colors.gray[200]};
  
  &:hover {
    background: ${({ theme }) => theme.colors.gray[50]};
  }
`;

const TableCell = styled.td`
  padding: ${({ theme }) => theme.spacing[4]};
  text-align: ${({ align }) => align || 'right'};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.gray[700]};
`;

const TableHeaderCell = styled.th`
  padding: ${({ theme }) => theme.spacing[4]};
  text-align: ${({ align }) => align || 'right'};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  color: ${({ theme }) => theme.colors.gray[900]};
  font-size: ${({ theme }) => theme.fontSizes.sm};
`;

const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4'];

const ReportsCenter = () => {
  const [dateRange, setDateRange] = useState('30');

  // جلب البيانات للتقارير
  const { data: statsData } = useQuery({
    queryKey: ['admin-stats'],
    queryFn: admin.getStats,
    select: (data) => data.data,
  });

  const { data: dailyStats } = useQuery({
    queryKey: ['daily-stats', dateRange],
    queryFn: () => admin.getDailyStats(dateRange),
    select: (data) => data.data,
  });

  const { data: subjectStats } = useQuery({
    queryKey: ['subject-stats'],
    queryFn: admin.getSubjectStats,
    select: (data) => data.data,
  });

  const { data: topFiles } = useQuery({
    queryKey: ['top-files'],
    queryFn: admin.getPopularFiles,
    select: (data) => data.data,
  });

  const { data: topUsers } = useQuery({
    queryKey: ['top-users'],
    queryFn: admin.getTopUsers,
    select: (data) => data.data,
  });

  const handleExportReport = () => {
    // تصدير التقرير كـ PDF أو Excel
    toast.info('ميزة التصدير قيد التطوير');
  };

  const quickStats = [
    {
      label: 'إجمالي المستخدمين',
      value: statsData?.total_users || 0,
      change: '+12%',
      color: '#3B82F6',
      icon: <FiUsers />
    },
    {
      label: 'إجمالي الملفات',
      value: statsData?.total_files || 0,
      change: '+8%',
      color: '#10B981',
      icon: <FiFileText />
    },
    {
      label: 'إجمالي التحميلات',
      value: statsData?.total_downloads || 0,
      change: '+25%',
      color: '#F59E0B',
      icon: <FiDownload />
    },
    {
      label: 'متوسط التقييم',
      value: statsData?.overall_average_rating?.toFixed(1) || '0.0',
      change: '+0.2',
      color: '#EF4444',
      icon: <FiTrendingUp />
    }
  ];

  return (
    <ReportsContainer>
      <ReportsHeader>
        <ReportsTitle>
          <FiBarChart size={28} />
          التقارير والإحصائيات
        </ReportsTitle>
        <FilterContainer>
          <DateRangeSelect
            value={dateRange}
            onChange={(e) => setDateRange(e.target.value)}
          >
            <option value="7">آخر 7 أيام</option>
            <option value="30">آخر 30 يوم</option>
            <option value="90">آخر 3 أشهر</option>
            <option value="365">آخر سنة</option>
          </DateRangeSelect>
          <Button
            variant="outline"
            leftIcon={<FiDownload size={16} />}
            onClick={handleExportReport}
          >
            تصدير التقرير
          </Button>
        </FilterContainer>
      </ReportsHeader>

      {/* إحصائيات سريعة */}
      <StatsGrid>
        {quickStats.map((stat, index) => (
          <StatCard
            key={stat.label}
            color={stat.color}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
              <div>
                <StatLabel>{stat.label}</StatLabel>
                <StatValue>{stat.value}</StatValue>
                <StatChange>
                  <FiTrendingUp size={12} />
                  {stat.change} من الشهر الماضي
                </StatChange>
              </div>
              <div style={{ fontSize: '24px', opacity: 0.8 }}>
                {stat.icon}
              </div>
            </div>
          </StatCard>
        ))}
      </StatsGrid>

      {/* الرسوم البيانية */}
      <ReportsGrid>
        {/* رسم بياني للتحميلات اليومية */}
        <ChartCard>
          <ChartTitle>
            <FiActivity size={20} />
            التحميلات اليومية
          </ChartTitle>
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart data={dailyStats}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Area 
                type="monotone" 
                dataKey="downloads" 
                stroke="#3B82F6" 
                fill="#3B82F6" 
                fillOpacity={0.3}
              />
            </AreaChart>
          </ResponsiveContainer>
        </ChartCard>

        {/* رسم بياني للمواد */}
        <ChartCard>
          <ChartTitle>
            <FiPieChart size={20} />
            توزيع الملفات حسب المادة
          </ChartTitle>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={subjectStats}
                cx="50%"
                cy="50%"
                outerRadius={80}
                fill="#8884d8"
                dataKey="file_count"
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
              >
                {subjectStats?.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </ChartCard>
      </ReportsGrid>

      {/* جداول التفاصيل */}
      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '24px' }}>
        {/* أكثر الملفات تحميلاً */}
        <TableContainer>
          <ChartTitle style={{ padding: '24px 24px 0' }}>
            <FiTrendingUp size={20} />
            أكثر الملفات تحميلاً
          </ChartTitle>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHeaderCell>الملف</TableHeaderCell>
                <TableHeaderCell align="center">التحميلات</TableHeaderCell>
                <TableHeaderCell align="center">التقييم</TableHeaderCell>
              </TableRow>
            </TableHeader>
            <tbody>
              {topFiles?.slice(0, 5).map((file) => (
                <TableRow key={file.id}>
                  <TableCell>{file.title}</TableCell>
                  <TableCell align="center">{file.download_count}</TableCell>
                  <TableCell align="center">{file.average_rating?.toFixed(1)}</TableCell>
                </TableRow>
              ))}
            </tbody>
          </Table>
        </TableContainer>

        {/* أكثر المستخدمين نشاطاً */}
        <TableContainer>
          <ChartTitle style={{ padding: '24px 24px 0' }}>
            <FiUsers size={20} />
            أكثر المستخدمين نشاطاً
          </ChartTitle>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHeaderCell>المستخدم</TableHeaderCell>
                <TableHeaderCell align="center">الملفات</TableHeaderCell>
                <TableHeaderCell align="center">التحميلات</TableHeaderCell>
              </TableRow>
            </TableHeader>
            <tbody>
              {topUsers?.slice(0, 5).map((user) => (
                <TableRow key={user.id}>
                  <TableCell>{user.full_name || user.email}</TableCell>
                  <TableCell align="center">{user.uploaded_files}</TableCell>
                  <TableCell align="center">{user.total_downloads}</TableCell>
                </TableRow>
              ))}
            </tbody>
          </Table>
        </TableContainer>
      </div>
    </ReportsContainer>
  );
};

export default ReportsCenter;
