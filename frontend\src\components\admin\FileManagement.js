import React, { useState } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  FiFile, 
  FiSearch, 
  FiFilter,
  FiEdit,
  FiTrash2,
  FiDownload,
  FiEye,
  FiCalendar,
  FiUser,
  FiMoreVertical,
  FiFileText,
  FiVideo,
  FiMusic,
  FiImage
} from 'react-icons/fi';
import { Card, Button, Input } from '../ui';

const FileManagementContainer = styled.div`
  padding: ${({ theme }) => theme.spacing[6]};
`;

const PageHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: ${({ theme }) => theme.spacing[8]};
  
  @media (max-width: ${({ theme }) => theme.breakpoints.md}) {
    flex-direction: column;
    gap: ${({ theme }) => theme.spacing[4]};
    align-items: stretch;
  }
`;

const HeaderLeft = styled.div`
  display: flex;
  flex-direction: column;
`;

const PageTitle = styled.h1`
  font-size: ${({ theme }) => theme.fontSizes['3xl']};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  color: ${({ theme }) => theme.colors.gray[900]};
  margin-bottom: ${({ theme }) => theme.spacing[2]};
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[3]};
`;

const PageSubtitle = styled.p`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  color: ${({ theme }) => theme.colors.gray[600]};
`;

const SearchAndFilter = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing[4]};
  margin-bottom: ${({ theme }) => theme.spacing[6]};
  
  @media (max-width: ${({ theme }) => theme.breakpoints.md}) {
    flex-direction: column;
  }
`;

const SearchContainer = styled.div`
  flex: 1;
  max-width: 400px;
`;

const FilesTable = styled(Card)`
  overflow: hidden;
`;

const TableHeader = styled.div`
  display: grid;
  grid-template-columns: 1fr 150px 120px 150px 100px 60px;
  gap: ${({ theme }) => theme.spacing[4]};
  padding: ${({ theme }) => theme.spacing[4]} ${({ theme }) => theme.spacing[6]};
  background: ${({ theme }) => theme.colors.gray[50]};
  border-bottom: 1px solid ${({ theme }) => theme.colors.gray[200]};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  color: ${({ theme }) => theme.colors.gray[700]};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  
  @media (max-width: ${({ theme }) => theme.breakpoints.lg}) {
    grid-template-columns: 1fr 100px 60px;
    
    .hide-mobile {
      display: none;
    }
  }
`;

const TableBody = styled.div`
  max-height: 600px;
  overflow-y: auto;
`;

const FileRow = styled(motion.div)`
  display: grid;
  grid-template-columns: 1fr 150px 120px 150px 100px 60px;
  gap: ${({ theme }) => theme.spacing[4]};
  padding: ${({ theme }) => theme.spacing[4]} ${({ theme }) => theme.spacing[6]};
  border-bottom: 1px solid ${({ theme }) => theme.colors.gray[100]};
  align-items: center;
  transition: all ${({ theme }) => theme.transitions.fast};
  
  &:hover {
    background: ${({ theme }) => theme.colors.gray[25]};
  }
  
  &:last-child {
    border-bottom: none;
  }
  
  @media (max-width: ${({ theme }) => theme.breakpoints.lg}) {
    grid-template-columns: 1fr 100px 60px;
    
    .hide-mobile {
      display: none;
    }
  }
`;

const FileInfo = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[3]};
`;

const FileIcon = styled.div`
  width: 40px;
  height: 40px;
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: white;
  
  ${({ fileType, theme }) => {
    switch (fileType) {
      case 'pdf':
        return `background: linear-gradient(135deg, ${theme.colors.error[500]}, ${theme.colors.error[600]});`;
      case 'doc':
      case 'docx':
        return `background: linear-gradient(135deg, ${theme.colors.primary[500]}, ${theme.colors.primary[600]});`;
      case 'ppt':
      case 'pptx':
        return `background: linear-gradient(135deg, ${theme.colors.warning[500]}, ${theme.colors.warning[600]});`;
      case 'mp4':
      case 'avi':
        return `background: linear-gradient(135deg, ${theme.colors.secondary[500]}, ${theme.colors.secondary[600]});`;
      case 'mp3':
      case 'wav':
        return `background: linear-gradient(135deg, ${theme.colors.success[500]}, ${theme.colors.success[600]});`;
      case 'jpg':
      case 'png':
        return `background: linear-gradient(135deg, ${theme.colors.primary[400]}, ${theme.colors.primary[500]});`;
      default:
        return `background: linear-gradient(135deg, ${theme.colors.gray[500]}, ${theme.colors.gray[600]});`;
    }
  }}
`;

const FileDetails = styled.div`
  flex: 1;
  min-width: 0;
`;

const FileName = styled.div`
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  color: ${({ theme }) => theme.colors.gray[900]};
  margin-bottom: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

const FileSize = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.gray[500]};
`;

const TypeBadge = styled.div`
  display: inline-flex;
  align-items: center;
  padding: ${({ theme }) => theme.spacing[1]} ${({ theme }) => theme.spacing[2]};
  border-radius: ${({ theme }) => theme.borderRadius.base};
  font-size: ${({ theme }) => theme.fontSizes.xs};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  background: ${({ theme }) => theme.colors.gray[100]};
  color: ${({ theme }) => theme.colors.gray[700]};
  text-transform: uppercase;
`;

const DownloadCount = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[1]};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.gray[600]};
`;

const ActionButton = styled(motion.button)`
  width: 32px;
  height: 32px;
  border: none;
  border-radius: ${({ theme }) => theme.borderRadius.base};
  background: ${({ theme }) => theme.colors.gray[100]};
  color: ${({ theme }) => theme.colors.gray[600]};
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all ${({ theme }) => theme.transitions.fast};
  
  &:hover {
    background: ${({ theme }) => theme.colors.gray[200]};
    color: ${({ theme }) => theme.colors.gray[800]};
  }
`;

const EmptyState = styled.div`
  text-align: center;
  padding: ${({ theme }) => theme.spacing[12]} ${({ theme }) => theme.spacing[4]};
  color: ${({ theme }) => theme.colors.gray[500]};
`;

const FileManagement = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');

  // Mock data - في التطبيق الحقيقي سيتم جلبها من API
  const files = [
    {
      id: 1,
      title: 'مقدمة في البرمجة',
      originalName: 'programming_intro.pdf',
      type: 'pdf',
      size: 2048576, // 2MB
      uploadedBy: 'أحمد محمد',
      uploadDate: '2024-01-15',
      downloadCount: 234
    },
    {
      id: 2,
      title: 'أساسيات الرياضيات',
      originalName: 'math_basics.docx',
      type: 'docx',
      size: 1024000, // 1MB
      uploadedBy: 'فاطمة علي',
      uploadDate: '2024-01-12',
      downloadCount: 156
    },
    {
      id: 3,
      title: 'شرح الفيزياء',
      originalName: 'physics_explanation.mp4',
      type: 'mp4',
      size: 52428800, // 50MB
      uploadedBy: 'محمد حسن',
      uploadDate: '2024-01-10',
      downloadCount: 89
    },
    {
      id: 4,
      title: 'تاريخ الحضارات',
      originalName: 'civilizations_history.pptx',
      type: 'pptx',
      size: 5242880, // 5MB
      uploadedBy: 'سارة أحمد',
      uploadDate: '2024-01-08',
      downloadCount: 67
    }
  ];

  const getFileIcon = (fileType) => {
    const type = fileType?.toLowerCase();
    switch (type) {
      case 'pdf':
        return <FiFileText />;
      case 'doc':
      case 'docx':
        return <FiFile />;
      case 'ppt':
      case 'pptx':
        return <FiFile />;
      case 'mp4':
      case 'avi':
        return <FiVideo />;
      case 'mp3':
      case 'wav':
        return <FiMusic />;
      case 'jpg':
      case 'png':
        return <FiImage />;
      default:
        return <FiFile />;
    }
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA');
  };

  const filteredFiles = files.filter(file => {
    const matchesSearch = file.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         file.originalName.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = filterType === 'all' || file.type === filterType;
    
    return matchesSearch && matchesType;
  });

  return (
    <FileManagementContainer>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <PageHeader>
          <HeaderLeft>
            <PageTitle>
              <FiFile size={32} />
              إدارة الملفات
            </PageTitle>
            <PageSubtitle>إدارة وتنظيم جميع الملفات المرفوعة في النظام</PageSubtitle>
          </HeaderLeft>
        </PageHeader>

        <SearchAndFilter>
          <SearchContainer>
            <Input
              placeholder="البحث في الملفات..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              leftIcon={<FiSearch size={18} />}
            />
          </SearchContainer>
          
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
            style={{
              padding: '12px 16px',
              border: '2px solid #e5e7eb',
              borderRadius: '8px',
              background: 'white',
              minWidth: '120px'
            }}
          >
            <option value="all">جميع الأنواع</option>
            <option value="pdf">PDF</option>
            <option value="docx">Word</option>
            <option value="pptx">PowerPoint</option>
            <option value="mp4">فيديو</option>
            <option value="mp3">صوت</option>
          </select>
        </SearchAndFilter>

        <FilesTable>
          <TableHeader>
            <div>الملف</div>
            <div className="hide-mobile">النوع</div>
            <div className="hide-mobile">التحميلات</div>
            <div className="hide-mobile">المرفوع بواسطة</div>
            <div className="hide-mobile">التاريخ</div>
            <div>إجراءات</div>
          </TableHeader>
          
          <TableBody>
            <AnimatePresence>
              {filteredFiles.length === 0 ? (
                <EmptyState>
                  <FiFile size={48} style={{ marginBottom: '16px', opacity: 0.5 }} />
                  <div>لا توجد ملفات</div>
                </EmptyState>
              ) : (
                filteredFiles.map((file, index) => (
                  <FileRow
                    key={file.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ delay: index * 0.05 }}
                  >
                    <FileInfo>
                      <FileIcon fileType={file.type}>
                        {getFileIcon(file.type)}
                      </FileIcon>
                      <FileDetails>
                        <FileName>{file.title}</FileName>
                        <FileSize>{formatFileSize(file.size)}</FileSize>
                      </FileDetails>
                    </FileInfo>
                    
                    <div className="hide-mobile">
                      <TypeBadge>{file.type.toUpperCase()}</TypeBadge>
                    </div>
                    
                    <div className="hide-mobile">
                      <DownloadCount>
                        <FiDownload size={14} />
                        {file.downloadCount}
                      </DownloadCount>
                    </div>
                    
                    <div className="hide-mobile">{file.uploadedBy}</div>
                    <div className="hide-mobile">{formatDate(file.uploadDate)}</div>
                    
                    <ActionButton
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                    >
                      <FiMoreVertical size={16} />
                    </ActionButton>
                  </FileRow>
                ))
              )}
            </AnimatePresence>
          </TableBody>
        </FilesTable>
      </motion.div>
    </FileManagementContainer>
  );
};

export default FileManagement;
