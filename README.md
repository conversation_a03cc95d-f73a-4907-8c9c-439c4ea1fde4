# 📚 مكتبة الملفات التعليمية

نظام إدارة الملفات التعليمية - منصة شاملة لتنظيم ومشاركة الملفات التعليمية مع واجهة مستخدم احترافية وحديثة.

## ✨ المميزات

### 🎨 تصميم احترافي وحديث
- واجهة مستخدم عصرية مع تصميم متجاوب
- نظام ألوان موحد ومتسق
- رسوم متحركة وتأثيرات بصرية جذابة
- دعم كامل للغة العربية مع اتجاه RTL

### 👥 إدارة المستخدمين
- نظام تسجيل دخول آمن
- أدوار مختلفة (طالب، مدير)
- إدارة شاملة للمستخدمين
- حماية الصفحات حسب الصلاحيات

### 📁 إدارة الملفات
- رفع الملفات بسهولة مع Drag & Drop
- دعم أنواع ملفات متعددة (PDF, DOC, PPT, MP4, MP3, صور)
- معاينة الملفات
- نظام تقييم وتفضيل الملفات
- إحصائيات التحميل

### 🔍 البحث والتصفية
- بحث متقدم في الملفات
- فلترة حسب النوع، المادة، الفصل الدراسي
- اقتراحات البحث الذكية
- حفظ عمليات البحث الأخيرة

### 📊 لوحة تحكم إدارية
- إحصائيات شاملة للنظام
- إدارة الملفات والمستخدمين
- تحليلات الاستخدام
- تقارير مفصلة

## 🛠️ التقنيات المستخدمة

### Frontend
- **React 18** - مكتبة JavaScript لبناء واجهات المستخدم
- **Styled Components** - CSS-in-JS للتصميم
- **Framer Motion** - مكتبة الرسوم المتحركة
- **React Router** - التنقل بين الصفحات
- **React Query** - إدارة حالة الخادم
- **React Dropzone** - رفع الملفات
- **React Icons** - مجموعة أيقونات شاملة
- **React Toastify** - إشعارات المستخدم
- **i18next** - دعم تعدد اللغات

### Backend
- **Supabase** - منصة Backend-as-a-Service شاملة
- **PostgreSQL** - قاعدة البيانات (عبر Supabase)
- **Supabase Auth** - نظام المصادقة والتفويض
- **Supabase Storage** - تخزين الملفات
- **Row Level Security (RLS)** - أمان على مستوى الصفوف
- **Real-time Subscriptions** - التحديثات المباشرة

## 🚀 التثبيت والتشغيل

### المتطلبات
- Node.js (الإصدار 16 أو أحدث)
- حساب Supabase (مجاني)
- npm أو yarn

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone https://github.com/your-username/educational-file-manager.git
cd educational-file-manager
```

2. **إعداد Supabase**
   - إنشاء حساب على [Supabase](https://supabase.com)
   - إنشاء مشروع جديد
   - الحصول على URL و API Key من إعدادات المشروع

3. **تثبيت التبعيات**
```bash
# تثبيت تبعيات Frontend
cd frontend
npm install
```

4. **إعداد متغيرات البيئة**
```bash
# في مجلد frontend
cp .env.example .env
```

قم بتحرير ملف `.env` وإضافة مفاتيح Supabase:
```env
REACT_APP_SUPABASE_URL=your_supabase_project_url
REACT_APP_SUPABASE_ANON_KEY=your_supabase_anon_key
```

5. **إعداد قاعدة البيانات**
   - انتقل إلى SQL Editor في Supabase Dashboard
   - قم بتشغيل الـ SQL scripts الموجودة في مجلد `database/`
   - أو استخدم الـ migrations المتوفرة

6. **تشغيل التطبيق**
```bash
# من مجلد frontend
npm start
```

سيتم تشغيل التطبيق على: http://localhost:3000

## 🗄️ إعداد Supabase

### إنشاء الجداول
قم بتشغيل الـ SQL التالي في Supabase SQL Editor:

```sql
-- إنشاء جدول profiles
CREATE TABLE IF NOT EXISTS public.profiles (
  id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  full_name TEXT,
  role TEXT DEFAULT 'student' CHECK (role IN ('student', 'admin')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول files
CREATE TABLE IF NOT EXISTS public.files (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT,
  file_name TEXT NOT NULL,
  file_path TEXT NOT NULL,
  file_type TEXT NOT NULL,
  file_size BIGINT NOT NULL,
  subject TEXT,
  semester TEXT,
  uploaded_by UUID REFERENCES public.profiles(id),
  download_count INTEGER DEFAULT 0,
  average_rating DECIMAL(3,2) DEFAULT 0.0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### إعداد Row Level Security (RLS)
```sql
-- تفعيل RLS
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.files ENABLE ROW LEVEL SECURITY;

-- سياسات الأمان
CREATE POLICY "Users can view own profile" ON public.profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Anyone can view active files" ON public.files
  FOR SELECT USING (is_active = true);
```

### إعداد Storage
1. انتقل إلى Storage في Supabase Dashboard
2. أنشئ bucket جديد باسم `educational-files`
3. اضبط الإعدادات: Public: false, File size limit: 100MB

## 📱 الاستجابة للأجهزة

التطبيق مُحسَّن للعمل على جميع الأجهزة:
- 📱 الهواتف الذكية (320px+)
- 📱 الأجهزة اللوحية (768px+)
- 💻 أجهزة الكمبيوتر المحمولة (1024px+)
- 🖥️ شاشات سطح المكتب (1280px+)

## 🎨 نظام التصميم

### الألوان
- **الأساسي**: درجات الأزرق (#0ea5e9)
- **الثانوي**: درجات البنفسجي (#d946ef)
- **النجاح**: درجات الأخضر (#22c55e)
- **التحذير**: درجات الأصفر (#f59e0b)
- **الخطأ**: درجات الأحمر (#ef4444)

### الخطوط
- **الأساسي**: Cairo (للنصوص العربية)
- **الثانوي**: Roboto (للنصوص الإنجليزية)
- **الكود**: Fira Code (للكود البرمجي)

## 🔧 البنية والمجلدات

```
educational-file-manager/
├── frontend/
│   ├── public/
│   ├── src/
│   │   ├── components/
│   │   │   ├── ui/           # مكونات UI أساسية
│   │   │   ├── admin/        # مكونات لوحة الإدارة
│   │   │   └── ...
│   │   ├── pages/            # صفحات التطبيق
│   │   ├── contexts/         # React Contexts
│   │   ├── services/         # خدمات API
│   │   ├── styles/           # ملفات التصميم
│   │   └── ...
│   └── package.json
├── backend/
│   ├── models/               # نماذج قاعدة البيانات
│   ├── routes/               # مسارات API
│   ├── middleware/           # Middleware functions
│   ├── uploads/              # مجلد الملفات المرفوعة
│   └── server.js
└── package.json
```

## 🧪 الاختبار

```bash
# تشغيل اختبارات Frontend
cd frontend
npm test

# تشغيل اختبارات Backend
cd backend
npm test
```

## 📦 البناء للإنتاج

```bash
# بناء Frontend
cd frontend
npm run build

# تشغيل Backend في وضع الإنتاج
cd backend
npm start
```

## 🤝 المساهمة

نرحب بمساهماتكم! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push للفرع (`git push origin feature/amazing-feature`)
5. فتح Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 👨‍💻 المطورون

- **اسم المطور** - [GitHub](https://github.com/username)

## 📞 التواصل

- البريد الإلكتروني: <EMAIL>
- الموقع: https://example.com
- Twitter: [@username](https://twitter.com/username)

## 🙏 شكر وتقدير

شكر خاص لجميع المساهمين والمكتبات مفتوحة المصدر المستخدمة في هذا المشروع.

---

⭐ إذا أعجبك هذا المشروع، لا تنس إعطاؤه نجمة على GitHub!
