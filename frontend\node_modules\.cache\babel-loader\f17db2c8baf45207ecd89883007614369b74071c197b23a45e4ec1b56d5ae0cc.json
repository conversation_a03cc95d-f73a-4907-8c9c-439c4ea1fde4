{"ast": null, "code": "'use client';\n\nimport { extends as _extends } from './_virtual/_rollupPluginBabelHelpers.mjs';\nimport * as React from 'react';\nconst defaultTheme = {\n  background: '#0b1521',\n  backgroundAlt: '#132337',\n  foreground: 'white',\n  gray: '#3f4e60',\n  grayAlt: '#222e3e',\n  inputBackgroundColor: '#fff',\n  inputTextColor: '#000',\n  success: '#00ab52',\n  danger: '#ff0085',\n  active: '#006bff',\n  paused: '#8c49eb',\n  warning: '#ffb200'\n};\nconst ThemeContext = /*#__PURE__*/React.createContext(defaultTheme);\nfunction ThemeProvider({\n  theme,\n  ...rest\n}) {\n  return /*#__PURE__*/React.createElement(ThemeContext.Provider, _extends({\n    value: theme\n  }, rest));\n}\nfunction useTheme() {\n  return React.useContext(ThemeContext);\n}\nexport { ThemeProvider, defaultTheme, useTheme };", "map": {"version": 3, "names": ["defaultTheme", "background", "backgroundAlt", "foreground", "gray", "grayAlt", "inputBackgroundColor", "inputTextColor", "success", "danger", "active", "paused", "warning", "ThemeContext", "React", "createContext", "ThemeProvider", "value", "theme", "rest", "useTheme", "useContext"], "sources": ["D:\\menasa\\frontend\\node_modules\\@tanstack\\react-query-devtools\\src\\theme.tsx"], "sourcesContent": ["'use client'\nimport * as React from 'react'\n\nexport const defaultTheme = {\n  background: '#0b1521',\n  backgroundAlt: '#132337',\n  foreground: 'white',\n  gray: '#3f4e60',\n  grayAlt: '#222e3e',\n  inputBackgroundColor: '#fff',\n  inputTextColor: '#000',\n  success: '#00ab52',\n  danger: '#ff0085',\n  active: '#006bff',\n  paused: '#8c49eb',\n  warning: '#ffb200',\n} as const\n\nexport type Theme = typeof defaultTheme\ninterface ProviderProps {\n  theme: Theme\n  children?: React.ReactNode\n}\n\nconst ThemeContext = React.createContext(defaultTheme)\n\nexport function ThemeProvider({ theme, ...rest }: ProviderProps) {\n  return <ThemeContext.Provider value={theme} {...rest} />\n}\n\nexport function useTheme() {\n  return React.useContext(ThemeContext)\n}\n"], "mappings": ";;;;AAGO,MAAAA,YAAA;EACLC,UAAA;EACAC,aAAA;EACAC,UAAA;EACAC,IAAA;EACAC,OAAA;EACAC,oBAAA;EACAC,cAAA;EACAC,OAAA;EACAC,MAAA;EACAC,MAAA;EACAC,MAAA;EACAC,OAAA;AAZ0B;AAqB5B,MAAAC,YAAA,gBAAAC,KAAA,CAAAC,aAAA,CAAAf,YAAA;AAEO,SAAAgB,cAAA;;;AAAuB;;IACEC,KAAA,EAAAC;EAAvB,GAAAC,IAAA;AACR;AAEM,SAAAC,SAAA;EACL,OAAAN,KAAA,CAAAO,UAAA,CAAAR,YAAA;AACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}