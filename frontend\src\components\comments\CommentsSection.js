import React, { useState } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { 
  FiMessageCircle, 
  FiSend, 
  FiHeart,
  FiMoreVertical,
  FiEdit,
  FiTrash2,
  <PERSON><PERSON><PERSON>ly,
  FiUser
} from 'react-icons/fi';
import { useAuth } from '../../contexts/AuthContext';
import { comments } from '../../services/api';
import { Button } from '../ui';
import { formatDistanceToNow } from 'date-fns';
import { ar } from 'date-fns/locale';
import { toast } from 'react-toastify';

const CommentsContainer = styled.div`
  background: white;
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  padding: ${({ theme }) => theme.spacing[6]};
  box-shadow: ${({ theme }) => theme.shadows.sm};
  border: 1px solid ${({ theme }) => theme.colors.gray[200]};
`;

const CommentsHeader = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[2]};
  margin-bottom: ${({ theme }) => theme.spacing[6]};
  padding-bottom: ${({ theme }) => theme.spacing[4]};
  border-bottom: 1px solid ${({ theme }) => theme.colors.gray[200]};
`;

const CommentsTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  color: ${({ theme }) => theme.colors.gray[900]};
  margin: 0;
`;

const CommentsCount = styled.span`
  background: ${({ theme }) => theme.colors.gray[100]};
  color: ${({ theme }) => theme.colors.gray[600]};
  padding: ${({ theme }) => theme.spacing[1]} ${({ theme }) => theme.spacing[2]};
  border-radius: ${({ theme }) => theme.borderRadius.full};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
`;

const CommentForm = styled.form`
  margin-bottom: ${({ theme }) => theme.spacing[6]};
`;

const CommentInputContainer = styled.div`
  position: relative;
  background: ${({ theme }) => theme.colors.gray[50]};
  border: 1px solid ${({ theme }) => theme.colors.gray[200]};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  padding: ${({ theme }) => theme.spacing[4]};
  transition: all 0.2s ease;
  
  &:focus-within {
    border-color: ${({ theme }) => theme.colors.primary[500]};
    background: white;
    box-shadow: ${({ theme }) => theme.shadows.sm};
  }
`;

const CommentInput = styled.textarea`
  width: 100%;
  border: none;
  outline: none;
  background: transparent;
  resize: vertical;
  min-height: 80px;
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.gray[900]};
  line-height: ${({ theme }) => theme.lineHeights.relaxed};
  
  &::placeholder {
    color: ${({ theme }) => theme.colors.gray[400]};
  }
`;

const CommentActions = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: ${({ theme }) => theme.spacing[3]};
  padding-top: ${({ theme }) => theme.spacing[3]};
  border-top: 1px solid ${({ theme }) => theme.colors.gray[200]};
`;

const CommentsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing[4]};
`;

const CommentItem = styled(motion.div)`
  background: ${({ theme }) => theme.colors.gray[50]};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  padding: ${({ theme }) => theme.spacing[4]};
  border: 1px solid ${({ theme }) => theme.colors.gray[100]};
`;

const CommentHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: ${({ theme }) => theme.spacing[3]};
`;

const CommentAuthor = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[3]};
`;

const AuthorAvatar = styled.div`
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: ${({ theme }) => theme.colors.primary[500]};
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  font-size: ${({ theme }) => theme.fontSizes.sm};
`;

const AuthorInfo = styled.div`
  display: flex;
  flex-direction: column;
`;

const AuthorName = styled.span`
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  color: ${({ theme }) => theme.colors.gray[900]};
  font-size: ${({ theme }) => theme.fontSizes.sm};
`;

const CommentTime = styled.span`
  font-size: ${({ theme }) => theme.fontSizes.xs};
  color: ${({ theme }) => theme.colors.gray[500]};
`;

const CommentMenu = styled.div`
  position: relative;
`;

const MenuButton = styled(motion.button)`
  background: none;
  border: none;
  padding: ${({ theme }) => theme.spacing[1]};
  border-radius: ${({ theme }) => theme.borderRadius.base};
  cursor: pointer;
  color: ${({ theme }) => theme.colors.gray[400]};
  
  &:hover {
    background: ${({ theme }) => theme.colors.gray[200]};
    color: ${({ theme }) => theme.colors.gray[600]};
  }
`;

const CommentText = styled.p`
  margin: 0 0 ${({ theme }) => theme.spacing[3]} 0;
  color: ${({ theme }) => theme.colors.gray[700]};
  line-height: ${({ theme }) => theme.lineHeights.relaxed};
  font-size: ${({ theme }) => theme.fontSizes.sm};
`;

const CommentFooter = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[4]};
`;

const CommentAction = styled(motion.button)`
  background: none;
  border: none;
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[1]};
  padding: ${({ theme }) => theme.spacing[1]} ${({ theme }) => theme.spacing[2]};
  border-radius: ${({ theme }) => theme.borderRadius.base};
  cursor: pointer;
  font-size: ${({ theme }) => theme.fontSizes.xs};
  color: ${({ theme }) => theme.colors.gray[500]};
  transition: all 0.2s ease;
  
  &:hover {
    background: ${({ theme }) => theme.colors.gray[100]};
    color: ${({ theme }) => theme.colors.gray[700]};
  }
  
  &.liked {
    color: ${({ theme }) => theme.colors.error[500]};
  }
`;

const EmptyState = styled.div`
  text-align: center;
  padding: ${({ theme }) => theme.spacing[8]};
  color: ${({ theme }) => theme.colors.gray[500]};
`;

const CommentsSection = ({ fileId }) => {
  const [newComment, setNewComment] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { user } = useAuth();
  const queryClient = useQueryClient();

  // جلب التعليقات
  const { data: commentsData, isLoading } = useQuery({
    queryKey: ['comments', fileId],
    queryFn: () => comments.getByFileId(fileId),
    enabled: !!fileId,
  });

  const handleSubmitComment = async (e) => {
    e.preventDefault();
    if (!newComment.trim() || isSubmitting) return;

    setIsSubmitting(true);
    try {
      await comments.create({
        file_id: fileId,
        content: newComment.trim()
      });
      
      setNewComment('');
      queryClient.invalidateQueries(['comments', fileId]);
      toast.success('تم إضافة التعليق بنجاح');
    } catch (error) {
      toast.error('فشل في إضافة التعليق');
      console.error('Error adding comment:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleLikeComment = async (commentId, isLiked) => {
    try {
      await comments.toggleLike(commentId);
      queryClient.invalidateQueries(['comments', fileId]);
    } catch (error) {
      toast.error('فشل في تحديث الإعجاب');
      console.error('Error toggling like:', error);
    }
  };

  const handleDeleteComment = async (commentId) => {
    if (window.confirm('هل أنت متأكد من حذف هذا التعليق؟')) {
      try {
        await comments.delete(commentId);
        queryClient.invalidateQueries(['comments', fileId]);
        toast.success('تم حذف التعليق');
      } catch (error) {
        toast.error('فشل في حذف التعليق');
        console.error('Error deleting comment:', error);
      }
    }
  };

  const formatTime = (dateString) => {
    return formatDistanceToNow(new Date(dateString), {
      addSuffix: true,
      locale: ar
    });
  };

  if (!user) {
    return (
      <CommentsContainer>
        <EmptyState>
          <FiMessageCircle size={48} style={{ marginBottom: '16px', opacity: 0.3 }} />
          <div>يجب تسجيل الدخول لعرض التعليقات</div>
        </EmptyState>
      </CommentsContainer>
    );
  }

  return (
    <CommentsContainer>
      <CommentsHeader>
        <FiMessageCircle size={20} />
        <CommentsTitle>التعليقات</CommentsTitle>
        <CommentsCount>{commentsData?.length || 0}</CommentsCount>
      </CommentsHeader>

      <CommentForm onSubmit={handleSubmitComment}>
        <CommentInputContainer>
          <CommentInput
            value={newComment}
            onChange={(e) => setNewComment(e.target.value)}
            placeholder="اكتب تعليقك هنا..."
            rows={3}
          />
          <CommentActions>
            <div style={{ fontSize: '12px', color: '#6b7280' }}>
              {newComment.length}/500 حرف
            </div>
            <Button
              type="submit"
              variant="primary"
              size="sm"
              disabled={!newComment.trim() || isSubmitting}
              leftIcon={<FiSend size={14} />}
            >
              {isSubmitting ? 'جاري الإرسال...' : 'إرسال'}
            </Button>
          </CommentActions>
        </CommentInputContainer>
      </CommentForm>

      <CommentsList>
        {isLoading ? (
          <EmptyState>جاري تحميل التعليقات...</EmptyState>
        ) : commentsData?.length === 0 ? (
          <EmptyState>
            <FiMessageCircle size={48} style={{ marginBottom: '16px', opacity: 0.3 }} />
            <div>لا توجد تعليقات بعد</div>
            <div style={{ fontSize: '14px', marginTop: '8px' }}>كن أول من يعلق على هذا الملف</div>
          </EmptyState>
        ) : (
          commentsData?.map((comment, index) => (
            <CommentItem
              key={comment.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <CommentHeader>
                <CommentAuthor>
                  <AuthorAvatar>
                    {comment.user_name?.charAt(0)?.toUpperCase() || <FiUser />}
                  </AuthorAvatar>
                  <AuthorInfo>
                    <AuthorName>{comment.user_name || 'مستخدم'}</AuthorName>
                    <CommentTime>{formatTime(comment.created_at)}</CommentTime>
                  </AuthorInfo>
                </CommentAuthor>
                
                {comment.user_id === user.id && (
                  <CommentMenu>
                    <MenuButton
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      onClick={() => handleDeleteComment(comment.id)}
                    >
                      <FiTrash2 size={14} />
                    </MenuButton>
                  </CommentMenu>
                )}
              </CommentHeader>

              <CommentText>{comment.content}</CommentText>

              <CommentFooter>
                <CommentAction
                  className={comment.user_liked ? 'liked' : ''}
                  onClick={() => handleLikeComment(comment.id, comment.user_liked)}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <FiHeart size={14} />
                  {comment.likes_count || 0}
                </CommentAction>
              </CommentFooter>
            </CommentItem>
          ))
        )}
      </CommentsList>
    </CommentsContainer>
  );
};

export default CommentsSection;
