import { supabase, handleSupabaseError, uploadFile as supabaseUploadFile, downloadFile as supabaseDownloadFile } from '../lib/supabase';

// Auth services
export const auth = {
  // تسجيل الدخول
  login: async (credentials) => {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email: credentials.email,
        password: credentials.password,
      });

      if (error) throw error;

      // الحصول على بيانات المستخدم الإضافية
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', data.user.id)
        .single();

      if (profileError) throw profileError;

      return {
        data: {
          user: { ...data.user, ...profile },
          session: data.session
        }
      };
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  // تسجيل حساب جديد
  register: async (userData) => {
    try {
      const { data, error } = await supabase.auth.signUp({
        email: userData.email,
        password: userData.password,
        options: {
          data: {
            full_name: userData.full_name,
            role: userData.role || 'student'
          }
        }
      });

      if (error) throw error;

      return { data };
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  // الحصول على بيانات المستخدم الحالي
  getProfile: async () => {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError) throw userError;
      if (!user) throw new Error('لم يتم العثور على المستخدم');

      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      if (profileError) throw profileError;

      return {
        data: { ...user, ...profile }
      };
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  // تسجيل الخروج
  logout: async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  // تحديث الملف الشخصي
  updateProfile: async (updates) => {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError) throw userError;

      const { data, error } = await supabase
        .from('profiles')
        .update(updates)
        .eq('id', user.id)
        .select()
        .single();

      if (error) throw error;
      return { data };
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  }
};

// Files services
export const files = {
  // الحصول على جميع الملفات مع الفلترة والبحث
  getAll: async (params = {}) => {
    try {
      let query = supabase
        .from('files_with_uploader')
        .select('*')
        .eq('is_active', true);

      // البحث في العنوان والوصف
      if (params.search) {
        query = query.or(`title.ilike.%${params.search}%,description.ilike.%${params.search}%`);
      }

      // فلترة حسب المادة
      if (params.subject) {
        query = query.eq('subject', params.subject);
      }

      // فلترة حسب الفصل الدراسي
      if (params.semester) {
        query = query.eq('semester', params.semester);
      }

      // فلترة حسب نوع الملف
      if (params.fileTypes && params.fileTypes.length > 0) {
        query = query.in('file_type', params.fileTypes);
      }

      // فلترة حسب التقييم
      if (params.minRating) {
        query = query.gte('average_rating', params.minRating);
      }
      if (params.maxRating) {
        query = query.lte('average_rating', params.maxRating);
      }

      // ترتيب النتائج
      const sortBy = params.sortBy || 'created_at';
      const sortOrder = params.sortOrder || 'desc';
      query = query.order(sortBy, { ascending: sortOrder === 'asc' });

      // تحديد عدد النتائج
      if (params.limit) {
        query = query.limit(params.limit);
      }

      const { data, error } = await query;
      if (error) throw error;

      return { data };
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  // الحصول على ملف واحد
  getById: async (id) => {
    try {
      const { data, error } = await supabase
        .from('files_with_uploader')
        .select('*')
        .eq('id', id)
        .eq('is_active', true)
        .single();

      if (error) throw error;
      return { data };
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  // رفع ملف جديد
  upload: async (fileData, onProgress) => {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError) throw userError;

      // إنشاء مسار فريد للملف
      const fileExt = fileData.file.name.split('.').pop();
      const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;
      const filePath = `${user.id}/${fileName}`;

      // رفع الملف إلى Storage
      const { data: uploadData, error: uploadError } = await supabaseUploadFile(fileData.file, filePath);
      if (uploadError) throw uploadError;

      // حفظ معلومات الملف في قاعدة البيانات
      const { data, error } = await supabase
        .from('files')
        .insert({
          title: fileData.title,
          description: fileData.description,
          file_name: fileData.file.name,
          file_path: filePath,
          file_type: fileExt.toLowerCase(),
          file_size: fileData.file.size,
          subject: fileData.subject,
          semester: fileData.semester,
          uploaded_by: user.id
        })
        .select()
        .single();

      if (error) throw error;
      return { data };
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  // تحميل ملف
  download: async (id) => {
    try {
      // الحصول على معلومات الملف
      const { data: fileInfo, error: fileError } = await supabase
        .from('files')
        .select('file_path, file_name')
        .eq('id', id)
        .single();

      if (fileError) throw fileError;

      // تسجيل التحميل
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        await supabase
          .from('downloads')
          .insert({
            file_id: id,
            user_id: user.id
          });
      }

      // تحميل الملف من Storage
      const fileBlob = await supabaseDownloadFile(fileInfo.file_path);
      return { data: fileBlob, fileName: fileInfo.file_name };
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  // تقييم ملف
  rate: async (fileId, rating) => {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError) throw userError;

      const { data, error } = await supabase
        .from('ratings')
        .upsert({
          file_id: fileId,
          user_id: user.id,
          rating: rating
        })
        .select()
        .single();

      if (error) throw error;
      return { data };
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  // إضافة/إزالة من المفضلة
  toggleFavorite: async (fileId) => {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError) throw userError;

      // التحقق من وجود الملف في المفضلة
      const { data: existing, error: checkError } = await supabase
        .from('favorites')
        .select('id')
        .eq('file_id', fileId)
        .eq('user_id', user.id)
        .single();

      if (checkError && checkError.code !== 'PGRST116') throw checkError;

      if (existing) {
        // إزالة من المفضلة
        const { error } = await supabase
          .from('favorites')
          .delete()
          .eq('id', existing.id);

        if (error) throw error;
        return { data: { isFavorite: false } };
      } else {
        // إضافة للمفضلة
        const { data, error } = await supabase
          .from('favorites')
          .insert({
            file_id: fileId,
            user_id: user.id
          })
          .select()
          .single();

        if (error) throw error;
        return { data: { isFavorite: true } };
      }
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  // الحصول على المفضلة للمستخدم
  getFavorites: async () => {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError) throw userError;

      const { data, error } = await supabase
        .from('favorites')
        .select(`
          *,
          files:file_id (
            *,
            profiles:uploaded_by (full_name, email)
          )
        `)
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return { data };
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  }
};

// Admin services
export const admin = {
  // الحصول على الإحصائيات العامة
  getStats: async () => {
    try {
      const { data, error } = await supabase
        .from('admin_stats')
        .select('*')
        .single();

      if (error) throw error;
      return { data };
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  // الحصول على جميع المستخدمين
  getUsers: async (params = {}) => {
    try {
      let query = supabase
        .from('profiles')
        .select('*')
        .order('created_at', { ascending: false });

      // فلترة حسب الدور
      if (params.role) {
        query = query.eq('role', params.role);
      }

      // البحث في الاسم والبريد الإلكتروني
      if (params.search) {
        query = query.or(`full_name.ilike.%${params.search}%,email.ilike.%${params.search}%`);
      }

      const { data, error } = await query;
      if (error) throw error;

      return { data };
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  // تحديث مستخدم
  updateUser: async (id, updates) => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return { data };
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  // حذف مستخدم
  deleteUser: async (id) => {
    try {
      const { error } = await supabase
        .from('profiles')
        .delete()
        .eq('id', id);

      if (error) throw error;
      return { success: true };
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  // الحصول على الملفات الشائعة
  getPopularFiles: async () => {
    try {
      const { data, error } = await supabase
        .from('popular_files')
        .select('*');

      if (error) throw error;
      return { data };
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  // الحصول على الملفات الحديثة
  getRecentFiles: async () => {
    try {
      const { data, error } = await supabase
        .from('recent_files')
        .select('*');

      if (error) throw error;
      return { data };
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  // الحصول على الأنشطة الحديثة
  getRecentActivities: async () => {
    try {
      const { data, error } = await supabase
        .from('recent_activities')
        .select('*');

      if (error) throw error;
      return { data };
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  // الحصول على إحصائيات المواد
  getSubjectStats: async () => {
    try {
      const { data, error } = await supabase
        .from('subject_stats')
        .select('*');

      if (error) throw error;
      return { data };
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  // حذف ملف
  deleteFile: async (id) => {
    try {
      // الحصول على معلومات الملف أولاً
      const { data: fileInfo, error: fileError } = await supabase
        .from('files')
        .select('file_path')
        .eq('id', id)
        .single();

      if (fileError) throw fileError;

      // حذف الملف من Storage
      const { error: storageError } = await supabase.storage
        .from('educational-files')
        .remove([fileInfo.file_path]);

      if (storageError) throw storageError;

      // حذف سجل الملف من قاعدة البيانات
      const { error } = await supabase
        .from('files')
        .delete()
        .eq('id', id);

      if (error) throw error;
      return { success: true };
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  // تحديث ملف
  updateFile: async (id, updates) => {
    try {
      const { data, error } = await supabase
        .from('files')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return { data };
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  // الحصول على الإحصائيات اليومية
  getDailyStats: async (days = 30) => {
    try {
      const { data, error } = await supabase
        .from('daily_downloads')
        .select('*')
        .limit(parseInt(days));

      if (error) throw error;
      return { data };
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  // الحصول على أكثر المستخدمين نشاطاً
  getTopUsers: async () => {
    try {
      const { data, error } = await supabase
        .from('user_stats')
        .select('*')
        .order('uploaded_files', { ascending: false })
        .limit(10);

      if (error) throw error;
      return { data };
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  }
};

// Legacy exports for backward compatibility
export const getFiles = files.getAll;
export const downloadFile = files.download;
export const uploadFile = files.upload;

// خدمة الإشعارات
export const notifications = {
  // جلب جميع الإشعارات للمستخدم الحالي
  getAll: async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('notifications')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data;
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  // تحديد إشعار كمقروء
  markAsRead: async (notificationId) => {
    try {
      const { error } = await supabase
        .from('notifications')
        .update({ is_read: true })
        .eq('id', notificationId);

      if (error) throw error;
      return { success: true };
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  // تحديد جميع الإشعارات كمقروءة
  markAllAsRead: async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { error } = await supabase
        .from('notifications')
        .update({ is_read: true })
        .eq('user_id', user.id);

      if (error) throw error;
      return { success: true };
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  // مسح جميع الإشعارات
  clearAll: async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { error } = await supabase
        .from('notifications')
        .delete()
        .eq('user_id', user.id);

      if (error) throw error;
      return { success: true };
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  // إنشاء إشعار جديد
  create: async (notificationData) => {
    try {
      const { data, error } = await supabase
        .from('notifications')
        .insert(notificationData)
        .select()
        .single();

      if (error) throw error;
      return { data };
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  // جلب عدد الإشعارات غير المقروءة
  getUnreadCount: async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { count, error } = await supabase
        .from('notifications')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', user.id)
        .eq('is_read', false);

      if (error) throw error;
      return count;
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  }
};

// خدمة التعليقات
export const comments = {
  // جلب تعليقات ملف معين
  getByFileId: async (fileId) => {
    try {
      const { data, error } = await supabase
        .from('comments_with_user')
        .select('*')
        .eq('file_id', fileId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data;
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  // إنشاء تعليق جديد
  create: async (commentData) => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('comments')
        .insert({
          ...commentData,
          user_id: user.id
        })
        .select()
        .single();

      if (error) throw error;
      return { data };
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  // حذف تعليق
  delete: async (commentId) => {
    try {
      const { error } = await supabase
        .from('comments')
        .delete()
        .eq('id', commentId);

      if (error) throw error;
      return { success: true };
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  // تحديث تعليق
  update: async (commentId, updates) => {
    try {
      const { data, error } = await supabase
        .from('comments')
        .update(updates)
        .eq('id', commentId)
        .select()
        .single();

      if (error) throw error;
      return { data };
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  // إعجاب/إلغاء إعجاب بتعليق
  toggleLike: async (commentId) => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // التحقق من وجود إعجاب سابق
      const { data: existing, error: checkError } = await supabase
        .from('comment_likes')
        .select('id')
        .eq('comment_id', commentId)
        .eq('user_id', user.id)
        .single();

      if (checkError && checkError.code !== 'PGRST116') {
        throw checkError;
      }

      if (existing) {
        // إزالة الإعجاب
        const { error } = await supabase
          .from('comment_likes')
          .delete()
          .eq('id', existing.id);

        if (error) throw error;
        return { data: { isLiked: false } };
      } else {
        // إضافة إعجاب
        const { data, error } = await supabase
          .from('comment_likes')
          .insert({
            comment_id: commentId,
            user_id: user.id
          })
          .select()
          .single();

        if (error) throw error;
        return { data: { isLiked: true } };
      }
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  }
};

// خدمة المشاركة
export const sharing = {
  // إنشاء رابط مشاركة
  createShareLink: async (fileId, options = {}) => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const shareData = {
        file_id: fileId,
        created_by: user.id,
        share_type: options.type || 'public',
        settings: options.settings || {},
        expires_at: options.settings?.expiresIn && options.settings.expiresIn !== '0'
          ? new Date(Date.now() + parseInt(options.settings.expiresIn) * 24 * 60 * 60 * 1000).toISOString()
          : null
      };

      const { data, error } = await supabase
        .from('file_shares')
        .insert(shareData)
        .select()
        .single();

      if (error) throw error;

      // إنشاء URL المشاركة
      const shareUrl = `${window.location.origin}/shared/${data.id}`;

      return {
        data: {
          ...data,
          shareUrl
        }
      };
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  // الحصول على معلومات المشاركة
  getShareInfo: async (shareId) => {
    try {
      const { data, error } = await supabase
        .from('file_shares_with_file')
        .select('*')
        .eq('id', shareId)
        .single();

      if (error) throw error;

      // التحقق من انتهاء الصلاحية
      if (data.expires_at && new Date(data.expires_at) < new Date()) {
        throw new Error('انتهت صلاحية رابط المشاركة');
      }

      return { data };
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  // حذف رابط مشاركة
  deleteShareLink: async (shareId) => {
    try {
      const { error } = await supabase
        .from('file_shares')
        .delete()
        .eq('id', shareId);

      if (error) throw error;
      return { success: true };
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  // الحصول على جميع روابط المشاركة لملف
  getFileShares: async (fileId) => {
    try {
      const { data, error } = await supabase
        .from('file_shares')
        .select('*')
        .eq('file_id', fileId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return { data };
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  },

  // تحديث إعدادات المشاركة
  updateShareSettings: async (shareId, settings) => {
    try {
      const { data, error } = await supabase
        .from('file_shares')
        .update({ settings })
        .eq('id', shareId)
        .select()
        .single();

      if (error) throw error;
      return { data };
    } catch (error) {
      throw new Error(handleSupabaseError(error));
    }
  }
};

// خدمة النسخ الاحتياطي
export const backup = {
  // الحصول على معلومات النسخ الاحتياطي
  getInfo: async () => {
    try {
      // محاكاة البيانات - في التطبيق الحقيقي ستأتي من Supabase
      const mockData = {
        lastBackup: {
          id: '1',
          status: 'success',
          created_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
          size: 1024 * 1024 * 50, // 50MB
          file_count: 125,
          progress: 100
        },
        autoBackup: true,
        frequency: 'daily',
        retention: 10
      };

      return { data: mockData };
    } catch (error) {
      throw new Error('فشل في جلب معلومات النسخ الاحتياطي');
    }
  },

  // الحصول على قائمة النسخ الاحتياطية
  getList: async () => {
    try {
      // محاكاة البيانات
      const mockBackups = [
        {
          id: '1',
          status: 'success',
          created_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
          size: 1024 * 1024 * 50,
          file_count: 125
        },
        {
          id: '2',
          status: 'success',
          created_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          size: 1024 * 1024 * 48,
          file_count: 120
        },
        {
          id: '3',
          status: 'success',
          created_at: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
          size: 1024 * 1024 * 45,
          file_count: 115
        }
      ];

      return { data: mockBackups };
    } catch (error) {
      throw new Error('فشل في جلب قائمة النسخ الاحتياطية');
    }
  },

  // إنشاء نسخة احتياطية جديدة
  create: async () => {
    try {
      // في التطبيق الحقيقي، سيتم استخدام Supabase Functions
      // لإنشاء نسخة احتياطية من قاعدة البيانات والملفات

      // محاكاة العملية
      await new Promise(resolve => setTimeout(resolve, 2000));

      return {
        data: {
          id: Date.now().toString(),
          status: 'running',
          message: 'تم بدء إنشاء النسخة الاحتياطية'
        }
      };
    } catch (error) {
      throw new Error('فشل في إنشاء النسخة الاحتياطية');
    }
  },

  // استعادة نسخة احتياطية
  restore: async (backupId) => {
    try {
      // في التطبيق الحقيقي، سيتم استخدام Supabase Functions
      // لاستعادة البيانات من النسخة الاحتياطية

      // محاكاة العملية
      await new Promise(resolve => setTimeout(resolve, 3000));

      return {
        data: {
          message: 'تم بدء استعادة النسخة الاحتياطية'
        }
      };
    } catch (error) {
      throw new Error('فشل في استعادة النسخة الاحتياطية');
    }
  },

  // تحميل نسخة احتياطية
  download: async (backupId) => {
    try {
      // في التطبيق الحقيقي، سيتم تحميل الملف من Supabase Storage

      // محاكاة تحميل ملف
      const mockData = new Blob(['Mock backup data'], { type: 'application/zip' });

      return {
        data: mockData,
        fileName: `backup-${backupId}.zip`
      };
    } catch (error) {
      throw new Error('فشل في تحميل النسخة الاحتياطية');
    }
  },

  // حذف نسخة احتياطية
  delete: async (backupId) => {
    try {
      // في التطبيق الحقيقي، سيتم حذف الملف من Supabase Storage

      // محاكاة العملية
      await new Promise(resolve => setTimeout(resolve, 1000));

      return { success: true };
    } catch (error) {
      throw new Error('فشل في حذف النسخة الاحتياطية');
    }
  },

  // تحديث إعدادات النسخ الاحتياطي
  updateSettings: async (settings) => {
    try {
      // في التطبيق الحقيقي، سيتم حفظ الإعدادات في قاعدة البيانات

      return {
        data: settings,
        message: 'تم تحديث إعدادات النسخ الاحتياطي'
      };
    } catch (error) {
      throw new Error('فشل في تحديث الإعدادات');
    }
  }
};

export default { auth, files, admin, notifications, comments, sharing, backup };