import React from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { 
  FiHome, 
  FiFile, 
  FiUpload, 
  FiUsers, 
  FiSettings,
  FiBarChart3,
  FiBook,
  FiTrendingUp
} from 'react-icons/fi';

const SidebarContainer = styled(motion.div)`
  width: 280px;
  min-height: 100vh;
  background: linear-gradient(180deg, 
    ${({ theme }) => theme.colors.gray[900]} 0%, 
    ${({ theme }) => theme.colors.gray[800]} 100%);
  color: ${({ theme }) => theme.colors.white};
  padding: ${({ theme }) => theme.spacing[6]} 0;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, 
      ${({ theme }) => theme.colors.primary[600]}20 0%, 
      transparent 50%);
    pointer-events: none;
  }
`;

const SidebarHeader = styled.div`
  padding: 0 ${({ theme }) => theme.spacing[6]};
  margin-bottom: ${({ theme }) => theme.spacing[8]};
  position: relative;
  z-index: 1;
`;

const Logo = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[3]};
  margin-bottom: ${({ theme }) => theme.spacing[2]};
`;

const LogoIcon = styled.div`
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, ${({ theme }) => theme.colors.primary[500]}, ${({ theme }) => theme.colors.primary[600]});
  border-radius: ${({ theme }) => theme.borderRadius.xl};
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: ${({ theme }) => theme.fontSizes.xl};
  box-shadow: ${({ theme }) => theme.shadows.lg};
`;

const LogoText = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.xl};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  color: ${({ theme }) => theme.colors.white};
`;

const AdminTitle = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.gray[300]};
  margin-top: ${({ theme }) => theme.spacing[1]};
`;

const Navigation = styled.nav`
  position: relative;
  z-index: 1;
`;

const NavSection = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing[6]};
`;

const SectionTitle = styled.div`
  padding: 0 ${({ theme }) => theme.spacing[6]};
  margin-bottom: ${({ theme }) => theme.spacing[3]};
  font-size: ${({ theme }) => theme.fontSizes.xs};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  color: ${({ theme }) => theme.colors.gray[400]};
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

const NavItem = styled(motion.button)`
  width: 100%;
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[3]};
  padding: ${({ theme }) => theme.spacing[3]} ${({ theme }) => theme.spacing[6]};
  background: none;
  border: none;
  color: ${({ theme }) => theme.colors.gray[300]};
  font-size: ${({ theme }) => theme.fontSizes.base};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  text-align: right;
  cursor: pointer;
  transition: all ${({ theme }) => theme.transitions.fast};
  position: relative;
  
  &:hover {
    background: ${({ theme }) => theme.colors.gray[800]};
    color: ${({ theme }) => theme.colors.white};
  }
  
  ${({ active, theme }) =>
    active &&
    `
    background: linear-gradient(90deg, 
      ${theme.colors.primary[600]}40 0%, 
      ${theme.colors.primary[500]}20 100%);
    color: ${theme.colors.white};
    border-right: 3px solid ${theme.colors.primary[500]};
    
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 3px;
      background: ${theme.colors.primary[500]};
    }
  `}
`;

const NavIcon = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  font-size: 18px;
`;

const NavText = styled.span`
  flex: 1;
`;

const Badge = styled.div`
  background: ${({ theme }) => theme.colors.primary[500]};
  color: ${({ theme }) => theme.colors.white};
  font-size: ${({ theme }) => theme.fontSizes.xs};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  padding: 2px 6px;
  border-radius: ${({ theme }) => theme.borderRadius.full};
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const SidebarFooter = styled.div`
  position: absolute;
  bottom: ${({ theme }) => theme.spacing[6]};
  left: ${({ theme }) => theme.spacing[6]};
  right: ${({ theme }) => theme.spacing[6]};
  padding-top: ${({ theme }) => theme.spacing[6]};
  border-top: 1px solid ${({ theme }) => theme.colors.gray[700]};
  z-index: 1;
`;

const FooterText = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.xs};
  color: ${({ theme }) => theme.colors.gray[400]};
  text-align: center;
`;

const Sidebar = ({ activeTab, onTabChange }) => {
  const menuItems = [
    {
      section: 'الرئيسية',
      items: [
        {
          id: 'dashboard',
          label: 'لوحة التحكم',
          icon: <FiHome />,
        },
        {
          id: 'analytics',
          label: 'التحليلات',
          icon: <FiBarChart3 />,
          badge: 'جديد'
        }
      ]
    },
    {
      section: 'إدارة المحتوى',
      items: [
        {
          id: 'files',
          label: 'إدارة الملفات',
          icon: <FiFile />,
        },
        {
          id: 'upload',
          label: 'رفع ملف جديد',
          icon: <FiUpload />,
        }
      ]
    },
    {
      section: 'إدارة النظام',
      items: [
        {
          id: 'users',
          label: 'إدارة المستخدمين',
          icon: <FiUsers />,
        },
        {
          id: 'settings',
          label: 'الإعدادات',
          icon: <FiSettings />,
        }
      ]
    }
  ];

  return (
    <SidebarContainer
      initial={{ x: -280 }}
      animate={{ x: 0 }}
      transition={{ duration: 0.3, ease: 'easeOut' }}
    >
      <SidebarHeader>
        <Logo>
          <LogoIcon>
            <FiBook />
          </LogoIcon>
          <div>
            <LogoText>المكتبة التعليمية</LogoText>
            <AdminTitle>لوحة الإدارة</AdminTitle>
          </div>
        </Logo>
      </SidebarHeader>

      <Navigation>
        {menuItems.map((section, sectionIndex) => (
          <NavSection key={section.section}>
            <SectionTitle>{section.section}</SectionTitle>
            {section.items.map((item, itemIndex) => (
              <NavItem
                key={item.id}
                active={activeTab === item.id}
                onClick={() => onTabChange(item.id)}
                whileHover={{ x: 5 }}
                whileTap={{ scale: 0.98 }}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ 
                  delay: (sectionIndex * 0.1) + (itemIndex * 0.05),
                  duration: 0.3 
                }}
              >
                <NavIcon>{item.icon}</NavIcon>
                <NavText>{item.label}</NavText>
                {item.badge && <Badge>{item.badge}</Badge>}
              </NavItem>
            ))}
          </NavSection>
        ))}
      </Navigation>

      <SidebarFooter>
        <FooterText>
          نظام إدارة الملفات التعليمية
          <br />
          الإصدار 1.0.0
        </FooterText>
      </SidebarFooter>
    </SidebarContainer>
  );
};

export default Sidebar;
