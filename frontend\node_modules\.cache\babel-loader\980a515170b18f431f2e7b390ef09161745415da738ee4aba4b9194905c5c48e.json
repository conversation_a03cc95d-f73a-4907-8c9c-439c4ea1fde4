{"ast": null, "code": "import { createSelector } from 'reselect';\nimport sortBy from 'es-toolkit/compat/sortBy';\nexport var selectLegendSettings = state => state.legend.settings;\nexport var selectLegendSize = state => state.legend.size;\nvar selectAllLegendPayload2DArray = state => state.legend.payload;\nexport var selectLegendPayload = createSelector([selectAllLegendPayload2DArray, selectLegendSettings], (payloads, _ref) => {\n  var {\n    itemSorter\n  } = _ref;\n  var flat = payloads.flat(1);\n  return itemSorter ? sortBy(flat, itemSorter) : flat;\n});", "map": {"version": 3, "names": ["createSelector", "sortBy", "selectLegendSettings", "state", "legend", "settings", "selectLegendSize", "size", "selectAllLegendPayload2DArray", "payload", "selectLegendPayload", "payloads", "_ref", "itemSorter", "flat"], "sources": ["D:/menasa/frontend/node_modules/recharts/es6/state/selectors/legendSelectors.js"], "sourcesContent": ["import { createSelector } from 'reselect';\nimport sortBy from 'es-toolkit/compat/sortBy';\nexport var selectLegendSettings = state => state.legend.settings;\nexport var selectLegendSize = state => state.legend.size;\nvar selectAllLegendPayload2DArray = state => state.legend.payload;\nexport var selectLegendPayload = createSelector([selectAllLegendPayload2DArray, selectLegendSettings], (payloads, _ref) => {\n  var {\n    itemSorter\n  } = _ref;\n  var flat = payloads.flat(1);\n  return itemSorter ? sortBy(flat, itemSorter) : flat;\n});"], "mappings": "AAAA,SAASA,cAAc,QAAQ,UAAU;AACzC,OAAOC,MAAM,MAAM,0BAA0B;AAC7C,OAAO,IAAIC,oBAAoB,GAAGC,KAAK,IAAIA,KAAK,CAACC,MAAM,CAACC,QAAQ;AAChE,OAAO,IAAIC,gBAAgB,GAAGH,KAAK,IAAIA,KAAK,CAACC,MAAM,CAACG,IAAI;AACxD,IAAIC,6BAA6B,GAAGL,KAAK,IAAIA,KAAK,CAACC,MAAM,CAACK,OAAO;AACjE,OAAO,IAAIC,mBAAmB,GAAGV,cAAc,CAAC,CAACQ,6BAA6B,EAAEN,oBAAoB,CAAC,EAAE,CAACS,QAAQ,EAAEC,IAAI,KAAK;EACzH,IAAI;IACFC;EACF,CAAC,GAAGD,IAAI;EACR,IAAIE,IAAI,GAAGH,QAAQ,CAACG,IAAI,CAAC,CAAC,CAAC;EAC3B,OAAOD,UAAU,GAAGZ,MAAM,CAACa,IAAI,EAAED,UAAU,CAAC,GAAGC,IAAI;AACrD,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}