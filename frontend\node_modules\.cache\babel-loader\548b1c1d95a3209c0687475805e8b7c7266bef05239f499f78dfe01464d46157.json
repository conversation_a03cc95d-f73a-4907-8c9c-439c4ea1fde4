{"ast": null, "code": "var _jsxFileName = \"D:\\\\menasa\\\\frontend\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query';\n// import { ReactQueryDevtools } from '@tanstack/react-query-devtools';\nimport { ToastContainer } from 'react-toastify';\nimport 'react-toastify/dist/ReactToastify.css';\nimport { AuthProvider } from './contexts/AuthContext';\nimport { ThemeProvider } from './contexts/ThemeContext';\nimport ProtectedRoute from './components/ProtectedRoute';\nimport ErrorBoundary from './components/ErrorBoundary';\nimport PWAInstallPrompt from './components/pwa/PWAInstallPrompt';\nimport Navbar from './components/Navbar';\nimport Login from './pages/Login';\nimport StudentDashboard from './pages/StudentDashboard';\nimport AdminDashboard from './pages/AdminDashboard';\nimport FileViewer from './pages/FileViewer';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst queryClient = new QueryClient({\n  defaultOptions: {\n    queries: {\n      retry: 1,\n      refetchOnWindowFocus: false,\n      staleTime: 5 * 60 * 1000 // 5 minutes\n    }\n  }\n});\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n    children: /*#__PURE__*/_jsxDEV(QueryClientProvider, {\n      client: queryClient,\n      children: [/*#__PURE__*/_jsxDEV(ThemeProvider, {\n        children: /*#__PURE__*/_jsxDEV(AuthProvider, {\n          children: /*#__PURE__*/_jsxDEV(Router, {\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"App\",\n              children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 37,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Routes, {\n                children: [/*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/\",\n                  element: /*#__PURE__*/_jsxDEV(Navigate, {\n                    to: \"/login\",\n                    replace: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 39,\n                    columnNumber: 44\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 39,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/login\",\n                  element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 40,\n                    columnNumber: 49\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 40,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/student\",\n                  element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                    role: \"student\",\n                    children: /*#__PURE__*/_jsxDEV(StudentDashboard, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 43,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 42,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 41,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/admin\",\n                  element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                    role: \"admin\",\n                    children: /*#__PURE__*/_jsxDEV(AdminDashboard, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 48,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 47,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 46,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Route, {\n                  path: \"/file/:id\",\n                  element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                    children: /*#__PURE__*/_jsxDEV(FileViewer, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 53,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 52,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 51,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 38,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(ToastContainer, {\n                position: \"top-left\",\n                autoClose: 3000,\n                hideProgressBar: false,\n                newestOnTop: false,\n                closeOnClick: true,\n                rtl: true,\n                pauseOnFocusLoss: true,\n                draggable: true,\n                pauseOnHover: true,\n                theme: \"light\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 57,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(PWAInstallPrompt, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 69,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 36,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ReactQueryDevtools, {\n        initialIsOpen: false\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 31,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "QueryClient", "QueryClientProvider", "ToastContainer", "<PERSON>th<PERSON><PERSON><PERSON>", "ThemeProvider", "ProtectedRoute", "Error<PERSON>ou<PERSON><PERSON>", "PWAInstallPrompt", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "StudentDashboard", "AdminDashboard", "File<PERSON>iewer", "jsxDEV", "_jsxDEV", "queryClient", "defaultOptions", "queries", "retry", "refetchOnWindowFocus", "staleTime", "App", "children", "client", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "to", "replace", "role", "position", "autoClose", "hideProgressBar", "newestOnTop", "closeOnClick", "rtl", "pauseOnFocusLoss", "draggable", "pauseOnHover", "theme", "ReactQueryDevtools", "initialIsOpen", "_c", "$RefreshReg$"], "sources": ["D:/menasa/frontend/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query';\n// import { ReactQueryDevtools } from '@tanstack/react-query-devtools';\nimport { ToastContainer } from 'react-toastify';\nimport 'react-toastify/dist/ReactToastify.css';\n\nimport { AuthProvider } from './contexts/AuthContext';\nimport { ThemeProvider } from './contexts/ThemeContext';\nimport ProtectedRoute from './components/ProtectedRoute';\nimport ErrorBoundary from './components/ErrorBoundary';\nimport PWAInstallPrompt from './components/pwa/PWAInstallPrompt';\nimport Navbar from './components/Navbar';\nimport Login from './pages/Login';\nimport StudentDashboard from './pages/StudentDashboard';\nimport AdminDashboard from './pages/AdminDashboard';\nimport FileViewer from './pages/FileViewer';\n\nconst queryClient = new QueryClient({\n  defaultOptions: {\n    queries: {\n      retry: 1,\n      refetchOnWindowFocus: false,\n      staleTime: 5 * 60 * 1000, // 5 minutes\n    },\n  },\n});\n\nfunction App() {\n  return (\n    <ErrorBoundary>\n      <QueryClientProvider client={queryClient}>\n        <ThemeProvider>\n          <AuthProvider>\n            <Router>\n              <div className=\"App\">\n                <Navbar />\n                <Routes>\n                  <Route path=\"/\" element={<Navigate to=\"/login\" replace />} />\n                  <Route path=\"/login\" element={<Login />} />\n                  <Route path=\"/student\" element={\n                    <ProtectedRoute role=\"student\">\n                      <StudentDashboard />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/admin\" element={\n                    <ProtectedRoute role=\"admin\">\n                      <AdminDashboard />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/file/:id\" element={\n                    <ProtectedRoute>\n                      <FileViewer />\n                    </ProtectedRoute>\n                  } />\n                </Routes>\n                <ToastContainer\n                  position=\"top-left\"\n                  autoClose={3000}\n                  hideProgressBar={false}\n                  newestOnTop={false}\n                  closeOnClick\n                  rtl={true}\n                  pauseOnFocusLoss\n                  draggable\n                  pauseOnHover\n                  theme=\"light\"\n                />\n                <PWAInstallPrompt />\n              </div>\n            </Router>\n          </AuthProvider>\n        </ThemeProvider>\n        <ReactQueryDevtools initialIsOpen={false} />\n      </QueryClientProvider>\n    </ErrorBoundary>\n  );\n}\n\nexport default App;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,uBAAuB;AACxE;AACA,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,OAAO,uCAAuC;AAE9C,SAASC,YAAY,QAAQ,wBAAwB;AACrD,SAASC,aAAa,QAAQ,yBAAyB;AACvD,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,gBAAgB,MAAM,mCAAmC;AAChE,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,gBAAgB,MAAM,0BAA0B;AACvD,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,UAAU,MAAM,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5C,MAAMC,WAAW,GAAG,IAAIf,WAAW,CAAC;EAClCgB,cAAc,EAAE;IACdC,OAAO,EAAE;MACPC,KAAK,EAAE,CAAC;MACRC,oBAAoB,EAAE,KAAK;MAC3BC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAE;IAC5B;EACF;AACF,CAAC,CAAC;AAEF,SAASC,GAAGA,CAAA,EAAG;EACb,oBACEP,OAAA,CAACR,aAAa;IAAAgB,QAAA,eACZR,OAAA,CAACb,mBAAmB;MAACsB,MAAM,EAAER,WAAY;MAAAO,QAAA,gBACvCR,OAAA,CAACV,aAAa;QAAAkB,QAAA,eACZR,OAAA,CAACX,YAAY;UAAAmB,QAAA,eACXR,OAAA,CAAClB,MAAM;YAAA0B,QAAA,eACLR,OAAA;cAAKU,SAAS,EAAC,KAAK;cAAAF,QAAA,gBAClBR,OAAA,CAACN,MAAM;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACVd,OAAA,CAACjB,MAAM;gBAAAyB,QAAA,gBACLR,OAAA,CAAChB,KAAK;kBAAC+B,IAAI,EAAC,GAAG;kBAACC,OAAO,eAAEhB,OAAA,CAACf,QAAQ;oBAACgC,EAAE,EAAC,QAAQ;oBAACC,OAAO;kBAAA;oBAAAP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC7Dd,OAAA,CAAChB,KAAK;kBAAC+B,IAAI,EAAC,QAAQ;kBAACC,OAAO,eAAEhB,OAAA,CAACL,KAAK;oBAAAgB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC3Cd,OAAA,CAAChB,KAAK;kBAAC+B,IAAI,EAAC,UAAU;kBAACC,OAAO,eAC5BhB,OAAA,CAACT,cAAc;oBAAC4B,IAAI,EAAC,SAAS;oBAAAX,QAAA,eAC5BR,OAAA,CAACJ,gBAAgB;sBAAAe,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN;gBACjB;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACJd,OAAA,CAAChB,KAAK;kBAAC+B,IAAI,EAAC,QAAQ;kBAACC,OAAO,eAC1BhB,OAAA,CAACT,cAAc;oBAAC4B,IAAI,EAAC,OAAO;oBAAAX,QAAA,eAC1BR,OAAA,CAACH,cAAc;sBAAAc,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ;gBACjB;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACJd,OAAA,CAAChB,KAAK;kBAAC+B,IAAI,EAAC,WAAW;kBAACC,OAAO,eAC7BhB,OAAA,CAACT,cAAc;oBAAAiB,QAAA,eACbR,OAAA,CAACF,UAAU;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA;gBACjB;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACTd,OAAA,CAACZ,cAAc;gBACbgC,QAAQ,EAAC,UAAU;gBACnBC,SAAS,EAAE,IAAK;gBAChBC,eAAe,EAAE,KAAM;gBACvBC,WAAW,EAAE,KAAM;gBACnBC,YAAY;gBACZC,GAAG,EAAE,IAAK;gBACVC,gBAAgB;gBAChBC,SAAS;gBACTC,YAAY;gBACZC,KAAK,EAAC;cAAO;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CAAC,eACFd,OAAA,CAACP,gBAAgB;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAChBd,OAAA,CAAC8B,kBAAkB;QAACC,aAAa,EAAE;MAAM;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEpB;AAACkB,EAAA,GAjDQzB,GAAG;AAmDZ,eAAeA,GAAG;AAAC,IAAAyB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}