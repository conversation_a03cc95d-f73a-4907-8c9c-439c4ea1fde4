# 🚀 دليل التشغيل السريع

## متطلبات النظام

- Node.js (الإصدار 16 أو أحدث)
- حساب Supabase (مجاني)
- Git

## التشغيل السريع

### 1. تحميل المشروع
```bash
git clone <repository-url>
cd educational-file-manager
```

### 2. التشغيل التلقائي

#### على Windows:
```bash
start.bat
```

#### على Linux/Mac:
```bash
chmod +x start.sh
./start.sh
```

### 3. التشغيل اليدوي

#### إعداد Supabase:
1. إنشاء حساب على [Supabase](https://supabase.com)
2. إنشاء مشروع جديد
3. الحصول على URL و API Key من إعدادات المشروع
4. تشغيل SQL scripts من مجلد `database/` في SQL Editor

#### تثبيت التبعيات:
```bash
# تبعيات Frontend
cd frontend
npm install
```

#### إعداد متغيرات البيئة:
```bash
cd frontend
cp .env.example .env
# قم بتحرير ملف .env وإضافة مفاتيح Supabase
```

#### تشغيل التطبيق:
```bash
# من مجلد frontend
npm start
```

## الوصول للتطبيق

- **التطبيق**: http://localhost:3000

## إنشاء حسابات

### إنشاء حساب مدير:
1. سجل حساب جديد من صفحة التسجيل
2. انتقل إلى Supabase Dashboard > Authentication > Users
3. ابحث عن المستخدم وغير role من 'student' إلى 'admin'

### إنشاء حساب طالب:
- سجل حساب جديد من صفحة التسجيل (الدور الافتراضي: طالب)

## الميزات الرئيسية

### للطلاب:
- ✅ تصفح الملفات التعليمية
- ✅ البحث والفلترة المتقدمة
- ✅ تحميل الملفات
- ✅ تقييم الملفات
- ✅ إضافة للمفضلة
- ✅ معاينة الملفات

### للمديرين:
- ✅ رفع الملفات الجديدة
- ✅ إدارة المستخدمين
- ✅ إحصائيات شاملة
- ✅ إدارة الملفات
- ✅ تحليلات الاستخدام

## استكشاف الأخطاء

### مشكلة في تثبيت التبعيات:
```bash
# مسح cache npm
npm cache clean --force

# حذف node_modules وإعادة التثبيت
rm -rf node_modules package-lock.json
npm install
```

### مشكلة في الاتصال بـ Supabase:
- تحقق من صحة SUPABASE_URL و SUPABASE_ANON_KEY في ملف .env
- تأكد من أن المشروع نشط في Supabase Dashboard

### مشكلة في تشغيل Frontend:
```bash
cd frontend
npm start
```

### مشكلة في قاعدة البيانات:
- تأكد من تشغيل SQL scripts في Supabase SQL Editor
- تحقق من إعدادات RLS policies

## الدعم

إذا واجهت أي مشاكل:
1. تحقق من ملف README.md للتفاصيل الكاملة
2. راجع ملفات الـ logs للأخطاء
3. تأكد من تثبيت جميع المتطلبات
4. تواصل مع فريق التطوير

## نصائح مفيدة

- استخدم `npm run dev` لتشغيل Frontend و Backend معاً
- استخدم `npm run build` لبناء النسخة النهائية
- راجع ملف package.json لمعرفة جميع الأوامر المتاحة

---

🎉 **مبروك! التطبيق جاهز للاستخدام**
