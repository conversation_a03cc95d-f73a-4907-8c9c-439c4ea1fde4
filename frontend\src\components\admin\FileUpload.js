import React, { useState } from 'react';
import { useDropzone } from 'react-dropzone';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { FiUpload, FiFile, FiX, FiCheck, FiAlertCircle } from 'react-icons/fi';
import { files } from '../../services/api';
import { toast } from 'react-toastify';
import { Card, Button, Input } from '../ui';

const UploadContainer = styled.div`
  padding: ${({ theme }) => theme.spacing[6]};
`;

const PageHeader = styled.div`
  margin-bottom: ${({ theme }) => theme.spacing[8]};
`;

const PageTitle = styled.h1`
  font-size: ${({ theme }) => theme.fontSizes['3xl']};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  color: ${({ theme }) => theme.colors.gray[900]};
  margin-bottom: ${({ theme }) => theme.spacing[2]};
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[3]};
`;

const PageSubtitle = styled.p`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  color: ${({ theme }) => theme.colors.gray[600]};
`;

const UploadCard = styled(Card)`
  margin-bottom: ${({ theme }) => theme.spacing[6]};
`;

const DropZone = styled(motion.div)`
  border: 2px dashed ${({ theme, isDragActive }) =>
    isDragActive ? theme.colors.primary[500] : theme.colors.gray[300]};
  border-radius: ${({ theme }) => theme.borderRadius.xl};
  padding: ${({ theme }) => theme.spacing[12]} ${({ theme }) => theme.spacing[8]};
  text-align: center;
  cursor: pointer;
  transition: all ${({ theme }) => theme.transitions.normal};
  background: ${({ theme, isDragActive }) =>
    isDragActive ? theme.colors.primary[25] : theme.colors.gray[50]};

  &:hover {
    border-color: ${({ theme }) => theme.colors.primary[400]};
    background: ${({ theme }) => theme.colors.primary[25]};
  }
`;

const DropZoneIcon = styled(motion.div)`
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, ${({ theme }) => theme.colors.primary[500]}, ${({ theme }) => theme.colors.primary[600]});
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto ${({ theme }) => theme.spacing[4]};
  color: white;
  font-size: ${({ theme }) => theme.fontSizes['2xl']};
  box-shadow: ${({ theme }) => theme.shadows.lg};
`;

const DropZoneText = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.xl};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  color: ${({ theme }) => theme.colors.gray[700]};
  margin-bottom: ${({ theme }) => theme.spacing[2]};
`;

const DropZoneSubtext = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.base};
  color: ${({ theme }) => theme.colors.gray[500]};
  margin-bottom: ${({ theme }) => theme.spacing[4]};
`;

const SupportedFormats = styled.div`
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: ${({ theme }) => theme.spacing[2]};
  margin-top: ${({ theme }) => theme.spacing[4]};
`;

const FormatBadge = styled.div`
  padding: ${({ theme }) => theme.spacing[1]} ${({ theme }) => theme.spacing[2]};
  background: ${({ theme }) => theme.colors.gray[100]};
  color: ${({ theme }) => theme.colors.gray[600]};
  border-radius: ${({ theme }) => theme.borderRadius.base};
  font-size: ${({ theme }) => theme.fontSizes.xs};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
`;

const ProgressSection = styled(motion.div)`
  margin-top: ${({ theme }) => theme.spacing[6]};
`;

const ProgressBar = styled.div`
  width: 100%;
  height: 8px;
  background: ${({ theme }) => theme.colors.gray[200]};
  border-radius: ${({ theme }) => theme.borderRadius.full};
  overflow: hidden;
  margin: ${({ theme }) => theme.spacing[3]} 0;
`;

const Progress = styled(motion.div)`
  height: 100%;
  background: linear-gradient(90deg, ${({ theme }) => theme.colors.primary[500]}, ${({ theme }) => theme.colors.primary[600]});
  border-radius: ${({ theme }) => theme.borderRadius.full};
  transition: width ${({ theme }) => theme.transitions.normal};
`;

const ProgressText = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme }) => theme.colors.gray[600]};
`;

const FileDetailsForm = styled.div`
  display: grid;
  gap: ${({ theme }) => theme.spacing[4]};
  margin-top: ${({ theme }) => theme.spacing[6]};
`;

const FormRow = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: ${({ theme }) => theme.spacing[4]};
`;

const SuccessMessage = styled(motion.div)`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[3]};
  padding: ${({ theme }) => theme.spacing[4]};
  background: ${({ theme }) => theme.colors.success[50]};
  border: 1px solid ${({ theme }) => theme.colors.success[200]};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  color: ${({ theme }) => theme.colors.success[700]};
  margin-top: ${({ theme }) => theme.spacing[4]};
`;

const ErrorMessage = styled(motion.div)`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing[3]};
  padding: ${({ theme }) => theme.spacing[4]};
  background: ${({ theme }) => theme.colors.error[50]};
  border: 1px solid ${({ theme }) => theme.colors.error[200]};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  color: ${({ theme }) => theme.colors.error[700]};
  margin-top: ${({ theme }) => theme.spacing[4]};
`;

function FileUpload() {
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploading, setUploading] = useState(false);
  const [uploadSuccess, setUploadSuccess] = useState(false);
  const [uploadError, setUploadError] = useState('');
  const [selectedFile, setSelectedFile] = useState(null);
  const [fileDetails, setFileDetails] = useState({
    title: '',
    description: '',
    subject: '',
    semester: ''
  });

  const supportedFormats = ['PDF', 'DOC', 'DOCX', 'PPT', 'PPTX', 'MP4', 'MP3', 'JPG', 'PNG'];

  const onDrop = async (acceptedFiles) => {
    const file = acceptedFiles[0];
    if (!file) return;

    setSelectedFile(file);
    setFileDetails(prev => ({
      ...prev,
      title: file.name.split('.')[0]
    }));
    setUploadSuccess(false);
    setUploadError('');
  };

  const handleUpload = async () => {
    if (!selectedFile) return;

    setUploading(true);
    setUploadProgress(0);
    setUploadError('');

    try {
      // محاكاة تقدم الرفع
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + Math.random() * 10;
        });
      }, 200);

      const uploadData = {
        file: selectedFile,
        title: fileDetails.title,
        description: fileDetails.description,
        subject: fileDetails.subject,
        semester: fileDetails.semester
      };

      const result = await files.upload(uploadData);

      clearInterval(progressInterval);
      setUploadProgress(100);
      setUploadSuccess(true);
      toast.success('تم رفع الملف بنجاح!');

      // Reset form
      setTimeout(() => {
        setSelectedFile(null);
        setFileDetails({
          title: '',
          description: '',
          subject: '',
          semester: ''
        });
        setUploadProgress(0);
        setUploadSuccess(false);
      }, 3000);

    } catch (error) {
      const errorMessage = error.message || 'فشل في رفع الملف';
      setUploadError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setUploading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFileDetails(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const resetUpload = () => {
    setSelectedFile(null);
    setFileDetails({
      title: '',
      description: '',
      subject: '',
      semester: ''
    });
    setUploadProgress(0);
    setUploadSuccess(false);
    setUploadError('');
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    multiple: false,
    accept: {
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'application/vnd.ms-powerpoint': ['.ppt'],
      'application/vnd.openxmlformats-officedocument.presentationml.presentation': ['.pptx'],
      'video/mp4': ['.mp4'],
      'audio/mpeg': ['.mp3'],
      'image/jpeg': ['.jpg', '.jpeg'],
      'image/png': ['.png']
    },
    maxSize: 100 * 1024 * 1024 // 100MB
  });

  return (
    <UploadContainer>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <PageHeader>
          <PageTitle>
            <FiUpload size={32} />
            رفع ملف جديد
          </PageTitle>
          <PageSubtitle>قم برفع الملفات التعليمية ومشاركتها مع الطلاب</PageSubtitle>
        </PageHeader>

        <UploadCard padding="xl">
          {!selectedFile ? (
            <DropZone
              {...getRootProps()}
              isDragActive={isDragActive}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <input {...getInputProps()} />
              <DropZoneIcon
                animate={isDragActive ? { scale: 1.1 } : { scale: 1 }}
                transition={{ duration: 0.2 }}
              >
                <FiUpload />
              </DropZoneIcon>
              <DropZoneText>
                {isDragActive ? 'اتركه هنا...' : 'اسحب الملف هنا أو انقر للاختيار'}
              </DropZoneText>
              <DropZoneSubtext>
                الحد الأقصى لحجم الملف: 100 ميجابايت
              </DropZoneSubtext>
              <SupportedFormats>
                {supportedFormats.map(format => (
                  <FormatBadge key={format}>{format}</FormatBadge>
                ))}
              </SupportedFormats>
            </DropZone>
          ) : (
            <div>
              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '24px' }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                  <FiFile size={24} />
                  <div>
                    <div style={{ fontWeight: 600 }}>{selectedFile.name}</div>
                    <div style={{ fontSize: '14px', color: '#6b7280' }}>
                      {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                    </div>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={resetUpload}
                  leftIcon={<FiX size={16} />}
                >
                  إزالة
                </Button>
              </div>

              <FileDetailsForm>
                <Input
                  label="عنوان الملف *"
                  name="title"
                  value={fileDetails.title}
                  onChange={handleInputChange}
                  placeholder="أدخل عنوان الملف"
                  required
                />

                <Input
                  label="وصف الملف"
                  name="description"
                  value={fileDetails.description}
                  onChange={handleInputChange}
                  placeholder="وصف مختصر للملف (اختياري)"
                />

                <FormRow>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 500 }}>
                      المادة *
                    </label>
                    <select
                      name="subject"
                      value={fileDetails.subject}
                      onChange={handleInputChange}
                      required
                      style={{
                        width: '100%',
                        padding: '12px 16px',
                        border: '2px solid #e5e7eb',
                        borderRadius: '8px',
                        fontSize: '16px'
                      }}
                    >
                      <option value="">اختر المادة</option>
                      <option value="math">الرياضيات</option>
                      <option value="science">العلوم</option>
                      <option value="arabic">اللغة العربية</option>
                      <option value="english">اللغة الإنجليزية</option>
                      <option value="history">التاريخ</option>
                      <option value="geography">الجغرافيا</option>
                    </select>
                  </div>

                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: 500 }}>
                      الفصل الدراسي *
                    </label>
                    <select
                      name="semester"
                      value={fileDetails.semester}
                      onChange={handleInputChange}
                      required
                      style={{
                        width: '100%',
                        padding: '12px 16px',
                        border: '2px solid #e5e7eb',
                        borderRadius: '8px',
                        fontSize: '16px'
                      }}
                    >
                      <option value="">اختر الفصل</option>
                      <option value="first">الفصل الأول</option>
                      <option value="second">الفصل الثاني</option>
                      <option value="summer">الفصل الصيفي</option>
                    </select>
                  </div>
                </FormRow>

                <div style={{ display: 'flex', gap: '12px', marginTop: '24px' }}>
                  <Button
                    variant="primary"
                    size="lg"
                    onClick={handleUpload}
                    loading={uploading}
                    disabled={!fileDetails.title || !fileDetails.subject || !fileDetails.semester}
                    leftIcon={<FiUpload size={18} />}
                    style={{ flex: 1 }}
                  >
                    {uploading ? 'جاري الرفع...' : 'رفع الملف'}
                  </Button>

                  <Button
                    variant="outline"
                    size="lg"
                    onClick={resetUpload}
                    disabled={uploading}
                  >
                    إلغاء
                  </Button>
                </div>
              </FileDetailsForm>
            </div>
          )}

          <AnimatePresence>
            {uploading && (
              <ProgressSection
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
              >
                <ProgressText>
                  <span>جاري رفع الملف...</span>
                  <span>{uploadProgress}%</span>
                </ProgressText>
                <ProgressBar>
                  <Progress
                    style={{ width: `${uploadProgress}%` }}
                    initial={{ width: 0 }}
                    animate={{ width: `${uploadProgress}%` }}
                  />
                </ProgressBar>
              </ProgressSection>
            )}

            {uploadSuccess && (
              <SuccessMessage
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
              >
                <FiCheck size={20} />
                <span>تم رفع الملف بنجاح! سيتم إعادة تعيين النموذج خلال ثوانٍ...</span>
              </SuccessMessage>
            )}

            {uploadError && (
              <ErrorMessage
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
              >
                <FiAlertCircle size={20} />
                <span>{uploadError}</span>
              </ErrorMessage>
            )}
          </AnimatePresence>
        </UploadCard>
      </motion.div>
    </UploadContainer>
  );
}

export default FileUpload;