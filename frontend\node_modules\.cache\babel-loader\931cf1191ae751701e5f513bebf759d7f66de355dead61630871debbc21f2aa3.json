{"ast": null, "code": "function infiniteQueryBehavior() {\n  return {\n    onFetch: context => {\n      context.fetchFn = () => {\n        var _context$fetchOptions, _context$fetchOptions2, _context$fetchOptions3, _context$fetchOptions4, _context$state$data, _context$state$data2;\n        const refetchPage = (_context$fetchOptions = context.fetchOptions) == null ? void 0 : (_context$fetchOptions2 = _context$fetchOptions.meta) == null ? void 0 : _context$fetchOptions2.refetchPage;\n        const fetchMore = (_context$fetchOptions3 = context.fetchOptions) == null ? void 0 : (_context$fetchOptions4 = _context$fetchOptions3.meta) == null ? void 0 : _context$fetchOptions4.fetchMore;\n        const pageParam = fetchMore == null ? void 0 : fetchMore.pageParam;\n        const isFetchingNextPage = (fetchMore == null ? void 0 : fetchMore.direction) === 'forward';\n        const isFetchingPreviousPage = (fetchMore == null ? void 0 : fetchMore.direction) === 'backward';\n        const oldPages = ((_context$state$data = context.state.data) == null ? void 0 : _context$state$data.pages) || [];\n        const oldPageParams = ((_context$state$data2 = context.state.data) == null ? void 0 : _context$state$data2.pageParams) || [];\n        let newPageParams = oldPageParams;\n        let cancelled = false;\n        const addSignalProperty = object => {\n          Object.defineProperty(object, 'signal', {\n            enumerable: true,\n            get: () => {\n              var _context$signal;\n              if ((_context$signal = context.signal) != null && _context$signal.aborted) {\n                cancelled = true;\n              } else {\n                var _context$signal2;\n                (_context$signal2 = context.signal) == null ? void 0 : _context$signal2.addEventListener('abort', () => {\n                  cancelled = true;\n                });\n              }\n              return context.signal;\n            }\n          });\n        }; // Get query function\n\n        const queryFn = context.options.queryFn || (() => Promise.reject(\"Missing queryFn for queryKey '\" + context.options.queryHash + \"'\"));\n        const buildNewPages = (pages, param, page, previous) => {\n          newPageParams = previous ? [param, ...newPageParams] : [...newPageParams, param];\n          return previous ? [page, ...pages] : [...pages, page];\n        }; // Create function to fetch a page\n\n        const fetchPage = (pages, manual, param, previous) => {\n          if (cancelled) {\n            return Promise.reject('Cancelled');\n          }\n          if (typeof param === 'undefined' && !manual && pages.length) {\n            return Promise.resolve(pages);\n          }\n          const queryFnContext = {\n            queryKey: context.queryKey,\n            pageParam: param,\n            meta: context.options.meta\n          };\n          addSignalProperty(queryFnContext);\n          const queryFnResult = queryFn(queryFnContext);\n          const promise = Promise.resolve(queryFnResult).then(page => buildNewPages(pages, param, page, previous));\n          return promise;\n        };\n        let promise; // Fetch first page?\n\n        if (!oldPages.length) {\n          promise = fetchPage([]);\n        } // Fetch next page?\n        else if (isFetchingNextPage) {\n          const manual = typeof pageParam !== 'undefined';\n          const param = manual ? pageParam : getNextPageParam(context.options, oldPages);\n          promise = fetchPage(oldPages, manual, param);\n        } // Fetch previous page?\n        else if (isFetchingPreviousPage) {\n          const manual = typeof pageParam !== 'undefined';\n          const param = manual ? pageParam : getPreviousPageParam(context.options, oldPages);\n          promise = fetchPage(oldPages, manual, param, true);\n        } // Refetch pages\n        else {\n          newPageParams = [];\n          const manual = typeof context.options.getNextPageParam === 'undefined';\n          const shouldFetchFirstPage = refetchPage && oldPages[0] ? refetchPage(oldPages[0], 0, oldPages) : true; // Fetch first page\n\n          promise = shouldFetchFirstPage ? fetchPage([], manual, oldPageParams[0]) : Promise.resolve(buildNewPages([], oldPageParams[0], oldPages[0])); // Fetch remaining pages\n\n          for (let i = 1; i < oldPages.length; i++) {\n            promise = promise.then(pages => {\n              const shouldFetchNextPage = refetchPage && oldPages[i] ? refetchPage(oldPages[i], i, oldPages) : true;\n              if (shouldFetchNextPage) {\n                const param = manual ? oldPageParams[i] : getNextPageParam(context.options, pages);\n                return fetchPage(pages, manual, param);\n              }\n              return Promise.resolve(buildNewPages(pages, oldPageParams[i], oldPages[i]));\n            });\n          }\n        }\n        const finalPromise = promise.then(pages => ({\n          pages,\n          pageParams: newPageParams\n        }));\n        return finalPromise;\n      };\n    }\n  };\n}\nfunction getNextPageParam(options, pages) {\n  return options.getNextPageParam == null ? void 0 : options.getNextPageParam(pages[pages.length - 1], pages);\n}\nfunction getPreviousPageParam(options, pages) {\n  return options.getPreviousPageParam == null ? void 0 : options.getPreviousPageParam(pages[0], pages);\n}\n/**\n * Checks if there is a next page.\n * Returns `undefined` if it cannot be determined.\n */\n\nfunction hasNextPage(options, pages) {\n  if (options.getNextPageParam && Array.isArray(pages)) {\n    const nextPageParam = getNextPageParam(options, pages);\n    return typeof nextPageParam !== 'undefined' && nextPageParam !== null && nextPageParam !== false;\n  }\n  return;\n}\n/**\n * Checks if there is a previous page.\n * Returns `undefined` if it cannot be determined.\n */\n\nfunction hasPreviousPage(options, pages) {\n  if (options.getPreviousPageParam && Array.isArray(pages)) {\n    const previousPageParam = getPreviousPageParam(options, pages);\n    return typeof previousPageParam !== 'undefined' && previousPageParam !== null && previousPageParam !== false;\n  }\n  return;\n}\nexport { getNextPageParam, getPreviousPageParam, hasNextPage, hasPreviousPage, infiniteQueryBehavior };", "map": {"version": 3, "names": ["infiniteQueryBehavior", "onFetch", "context", "fetchFn", "_context$fetchOptions", "_context$fetchOptions2", "_context$fetchOptions3", "_context$fetchOptions4", "_context$state$data", "_context$state$data2", "refetchPage", "fetchOptions", "meta", "fetchMore", "pageParam", "isFetchingNextPage", "direction", "isFetchingPreviousPage", "oldPages", "state", "data", "pages", "oldPageParams", "pageParams", "newPageParams", "cancelled", "addSignalProperty", "object", "Object", "defineProperty", "enumerable", "get", "_context$signal", "signal", "aborted", "_context$signal2", "addEventListener", "queryFn", "options", "Promise", "reject", "queryHash", "buildNewPages", "param", "page", "previous", "fetchPage", "manual", "length", "resolve", "queryFnContext", "query<PERSON><PERSON>", "queryFnResult", "promise", "then", "getNextPageParam", "getPreviousPageParam", "shouldFetchFirstPage", "i", "shouldFetchNextPage", "finalPromise", "hasNextPage", "Array", "isArray", "nextPageParam", "hasPreviousPage", "previousPageParam"], "sources": ["D:\\menasa\\frontend\\node_modules\\@tanstack\\query-core\\src\\infiniteQueryBehavior.ts"], "sourcesContent": ["import type { QueryBehavior } from './query'\n\nimport type {\n  InfiniteData,\n  QueryFunctionContext,\n  QueryOptions,\n  RefetchQueryFilters,\n} from './types'\n\nexport function infiniteQueryBehavior<\n  TQueryFnData,\n  TError,\n  TData,\n>(): QueryBehavior<TQueryFnData, TError, InfiniteData<TData>> {\n  return {\n    onFetch: (context) => {\n      context.fetchFn = () => {\n        const refetchPage: RefetchQueryFilters['refetchPage'] | undefined =\n          context.fetchOptions?.meta?.refetchPage\n        const fetchMore = context.fetchOptions?.meta?.fetchMore\n        const pageParam = fetchMore?.pageParam\n        const isFetchingNextPage = fetchMore?.direction === 'forward'\n        const isFetchingPreviousPage = fetchMore?.direction === 'backward'\n        const oldPages = context.state.data?.pages || []\n        const oldPageParams = context.state.data?.pageParams || []\n        let newPageParams = oldPageParams\n        let cancelled = false\n\n        const addSignalProperty = (object: unknown) => {\n          Object.defineProperty(object, 'signal', {\n            enumerable: true,\n            get: () => {\n              if (context.signal?.aborted) {\n                cancelled = true\n              } else {\n                context.signal?.addEventListener('abort', () => {\n                  cancelled = true\n                })\n              }\n              return context.signal\n            },\n          })\n        }\n\n        // Get query function\n        const queryFn =\n          context.options.queryFn ||\n          (() =>\n            Promise.reject(\n              `Missing queryFn for queryKey '${context.options.queryHash}'`,\n            ))\n\n        const buildNewPages = (\n          pages: unknown[],\n          param: unknown,\n          page: unknown,\n          previous?: boolean,\n        ) => {\n          newPageParams = previous\n            ? [param, ...newPageParams]\n            : [...newPageParams, param]\n          return previous ? [page, ...pages] : [...pages, page]\n        }\n\n        // Create function to fetch a page\n        const fetchPage = (\n          pages: unknown[],\n          manual?: boolean,\n          param?: unknown,\n          previous?: boolean,\n        ): Promise<unknown[]> => {\n          if (cancelled) {\n            return Promise.reject('Cancelled')\n          }\n\n          if (typeof param === 'undefined' && !manual && pages.length) {\n            return Promise.resolve(pages)\n          }\n\n          const queryFnContext: QueryFunctionContext = {\n            queryKey: context.queryKey,\n            pageParam: param,\n            meta: context.options.meta,\n          }\n\n          addSignalProperty(queryFnContext)\n\n          const queryFnResult = queryFn(queryFnContext)\n\n          const promise = Promise.resolve(queryFnResult).then((page) =>\n            buildNewPages(pages, param, page, previous),\n          )\n\n          return promise\n        }\n\n        let promise: Promise<unknown[]>\n\n        // Fetch first page?\n        if (!oldPages.length) {\n          promise = fetchPage([])\n        }\n\n        // Fetch next page?\n        else if (isFetchingNextPage) {\n          const manual = typeof pageParam !== 'undefined'\n          const param = manual\n            ? pageParam\n            : getNextPageParam(context.options, oldPages)\n          promise = fetchPage(oldPages, manual, param)\n        }\n\n        // Fetch previous page?\n        else if (isFetchingPreviousPage) {\n          const manual = typeof pageParam !== 'undefined'\n          const param = manual\n            ? pageParam\n            : getPreviousPageParam(context.options, oldPages)\n          promise = fetchPage(oldPages, manual, param, true)\n        }\n\n        // Refetch pages\n        else {\n          newPageParams = []\n\n          const manual = typeof context.options.getNextPageParam === 'undefined'\n\n          const shouldFetchFirstPage =\n            refetchPage && oldPages[0]\n              ? refetchPage(oldPages[0], 0, oldPages)\n              : true\n\n          // Fetch first page\n          promise = shouldFetchFirstPage\n            ? fetchPage([], manual, oldPageParams[0])\n            : Promise.resolve(buildNewPages([], oldPageParams[0], oldPages[0]))\n\n          // Fetch remaining pages\n          for (let i = 1; i < oldPages.length; i++) {\n            promise = promise.then((pages) => {\n              const shouldFetchNextPage =\n                refetchPage && oldPages[i]\n                  ? refetchPage(oldPages[i], i, oldPages)\n                  : true\n\n              if (shouldFetchNextPage) {\n                const param = manual\n                  ? oldPageParams[i]\n                  : getNextPageParam(context.options, pages)\n                return fetchPage(pages, manual, param)\n              }\n              return Promise.resolve(\n                buildNewPages(pages, oldPageParams[i], oldPages[i]),\n              )\n            })\n          }\n        }\n\n        const finalPromise = promise.then((pages) => ({\n          pages,\n          pageParams: newPageParams,\n        }))\n\n        return finalPromise\n      }\n    },\n  }\n}\n\nexport function getNextPageParam(\n  options: QueryOptions<any, any>,\n  pages: unknown[],\n): unknown | undefined {\n  return options.getNextPageParam?.(pages[pages.length - 1], pages)\n}\n\nexport function getPreviousPageParam(\n  options: QueryOptions<any, any>,\n  pages: unknown[],\n): unknown | undefined {\n  return options.getPreviousPageParam?.(pages[0], pages)\n}\n\n/**\n * Checks if there is a next page.\n * Returns `undefined` if it cannot be determined.\n */\nexport function hasNextPage(\n  options: QueryOptions<any, any, any, any>,\n  pages?: unknown,\n): boolean | undefined {\n  if (options.getNextPageParam && Array.isArray(pages)) {\n    const nextPageParam = getNextPageParam(options, pages)\n    return (\n      typeof nextPageParam !== 'undefined' &&\n      nextPageParam !== null &&\n      nextPageParam !== false\n    )\n  }\n  return\n}\n\n/**\n * Checks if there is a previous page.\n * Returns `undefined` if it cannot be determined.\n */\nexport function hasPreviousPage(\n  options: QueryOptions<any, any, any, any>,\n  pages?: unknown,\n): boolean | undefined {\n  if (options.getPreviousPageParam && Array.isArray(pages)) {\n    const previousPageParam = getPreviousPageParam(options, pages)\n    return (\n      typeof previousPageParam !== 'undefined' &&\n      previousPageParam !== null &&\n      previousPageParam !== false\n    )\n  }\n  return\n}\n"], "mappings": "AASO,SAASA,qBAATA,CAAA,EAIuD;EAC5D,OAAO;IACLC,OAAO,EAAGC,OAAD,IAAa;MACpBA,OAAO,CAACC,OAAR,GAAkB,MAAM;QAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,mBAAA,EAAAC,oBAAA;QACtB,MAAMC,WAA2D,GAC/D,CAAAN,qBAAA,GAAAF,OAAO,CAACS,YADuD,sBAAAN,sBAAA,GAC/DD,qBAAsB,CAAAQ,IADyC,KAC/D,gBAAAP,sBAAA,CAA4BK,WAD9B;QAEA,MAAMG,SAAS,GAAG,CAAAP,sBAAA,GAAAJ,OAAO,CAACS,YAAX,sBAAAJ,sBAAA,GAAGD,sBAAsB,CAAAM,IAAzB,KAAG,gBAAAL,sBAAA,CAA4BM,SAA9C;QACA,MAAMC,SAAS,GAAGD,SAAH,IAAG,gBAAAA,SAAS,CAAEC,SAA7B;QACA,MAAMC,kBAAkB,GAAG,CAAAF,SAAS,QAAT,YAAAA,SAAS,CAAEG,SAAX,MAAyB,SAApD;QACA,MAAMC,sBAAsB,GAAG,CAAAJ,SAAS,QAAT,YAAAA,SAAS,CAAEG,SAAX,MAAyB,UAAxD;QACA,MAAME,QAAQ,GAAG,EAAAV,mBAAA,GAAAN,OAAO,CAACiB,KAAR,CAAcC,IAAd,qBAAAZ,mBAAA,CAAoBa,KAApB,KAA6B,EAA9C;QACA,MAAMC,aAAa,GAAG,EAAAb,oBAAA,GAAAP,OAAO,CAACiB,KAAR,CAAcC,IAAd,qBAAAX,oBAAA,CAAoBc,UAApB,KAAkC,EAAxD;QACA,IAAIC,aAAa,GAAGF,aAApB;QACA,IAAIG,SAAS,GAAG,KAAhB;QAEA,MAAMC,iBAAiB,GAAIC,MAAD,IAAqB;UAC7CC,MAAM,CAACC,cAAP,CAAsBF,MAAtB,EAA8B,QAA9B,EAAwC;YACtCG,UAAU,EAAE,IAD0B;YAEtCC,GAAG,EAAEA,CAAA,KAAM;cAAA,IAAAC,eAAA;cACT,KAAAA,eAAA,GAAI9B,OAAO,CAAC+B,MAAZ,KAAI,QAAAD,eAAA,CAAgBE,OAApB,EAA6B;gBAC3BT,SAAS,GAAG,IAAZ;cACD,CAFD,MAEO;gBAAA,IAAAU,gBAAA;gBACL,CAAAA,gBAAA,GAAAjC,OAAO,CAAC+B,MAAR,qBAAAE,gBAAA,CAAgBC,gBAAhB,CAAiC,OAAjC,EAA0C,MAAM;kBAC9CX,SAAS,GAAG,IAAZ;iBADF;cAGD;cACD,OAAOvB,OAAO,CAAC+B,MAAf;YACD;WAXH;QAaD,CAdD,CAZsB;;QA6BtB,MAAMI,OAAO,GACXnC,OAAO,CAACoC,OAAR,CAAgBD,OAAhB,KACC,MACCE,OAAO,CAACC,MAAR,oCACmCtC,OAAO,CAACoC,OAAR,CAAgBG,SADnD,OAFF,CADF;QAOA,MAAMC,aAAa,GAAGA,CACpBrB,KADoB,EAEpBsB,KAFoB,EAGpBC,IAHoB,EAIpBC,QAJoB,KAKjB;UACHrB,aAAa,GAAGqB,QAAQ,GACpB,CAACF,KAAD,EAAQ,GAAGnB,aAAX,CADoB,GAEpB,CAAC,GAAGA,aAAJ,EAAmBmB,KAAnB,CAFJ;UAGA,OAAOE,QAAQ,GAAG,CAACD,IAAD,EAAO,GAAGvB,KAAV,CAAH,GAAsB,CAAC,GAAGA,KAAJ,EAAWuB,IAAX,CAArC;QACD,CAVD,CApCsB;;QAiDtB,MAAME,SAAS,GAAGA,CAChBzB,KADgB,EAEhB0B,MAFgB,EAGhBJ,KAHgB,EAIhBE,QAJgB,KAKO;UACvB,IAAIpB,SAAJ,EAAe;YACb,OAAOc,OAAO,CAACC,MAAR,CAAe,WAAf,CAAP;UACD;UAED,IAAI,OAAOG,KAAP,KAAiB,WAAjB,IAAgC,CAACI,MAAjC,IAA2C1B,KAAK,CAAC2B,MAArD,EAA6D;YAC3D,OAAOT,OAAO,CAACU,OAAR,CAAgB5B,KAAhB,CAAP;UACD;UAED,MAAM6B,cAAoC,GAAG;YAC3CC,QAAQ,EAAEjD,OAAO,CAACiD,QADyB;YAE3CrC,SAAS,EAAE6B,KAFgC;YAG3C/B,IAAI,EAAEV,OAAO,CAACoC,OAAR,CAAgB1B;WAHxB;UAMAc,iBAAiB,CAACwB,cAAD,CAAjB;UAEA,MAAME,aAAa,GAAGf,OAAO,CAACa,cAAD,CAA7B;UAEA,MAAMG,OAAO,GAAGd,OAAO,CAACU,OAAR,CAAgBG,aAAhB,CAA+B,CAAAE,IAA/B,CAAqCV,IAAD,IAClDF,aAAa,CAACrB,KAAD,EAAQsB,KAAR,EAAeC,IAAf,EAAqBC,QAArB,CADC,CAAhB;UAIA,OAAOQ,OAAP;SA5BF;QA+BA,IAAIA,OAAJ,CAhFsB;;QAmFtB,IAAI,CAACnC,QAAQ,CAAC8B,MAAd,EAAsB;UACpBK,OAAO,GAAGP,SAAS,CAAC,EAAD,CAAnB;QACD,CAFD;QAAA,KAKK,IAAI/B,kBAAJ,EAAwB;UAC3B,MAAMgC,MAAM,GAAG,OAAOjC,SAAP,KAAqB,WAApC;UACA,MAAM6B,KAAK,GAAGI,MAAM,GAChBjC,SADgB,GAEhByC,gBAAgB,CAACrD,OAAO,CAACoC,OAAT,EAAkBpB,QAAlB,CAFpB;UAGAmC,OAAO,GAAGP,SAAS,CAAC5B,QAAD,EAAW6B,MAAX,EAAmBJ,KAAnB,CAAnB;QACD,CANI;QAAA,KASA,IAAI1B,sBAAJ,EAA4B;UAC/B,MAAM8B,MAAM,GAAG,OAAOjC,SAAP,KAAqB,WAApC;UACA,MAAM6B,KAAK,GAAGI,MAAM,GAChBjC,SADgB,GAEhB0C,oBAAoB,CAACtD,OAAO,CAACoC,OAAT,EAAkBpB,QAAlB,CAFxB;UAGAmC,OAAO,GAAGP,SAAS,CAAC5B,QAAD,EAAW6B,MAAX,EAAmBJ,KAAnB,EAA0B,IAA1B,CAAnB;QACD,CANI;QAAA,KASA;UACHnB,aAAa,GAAG,EAAhB;UAEA,MAAMuB,MAAM,GAAG,OAAO7C,OAAO,CAACoC,OAAR,CAAgBiB,gBAAvB,KAA4C,WAA3D;UAEA,MAAME,oBAAoB,GACxB/C,WAAW,IAAIQ,QAAQ,CAAC,CAAD,CAAvB,GACIR,WAAW,CAACQ,QAAQ,CAAC,CAAD,CAAT,EAAc,CAAd,EAAiBA,QAAjB,CADf,GAEI,IAHN,CALG;;UAWHmC,OAAO,GAAGI,oBAAoB,GAC1BX,SAAS,CAAC,EAAD,EAAKC,MAAL,EAAazB,aAAa,CAAC,CAAD,CAA1B,CADiB,GAE1BiB,OAAO,CAACU,OAAR,CAAgBP,aAAa,CAAC,EAAD,EAAKpB,aAAa,CAAC,CAAD,CAAlB,EAAuBJ,QAAQ,CAAC,CAAD,CAA/B,CAA7B,CAFJ,CAXG;;UAgBH,KAAK,IAAIwC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGxC,QAAQ,CAAC8B,MAA7B,EAAqCU,CAAC,EAAtC,EAA0C;YACxCL,OAAO,GAAGA,OAAO,CAACC,IAAR,CAAcjC,KAAD,IAAW;cAChC,MAAMsC,mBAAmB,GACvBjD,WAAW,IAAIQ,QAAQ,CAACwC,CAAD,CAAvB,GACIhD,WAAW,CAACQ,QAAQ,CAACwC,CAAD,CAAT,EAAcA,CAAd,EAAiBxC,QAAjB,CADf,GAEI,IAHN;cAKA,IAAIyC,mBAAJ,EAAyB;gBACvB,MAAMhB,KAAK,GAAGI,MAAM,GAChBzB,aAAa,CAACoC,CAAD,CADG,GAEhBH,gBAAgB,CAACrD,OAAO,CAACoC,OAAT,EAAkBjB,KAAlB,CAFpB;gBAGA,OAAOyB,SAAS,CAACzB,KAAD,EAAQ0B,MAAR,EAAgBJ,KAAhB,CAAhB;cACD;cACD,OAAOJ,OAAO,CAACU,OAAR,CACLP,aAAa,CAACrB,KAAD,EAAQC,aAAa,CAACoC,CAAD,CAArB,EAA0BxC,QAAQ,CAACwC,CAAD,CAAlC,CADR,CAAP;YAGD,CAfS,CAAV;UAgBD;QACF;QAED,MAAME,YAAY,GAAGP,OAAO,CAACC,IAAR,CAAcjC,KAAD,KAAY;UAC5CA,KAD4C;UAE5CE,UAAU,EAAEC;QAFgC,CAAZ,CAAb,CAArB;QAKA,OAAOoC,YAAP;OAnJF;IAqJD;GAvJH;AAyJD;AAEM,SAASL,gBAATA,CACLjB,OADK,EAELjB,KAFK,EAGgB;EACrB,OAAOiB,OAAO,CAACiB,gBAAf,oBAAOjB,OAAO,CAACiB,gBAAR,CAA2BlC,KAAK,CAACA,KAAK,CAAC2B,MAAN,GAAe,CAAhB,CAAhC,EAAoD3B,KAApD,CAAP;AACD;AAEM,SAASmC,oBAATA,CACLlB,OADK,EAELjB,KAFK,EAGgB;EACrB,OAAOiB,OAAO,CAACkB,oBAAf,oBAAOlB,OAAO,CAACkB,oBAAR,CAA+BnC,KAAK,CAAC,CAAD,CAApC,EAAyCA,KAAzC,CAAP;AACD;AAED;AACA;AACA;AACA;;AACO,SAASwC,WAATA,CACLvB,OADK,EAELjB,KAFK,EAGgB;EACrB,IAAIiB,OAAO,CAACiB,gBAAR,IAA4BO,KAAK,CAACC,OAAN,CAAc1C,KAAd,CAAhC,EAAsD;IACpD,MAAM2C,aAAa,GAAGT,gBAAgB,CAACjB,OAAD,EAAUjB,KAAV,CAAtC;IACA,OACE,OAAO2C,aAAP,KAAyB,WAAzB,IACAA,aAAa,KAAK,IADlB,IAEAA,aAAa,KAAK,KAHpB;EAKD;EACD;AACD;AAED;AACA;AACA;AACA;;AACO,SAASC,eAATA,CACL3B,OADK,EAELjB,KAFK,EAGgB;EACrB,IAAIiB,OAAO,CAACkB,oBAAR,IAAgCM,KAAK,CAACC,OAAN,CAAc1C,KAAd,CAApC,EAA0D;IACxD,MAAM6C,iBAAiB,GAAGV,oBAAoB,CAAClB,OAAD,EAAUjB,KAAV,CAA9C;IACA,OACE,OAAO6C,iBAAP,KAA6B,WAA7B,IACAA,iBAAiB,KAAK,IADtB,IAEAA,iBAAiB,KAAK,KAHxB;EAKD;EACD;AACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}